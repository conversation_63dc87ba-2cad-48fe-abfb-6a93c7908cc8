package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.ExtCompany;
import com.rich.common.core.domain.entity.RsRct;
import com.rich.common.core.domain.model.LoginUser;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.*;
import com.rich.system.service.OptimizedRsRctService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 操作单Controller
 *
 * <AUTHOR>
 * @date 2023-12-20
 */
@Slf4j
@RestController
@RequestMapping("/system/rct")
public class RsRctController extends BaseController {
    @Autowired
    private RsRctService rsRctService;

    @Autowired
    private BasDistLocationService basDistLocationService;

    @Autowired
    private ExtCompanyService extCompanyService;

    @Resource
    private BasPositionService basPositionService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DataAggregatorService dataAggregatorService;

    @Autowired
    private RsOutboundRecordService rsOutboundRecordService;

    @Autowired
    private OptimizedRsRctService optimizedRsRctService;

    /**
     * 查询操作单列表
     */
    @PreAuthorize("@ss.hasPermi('system:rct:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsRct rsRct) {
        Long position = basPositionService.selectPostByUserId(SecurityUtils.getUserId());
        List<RsRct> list;
        if (!rsRct.getParams().containsKey("op") && position < 14 && SecurityUtils.getLoginUser().getUser().getDept().getCreateListNum() != 4) {
            rsRct.setOpId(SecurityUtils.getUserId());
            list = rsRctService.selectRsRctList(rsRct);
            if (list != null && !list.isEmpty()) {
                return getDataTable(list);
            } else {
                return getDataTable(Collections.emptyList());
            }
        }
        list = rsRctService.selectRsRctList(rsRct);
        if (list != null && !list.isEmpty()) {
            return getDataTable(list);
        } else {
            return getDataTable(Collections.emptyList());
        }
    }

    /**
     * 查询审核列表(业务订舱已提交)
     */
    @PreAuthorize("@ss.hasAnyPermi('system:booking:list,system:psa:list')")
    @GetMapping("/listVerifyList")
    public TableDataInfo listVerifyList(RsRct rsRct) {
        List<RsRct> list;
        if (SecurityUtils.getDeptId() == 102L || SecurityUtils.getLoginUser().getUser().isAdmin()) {
            rsRct.setPermissionLevel(null);
            rsRct.setSearchValue(null);
        }
        list = rsRctService.selectUnVerifyRsRctList(rsRct);
        if (list != null && !list.isEmpty()) {
            return getDataTable(list);
        } else {
            return getDataTable(Collections.emptyList());
        }
    }

    @GetMapping("/aggregatorBooking")
    public AjaxResult aggregatorBooking(RsRct rsRct) {
        try {
            // 1. 处理查询参数
            if (SecurityUtils.getDeptId() == 102L || SecurityUtils.getLoginUser().getUser().isAdmin()) {
                rsRct.setPermissionLevel(null);
                rsRct.setSearchValue(null);
            }

            // 2. 获取数据列表
            List<RsRct> dataList = rsRctService.selectUnVerifyExportRsRctList(rsRct);

            // 3. 使用Service提取聚合配置
            Map<String, Object> aggregatorConfig;
            try {
                aggregatorConfig = dataAggregatorService.extractAggregatorConfig(rsRct);
            } catch (RuntimeException e) {
                logger.error("提取聚合配置失败", e);
                return AjaxResult.error(e.getMessage());
            }

            // 4. 判断是否需要执行聚合
            if (aggregatorConfig.isEmpty() || aggregatorConfig.get("primaryField") == null) {
                // 没有有效的聚合配置，返回原始数据
                dataList = dataList.stream().peek(rsRct1 -> {
                    if (rsRct1.getCnInRmb() != null && rsRct1.getCnInRmb().compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal profitRate = rsRct1.getProfitInRmb()
                                .divide(rsRct1.getCnInRmb(), 4, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100"));
                        rsRct1.setProfitRate(profitRate);
                    }
                }).collect(Collectors.toList());

                return AjaxResult.success(dataList);
            }

            // 5. 执行数据聚合
            List<Map<String, Object>> aggregatedData = dataAggregatorService.aggregateData(dataList, aggregatorConfig);

            // 6. 新增：为汇总数据计算毛利率
            for (Map<String, Object> group : aggregatedData) {
                // 获取汇总后的profitInRmb和cnInRmb
                Object profitInRmbObj = group.get("profitInRmb_sum");
                Object dnInRmbObj = group.get("dnInRmb_sum");

                // 检查是否都存在且不为空
                if (profitInRmbObj != null && dnInRmbObj != null) {
                    // 转换为BigDecimal处理
                    BigDecimal profitInRmb = (profitInRmbObj instanceof BigDecimal) ?
                            (BigDecimal) profitInRmbObj : new BigDecimal(profitInRmbObj.toString());
                    BigDecimal cnInRmb = (dnInRmbObj instanceof BigDecimal) ?
                            (BigDecimal) dnInRmbObj : new BigDecimal(dnInRmbObj.toString());

                    // 计算毛利率 (profitInRmb/cnInRmb)*100
                    if (cnInRmb.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal profitRate = profitInRmb
                                .divide(cnInRmb, 4, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100"));
                        // 添加到结果中
                        group.put("profitRateAgg", profitRate);
                    }
                }
            }
            return AjaxResult.success(aggregatedData);
        } catch (Exception e) {
            logger.error("数据汇总处理失败", e);
            return AjaxResult.error("数据汇总处理失败: " + e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasAnyPermi('system:booking:list,system:psa:list')")
    @GetMapping("/listVerifyAggregatorList")
    public TableDataInfo listVerifyAggregatorList(RsRct rsRct) {
        Long position = basPositionService.selectPostByUserId(SecurityUtils.getUserId());
        List<RsRct> list;
        if (SecurityUtils.getDeptId() == 102L || SecurityUtils.getLoginUser().getUser().isAdmin()) {
            rsRct.setPermissionLevel(null);
            rsRct.setSearchValue(null);
        }
        list = rsRctService.selectUnVerifyRsRctAggregatorList(rsRct);
        if (list != null && !list.isEmpty()) {
            return getDataTable(list);
        } else {
            return getDataTable(Collections.emptyList());
        }
    }

    /**
     * 导出操作单列表
     */
    @PreAuthorize("@ss.hasAnyPermi('system:rct:export,system:booking:export')")
    @Log(title = "操作单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsRct rsRct) {
        List<RsRct> list;
        Long position = basPositionService.selectPostByUserId(SecurityUtils.getUserId());
        if (position < 14 && SecurityUtils.getLoginUser().getUser().getDept().getCreateListNum() != 4) {
            rsRct.setOpId(SecurityUtils.getUserId());
            list = rsRctService.selectRsRctListToExport(rsRct);
        } else {
            list = rsRctService.selectRsRctListToExport(rsRct);
        }

        ExcelUtil<RsRct> util = new ExcelUtil<>(RsRct.class);
        util.exportExcel(response, list, "操作单数据");
    }

    /**
     * 查询操作单列表并进行动态分类汇总
     */
    @GetMapping("/aggregator")
    public AjaxResult aggregator(RsRct rsRct) {
        try {
            // 1. 处理查询参数
            Long position = basPositionService.selectPostByUserId(SecurityUtils.getUserId());
            if (position < 14 && SecurityUtils.getLoginUser().getUser().getDept().getCreateListNum() != 4) {
                rsRct.setOpId(SecurityUtils.getUserId());
            }

            // 2. 获取数据列表
            List<RsRct> dataList = rsRctService.selectRsRctListToExport(rsRct);

            // 3. 使用Service提取聚合配置
            Map<String, Object> aggregatorConfig;
            try {
                aggregatorConfig = dataAggregatorService.extractAggregatorConfig(rsRct);
            } catch (RuntimeException e) {
                logger.error("提取聚合配置失败", e);
                return AjaxResult.error(e.getMessage());
            }

            // 4. 判断是否需要执行聚合
            if (aggregatorConfig.isEmpty() || aggregatorConfig.get("primaryField") == null) {
                // 没有有效的聚合配置，返回原始数据
                dataList = dataList.stream().peek(rsRct1 -> {
                    if (rsRct1.getCnInRmb() != null && rsRct1.getCnInRmb().compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal profitRate = rsRct1.getProfitInRmb()
                                .divide(rsRct1.getCnInRmb(), 4, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100"));
                        rsRct1.setProfitRate(profitRate);
                    }
                }).collect(Collectors.toList());

                return AjaxResult.success(dataList);
            }

            // 5. 执行数据聚合
            List<Map<String, Object>> aggregatedData = dataAggregatorService.aggregateData(dataList, aggregatorConfig);

            // 6. 新增：为汇总数据计算毛利率
            for (Map<String, Object> group : aggregatedData) {
                // 获取汇总后的profitInRmb和cnInRmb
                Object profitInRmbObj = group.get("profitInRmb_sum");
                Object dnInRmbObj = group.get("dnInRmb_sum");

                // 检查是否都存在且不为空
                if (profitInRmbObj != null && dnInRmbObj != null) {
                    // 转换为BigDecimal处理
                    BigDecimal profitInRmb = (profitInRmbObj instanceof BigDecimal) ?
                            (BigDecimal) profitInRmbObj : new BigDecimal(profitInRmbObj.toString());
                    BigDecimal cnInRmb = (dnInRmbObj instanceof BigDecimal) ?
                            (BigDecimal) dnInRmbObj : new BigDecimal(dnInRmbObj.toString());

                    // 计算毛利率 (profitInRmb/cnInRmb)*100
                    if (cnInRmb.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal profitRate = profitInRmb
                                .divide(cnInRmb, 4, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100"));
                        // 添加到结果中
                        group.put("profitRateAgg", profitRate);
                    }
                }
            }
            return AjaxResult.success(aggregatedData);
        } catch (Exception e) {
            logger.error("数据汇总处理失败", e);
            return AjaxResult.error("数据汇总处理失败: " + e.getMessage());
        }
    }

    /**
     * 导出操作单列表
     */
    @PreAuthorize("@ss.hasAnyPermi('system:rct:export,system:booking:export')")
    @Log(title = "操作单", businessType = BusinessType.EXPORT)
    @PostMapping("/bookingExport")
    public void bookingExport(HttpServletResponse response, RsRct rsRct) {
        List<RsRct> list;
        Long position = basPositionService.selectPostByUserId(SecurityUtils.getUserId());
        if (SecurityUtils.getDeptId().equals(106L) || SecurityUtils.getDeptId() == 102L || SecurityUtils.getLoginUser().getUser().isAdmin()) {
            rsRct.setPermissionLevel(null);
            rsRct.setSearchValue(null);
        }
        list = rsRctService.selectUnVerifyExportRsRctList(rsRct);

        // 计算毛利率
        list = list.stream().peek(rsRct1 -> {
            if (rsRct1.getCnInRmb() != null && rsRct1.getCnInRmb().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal profitRate = rsRct1.getProfitInRmb()
                        .divide(rsRct1.getCnInRmb(), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                rsRct1.setProfitRate(profitRate); // 假设RsRct类中有setProfitRate方法
            }
        }).collect(Collectors.toList());

        ExcelUtil<RsRct> util = new ExcelUtil<>(RsRct.class);
        util.exportExcel(response, list, "操作单数据");
    }

    /**
     * 获取操作单详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:rct:query')")
    @PreAuthorize("@ss.hasAnyPermi('system:rct:query,system:booking:query')")
    @GetMapping(value = "/{rctId}")
    public AjaxResult getInfo(@PathVariable("rctId") Long rctId) {
        // 1. 获取基础数据
        RsRct rsRct = rsRctService.selectRsRctByRctId(rctId);
        if (rsRct == null) {
            return AjaxResult.error("数据不存在");
        }
        
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put(AjaxResult.DATA_TAG, rsRct);

        // 2. 处理位置信息 - 一次性收集所有位置ID
        Set<Long> locationIds = new HashSet<>();
        addIfNotNull(locationIds, rsRct.getPolId());
        addIfNotNull(locationIds, rsRct.getDestinationPortId());
        addIfNotNull(locationIds, rsRct.getLocalBasicPortId());
        addIfNotNull(locationIds, rsRct.getTransitPortId());
        addIfNotNull(locationIds, rsRct.getDispatchRegionId());
        addIfNotNull(locationIds, rsRct.getPrecarriageRegionId());

        // 只在有位置ID时查询数据库
        if (!locationIds.isEmpty()) {
            ajaxResult.put("locationOptions", basDistLocationService.selectBasDistLocationByIds(locationIds));
        } else {
            ajaxResult.put("locationOptions", null);
        }

        // 3. 处理公司信息 - 一次性收集所有公司ID
        Set<Long> companyIds = new HashSet<>();
        addIfNotNull(companyIds, rsRct.getClientId());
        addIfNotNull(companyIds, rsRct.getPolBookingAgent());

        // 高效处理关联单位ID
        if (rsRct.getRelationClientIdList() != null && !rsRct.getRelationClientIdList().isEmpty()) {
            for (String idStr : rsRct.getRelationClientIdList().split(",")) {
                try {
                    companyIds.add(Long.parseLong(idStr.trim()));
                } catch (NumberFormatException e) {
                    // 忽略无效ID
                }
            }
        }

        // 添加供应商ID
        if (rsRct.getSupplierIds() != null) {
            companyIds.addAll(rsRct.getSupplierIds());
        }

        // 4. 处理公司和供应商数据 - 一次查询后在内存中处理
        List<ExtCompany> extCompanyList = Collections.emptyList();
        List<ExtCompany> supplierList = Collections.emptyList();

        if (!companyIds.isEmpty()) {
            extCompanyList = extCompanyService.selectExtCompanyByCompanyIds(companyIds);

            // 在内存中过滤供应商，避免额外查询
            if (rsRct.getSupplierIds() != null && !rsRct.getSupplierIds().isEmpty()) {
                supplierList = new ArrayList<>();
                for (ExtCompany company : extCompanyList) {
                    if (rsRct.getSupplierIds().contains(company.getCompanyId())) {
                        supplierList.add(company);
                    }
                }
            }
        }

        ajaxResult.put("companyList", extCompanyList);
        ajaxResult.put("supplierList", supplierList);

        // 5. 获取出仓记录 - 可考虑与主查询合并
        ajaxResult.put("outboundRecord", rsOutboundRecordService.selectRsOutboundRecordByRctId(rctId));

        return ajaxResult;
    }

    /**
     * 工具方法：当值不为null时添加到集合
     */
    private void addIfNotNull(Set<Long> set, Long value) {
        if (value != null) {
            set.add(value);
        }
    }

    /**
     * 新增操作单
     */
//    @PreAuthorize("@ss.hasPermi('system:rct:add')")
    @PreAuthorize("@ss.hasAnyPermi('system:rct:add,system:booking:add')")
    @Log(title = "操作单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsRct rsRct) {
        rsRct.setProcessStatusId(2L);
        return AjaxResult.success(rsRctService.insertRsRct(rsRct));
    }

    @PreAuthorize("@ss.hasAnyPermi('system:rct:add,system:booking:add')")
    @Log(title = "操作单", businessType = BusinessType.INSERT)
    @PostMapping("/saveAs")
    public AjaxResult saveAs(@RequestBody RsRct rsRct) {
        rsRct.setRctId(null);
        rsRct.setRctNo(null);
        rsRct.setVerifyPsaId(null);
        rsRct.setOpId(null);
        rsRct.setStatusUpdateTime(null);
        rsRct.setPsaVerifyTime(null);
        rsRct.setRctCreateTime(null);
        rsRct.setPsaVerify("0");
        rsRct.setPsaVerifyStatusId(0L);
        rsRct.setProcessStatusId(2L);
        rsRct.setOpAccept("0");
        rsRct.setRctCreateTime(null);
        rsRct.setSqdShippingBookingStatus("0");
        rsRct.setPodEta(null);
        rsRct.setDestinationPortEta(null);
        rsRct.setNewBookingTime(new Date());
        // 备注清空
//        rsRct.setInquiryInnerRemarkSum(null);
//        rsRct.setNewBookingRemark(null);
//        rsRct.setOpInnerRemark(null);
//        审核和操作都要清空
        return AjaxResult.success(rsRctService.insertRsRct(rsRct));
    }

    /**
     * 修改操作单
     */
    @PreAuthorize("@ss.hasAnyPermi('system:rct:edit,system:booking:edit')")
    @Log(title = "操作单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsRct rsRct) {
        int rows = rsRctService.updateRsRct(rsRct);
        this.notification();
        return toAjax(rows);
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:rct:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsRct rsRct) {
        rsRct.setUpdateBy(getUserId());
        return toAjax(rsRctService.changeStatus(rsRct));
    }

    /**
     * 删除操作单
     */
    @PreAuthorize("@ss.hasAnyPermi('system:rct:remove,system:booking:remove')")
    @Log(title = "操作单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{rctIds}")
    public AjaxResult remove(@PathVariable Long[] rctIds) {
        return toAjax(rsRctService.deleteRsRctByRctIds(rctIds));
    }

    /**
     * 客户信息
     *
     * @param rsRct
     * @return
     */
    @PreAuthorize("@ss.hasAnyPermi('system:rct:add,system:rct:edit')")
    @PostMapping("/saveClientMessage")
    public AjaxResult saveClientMessage(@RequestBody RsRct rsRct) {
        return toAjax(rsRctService.saveClientMessage(rsRct));
    }

    @GetMapping("/mon")
    public AjaxResult getMon() {
        return AjaxResult.success(rsRctService.getMon());
    }

    @GetMapping("/CFmon")
    public AjaxResult getCFMon() {
        return AjaxResult.success(rsRctService.getCFMon());
    }

    @GetMapping("/RSWHMon")
    public AjaxResult getRSWHMon() {
        return AjaxResult.success(rsRctService.getRSWHMon());
    }

    /**
     * 保存操作单所有服务（优化版本）
     * 使用事务分解和异步处理策略提升性能
     */
    @PostMapping("/saveAllService")
    public AjaxResult saveAllService(@RequestBody RsRct rsRct) {
        try {
            // 使用优化的服务处理方法
            RsRct result = optimizedRsRctService.saveAllServicesOptimized(rsRct);
            return AjaxResult.success("操作单服务保存成功", result);
        } catch (Exception e) {
            log.error("保存操作单服务失败，操作单号: {}, 错误: {}", rsRct.getRctNo(), e.getMessage(), e);
            return AjaxResult.error("保存操作单服务失败: " + e.getMessage());
        }
    }

    /**
     * 保存操作单所有服务（原始版本，保留作为备用）
     */
    @PostMapping("/saveAllServiceLegacy")
    public AjaxResult saveAllServiceLegacy(@RequestBody RsRct rsRct) {
        try {
            RsRct savedRct = rsRctService.saveAllServices(rsRct);
            return AjaxResult.success(savedRct);
        } catch (Exception e) {
            return AjaxResult.error("保存服务失败: " + e.getMessage());
        }
    }

    @PostMapping("/saveAsAllService")
    public AjaxResult saveAsAllService(@RequestBody RsRct rsRct) {
        return AjaxResult.success(rsRctService.saveAsAllServices(rsRct));
    }

    @GetMapping("/listRctNoByCompany/{companyId}")
    public AjaxResult listRctNoByCompany(@PathVariable("companyId") Long companyId) {
        return AjaxResult.success(rsRctService.selectRsRctByCompanyId(companyId));
    }

    @GetMapping("/op")
    public AjaxResult op() {
        return AjaxResult.success(rsRctService.statisticsOp());
    }

    @GetMapping("/notification")
    public void notification() {
        rsRctService.notification();
    }

    /**
     * 财务销账后更新操作单中的已收未收
     */
    @PostMapping("/writeoff")
    public void writeoff(@RequestBody List<String> rctNoList) {
        rsRctService.writeoff(rctNoList);
    }

    /**
     * 从Redis获取通知数量,数量会显示在菜单的徽章上
     */
    @GetMapping("/getNotificationCount")
    public AjaxResult getNotificationCount() {
        try {
            int opCount = getOpNotificationCount();
            int psaCount = getPsaNotificationCount();

            Map<String, Integer> countMap = new HashMap<>();
            countMap.put("opCount", opCount);
            countMap.put("psaCount", psaCount);

            return AjaxResult.success(countMap);
        } catch (Exception e) {
            return AjaxResult.error("获取通知数量失败：" + e.getMessage());
        }
    }

    /**
     * 获取操作通知数量
     *
     * @return 通知数量
     */
    private int getOpNotificationCount() {
        // 获取用户ID
        Long userId = SecurityUtils.getUserId();
        // 获取用户职位
        Long position = basPositionService.selectPostByUserId(userId);
        // 获取登录用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();

        // 检查是否满足特定条件
        boolean isOperator = loginUser.getUser().getRoles().stream()
                .anyMatch(role -> "Operator".equals(role.getRoleKey()));
        int deptCreateListNum = loginUser.getUser().getDept().getCreateListNum();

        // 根据条件返回不同的通知数量
        if (isOperator && position < 14 && deptCreateListNum != 4) {
            // 操作员角色且职位<14且部门创建列表数!=4的情况
            return rsRctService.opNotification(userId);
        } else {
            // 其他情况
            return rsRctService.opNotification(null);
        }
    }

    /**
     * 获取PSA通知数量，优先从Redis获取，为空时调用notification方法
     */
    private int getPsaNotificationCount() {
        // 尝试从Redis获取
        Object cachedCount = redisCache.getCacheObject("notification:psa:count");

        // 如果缓存为空，则调用notification方法获取
        if (cachedCount == null) {
            int psaCount = rsRctService.psaNotification();
            // 将新获取的数据存入Redis
            redisCache.setCacheObject("notification:psa:count", psaCount, 600, TimeUnit.SECONDS);
            return psaCount;
        }

        return (Integer) cachedCount;
    }
}
