{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\DocDeclareComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\DocDeclareComponent.vue", "mtime": 1754646305901}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_audit", "_interopRequireDefault", "require", "_logisticsProgress", "_chargeList", "name", "components", "Audit", "LogisticsProgress", "ChargeList", "props", "docDeclareList", "type", "Array", "default", "_default", "form", "Object", "branchInfo", "Boolean", "logisticsInfo", "chargeInfo", "auditInfo", "disabled", "booking", "psaVerify", "supplierList", "companyList", "data", "bookingBillConfig", "file", "templateList", "computed", "isDisabled", "methods", "isFieldDisabled", "item", "getServiceInstanceDisable", "rsServiceInstances", "getSupplierEmail", "supplierId", "supplier", "find", "v", "companyId", "staffEmail", "getAgreementDisplay", "serviceInstance", "agreementTypeCode", "agreementNo", "getBookingBill", "template", "$emit", "deleteLogItem", "logItem", "rsOpLogList", "filter", "log", "deleteChargeItem", "chargeItem", "rsChargeList", "charge", "changeServiceFold", "addDocDeclare", "deleteRsOpDocDeclare", "openChargeSelect", "auditCharge", "event", "generateFreight", "type1", "type2", "selectPsaBookingOpen", "psaBookingCancel", "copyFreight", "calculateCharge", "serviceType", "getPayable", "$parent", "exports", "_default2"], "sources": ["src/views/system/document/serviceComponents/DocDeclareComponent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"doc-declare-component\">\r\n    <!--单证报关-->\r\n    <div v-for=\"(item, index) in docDeclareList\" :key=\"`doc-declare-${index}`\" class=\"doc-declare-item\">\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <!--标题栏-->\r\n          <div class=\"service-bar\">\r\n            <a\r\n              :class=\"[\r\n                'service-toggle-icon',\r\n                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeServiceFold(item.rsServiceInstances)\">\r\n                报关-DocDeclare\r\n              </h3>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"addDocDeclare\"\r\n              >[+]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"deleteRsOpDocDeclare(item)\"\r\n              >[-]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"openChargeSelect(item)\"\r\n              >[CN...]\r\n              </el-button>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"item.rsServiceInstances\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(item.serviceTypeId, item)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @auditFee=\"auditCharge(item, $event)\"\r\n              @return=\"item = $event\"\r\n            />\r\n            <div class=\"booking-bill-container\">\r\n              <el-popover\r\n                v-for=\"(billConfig, billIndex) in bookingBillConfig\"\r\n                :key=\"`bill-${billIndex}`\"\r\n                placement=\"top\"\r\n                trigger=\"click\"\r\n                width=\"100\"\r\n              >\r\n                <el-button\r\n                  v-for=\"(template, templateIndex) in billConfig.templateList\"\r\n                  :key=\"`template-${templateIndex}`\"\r\n                  @click=\"getBookingBill(item, template)\"\r\n                >\r\n                  {{ template }}\r\n                </el-button>\r\n                <a\r\n                  slot=\"reference\"\r\n                  class=\"booking-bill-link\"\r\n                  target=\"_blank\"\r\n                >\r\n                  [{{ billConfig.file }}]\r\n                </a>\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"item.rsServiceInstances.serviceFold\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(6, 60, item)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(item.rsServiceInstances.supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"item.rsServiceInstances.supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.rsServiceInstances)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-col :span=\"5\">\r\n                <el-form-item label=\"商务单号\">\r\n                  <el-row>\r\n                    <el-col :span=\"20\">\r\n                      <el-input\r\n                        :class=\"item.sqdPsaNo ? 'disable-form' : ''\"\r\n                        :disabled=\"!!item.sqdPsaNo\"\r\n                        :value=\"item.sqdPsaNo\"\r\n                        @focus=\"selectPsaBookingOpen(item)\"\r\n                      />\r\n                    </el-col>\r\n                    <el-col :span=\"4\">\r\n                      <el-button\r\n                        :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                        size=\"mini\"\r\n                        style=\"color: red\"\r\n                        type=\"text\"\r\n                        @click=\"psaBookingCancel\"\r\n                      >取消\r\n                      </el-button>\r\n                    </el-col>\r\n                  </el-row>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"5\">\r\n                <el-form-item label=\"参考号\">\r\n                  <el-input\r\n                    :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                    :disabled=\"isFieldDisabled(item)\"\r\n                    placeholder=\"参考号\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"isFieldDisabled(item)\"\r\n                :logistics-progress-data=\"item.rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"60\"\r\n                @deleteItem=\"deleteLogItem(item, $event)\"\r\n                @return=\"item.rsOpLogList=$event\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"item.rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"disabled || getServiceInstanceDisable(item.rsServiceInstances)\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"item.payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"item.payableRMBTax\"\r\n              :pay-detail-u-s-d=\"item.payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"item.payableUSDTax\"\r\n              :service-type-id=\"item.serviceTypeId\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"item.rsChargeList=[]\"\r\n              @deleteItem=\"deleteChargeItem(item, $event)\"\r\n              @return=\"calculateCharge(60, $event, item)\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\n\r\nexport default {\r\n  name: \"DocDeclareComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    // 单证报关数据列表\r\n    docDeclareList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      bookingBillConfig: [{\r\n        file: \"订舱单\",\r\n        templateList: [\"bookingOrder1\"]\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    // 判断字段是否禁用\r\n    isFieldDisabled(item) {\r\n      return this.psaVerify || this.disabled || this.getServiceInstanceDisable(item.rsServiceInstances)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 处理订舱单生成\r\n    getBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    // 删除物流进度\r\n    deleteLogItem(item, logItem) {\r\n      item.rsOpLogList = item.rsOpLogList.filter(log => log !== logItem)\r\n    },\r\n    // 删除费用项\r\n    deleteChargeItem(item, chargeItem) {\r\n      item.rsChargeList = item.rsChargeList.filter(charge => charge !== chargeItem)\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceInstance) {\r\n      this.$emit(\"changeServiceFold\", serviceInstance)\r\n    },\r\n    addDocDeclare() {\r\n      this.$emit(\"addDocDeclare\")\r\n    },\r\n    deleteRsOpDocDeclare(item) {\r\n      this.$emit(\"deleteRsOpDocDeclare\", item)\r\n    },\r\n    openChargeSelect(item) {\r\n      this.$emit(\"openChargeSelect\", item)\r\n    },\r\n    auditCharge(item, event) {\r\n      this.$emit(\"auditCharge\", item, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    selectPsaBookingOpen(item) {\r\n      this.$emit(\"selectPsaBookingOpen\", item)\r\n    },\r\n    psaBookingCancel() {\r\n      this.$emit(\"psaBookingCancel\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceType, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceType, event, item)\r\n    },\r\n    getPayable(serviceType, item) {\r\n      return this.$parent.getPayable ? this.$parent.getPayable(serviceType, item) : null\r\n    },\r\n    getServiceInstanceDisable(serviceInstance) {\r\n      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// DocDeclare组件特定样式\r\n.doc-declare-component {\r\n  width: 100%;\r\n\r\n  .doc-declare-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .service-action-btn {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .booking-bill-container {\r\n      margin-left: auto;\r\n\r\n      .booking-bill-link {\r\n        color: blue;\r\n        padding: 0;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAgNA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAG,IAAA;EACAC,UAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,UAAA,EAAAA;EACA;EACAC,KAAA;IACA;IACAC,cAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAC,IAAA;MACAJ,IAAA,EAAAK,MAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAG,UAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAM,aAAA;MACAR,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAO,UAAA;MACAT,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAQ,SAAA;MACAV,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAS,QAAA;MACAX,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAU,OAAA;MACAZ,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAW,SAAA;MACAb,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAY,YAAA;MACAd,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAY,WAAA;MACAf,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;EACA;EACAa,IAAA,WAAAA,KAAA;IACA;MACAC,iBAAA;QACAC,IAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAV,QAAA,SAAAE,SAAA;IACA;EACA;EACAS,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,YAAAX,SAAA,SAAAF,QAAA,SAAAc,yBAAA,CAAAD,IAAA,CAAAE,kBAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,UAAA;MACA,IAAAC,QAAA,QAAAf,YAAA,CAAAgB,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,SAAA,KAAAJ,UAAA;MAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAI,UAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,eAAA;MACA,OAAAA,eAAA,CAAAC,iBAAA,GAAAD,eAAA,CAAAE,WAAA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAd,IAAA,EAAAe,QAAA;MACA,KAAAC,KAAA,mBAAAhB,IAAA,EAAAe,QAAA;IACA;IACA;IACAE,aAAA,WAAAA,cAAAjB,IAAA,EAAAkB,OAAA;MACAlB,IAAA,CAAAmB,WAAA,GAAAnB,IAAA,CAAAmB,WAAA,CAAAC,MAAA,WAAAC,GAAA;QAAA,OAAAA,GAAA,KAAAH,OAAA;MAAA;IACA;IACA;IACAI,gBAAA,WAAAA,iBAAAtB,IAAA,EAAAuB,UAAA;MACAvB,IAAA,CAAAwB,YAAA,GAAAxB,IAAA,CAAAwB,YAAA,CAAAJ,MAAA,WAAAK,MAAA;QAAA,OAAAA,MAAA,KAAAF,UAAA;MAAA;IACA;IACA;IACAG,iBAAA,WAAAA,kBAAAf,eAAA;MACA,KAAAK,KAAA,sBAAAL,eAAA;IACA;IACAgB,aAAA,WAAAA,cAAA;MACA,KAAAX,KAAA;IACA;IACAY,oBAAA,WAAAA,qBAAA5B,IAAA;MACA,KAAAgB,KAAA,yBAAAhB,IAAA;IACA;IACA6B,gBAAA,WAAAA,iBAAA7B,IAAA;MACA,KAAAgB,KAAA,qBAAAhB,IAAA;IACA;IACA8B,WAAA,WAAAA,YAAA9B,IAAA,EAAA+B,KAAA;MACA,KAAAf,KAAA,gBAAAhB,IAAA,EAAA+B,KAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA,EAAAC,KAAA,EAAAlC,IAAA;MACA,KAAAgB,KAAA,oBAAAiB,KAAA,EAAAC,KAAA,EAAAlC,IAAA;IACA;IACAmC,oBAAA,WAAAA,qBAAAnC,IAAA;MACA,KAAAgB,KAAA,yBAAAhB,IAAA;IACA;IACAoC,gBAAA,WAAAA,iBAAA;MACA,KAAApB,KAAA;IACA;IACAqB,WAAA,WAAAA,YAAAN,KAAA;MACA,KAAAf,KAAA,gBAAAe,KAAA;IACA;IACAO,eAAA,WAAAA,gBAAAC,WAAA,EAAAR,KAAA,EAAA/B,IAAA;MACA,KAAAgB,KAAA,oBAAAuB,WAAA,EAAAR,KAAA,EAAA/B,IAAA;IACA;IACAwC,UAAA,WAAAA,WAAAD,WAAA,EAAAvC,IAAA;MACA,YAAAyC,OAAA,CAAAD,UAAA,QAAAC,OAAA,CAAAD,UAAA,CAAAD,WAAA,EAAAvC,IAAA;IACA;IACAC,yBAAA,WAAAA,0BAAAU,eAAA;MACA,YAAA8B,OAAA,CAAAxC,yBAAA,QAAAwC,OAAA,CAAAxC,yBAAA,CAAAU,eAAA;IACA;EACA;AACA;AAAA+B,OAAA,CAAAhE,OAAA,GAAAiE,SAAA"}]}