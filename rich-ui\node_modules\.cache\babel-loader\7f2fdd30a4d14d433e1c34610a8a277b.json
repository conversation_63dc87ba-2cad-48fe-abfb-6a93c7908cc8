{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\FreeDeclareComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\FreeDeclareComponent.vue", "mtime": 1754646305903}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_audit", "_interopRequireDefault", "require", "_logisticsProgress", "_chargeList", "name", "components", "Audit", "LogisticsProgress", "ChargeList", "props", "freeDeclareList", "type", "Array", "default", "_default", "form", "Object", "branchInfo", "Boolean", "logisticsInfo", "chargeInfo", "auditInfo", "disabled", "booking", "psaVerify", "supplierList", "companyList", "data", "bookingBillConfig", "file", "templateList", "computed", "isDisabled", "methods", "isFieldDisabled", "item", "getServiceInstanceDisable", "rsServiceInstances", "getSupplierEmail", "supplierId", "supplier", "find", "v", "companyId", "staffEmail", "getAgreementDisplay", "serviceInstance", "agreementTypeCode", "agreementNo", "getBookingBill", "template", "$emit", "deleteLogItem", "logItem", "rsOpLogList", "filter", "log", "deleteChargeItem", "chargeItem", "rsChargeList", "charge", "changeServiceFold", "addFreeDeclare", "deleteRsOpFreeDeclare", "openChargeSelect", "auditCharge", "event", "generateFreight", "type1", "type2", "copyFreight", "calculateCharge", "serviceType", "getPayable", "$parent", "exports", "_default2"], "sources": ["src/views/system/document/serviceComponents/FreeDeclareComponent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"free-declare-component\">\r\n    <!--全包报关-->\r\n    <div v-for=\"(item, index) in freeDeclareList\" :key=\"`free-declare-${index}`\" class=\"free-declare-item\">\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <!--标题栏-->\r\n          <div class=\"service-bar\">\r\n            <a\r\n              :class=\"[\r\n                'service-toggle-icon',\r\n                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeServiceFold(item.rsServiceInstances)\">\r\n                报关-FreeDeclare\r\n              </h3>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"addFreeDeclare\"\r\n              >[+]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"deleteRsOpFreeDeclare(item)\"\r\n              >[-]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"openChargeSelect(item)\"\r\n              >[CN...]\r\n              </el-button>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"item.rsServiceInstances\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(item.serviceTypeId, item)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @auditFee=\"auditCharge(item, $event)\"\r\n              @return=\"item = $event\"\r\n            />\r\n            <div class=\"booking-bill-container\">\r\n              <el-popover\r\n                v-for=\"(billConfig, billIndex) in bookingBillConfig\"\r\n                :key=\"`bill-${billIndex}`\"\r\n                placement=\"top\"\r\n                trigger=\"click\"\r\n                width=\"100\"\r\n              >\r\n                <el-button\r\n                  v-for=\"(template, templateIndex) in billConfig.templateList\"\r\n                  :key=\"`template-${templateIndex}`\"\r\n                  @click=\"getBookingBill(item, template)\"\r\n                >\r\n                  {{ template }}\r\n                </el-button>\r\n                <a\r\n                  slot=\"reference\"\r\n                  class=\"booking-bill-link\"\r\n                  target=\"_blank\"\r\n                >\r\n                  [{{ billConfig.file }}]\r\n                </a>\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"item.rsServiceInstances.serviceFold\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(6, 61, item)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(item.rsServiceInstances.supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"item.rsServiceInstances.supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.rsServiceInstances)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-col :span=\"5\">\r\n                <el-form-item label=\"商务单号\">\r\n                  <el-row>\r\n                    <el-col :span=\"20\">\r\n                      <!-- 空白区域 -->\r\n                    </el-col>\r\n                    <el-col :span=\"4\">\r\n                      <!-- 空白区域 -->\r\n                    </el-col>\r\n                  </el-row>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"5\">\r\n                <el-form-item label=\"参考号\">\r\n                  <el-input\r\n                    :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                    :disabled=\"isFieldDisabled(item)\"\r\n                    placeholder=\"参考号\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"isFieldDisabled(item)\"\r\n                :logistics-progress-data=\"item.rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"60\"\r\n                @deleteItem=\"deleteLogItem(item, $event)\"\r\n                @return=\"item.rsOpLogList=$event\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"item.rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"disabled || getServiceInstanceDisable(item.rsServiceInstances)\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"item.payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"item.payableRMBTax\"\r\n              :pay-detail-u-s-d=\"item.payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"item.payableUSDTax\"\r\n              :pay-detail=\"getPayable(item.serviceTypeId, item)\"\r\n              :service-type-id=\"item.serviceTypeId\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"item.rsChargeList=[]\"\r\n              @deleteItem=\"deleteChargeItem(item, $event)\"\r\n              @return=\"calculateCharge(61, $event, item)\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\n\r\nexport default {\r\n  name: \"FreeDeclareComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    // 全包报关数据列表\r\n    freeDeclareList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      bookingBillConfig: [{\r\n        file: \"订舱单\",\r\n        templateList: [\"bookingOrder1\"]\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    // 判断字段是否禁用\r\n    isFieldDisabled(item) {\r\n      return this.psaVerify || this.disabled || this.getServiceInstanceDisable(item.rsServiceInstances)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 处理订舱单生成\r\n    getBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    // 删除物流进度\r\n    deleteLogItem(item, logItem) {\r\n      item.rsOpLogList = item.rsOpLogList.filter(log => log !== logItem)\r\n    },\r\n    // 删除费用项\r\n    deleteChargeItem(item, chargeItem) {\r\n      item.rsChargeList = item.rsChargeList.filter(charge => charge !== chargeItem)\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceInstance) {\r\n      this.$emit(\"changeServiceFold\", serviceInstance)\r\n    },\r\n    addFreeDeclare() {\r\n      this.$emit(\"addFreeDeclare\")\r\n    },\r\n    deleteRsOpFreeDeclare(item) {\r\n      this.$emit(\"deleteRsOpFreeDeclare\", item)\r\n    },\r\n    openChargeSelect(item) {\r\n      this.$emit(\"openChargeSelect\", item)\r\n    },\r\n    auditCharge(item, event) {\r\n      this.$emit(\"auditCharge\", item, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceType, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceType, event, item)\r\n    },\r\n    getPayable(serviceType, item) {\r\n      return this.$parent.getPayable ? this.$parent.getPayable(serviceType, item) : null\r\n    },\r\n    getServiceInstanceDisable(serviceInstance) {\r\n      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// FreeDeclare组件特定样式\r\n.free-declare-component {\r\n  width: 100%;\r\n\r\n  .free-declare-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .service-action-btn {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .booking-bill-container {\r\n      margin-left: auto;\r\n\r\n      .booking-bill-link {\r\n        color: blue;\r\n        padding: 0;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAqMA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAG,IAAA;EACAC,UAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,UAAA,EAAAA;EACA;EACAC,KAAA;IACA;IACAC,eAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAC,IAAA;MACAJ,IAAA,EAAAK,MAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAG,UAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAM,aAAA;MACAR,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAO,UAAA;MACAT,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAQ,SAAA;MACAV,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAS,QAAA;MACAX,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAU,OAAA;MACAZ,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAW,SAAA;MACAb,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAY,YAAA;MACAd,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAY,WAAA;MACAf,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;EACA;EACAa,IAAA,WAAAA,KAAA;IACA;MACAC,iBAAA;QACAC,IAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAV,QAAA,SAAAE,SAAA;IACA;EACA;EACAS,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,YAAAX,SAAA,SAAAF,QAAA,SAAAc,yBAAA,CAAAD,IAAA,CAAAE,kBAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,UAAA;MACA,IAAAC,QAAA,QAAAf,YAAA,CAAAgB,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,SAAA,KAAAJ,UAAA;MAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAI,UAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,eAAA;MACA,OAAAA,eAAA,CAAAC,iBAAA,GAAAD,eAAA,CAAAE,WAAA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAd,IAAA,EAAAe,QAAA;MACA,KAAAC,KAAA,mBAAAhB,IAAA,EAAAe,QAAA;IACA;IACA;IACAE,aAAA,WAAAA,cAAAjB,IAAA,EAAAkB,OAAA;MACAlB,IAAA,CAAAmB,WAAA,GAAAnB,IAAA,CAAAmB,WAAA,CAAAC,MAAA,WAAAC,GAAA;QAAA,OAAAA,GAAA,KAAAH,OAAA;MAAA;IACA;IACA;IACAI,gBAAA,WAAAA,iBAAAtB,IAAA,EAAAuB,UAAA;MACAvB,IAAA,CAAAwB,YAAA,GAAAxB,IAAA,CAAAwB,YAAA,CAAAJ,MAAA,WAAAK,MAAA;QAAA,OAAAA,MAAA,KAAAF,UAAA;MAAA;IACA;IACA;IACAG,iBAAA,WAAAA,kBAAAf,eAAA;MACA,KAAAK,KAAA,sBAAAL,eAAA;IACA;IACAgB,cAAA,WAAAA,eAAA;MACA,KAAAX,KAAA;IACA;IACAY,qBAAA,WAAAA,sBAAA5B,IAAA;MACA,KAAAgB,KAAA,0BAAAhB,IAAA;IACA;IACA6B,gBAAA,WAAAA,iBAAA7B,IAAA;MACA,KAAAgB,KAAA,qBAAAhB,IAAA;IACA;IACA8B,WAAA,WAAAA,YAAA9B,IAAA,EAAA+B,KAAA;MACA,KAAAf,KAAA,gBAAAhB,IAAA,EAAA+B,KAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA,EAAAC,KAAA,EAAAlC,IAAA;MACA,KAAAgB,KAAA,oBAAAiB,KAAA,EAAAC,KAAA,EAAAlC,IAAA;IACA;IACAmC,WAAA,WAAAA,YAAAJ,KAAA;MACA,KAAAf,KAAA,gBAAAe,KAAA;IACA;IACAK,eAAA,WAAAA,gBAAAC,WAAA,EAAAN,KAAA,EAAA/B,IAAA;MACA,KAAAgB,KAAA,oBAAAqB,WAAA,EAAAN,KAAA,EAAA/B,IAAA;IACA;IACAsC,UAAA,WAAAA,WAAAD,WAAA,EAAArC,IAAA;MACA,YAAAuC,OAAA,CAAAD,UAAA,QAAAC,OAAA,CAAAD,UAAA,CAAAD,WAAA,EAAArC,IAAA;IACA;IACAC,yBAAA,WAAAA,0BAAAU,eAAA;MACA,YAAA4B,OAAA,CAAAtC,yBAAA,QAAAsC,OAAA,CAAAtC,yBAAA,CAAAU,eAAA;IACA;EACA;AACA;AAAA6B,OAAA,CAAA9D,OAAA,GAAA+D,SAAA"}]}