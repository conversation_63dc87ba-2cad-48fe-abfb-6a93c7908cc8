{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\DocDeclareComponent.vue?vue&type=style&index=0&id=07b46249&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\DocDeclareComponent.vue", "mtime": 1754646305901}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DocDeclareComponent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgWA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "DocDeclareComponent.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div class=\"doc-declare-component\">\r\n    <!--单证报关-->\r\n    <div v-for=\"(item, index) in docDeclareList\" :key=\"`doc-declare-${index}`\" class=\"doc-declare-item\">\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <!--标题栏-->\r\n          <div class=\"service-bar\">\r\n            <a\r\n              :class=\"[\r\n                'service-toggle-icon',\r\n                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeServiceFold(item.rsServiceInstances)\">\r\n                报关-DocDeclare\r\n              </h3>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"addDocDeclare\"\r\n              >[+]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"deleteRsOpDocDeclare(item)\"\r\n              >[-]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"openChargeSelect(item)\"\r\n              >[CN...]\r\n              </el-button>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"item.rsServiceInstances\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(item.serviceTypeId, item)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @auditFee=\"auditCharge(item, $event)\"\r\n              @return=\"item = $event\"\r\n            />\r\n            <div class=\"booking-bill-container\">\r\n              <el-popover\r\n                v-for=\"(billConfig, billIndex) in bookingBillConfig\"\r\n                :key=\"`bill-${billIndex}`\"\r\n                placement=\"top\"\r\n                trigger=\"click\"\r\n                width=\"100\"\r\n              >\r\n                <el-button\r\n                  v-for=\"(template, templateIndex) in billConfig.templateList\"\r\n                  :key=\"`template-${templateIndex}`\"\r\n                  @click=\"getBookingBill(item, template)\"\r\n                >\r\n                  {{ template }}\r\n                </el-button>\r\n                <a\r\n                  slot=\"reference\"\r\n                  class=\"booking-bill-link\"\r\n                  target=\"_blank\"\r\n                >\r\n                  [{{ billConfig.file }}]\r\n                </a>\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"item.rsServiceInstances.serviceFold\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(6, 60, item)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(item.rsServiceInstances.supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"item.rsServiceInstances.supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.rsServiceInstances)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-col :span=\"5\">\r\n                <el-form-item label=\"商务单号\">\r\n                  <el-row>\r\n                    <el-col :span=\"20\">\r\n                      <el-input\r\n                        :class=\"item.sqdPsaNo ? 'disable-form' : ''\"\r\n                        :disabled=\"!!item.sqdPsaNo\"\r\n                        :value=\"item.sqdPsaNo\"\r\n                        @focus=\"selectPsaBookingOpen(item)\"\r\n                      />\r\n                    </el-col>\r\n                    <el-col :span=\"4\">\r\n                      <el-button\r\n                        :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                        size=\"mini\"\r\n                        style=\"color: red\"\r\n                        type=\"text\"\r\n                        @click=\"psaBookingCancel\"\r\n                      >取消\r\n                      </el-button>\r\n                    </el-col>\r\n                  </el-row>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"5\">\r\n                <el-form-item label=\"参考号\">\r\n                  <el-input\r\n                    :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                    :disabled=\"isFieldDisabled(item)\"\r\n                    placeholder=\"参考号\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"isFieldDisabled(item)\"\r\n                :logistics-progress-data=\"item.rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"60\"\r\n                @deleteItem=\"deleteLogItem(item, $event)\"\r\n                @return=\"item.rsOpLogList=$event\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"item.rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"disabled || getServiceInstanceDisable(item.rsServiceInstances)\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"item.payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"item.payableRMBTax\"\r\n              :pay-detail-u-s-d=\"item.payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"item.payableUSDTax\"\r\n              :service-type-id=\"item.serviceTypeId\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"item.rsChargeList=[]\"\r\n              @deleteItem=\"deleteChargeItem(item, $event)\"\r\n              @return=\"calculateCharge(60, $event, item)\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\n\r\nexport default {\r\n  name: \"DocDeclareComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    // 单证报关数据列表\r\n    docDeclareList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      bookingBillConfig: [{\r\n        file: \"订舱单\",\r\n        templateList: [\"bookingOrder1\"]\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    // 判断字段是否禁用\r\n    isFieldDisabled(item) {\r\n      return this.psaVerify || this.disabled || this.getServiceInstanceDisable(item.rsServiceInstances)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 处理订舱单生成\r\n    getBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    // 删除物流进度\r\n    deleteLogItem(item, logItem) {\r\n      item.rsOpLogList = item.rsOpLogList.filter(log => log !== logItem)\r\n    },\r\n    // 删除费用项\r\n    deleteChargeItem(item, chargeItem) {\r\n      item.rsChargeList = item.rsChargeList.filter(charge => charge !== chargeItem)\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceInstance) {\r\n      this.$emit(\"changeServiceFold\", serviceInstance)\r\n    },\r\n    addDocDeclare() {\r\n      this.$emit(\"addDocDeclare\")\r\n    },\r\n    deleteRsOpDocDeclare(item) {\r\n      this.$emit(\"deleteRsOpDocDeclare\", item)\r\n    },\r\n    openChargeSelect(item) {\r\n      this.$emit(\"openChargeSelect\", item)\r\n    },\r\n    auditCharge(item, event) {\r\n      this.$emit(\"auditCharge\", item, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    selectPsaBookingOpen(item) {\r\n      this.$emit(\"selectPsaBookingOpen\", item)\r\n    },\r\n    psaBookingCancel() {\r\n      this.$emit(\"psaBookingCancel\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceType, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceType, event, item)\r\n    },\r\n    getPayable(serviceType, item) {\r\n      return this.$parent.getPayable ? this.$parent.getPayable(serviceType, item) : null\r\n    },\r\n    getServiceInstanceDisable(serviceInstance) {\r\n      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// DocDeclare组件特定样式\r\n.doc-declare-component {\r\n  width: 100%;\r\n\r\n  .doc-declare-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .service-action-btn {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .booking-bill-container {\r\n      margin-left: auto;\r\n\r\n      .booking-bill-link {\r\n        color: blue;\r\n        padding: 0;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}