<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rich.system.mapper.OptimizedRsChargeMapper">

    <resultMap type="RsCharge" id="RsChargeResult">
        <result property="chargeId" column="charge_id"/>
        <result property="serviceId" column="service_id"/>
        <result property="sqdRctId" column="sqd_rct_id"/>
        <result property="sqdServiceTypeId" column="sqd_service_type_id"/>
        <result property="sqdRctNo" column="sqd_rct_no"/>
        <result property="relatedFreightId" column="related_freight_id"/>
        <result property="isRecievingOrPaying" column="is_recieving_or_paying"/>
        <result property="clearingCompanyId" column="clearing_company_id"/>
        <result property="clearingCompanySummary" column="clearing_company_summary"/>
        <result property="quotationStrategyId" column="quotation_strategy_id"/>
        <result property="dnChargeNameId" column="dn_charge_name_id"/>
        <result property="dnCurrencyCode" column="dn_currency_code"/>
        <result property="dnUnitRate" column="dn_unit_rate"/>
        <result property="dnUnitCode" column="dn_unit_code"/>
        <result property="dnAmount" column="dn_amount"/>
        <result property="basicCurrencyRate" column="basic_currency_rate"/>
        <result property="dutyRate" column="duty_rate"/>
        <result property="subtotal" column="subtotal"/>
        <result property="chargeRemark" column="charge_remark"/>
        <result property="clearingCurrencyCode" column="clearing_currency_code"/>
        <result property="dnCurrencyReceived" column="dn_currency_received"/>
        <result property="dnCurrencyPaid" column="dn_currency_paid"/>
        <result property="dnCurrencyBalance" column="dn_currency_balance"/>
        <result property="accountReceivedIdList" column="account_received_id_list"/>
        <result property="accountPaidIdList" column="account_paid_id_list"/>
        <result property="logisticsInvoiceIdList" column="logistics_invoice_id_list"/>
        <result property="sqdServiceDetailsCode" column="sqd_service_details_code"/>
        <result property="paymentTitleCode" column="payment_title_code"/>
        <result property="logisticsPaymentTermsCode" column="logistics_payment_terms_code"/>
        <result property="isAccountConfirmed" column="is_account_confirmed"/>
        <result property="sqdDnCurrencyPaid" column="sqd_dn_currency_paid"/>
        <result property="sqdDnCurrencyBalance" column="sqd_dn_currency_balance"/>
        <result property="sqdWriteoffNoList" column="sqd_writeoff_no_list"/>
        <result property="sqdInvoiceIssued" column="sqd_invoice_issued"/>
        <result property="sqdInvoiceBalance" column="sqd_invoice_balance"/>
        <result property="currencyRateCalculateDate" column="currency_rate_calculate_date"/>
        <result property="writeoffStatus" column="writeoff_status"/>
    </resultMap>

    <!-- 批量插入费用记录 -->
    <insert id="batchInsertCharges" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="chargeId">
        INSERT INTO rs_charge (
            service_id, sqd_rct_id, sqd_service_type_id, sqd_rct_no, related_freight_id,
            is_recieving_or_paying, clearing_company_id, clearing_company_summary,
            quotation_strategy_id, dn_charge_name_id, dn_currency_code, dn_unit_rate,
            dn_unit_code, dn_amount, basic_currency_rate, duty_rate, subtotal,
            charge_remark, clearing_currency_code, dn_currency_received, dn_currency_paid,
            dn_currency_balance, account_received_id_list, account_paid_id_list,
            logistics_invoice_id_list, sqd_service_details_code, payment_title_code,
            logistics_payment_terms_code, is_account_confirmed, sqd_dn_currency_paid,
            sqd_dn_currency_balance, sqd_writeoff_no_list, sqd_invoice_issued,
            sqd_invoice_balance, currency_rate_calculate_date, writeoff_status
        ) VALUES
        <foreach collection="charges" item="charge" separator=",">
            (
                #{charge.serviceId}, #{charge.sqdRctId}, #{charge.sqdServiceTypeId},
                #{charge.sqdRctNo}, #{charge.relatedFreightId}, #{charge.isRecievingOrPaying},
                #{charge.clearingCompanyId}, #{charge.clearingCompanySummary},
                #{charge.quotationStrategyId}, #{charge.dnChargeNameId}, #{charge.dnCurrencyCode},
                #{charge.dnUnitRate}, #{charge.dnUnitCode}, #{charge.dnAmount},
                #{charge.basicCurrencyRate}, #{charge.dutyRate}, #{charge.subtotal},
                #{charge.chargeRemark}, #{charge.clearingCurrencyCode}, #{charge.dnCurrencyReceived},
                #{charge.dnCurrencyPaid}, #{charge.dnCurrencyBalance}, #{charge.accountReceivedIdList},
                #{charge.accountPaidIdList}, #{charge.logisticsInvoiceIdList},
                #{charge.sqdServiceDetailsCode}, #{charge.paymentTitleCode},
                #{charge.logisticsPaymentTermsCode}, #{charge.isAccountConfirmed},
                #{charge.sqdDnCurrencyPaid}, #{charge.sqdDnCurrencyBalance},
                #{charge.sqdWriteoffNoList}, #{charge.sqdInvoiceIssued},
                #{charge.sqdInvoiceBalance}, #{charge.currencyRateCalculateDate},
                #{charge.writeoffStatus}
            )
        </foreach>
    </insert>

    <!-- 批量UPSERT费用记录 -->
    <insert id="batchUpsertCharges" parameterType="java.util.List">
        INSERT INTO rs_charge (
            charge_id, service_id, sqd_rct_id, sqd_service_type_id, sqd_rct_no,
            related_freight_id, is_recieving_or_paying, clearing_company_id,
            clearing_company_summary, quotation_strategy_id, dn_charge_name_id,
            dn_currency_code, dn_unit_rate, dn_unit_code, dn_amount,
            basic_currency_rate, duty_rate, subtotal, charge_remark,
            clearing_currency_code, dn_currency_received, dn_currency_paid,
            dn_currency_balance, account_received_id_list, account_paid_id_list,
            logistics_invoice_id_list, sqd_service_details_code, payment_title_code,
            logistics_payment_terms_code, is_account_confirmed, sqd_dn_currency_paid,
            sqd_dn_currency_balance, sqd_writeoff_no_list, sqd_invoice_issued,
            sqd_invoice_balance, currency_rate_calculate_date, writeoff_status
        ) VALUES
        <foreach collection="charges" item="charge" separator=",">
            (
                #{charge.chargeId}, #{charge.serviceId}, #{charge.sqdRctId},
                #{charge.sqdServiceTypeId}, #{charge.sqdRctNo}, #{charge.relatedFreightId},
                #{charge.isRecievingOrPaying}, #{charge.clearingCompanyId},
                #{charge.clearingCompanySummary}, #{charge.quotationStrategyId},
                #{charge.dnChargeNameId}, #{charge.dnCurrencyCode}, #{charge.dnUnitRate},
                #{charge.dnUnitCode}, #{charge.dnAmount}, #{charge.basicCurrencyRate},
                #{charge.dutyRate}, #{charge.subtotal}, #{charge.chargeRemark},
                #{charge.clearingCurrencyCode}, #{charge.dnCurrencyReceived},
                #{charge.dnCurrencyPaid}, #{charge.dnCurrencyBalance},
                #{charge.accountReceivedIdList}, #{charge.accountPaidIdList},
                #{charge.logisticsInvoiceIdList}, #{charge.sqdServiceDetailsCode},
                #{charge.paymentTitleCode}, #{charge.logisticsPaymentTermsCode},
                #{charge.isAccountConfirmed}, #{charge.sqdDnCurrencyPaid},
                #{charge.sqdDnCurrencyBalance}, #{charge.sqdWriteoffNoList},
                #{charge.sqdInvoiceIssued}, #{charge.sqdInvoiceBalance},
                #{charge.currencyRateCalculateDate}, #{charge.writeoffStatus}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            service_id = VALUES(service_id),
            sqd_rct_id = VALUES(sqd_rct_id),
            sqd_service_type_id = VALUES(sqd_service_type_id),
            sqd_rct_no = VALUES(sqd_rct_no),
            related_freight_id = VALUES(related_freight_id),
            is_recieving_or_paying = VALUES(is_recieving_or_paying),
            clearing_company_id = VALUES(clearing_company_id),
            clearing_company_summary = VALUES(clearing_company_summary),
            quotation_strategy_id = VALUES(quotation_strategy_id),
            dn_charge_name_id = VALUES(dn_charge_name_id),
            dn_currency_code = VALUES(dn_currency_code),
            dn_unit_rate = VALUES(dn_unit_rate),
            dn_unit_code = VALUES(dn_unit_code),
            dn_amount = VALUES(dn_amount),
            basic_currency_rate = VALUES(basic_currency_rate),
            duty_rate = VALUES(duty_rate),
            subtotal = VALUES(subtotal),
            charge_remark = VALUES(charge_remark),
            clearing_currency_code = VALUES(clearing_currency_code),
            dn_currency_received = VALUES(dn_currency_received),
            dn_currency_paid = VALUES(dn_currency_paid),
            dn_currency_balance = VALUES(dn_currency_balance),
            account_received_id_list = VALUES(account_received_id_list),
            account_paid_id_list = VALUES(account_paid_id_list),
            logistics_invoice_id_list = VALUES(logistics_invoice_id_list),
            sqd_service_details_code = VALUES(sqd_service_details_code),
            payment_title_code = VALUES(payment_title_code),
            logistics_payment_terms_code = VALUES(logistics_payment_terms_code),
            is_account_confirmed = VALUES(is_account_confirmed),
            sqd_dn_currency_paid = VALUES(sqd_dn_currency_paid),
            sqd_dn_currency_balance = VALUES(sqd_dn_currency_balance),
            sqd_writeoff_no_list = VALUES(sqd_writeoff_no_list),
            sqd_invoice_issued = VALUES(sqd_invoice_issued),
            sqd_invoice_balance = VALUES(sqd_invoice_balance),
            currency_rate_calculate_date = VALUES(currency_rate_calculate_date),
            writeoff_status = VALUES(writeoff_status)
    </insert>

    <!-- 删除孤立的费用记录 -->
    <delete id="deleteOrphanedCharges">
        DELETE FROM rs_charge 
        WHERE sqd_rct_id = #{rctId}
        <if test="activeServiceIds != null and activeServiceIds.size() > 0">
            AND service_id NOT IN
            <foreach collection="activeServiceIds" item="serviceId" open="(" separator="," close=")">
                #{serviceId}
            </foreach>
        </if>
    </delete>

    <!-- 按服务ID批量删除费用记录 -->
    <delete id="batchDeleteChargesByServiceIds">
        DELETE FROM rs_charge 
        WHERE service_id IN
        <foreach collection="serviceIds" item="serviceId" open="(" separator="," close=")">
            #{serviceId}
        </foreach>
    </delete>

    <!-- 查询指定操作单的所有费用记录 -->
    <select id="selectChargesByRctIdGroupByService" parameterType="Long" resultMap="RsChargeResult">
        SELECT * FROM rs_charge 
        WHERE sqd_rct_id = #{rctId}
        ORDER BY service_id, charge_id
    </select>

    <!-- 查询指定服务ID列表的费用记录 -->
    <select id="selectChargesByServiceIds" resultMap="RsChargeResult">
        SELECT * FROM rs_charge 
        WHERE service_id IN
        <foreach collection="serviceIds" item="serviceId" open="(" separator="," close=")">
            #{serviceId}
        </foreach>
        ORDER BY service_id, charge_id
    </select>

    <!-- 统计指定操作单的费用记录数量 -->
    <select id="countChargesByRctId" parameterType="Long" resultType="int">
        SELECT COUNT(*) FROM rs_charge WHERE sqd_rct_id = #{rctId}
    </select>

    <!-- 检查费用记录是否存在 -->
    <select id="existsChargeById" parameterType="Long" resultType="boolean">
        SELECT COUNT(*) > 0 FROM rs_charge WHERE charge_id = #{chargeId}
    </select>

    <!-- 批量检查费用记录是否存在 -->
    <select id="batchCheckChargesExist" resultType="Long">
        SELECT charge_id FROM rs_charge 
        WHERE charge_id IN
        <foreach collection="chargeIds" item="chargeId" open="(" separator="," close=")">
            #{chargeId}
        </foreach>
    </select>

    <!-- 获取费用记录的汇总信息 -->
    <select id="getChargeSummary" parameterType="Long" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_count,
            SUM(CASE WHEN subtotal IS NOT NULL THEN subtotal ELSE 0 END) as total_amount,
            COUNT(DISTINCT service_id) as service_count,
            COUNT(DISTINCT sqd_service_type_id) as service_type_count
        FROM rs_charge 
        WHERE sqd_rct_id = #{rctId}
    </select>

    <!-- 按服务类型统计费用 -->
    <select id="getChargeStatsByServiceType" parameterType="Long" resultType="java.util.Map">
        SELECT 
            sqd_service_type_id as service_type_id,
            COUNT(*) as charge_count,
            SUM(CASE WHEN subtotal IS NOT NULL THEN subtotal ELSE 0 END) as total_amount,
            AVG(CASE WHEN subtotal IS NOT NULL THEN subtotal ELSE 0 END) as avg_amount
        FROM rs_charge 
        WHERE sqd_rct_id = #{rctId}
        GROUP BY sqd_service_type_id
        ORDER BY sqd_service_type_id
    </select>

</mapper>
