package com.rich.system.service.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 事务监控器
 * 监控事务的执行时间和状态
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class TransactionMonitor {
    
    /**
     * 长事务阈值（毫秒）
     */
    private static final long LONG_TRANSACTION_THRESHOLD = 30000; // 30秒
    
    /**
     * 超长事务阈值（毫秒）
     */
    private static final long VERY_LONG_TRANSACTION_THRESHOLD = 60000; // 60秒
    
    /**
     * 事务统计信息
     */
    private final TransactionStatistics statistics = new TransactionStatistics();
    
    /**
     * 活跃事务跟踪
     */
    private final ConcurrentHashMap<String, TransactionInfo> activeTransactions = new ConcurrentHashMap<>();
    
    /**
     * 监听事务开始
     */
    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
    public void handleTransactionStart(Object event) {
        String transactionId = getCurrentTransactionId();
        TransactionInfo info = TransactionInfo.builder()
                .transactionId(transactionId)
                .startTime(System.currentTimeMillis())
                .startDateTime(LocalDateTime.now())
                .threadName(Thread.currentThread().getName())
                .build();
        
        activeTransactions.put(transactionId, info);
        statistics.incrementActiveTransactions();
        
        log.debug("事务开始: {}", transactionId);
    }
    
    /**
     * 监听事务提交
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleTransactionCommit(Object event) {
        String transactionId = getCurrentTransactionId();
        TransactionInfo info = activeTransactions.remove(transactionId);
        
        if (info != null) {
            long duration = System.currentTimeMillis() - info.getStartTime();
            info.setEndTime(System.currentTimeMillis());
            info.setDuration(duration);
            info.setStatus(TransactionStatus.COMMITTED);
            
            statistics.recordTransaction(info);
            
            // 检查是否为长事务
            if (duration > VERY_LONG_TRANSACTION_THRESHOLD) {
                log.warn("检测到超长事务: {} 耗时: {}ms", transactionId, duration);
                handleVeryLongTransaction(info);
            } else if (duration > LONG_TRANSACTION_THRESHOLD) {
                log.warn("检测到长事务: {} 耗时: {}ms", transactionId, duration);
                handleLongTransaction(info);
            }
            
            log.debug("事务提交: {} 耗时: {}ms", transactionId, duration);
        }
    }
    
    /**
     * 监听事务回滚
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_ROLLBACK)
    public void handleTransactionRollback(Object event) {
        String transactionId = getCurrentTransactionId();
        TransactionInfo info = activeTransactions.remove(transactionId);
        
        if (info != null) {
            long duration = System.currentTimeMillis() - info.getStartTime();
            info.setEndTime(System.currentTimeMillis());
            info.setDuration(duration);
            info.setStatus(TransactionStatus.ROLLED_BACK);
            
            statistics.recordTransaction(info);
            
            log.warn("事务回滚: {} 耗时: {}ms", transactionId, duration);
        }
    }
    
    /**
     * 处理长事务
     */
    private void handleLongTransaction(TransactionInfo info) {
        // 可以在这里添加告警逻辑
        // 例如：发送邮件、推送通知等
        log.warn("长事务告警: 事务ID={}, 耗时={}ms, 线程={}", 
                info.getTransactionId(), info.getDuration(), info.getThreadName());
    }
    
    /**
     * 处理超长事务
     */
    private void handleVeryLongTransaction(TransactionInfo info) {
        // 可以在这里添加紧急告警逻辑
        log.error("超长事务紧急告警: 事务ID={}, 耗时={}ms, 线程={}", 
                info.getTransactionId(), info.getDuration(), info.getThreadName());
    }
    
    /**
     * 获取当前事务ID
     */
    private String getCurrentTransactionId() {
        // 简单实现，实际可以使用Spring的事务管理器获取真实的事务ID
        return Thread.currentThread().getName() + "-" + System.currentTimeMillis();
    }
    
    /**
     * 获取事务统计信息
     */
    public TransactionStatistics getStatistics() {
        return statistics;
    }
    
    /**
     * 获取活跃事务信息
     */
    public java.util.Collection<TransactionInfo> getActiveTransactions() {
        return activeTransactions.values();
    }
    
    /**
     * 检查是否有长时间运行的事务
     */
    public java.util.List<TransactionInfo> getLongRunningTransactions() {
        long currentTime = System.currentTimeMillis();
        return activeTransactions.values().stream()
                .filter(info -> (currentTime - info.getStartTime()) > LONG_TRANSACTION_THRESHOLD)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 事务信息
     */
    @lombok.Data
    @lombok.Builder
    public static class TransactionInfo {
        private String transactionId;
        private long startTime;
        private long endTime;
        private long duration;
        private LocalDateTime startDateTime;
        private String threadName;
        private TransactionStatus status;
    }
    
    /**
     * 事务状态枚举
     */
    public enum TransactionStatus {
        ACTIVE,      // 活跃
        COMMITTED,   // 已提交
        ROLLED_BACK  // 已回滚
    }
    
    /**
     * 事务统计信息
     */
    @lombok.Data
    public static class TransactionStatistics {
        private final AtomicLong totalTransactions = new AtomicLong(0);
        private final AtomicLong committedTransactions = new AtomicLong(0);
        private final AtomicLong rolledBackTransactions = new AtomicLong(0);
        private final AtomicLong activeTransactions = new AtomicLong(0);
        private final AtomicLong longTransactions = new AtomicLong(0);
        private final AtomicLong veryLongTransactions = new AtomicLong(0);
        
        private volatile long maxDuration = 0;
        private volatile long minDuration = Long.MAX_VALUE;
        private volatile double avgDuration = 0;
        
        public void recordTransaction(TransactionInfo info) {
            totalTransactions.incrementAndGet();
            
            if (info.getStatus() == TransactionStatus.COMMITTED) {
                committedTransactions.incrementAndGet();
            } else if (info.getStatus() == TransactionStatus.ROLLED_BACK) {
                rolledBackTransactions.incrementAndGet();
            }
            
            long duration = info.getDuration();
            
            // 更新最大最小耗时
            if (duration > maxDuration) {
                maxDuration = duration;
            }
            if (duration < minDuration) {
                minDuration = duration;
            }
            
            // 更新平均耗时（简单实现）
            avgDuration = (avgDuration * (totalTransactions.get() - 1) + duration) / totalTransactions.get();
            
            // 统计长事务
            if (duration > VERY_LONG_TRANSACTION_THRESHOLD) {
                veryLongTransactions.incrementAndGet();
            } else if (duration > LONG_TRANSACTION_THRESHOLD) {
                longTransactions.incrementAndGet();
            }
            
            activeTransactions.decrementAndGet();
        }
        
        public void incrementActiveTransactions() {
            activeTransactions.incrementAndGet();
        }
        
        public double getSuccessRate() {
            long total = totalTransactions.get();
            if (total == 0) return 0.0;
            return (double) committedTransactions.get() / total * 100;
        }
        
        public double getLongTransactionRate() {
            long total = totalTransactions.get();
            if (total == 0) return 0.0;
            return (double) (longTransactions.get() + veryLongTransactions.get()) / total * 100;
        }
    }
}
