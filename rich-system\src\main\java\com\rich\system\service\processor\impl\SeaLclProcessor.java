package com.rich.system.service.processor.impl;

import com.rich.common.core.domain.entity.RsRct;
import com.rich.common.core.domain.entity.RsServiceInstances;
import com.rich.common.core.domain.entity.RsOpSeaLcl;
import com.rich.common.utils.DateUtils;
import com.rich.system.service.context.ServiceProcessingContext;
import com.rich.system.service.enums.ServiceTypeEnum;
import com.rich.system.service.processor.AbstractServiceProcessor;
import com.rich.system.mapper.RsOpSeaLclMapper;
import com.rich.system.service.RedisIdGeneratorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 海运拼箱服务处理器
 * 处理海运拼箱相关的服务数据
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SeaLclProcessor extends AbstractServiceProcessor {
    
    @Autowired
    private RsOpSeaLclMapper rsOpSeaLclMapper;
    
    @Autowired
    private RedisIdGeneratorService redisIdGeneratorService;
    
    @Override
    public ServiceTypeEnum getSupportedServiceType() {
        return ServiceTypeEnum.SEA_LCL;
    }
    
    @Override
    public boolean canProcess(RsRct rsRct, ServiceProcessingContext context) {
        return rsRct.getRsOpSeaLclList() != null && !rsRct.getRsOpSeaLclList().isEmpty();
    }
    
    @Override
    protected void doProcess(RsRct rsRct, ServiceProcessingContext context) throws Exception {
        List<RsOpSeaLcl> seaLclList = rsRct.getRsOpSeaLclList();
        if (seaLclList == null || seaLclList.isEmpty()) {
            log.debug("海运拼箱服务列表为空，跳过处理");
            return;
        }
        
        Long rctId = rsRct.getRctId();
        
        // 查询现有记录
        List<RsOpSeaLcl> existingList = rsOpSeaLclMapper.selectRsOpSeaLclByRctId(rctId, 2L);
        
        // 找出需要删除的记录
        List<RsOpSeaLcl> toDeleteList = existingList.stream()
                .filter(existing -> seaLclList.stream()
                        .noneMatch(newItem -> newItem.getSeaId() != null &&
                                newItem.getSeaId().equals(existing.getSeaId())))
                .collect(Collectors.toList());
        
        // 删除不再需要的记录
        deleteObsoleteRecords(toDeleteList);
        
        // 处理每个海运拼箱服务
        for (RsOpSeaLcl seaLcl : seaLclList) {
            processSeaLclItem(seaLcl, rsRct, context);
        }
        
        log.debug("海运拼箱服务处理完成，处理了 {} 个服务项", seaLclList.size());
    }
    
    /**
     * 处理单个海运拼箱服务项
     */
    private void processSeaLclItem(RsOpSeaLcl seaLcl, RsRct rsRct, ServiceProcessingContext context) {
        // 处理服务实例
        RsServiceInstances serviceInstance = seaLcl.getRsServiceInstances();
        if (serviceInstance == null) {
            serviceInstance = getOrCreateServiceInstance(rsRct, context);
            seaLcl.setRsServiceInstances(serviceInstance);
        }
        
        serviceInstance = processServiceInstance(serviceInstance, rsRct, context);
        
        // 设置海运拼箱服务的基本信息
        seaLcl.setServiceId(serviceInstance.getServiceId());
        seaLcl.setSqdRctNo(rsRct.getRctNo());
        seaLcl.setSqdServiceTypeId(2L);
        
        // 如果是新记录，生成PSA编号
        if (seaLcl.getSeaId() == null) {
            String psaNo = redisIdGeneratorService.generateUniqueId("psa_no");
            String date = DateUtils.dateTime();
            seaLcl.setSqdPsaNo("PSA" + date.substring(2, 6) + psaNo);
        }
        
        // 保存或更新海运拼箱服务记录
        rsOpSeaLclMapper.upsertRsOpSeaLcl(seaLcl);
        
        // 处理费用记录
        if (seaLcl.getRsChargeList() != null && !seaLcl.getRsChargeList().isEmpty()) {
            processCharges(seaLcl.getRsChargeList(), serviceInstance, rsRct);
        }
        
        // 处理操作日志
        if (seaLcl.getRsOpLogList() != null && !seaLcl.getRsOpLogList().isEmpty()) {
            processOpLogs(seaLcl.getRsOpLogList(), serviceInstance, rsRct);
        }
    }
    
    /**
     * 删除过时的记录
     */
    private void deleteObsoleteRecords(List<RsOpSeaLcl> toDeleteList) {
        if (toDeleteList.isEmpty()) {
            return;
        }
        
        for (RsOpSeaLcl seaLcl : toDeleteList) {
            // 删除海运拼箱服务记录
            rsOpSeaLclMapper.deleteRsOpSeaLclBySeaLclId(seaLcl.getSeaId());
            
            // 删除关联的服务实例
            if (seaLcl.getRsServiceInstances() != null && 
                seaLcl.getRsServiceInstances().getServiceId() != null) {
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(
                    seaLcl.getRsServiceInstances().getServiceId());
            }
        }
        
        log.debug("删除了 {} 个过时的海运拼箱服务记录", toDeleteList.size());
    }
    
    @Override
    public long getEstimatedProcessingTime(RsRct rsRct) {
        List<RsOpSeaLcl> seaLclList = rsRct.getRsOpSeaLclList();
        if (seaLclList == null || seaLclList.isEmpty()) {
            return 0L;
        }
        
        // 每个海运拼箱服务项估计处理时间为800毫秒
        return seaLclList.size() * 800L;
    }
}
