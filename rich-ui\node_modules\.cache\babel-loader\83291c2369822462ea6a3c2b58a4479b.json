{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\ExtendServiceComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\ExtendServiceComponent.vue", "mtime": 1754646305902}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_audit", "_interopRequireDefault", "require", "_logisticsProgress", "_chargeList", "name", "components", "Audit", "LogisticsProgress", "ChargeList", "props", "serviceItem", "type", "Object", "default", "_default", "extendServiceList", "Array", "form", "branchInfo", "Boolean", "logisticsInfo", "chargeInfo", "auditInfo", "disabled", "booking", "psaVerify", "supplierList", "companyList", "foldState", "serviceInstance", "serviceObject", "formDisable", "payable", "computed", "isDisabled", "methods", "changeFold", "serviceTypeId", "console", "log", "$emit", "changeServiceObject", "deleteLogItem", "event", "getServiceObject", "rsOpLogList", "filter", "item", "updateLogList", "getFold", "getServiceInstance", "getPayable", "getFormDisable", "getSupplierEmail", "supplierId", "supplier", "find", "v", "companyId", "staffEmail", "getAgreementDisplay", "agreementTypeCode", "agreementNo", "changeServiceFold", "serviceData", "serviceFold", "auditCharge", "generateFreight", "type1", "type2", "psaBookingCancel", "copyFreight", "calculateCharge", "deleteAllCharge", "rsChargeList", "deleteChargeItem", "exports", "_default2"], "sources": ["src/views/system/document/serviceComponents/ExtendServiceComponent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"extend-service-component\">\r\n    <!--拓展服务-->\r\n    <div class=\"extend-service-item\">\r\n      <!--标题栏-->\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <div class=\"service-bar\">\r\n            <a :class=\"[\r\n                'service-toggle-icon',\r\n                getFold(serviceItem.serviceTypeId) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n               @click=\"changeFold(serviceItem.serviceTypeId)\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeFold(serviceItem.serviceTypeId)\">\r\n                拓展服务-{{ serviceItem.serviceShortName }}\r\n              </h3>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"getServiceInstance(serviceItem.serviceTypeId)\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(serviceItem.serviceTypeId)\"\r\n              :rs-charge-list=\"serviceItem.rsChargeList\"\r\n              @auditFee=\"auditCharge(serviceItem, $event)\"\r\n              @return=\"changeServiceObject(serviceItem.serviceTypeId, $event)\"\r\n            />\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"getFold(serviceItem.serviceTypeId)\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNo\"\r\n                  :class=\"{ 'disable-form': disabled }\"\r\n                  :disabled=\"disabled\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(9, serviceItem.serviceTypeId, serviceItem)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(getServiceInstance(serviceItem.serviceTypeId).supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"getServiceInstance(serviceItem.serviceTypeId).supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(getServiceInstance(serviceItem.serviceTypeId))\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"12\">\r\n              <el-form-item label=\"商务单号\" prop=\"supplierId\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input :value=\"form.sqdPsaNo\" class=\"disable-form\"/>\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      style=\"color: red\"\r\n                      type=\"text\"\r\n                      @click=\"psaBookingCancel\"\r\n                    >\r\n                      取消\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getFormDisable(serviceItem.serviceTypeId) || disabled || psaVerify\"\r\n                :logistics-progress-data=\"getServiceObject(serviceItem.serviceTypeId).rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"101\"\r\n                @deleteItem=\"deleteLogItem(serviceItem.serviceTypeId, $event)\"\r\n                @return=\"updateLogList(serviceItem.serviceTypeId, $event)\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"getServiceObject(serviceItem.serviceTypeId).rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"getFormDisable(serviceItem.serviceTypeId) || disabled\"\r\n              :hidden-supplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"getServiceObject(serviceItem.serviceTypeId).payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"getServiceObject(serviceItem.serviceTypeId).payableRMBTax\"\r\n              :pay-detail-u-s-d=\"getServiceObject(serviceItem.serviceTypeId).payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"getServiceObject(serviceItem.serviceTypeId).payableUSDTax\"\r\n              :service-type-id=\"101\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"deleteAllCharge(serviceItem.serviceTypeId)\"\r\n              @deleteItem=\"deleteChargeItem(serviceItem.serviceTypeId, $event)\"\r\n              @return=\"calculateCharge(serviceItem.serviceTypeId, $event, getServiceObject(serviceItem.serviceTypeId))\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\n\r\nexport default {\r\n  name: \"ExtendServiceComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    serviceItem: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 拓展服务数据列表\r\n    extendServiceList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 新增属性，不再依赖$parent\r\n    foldState: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    serviceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    serviceObject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    formDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    payable: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    changeFold(serviceTypeId) {\r\n      console.log(serviceTypeId)\r\n      this.$emit(\"changeFold\", serviceTypeId)\r\n    },\r\n    changeServiceObject(serviceTypeId, serviceObject) {\r\n      this.$emit(\"changeServiceObject\", serviceTypeId, serviceObject)\r\n    },\r\n    // 物流进度相关方法\r\n    deleteLogItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsOpLogList) {\r\n        serviceObject.rsOpLogList = serviceObject.rsOpLogList.filter(item => item !== event)\r\n      }\r\n    },\r\n    updateLogList(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsOpLogList = event\r\n      }\r\n    },\r\n    getFold(serviceTypeId) {\r\n      return this.foldState\r\n    },\r\n    getServiceInstance(serviceTypeId) {\r\n      return this.serviceInstance\r\n    },\r\n    getServiceObject(serviceTypeId) {\r\n      return this.serviceObject\r\n    },\r\n    getPayable(serviceTypeId) {\r\n      return this.payable\r\n    },\r\n    getFormDisable(serviceTypeId) {\r\n      return this.formDisable\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return (serviceInstance.agreementTypeCode || '') + (serviceInstance.agreementNo || '')\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceData) {\r\n      serviceData.serviceFold = !serviceData.serviceFold\r\n      this.$emit(\"changeServiceFold\", serviceData)\r\n    },\r\n    auditCharge(serviceData, event) {\r\n      this.$emit(\"auditCharge\", serviceData, event)\r\n    },\r\n    generateFreight(type1, type2, serviceData) {\r\n      this.$emit(\"generateFreight\", type1, type2, serviceData)\r\n    },\r\n    psaBookingCancel() {\r\n      this.$emit(\"psaBookingCancel\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceTypeId, event, serviceData) {\r\n      this.$emit(\"calculateCharge\", serviceTypeId, event, serviceData)\r\n    },\r\n    // 费用列表相关方法\r\n    deleteAllCharge(serviceTypeId) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsChargeList = []\r\n      }\r\n    },\r\n    deleteChargeItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsChargeList) {\r\n        serviceObject.rsChargeList = serviceObject.rsChargeList.filter(item => item !== event)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// ExtendService组件特定样式\r\n.extend-service-component {\r\n  width: 100%;\r\n\r\n  .extend-service-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAwJA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAG,IAAA;EACAC,UAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,UAAA,EAAAA;EACA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAC,iBAAA;MACAJ,IAAA,EAAAK,KAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAG,IAAA;MACAN,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAI,UAAA;MACAP,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;IACAO,aAAA;MACAT,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;IACAQ,UAAA;MACAV,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;IACAS,SAAA;MACAX,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;IACA;IACAU,QAAA;MACAZ,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;IACAW,OAAA;MACAb,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;IACAY,SAAA;MACAd,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;IACA;IACAa,YAAA;MACAf,IAAA,EAAAK,KAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAa,WAAA;MACAhB,IAAA,EAAAK,KAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAc,SAAA;MACAjB,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;IACAgB,eAAA;MACAlB,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAgB,aAAA;MACAnB,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAiB,WAAA;MACApB,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;IACAmB,OAAA;MACArB,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAoB,QAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAX,QAAA,SAAAE,SAAA;IACA;EACA;EACAU,OAAA;IACAC,UAAA,WAAAA,WAAAC,aAAA;MACAC,OAAA,CAAAC,GAAA,CAAAF,aAAA;MACA,KAAAG,KAAA,eAAAH,aAAA;IACA;IACAI,mBAAA,WAAAA,oBAAAJ,aAAA,EAAAP,aAAA;MACA,KAAAU,KAAA,wBAAAH,aAAA,EAAAP,aAAA;IACA;IACA;IACAY,aAAA,WAAAA,cAAAL,aAAA,EAAAM,KAAA;MACA,IAAAb,aAAA,QAAAc,gBAAA,CAAAP,aAAA;MACA,IAAAP,aAAA,IAAAA,aAAA,CAAAe,WAAA;QACAf,aAAA,CAAAe,WAAA,GAAAf,aAAA,CAAAe,WAAA,CAAAC,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,KAAAJ,KAAA;QAAA;MACA;IACA;IACAK,aAAA,WAAAA,cAAAX,aAAA,EAAAM,KAAA;MACA,IAAAb,aAAA,QAAAc,gBAAA,CAAAP,aAAA;MACA,IAAAP,aAAA;QACAA,aAAA,CAAAe,WAAA,GAAAF,KAAA;MACA;IACA;IACAM,OAAA,WAAAA,QAAAZ,aAAA;MACA,YAAAT,SAAA;IACA;IACAsB,kBAAA,WAAAA,mBAAAb,aAAA;MACA,YAAAR,eAAA;IACA;IACAe,gBAAA,WAAAA,iBAAAP,aAAA;MACA,YAAAP,aAAA;IACA;IACAqB,UAAA,WAAAA,WAAAd,aAAA;MACA,YAAAL,OAAA;IACA;IACAoB,cAAA,WAAAA,eAAAf,aAAA;MACA,YAAAN,WAAA;IACA;IACA;IACAsB,gBAAA,WAAAA,iBAAAC,UAAA;MACA,IAAAC,QAAA,QAAA7B,YAAA,CAAA8B,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,SAAA,KAAAJ,UAAA;MAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAI,UAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAA/B,eAAA;MACA,QAAAA,eAAA,CAAAgC,iBAAA,WAAAhC,eAAA,CAAAiC,WAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,WAAA;MACAA,WAAA,CAAAC,WAAA,IAAAD,WAAA,CAAAC,WAAA;MACA,KAAAzB,KAAA,sBAAAwB,WAAA;IACA;IACAE,WAAA,WAAAA,YAAAF,WAAA,EAAArB,KAAA;MACA,KAAAH,KAAA,gBAAAwB,WAAA,EAAArB,KAAA;IACA;IACAwB,eAAA,WAAAA,gBAAAC,KAAA,EAAAC,KAAA,EAAAL,WAAA;MACA,KAAAxB,KAAA,oBAAA4B,KAAA,EAAAC,KAAA,EAAAL,WAAA;IACA;IACAM,gBAAA,WAAAA,iBAAA;MACA,KAAA9B,KAAA;IACA;IACA+B,WAAA,WAAAA,YAAA5B,KAAA;MACA,KAAAH,KAAA,gBAAAG,KAAA;IACA;IACA6B,eAAA,WAAAA,gBAAAnC,aAAA,EAAAM,KAAA,EAAAqB,WAAA;MACA,KAAAxB,KAAA,oBAAAH,aAAA,EAAAM,KAAA,EAAAqB,WAAA;IACA;IACA;IACAS,eAAA,WAAAA,gBAAApC,aAAA;MACA,IAAAP,aAAA,QAAAc,gBAAA,CAAAP,aAAA;MACA,IAAAP,aAAA;QACAA,aAAA,CAAA4C,YAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAAtC,aAAA,EAAAM,KAAA;MACA,IAAAb,aAAA,QAAAc,gBAAA,CAAAP,aAAA;MACA,IAAAP,aAAA,IAAAA,aAAA,CAAA4C,YAAA;QACA5C,aAAA,CAAA4C,YAAA,GAAA5C,aAAA,CAAA4C,YAAA,CAAA5B,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,KAAAJ,KAAA;QAAA;MACA;IACA;EACA;AACA;AAAAiC,OAAA,CAAA/D,OAAA,GAAAgE,SAAA"}]}