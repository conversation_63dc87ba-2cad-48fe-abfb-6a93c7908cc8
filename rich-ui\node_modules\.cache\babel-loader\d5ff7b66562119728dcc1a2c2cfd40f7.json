{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregatorBackGround\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregatorBackGround\\index.vue", "mtime": 1754646305886}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_moment", "_interopRequireDefault", "require", "_currency", "_aggregator", "_html2pdf", "name", "props", "fieldLabelMap", "type", "Object", "required", "default", "_default", "dataSourceType", "String", "aggregateFunction", "Function", "configType", "data", "config<PERSON><PERSON>", "config", "primaryField", "matchOptions", "exact", "caseSensitive", "dateField", "dateOptions", "convertToNumber", "formatType", "showDetails", "fields", "filters", "label", "value", "aggregationOptions", "operatorOptions", "loading", "configDialogVisible", "filterDialogVisible", "savedConfigs", "configLoading", "isLandscape", "showResult", "processedData", "currentFilter", "field", "operator", "computed", "availableFields", "keys", "dateFields", "_this", "filter", "display", "groupFieldName", "concat", "getFieldLabel", "methods", "_this$fieldLabelMap$f", "getOperatorLabel", "op", "find", "item", "openFilterDialog", "addFilter", "$message", "warning", "$set", "push", "_objectSpread2", "removeFilter", "index", "splice", "getFieldDisplay", "<PERSON><PERSON><PERSON>", "fieldConfig", "isAggregatable", "aggregated", "addField", "aggregation", "format", "sort", "hideZero<PERSON><PERSON><PERSON>", "removeField", "moveField", "direction", "_toConsumableArray2", "_ref", "length", "_ref2", "handleFieldSelect", "getDefaultFormat", "displayType", "resetConfig", "getDateFormat", "handleServerAggregate", "_this2", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "<PERSON><PERSON><PERSON>", "params", "response", "wrap", "_callee$", "_context", "prev", "next", "abrupt", "error", "sent", "code", "filterZeroValueRecords", "msg", "t0", "console", "message", "finish", "stop", "_this3", "zeroFilter<PERSON>ields", "map", "key", "aggProp", "getResultProp", "record", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "err", "e", "f", "getResultLabel", "baseLabel", "_this$aggregationOpti", "aggregationLabel", "opt", "formatCellValue", "numValue", "Number", "isNaN", "toFixed", "moment", "formatGroupKey", "groupKey", "_typeof2", "primary", "undefined", "date", "primaryFieldConfig", "primaryValue", "getSummary", "_ref3", "_this4", "columns", "sums", "for<PERSON>ach", "column", "fieldIndex", "values", "prop", "val", "sum", "reduce", "a", "b", "Math", "max", "apply", "min", "mean", "pow", "percentage", "saveConfig", "_this5", "_callee2", "configToSave", "_callee2$", "_context2", "saveAggregatorConfig", "success", "loadConfigs", "_this6", "_callee3", "result", "_err$response", "_callee3$", "_context3", "loadAggregatorConfigs", "rows", "handleConfigSelect", "row", "_this7", "_callee4", "_callee4$", "_context4", "JSON", "parse", "deleteConfig", "_this8", "_callee5", "_callee5$", "_context5", "$confirm", "deleteAggregatorConfig", "id", "printTable", "printWindow", "window", "open", "table", "$refs", "resultTable", "$el", "cloneNode", "title", "Date", "toLocaleDateString", "headerTemplate", "document", "write", "outerHTML", "replace", "close", "setTimeout", "focus", "print", "exportToPDF", "_this9", "_callee6", "element", "_callee6$", "_context6", "margin", "filename", "image", "quality", "html2canvas", "scale", "jsPDF", "unit", "orientation", "pagebreak", "mode", "header", "text", "style", "alignment", "footer", "height", "contents", "html2pdf", "set", "from", "save", "getName", "staff", "$store", "state", "allRsStaffList", "rsStaff", "staffId", "staffShortName", "staffFamilyEnName", "includes", "isDecimal", "percentValue", "getColumnAlign", "align", "getColumnWidth", "width", "exports", "_default2"], "sources": ["src/views/system/DataAggregatorBackGround/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"data-aggregator\">\r\n    <el-row :gutter=\"20\">\r\n      <!-- 配置区域 - 左侧 -->\r\n      <el-col :span=\"showResult ? 10 : 10\">\r\n        <el-card class=\"config-card\">\r\n          <template #header>\r\n            <div class=\"header-with-operations\">\r\n              <span>汇总配置</span>\r\n            </div>\r\n          </template>\r\n          <el-form class=\"edit\" label-width=\"80px\">\r\n            <!-- 速查名称 -->\r\n            <el-form-item label=\"速查名称\" required>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"18\">\r\n                  <el-input v-model=\"config.name\" placeholder=\"请输入速查名称\"/>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-button size=\"small\" type=\"text\" @click=\"saveConfig\">[↗]</el-button>\r\n                  <el-button size=\"small\" type=\"text\" @click=\"loadConfigs\">[...]</el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 分组依据 -->\r\n            <el-form-item label=\"分组依据\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <el-select v-model=\"config.primaryField\" clearable filterable placeholder=\"操作单号\">\r\n                    <el-option\r\n                      v-for=\"field in availableFields\"\r\n                      :key=\"field\"\r\n                      :label=\"getFieldLabel(field)\"\r\n                      :value=\"field\"\r\n                    />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"15\">\r\n                  <el-checkbox v-model=\"config.matchOptions.exact\">精确匹配</el-checkbox>\r\n                  <el-checkbox v-model=\"config.matchOptions.caseSensitive\">区分大小写</el-checkbox>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 分组日期 -->\r\n            <el-form-item label=\"分组日期\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <el-select v-model=\"config.dateField\" clearable filterable placeholder=\"分组日期\">\r\n                    <el-option\r\n                      v-for=\"field in dateFields\"\r\n                      :key=\"field\"\r\n                      :label=\"getFieldLabel(field)\"\r\n                      :value=\"field\"\r\n                    />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"15\">\r\n                  <el-checkbox v-model=\"config.dateOptions.convertToNumber\">转换为数字</el-checkbox>\r\n                  <el-radio-group v-model=\"config.dateOptions.formatType\" style=\"display: flex;line-height: 26px\">\r\n                    <el-radio label=\"year\">按年</el-radio>\r\n                    <el-radio label=\"month\">按月</el-radio>\r\n                    <el-radio label=\"day\">按天</el-radio>\r\n                  </el-radio-group>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 数据筛选 -->\r\n            <!--<el-form-item label=\"数据筛选\">-->\r\n            <!--  <el-button type=\"primary\" plain size=\"small\" @click=\"openFilterDialog\">设置筛选条件</el-button>-->\r\n            <!--  <div v-if=\"config.filters && config.filters.length\" class=\"filter-tags\">-->\r\n            <!--    <el-tag-->\r\n            <!--      v-for=\"(filter, index) in config.filters\"-->\r\n            <!--      :key=\"index\"-->\r\n            <!--      closable-->\r\n            <!--      @close=\"removeFilter(index)\"-->\r\n            <!--    >-->\r\n            <!--      {{getFieldLabel(filter.field)}} {{getOperatorLabel(filter.operator)}} {{filter.value}}-->\r\n            <!--    </el-tag>-->\r\n            <!--  </div>-->\r\n            <!--</el-form-item>-->\r\n\r\n            <!-- 显示方式 -->\r\n            <el-form-item label=\"显示方式\">\r\n              <el-checkbox v-model=\"config.showDetails\" style=\"padding-left: 5px;\">含明细</el-checkbox>\r\n            </el-form-item>\r\n\r\n            <!-- 动态字段配置 -->\r\n            <el-table\r\n              :data=\"config.fields\"\r\n              border\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-table-column\r\n                align=\"center\"\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"60\"\r\n              />\r\n\r\n              <el-table-column label=\"表头名称\" min-width=\"100\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.fieldKey\"\r\n                    filterable\r\n                    placeholder=\"选择字段\"\r\n                    style=\"width: 100%\"\r\n                    @change=\"handleFieldSelect(scope.$index)\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(config, key) in fieldLabelMap\"\r\n                      :key=\"key\"\r\n                      :label=\"config.name\"\r\n                      :value=\"key\"\r\n                    />\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"排序\" width=\"60\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.sort\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <el-option label=\"∧\" value=\"asc\"/>\r\n                    <el-option label=\"∨ \" value=\"desc\"/>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"汇总方式\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.aggregation\"\r\n                    :disabled=\"!isAggregatable(scope.row.fieldKey)\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <el-option label=\"计数\" value=\"count\"/>\r\n                    <el-option label=\"求和\" value=\"sum\"/>\r\n                    <el-option label=\"平均值\" value=\"avg\"/>\r\n                    <el-option label=\"最大值\" value=\"max\"/>\r\n                    <el-option label=\"最小值\" value=\"min\"/>\r\n                    <el-option label=\"方差\" value=\"variance\"/>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"显示格式\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.format\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <template v-if=\"getFieldDisplay(scope.row.fieldKey) === 'date'\">\r\n                      <el-option label=\"YYYYMM\" value=\"YYYYMM\"/>\r\n                      <el-option label=\"MM-DD\" value=\"MM-DD\"/>\r\n                      <el-option label=\"YYYY-MM-DD\" value=\"YYYY-MM-DD\"/>\r\n                    </template>\r\n                    <template v-if=\"getFieldDisplay(scope.row.fieldKey) === 'number'\">\r\n                      <el-option label=\"0.00\" value=\"decimal\"/>\r\n                      <el-option label=\"0.00%\" value=\"percent\"/>\r\n                      <el-option label=\"¥0.00\" value=\"currency\"/>\r\n                      <el-option label=\"$0.00\" value=\"usd\"/>\r\n                      <el-option label=\"0不显示\" value=\"hideZero\"/>\r\n                    </template>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column align=\"center\" label=\"0隐藏\" width=\"40\">\r\n                <template #default=\"scope\">\r\n                  <el-checkbox\r\n                    v-model=\"scope.row.hideZeroValues\"\r\n                    :disabled=\"getFieldDisplay(scope.row.fieldKey) !== 'number'\"\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column align=\"center\" label=\"操作\" width=\"100\">\r\n                <template #default=\"scope\">\r\n                  <el-button-group>\r\n                    <el-button\r\n                      :disabled=\"scope.$index === 0\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"moveField(scope.$index, 'up')\"\r\n                    >[∧]\r\n                    </el-button>\r\n                    <el-button\r\n                      :disabled=\"scope.$index === config.fields.length - 1\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"moveField(scope.$index, 'down')\"\r\n                    >[∨]\r\n                    </el-button>\r\n                    <el-button\r\n                      icon=\"el-icon-delete\"\r\n                      size=\"mini\"\r\n                      style=\"color: red\"\r\n                      type=\"text\"\r\n                      @click=\"removeField(scope.$index)\"\r\n                    >\r\n                    </el-button>\r\n                  </el-button-group>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <div style=\"margin-top: 10px;\">\r\n              <el-button plain type=\"text\" @click=\"addField\">[ + ]</el-button>\r\n            </div>\r\n\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleServerAggregate\">分类汇总</el-button>\r\n              <el-button @click=\"resetConfig\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 结果展示 - 右侧 -->\r\n      <el-col v-if=\"showResult\" :span=\"14\">\r\n        <el-card class=\"result-card\">\r\n          <template #header>\r\n            <div class=\"header-with-operations\">\r\n              <span>汇总结果</span>\r\n              <div class=\"operations\">\r\n                <el-switch\r\n                  v-model=\"isLandscape\"\r\n                  active-text=\"横向\"\r\n                  inactive-text=\"纵向\"\r\n                  style=\"margin-right: 15px\"\r\n                />\r\n                <el-button size=\"small\" @click=\"printTable\">打印</el-button>\r\n                <el-button size=\"small\" type=\"primary\" @click=\"exportToPDF\">导出PDF</el-button>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <el-table\r\n            ref=\"resultTable\"\r\n            v-loading=\"loading\"\r\n            :data=\"processedData\"\r\n            :summary-method=\"getSummary\"\r\n            border\r\n            show-summary\r\n            style=\"width: 100%\"\r\n          >\r\n            <!-- 分组字段列 -->\r\n            <el-table-column\r\n              :align=\"config.primaryField ? fieldLabelMap[config.primaryField].align : 'left'\"\r\n              :label=\"groupFieldName\"\r\n              :width=\"config.primaryField ? fieldLabelMap[config.primaryField].width : ''\"\r\n            >\r\n              <template #default=\"scope\">\r\n                {{ formatGroupKey(scope.row.groupKey) }}\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <!-- 动态字段列 - 按照config.fields的顺序渲染 -->\r\n            <template v-for=\"(field, fieldIndex) in config.fields\">\r\n              <el-table-column\r\n                v-if=\"field.fieldKey\"\r\n                :key=\"field.fieldKey + '_' + fieldIndex\"\r\n                :align=\"getColumnAlign(field.fieldKey)\"\r\n                :label=\"getResultLabel(field)\"\r\n                :width=\"getColumnWidth(field.fieldKey)\"\r\n              >\r\n                <template #default=\"scope\">\r\n                  {{ formatCellValue(scope.row[getResultProp(field)], field) }}\r\n                </template>\r\n              </el-table-column>\r\n            </template>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 加载配置对话框 -->\r\n    <el-dialog :visible.sync=\"configDialogVisible\" append-to-body title=\"加载配置\" width=\"550px\">\r\n      <el-table\r\n        v-loading=\"configLoading\"\r\n        :data=\"savedConfigs\"\r\n        style=\"width: 100%\"\r\n        @row-click=\"handleConfigSelect\"\r\n      >\r\n        <el-table-column label=\"配置名称\" prop=\"name\"/>\r\n        <el-table-column label=\"创建时间\" prop=\"createTime\"/>\r\n        <el-table-column width=\"50\">\r\n          <template #default=\"scope\">\r\n            <el-button type=\"text\" @click.stop=\"deleteConfig(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n\r\n    <!-- 筛选条件对话框 -->\r\n    <el-dialog :visible.sync=\"filterDialogVisible\" title=\"设置筛选条件\" width=\"650px\">\r\n      <el-form :model=\"currentFilter\" label-width=\"100px\">\r\n        <el-form-item label=\"字段\">\r\n          <el-select v-model=\"currentFilter.field\" filterable placeholder=\"选择字段\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"(config, key) in fieldLabelMap\"\r\n              :key=\"key\"\r\n              :label=\"config.name\"\r\n              :value=\"key\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"操作符\">\r\n          <el-select v-model=\"currentFilter.operator\" placeholder=\"选择操作符\" style=\"width: 100%\">\r\n            <el-option label=\"等于\" value=\"eq\"/>\r\n            <el-option label=\"不等于\" value=\"ne\"/>\r\n            <el-option label=\"大于\" value=\"gt\"/>\r\n            <el-option label=\"大于等于\" value=\"ge\"/>\r\n            <el-option label=\"小于\" value=\"lt\"/>\r\n            <el-option label=\"小于等于\" value=\"le\"/>\r\n            <el-option label=\"包含\" value=\"contains\"/>\r\n            <el-option label=\"开始于\" value=\"startsWith\"/>\r\n            <el-option label=\"结束于\" value=\"endsWith\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"值\">\r\n          <el-input v-model=\"currentFilter.value\" placeholder=\"输入筛选值\"/>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"addFilter\">添加筛选条件</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div v-if=\"config.filters && config.filters.length\">\r\n        <h4>已添加的筛选条件</h4>\r\n        <el-table :data=\"config.filters\" border>\r\n          <el-table-column label=\"字段\" prop=\"field\">\r\n            <template #default=\"scope\">\r\n              {{ getFieldLabel(scope.row.field) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作符\" prop=\"operator\">\r\n            <template #default=\"scope\">\r\n              {{ getOperatorLabel(scope.row.operator) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"值\" prop=\"value\"/>\r\n          <el-table-column label=\"操作\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <el-button\r\n                circle\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                @click=\"removeFilter(scope.$index)\"\r\n              ></el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"filterDialogVisible = false\">关闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\nimport {saveAggregatorConfig, loadAggregatorConfigs, deleteAggregatorConfig} from \"@/api/system/aggregator\"\r\nimport html2pdf from \"html2pdf.js\"\r\n\r\nexport default {\r\n  name: \"DataAggregatorBackGround\",\r\n  props: {\r\n    // 可用字段配置映射\r\n    fieldLabelMap: {\r\n      type: Object,\r\n      required: true,\r\n      default: () => ({})\r\n    },\r\n    // 指定数据来源类型，用于后端查询\r\n    dataSourceType: {\r\n      type: String,\r\n      required: true,\r\n      default: 'rct' // 默认是操作单数据\r\n    },\r\n    // 从父组件传入的汇总函数\r\n    aggregateFunction: {\r\n      type: Function,\r\n      required: true\r\n    },\r\n    configType: {\r\n      type: String,\r\n      required: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      configName: \"\",\r\n      config: {\r\n        name: \"\",\r\n        primaryField: \"\",\r\n        matchOptions: {\r\n          exact: true,\r\n          caseSensitive: false\r\n        },\r\n        dateField: \"\",\r\n        dateOptions: {\r\n          convertToNumber: false,\r\n          formatType: \"day\"\r\n        },\r\n        showDetails: false,\r\n        fields: [],\r\n        filters: [] // 存储筛选条件\r\n      },\r\n      dateOptions: [\r\n        {label: \"按年\", value: \"year\"},\r\n        {label: \"按月\", value: \"month\"},\r\n        {label: \"按周\", value: \"week\"},\r\n        {label: \"按日\", value: \"day\"},\r\n        {label: \"按时\", value: \"hour\"},\r\n        {label: \"按分\", value: \"minute\"}\r\n      ],\r\n      aggregationOptions: [\r\n        {label: \"计数\", value: \"count\"},\r\n        {label: \"求和\", value: \"sum\"},\r\n        {label: \"平均值\", value: \"avg\"},\r\n        {label: \"方差\", value: \"variance\"},\r\n        {label: \"最大值\", value: \"max\"},\r\n        {label: \"最小值\", value: \"min\"}\r\n      ],\r\n      operatorOptions: [\r\n        {label: \"等于\", value: \"eq\"},\r\n        {label: \"不等于\", value: \"ne\"},\r\n        {label: \"大于\", value: \"gt\"},\r\n        {label: \"大于等于\", value: \"ge\"},\r\n        {label: \"小于\", value: \"lt\"},\r\n        {label: \"小于等于\", value: \"le\"},\r\n        {label: \"包含\", value: \"contains\"},\r\n        {label: \"开始于\", value: \"startsWith\"},\r\n        {label: \"结束于\", value: \"endsWith\"}\r\n      ],\r\n      loading: false,\r\n      configDialogVisible: false,\r\n      filterDialogVisible: false,\r\n      savedConfigs: [],\r\n      configLoading: false,\r\n      isLandscape: false,\r\n      showResult: false,\r\n      processedData: [],\r\n      currentFilter: {\r\n        field: \"\",\r\n        operator: \"eq\",\r\n        value: \"\"\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 可用字段列表\r\n    availableFields() {\r\n      // 返回在 fieldLabelMap 中定义的所有字段\r\n      return Object.keys(this.fieldLabelMap)\r\n    },\r\n\r\n    // 日期类型字段列表\r\n    dateFields() {\r\n      return this.availableFields.filter(field => {\r\n        // 检查 fieldLabelMap 中的 display 属性\r\n        return this.fieldLabelMap[field] && this.fieldLabelMap[field].display === \"date\"\r\n      })\r\n    },\r\n\r\n    // 分组字段名称\r\n    groupFieldName() {\r\n      if (this.config.primaryField && this.config.dateField) {\r\n        return `${this.getFieldLabel(this.config.dateField)}+${this.getFieldLabel(this.config.primaryField)}`\r\n      } else if (this.config.primaryField) {\r\n        return this.getFieldLabel(this.config.primaryField)\r\n      } else if (this.config.dateField) {\r\n        return this.getFieldLabel(this.config.dateField)\r\n      }\r\n      return \"分组\"\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * 根据字段键获取字段标签\r\n     * @param {String} field - 字段的键\r\n     * @returns {String} 字段的标签\r\n     */\r\n    getFieldLabel(field) {\r\n      return this.fieldLabelMap[field]?.name || field\r\n    },\r\n\r\n    /**\r\n     * 根据操作符代码获取操作符标签\r\n     * @param {String} op - 操作符代码\r\n     * @returns {String} 操作符标签\r\n     */\r\n    getOperatorLabel(op) {\r\n      const operator = this.operatorOptions.find(item => item.value === op)\r\n      return operator ? operator.label : op\r\n    },\r\n\r\n    /**\r\n     * 打开筛选条件对话框\r\n     */\r\n    openFilterDialog() {\r\n      this.currentFilter = {\r\n        field: \"\",\r\n        operator: \"eq\",\r\n        value: \"\"\r\n      }\r\n      this.filterDialogVisible = true\r\n    },\r\n\r\n    /**\r\n     * 添加筛选条件\r\n     */\r\n    addFilter() {\r\n      if (!this.currentFilter.field) {\r\n        this.$message.warning(\"请选择筛选字段\")\r\n        return\r\n      }\r\n\r\n      if (!this.currentFilter.value && this.currentFilter.value !== 0) {\r\n        this.$message.warning(\"请输入筛选值\")\r\n        return\r\n      }\r\n\r\n      // 初始化filters数组（如果不存在）\r\n      if (!this.config.filters) {\r\n        this.$set(this.config, 'filters', [])\r\n      }\r\n\r\n      // 添加新的筛选条件\r\n      this.config.filters.push({...this.currentFilter})\r\n\r\n      // 重置当前筛选条件\r\n      this.currentFilter = {\r\n        field: \"\",\r\n        operator: \"eq\",\r\n        value: \"\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 移除筛选条件\r\n     * @param {Number} index - 要移除的筛选条件索引\r\n     */\r\n    removeFilter(index) {\r\n      this.config.filters.splice(index, 1)\r\n    },\r\n\r\n    /**\r\n     * 获取字段的显示类型\r\n     * @param {string} fieldKey - 字段标识\r\n     * @returns {string} 字段显示类型（text/number/date/boolean/custom）\r\n     */\r\n    getFieldDisplay(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      if (!fieldConfig) return \"text\"\r\n\r\n      // 检查是否是自定义方法\r\n      if (fieldConfig.display && typeof this[fieldConfig.display] === \"function\") {\r\n        return \"custom\"\r\n      }\r\n\r\n      return fieldConfig.display || \"text\"\r\n    },\r\n\r\n    /**\r\n     * 判断字段是否可以进行汇总计算\r\n     * @param {string} fieldKey - 字段标识\r\n     * @returns {boolean} 是否可汇总\r\n     */\r\n    isAggregatable(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      return fieldConfig?.aggregated || false\r\n    },\r\n\r\n    /**\r\n     * 添加新的字段配置行\r\n     */\r\n    addField() {\r\n      this.config.fields.push({\r\n        fieldKey: \"\",\r\n        aggregation: \"none\",\r\n        format: \"none\",\r\n        sort: \"none\",\r\n        hideZeroValues: false\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 删除指定索引的字段配置行\r\n     * @param {number} index - 要删除的字段索引\r\n     */\r\n    removeField(index) {\r\n      this.config.fields.splice(index, 1)\r\n    },\r\n\r\n    /**\r\n     * 移动字段配置行的位置\r\n     * @param {number} index - 当前字段的索引\r\n     * @param {string} direction - 移动方向，'up' 或 'down'\r\n     */\r\n    moveField(index, direction) {\r\n      const fields = [...this.config.fields] // 创建数组副本\r\n\r\n      if (direction === \"up\" && index > 0) {\r\n        // 向上移动，与上一个元素交换位置\r\n        [fields[index], fields[index - 1]] = [fields[index - 1], fields[index]]\r\n      } else if (direction === \"down\" && index < fields.length - 1) {\r\n        // 向下移动，与下一个元素交换位置\r\n        [fields[index], fields[index + 1]] = [fields[index + 1], fields[index]]\r\n      }\r\n\r\n      // 使用整个新数组替换，确保响应式更新\r\n      this.$set(this.config, \"fields\", fields)\r\n    },\r\n\r\n    /**\r\n     * 处理字段选择变更事件\r\n     * @param {number} index - 变更的字段索引\r\n     */\r\n    handleFieldSelect(index) {\r\n      const field = this.config.fields[index]\r\n      const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n      if (fieldConfig) {\r\n        // 根据字段配置设置默认值\r\n        field.format = this.getDefaultFormat(fieldConfig.display)\r\n        field.aggregation = fieldConfig.aggregated ? \"sum\" : \"none\"\r\n        field.sort = \"none\" // 默认不排序\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 根据显示类型获取默认的格式化方式\r\n     * @param {string} displayType - 显示类型\r\n     * @returns {string} 默认格式\r\n     */\r\n    getDefaultFormat(displayType) {\r\n      switch (displayType) {\r\n        case \"date\":\r\n          return \"YYYY-MM-DD\"\r\n        case \"number\":\r\n          return \"decimal\"\r\n        default:\r\n          return \"none\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 重置配置到初始状态\r\n     */\r\n    resetConfig() {\r\n      this.config = {\r\n        name: \"\",\r\n        primaryField: \"\",\r\n        matchOptions: {\r\n          exact: true,\r\n          caseSensitive: false\r\n        },\r\n        dateField: \"\",\r\n        dateOptions: {\r\n          convertToNumber: false,\r\n          formatType: \"day\"\r\n        },\r\n        showDetails: false,\r\n        fields: [],\r\n        filters: []\r\n      }\r\n      this.showResult = false\r\n    },\r\n\r\n    /**\r\n     * 获取日期格式化模式\r\n     * @returns {String} 日期格式\r\n     */\r\n    getDateFormat() {\r\n      switch (this.config.dateOptions.formatType) {\r\n        case \"year\":\r\n          return \"YYYY\"\r\n        case \"month\":\r\n          return \"YYYY-MM\"\r\n        case \"day\":\r\n        default:\r\n          return \"YYYY-MM-DD\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 调用后端服务进行数据汇总\r\n     */\r\n    async handleServerAggregate() {\r\n      // 验证分组依据和分组日期至少填写一个\r\n      if (!this.config.primaryField && !this.config.dateField) {\r\n        this.$message.warning(\"请至少选择分组依据或分组日期其中之一\");\r\n        return;\r\n      }\r\n\r\n      // 验证是否有字段配置\r\n      if (!this.config.fields.length) {\r\n        this.$message.warning(\"请添加至少一个字段\");\r\n        return;\r\n      }\r\n\r\n      // 验证字段配置是否完整\r\n      const incompleteField = this.config.fields.find(field => !field.fieldKey);\r\n      if (incompleteField) {\r\n        this.$message.warning(\"请完成所有字段的配置\");\r\n        return;\r\n      }\r\n\r\n      // 确保汇总函数已经传入\r\n      if (!this.aggregateFunction) {\r\n        this.$message.error(\"汇总函数未定义\")\r\n        return\r\n      }\r\n\r\n      try {\r\n        this.loading = true\r\n\r\n        // 准备请求参数\r\n        const params = {\r\n          dataSourceType: this.dataSourceType,\r\n          config: {\r\n            primaryField: this.config.primaryField,\r\n            matchOptions: this.config.matchOptions,\r\n            dateField: this.config.dateField,\r\n            dateOptions: this.config.dateOptions,\r\n            showDetails: this.config.showDetails,\r\n            fields: this.config.fields,\r\n            filters: this.config.filters || []\r\n          }\r\n        }\r\n\r\n        // 调用从父组件传入的汇总函数\r\n        const response = await this.aggregateFunction(params)\r\n\r\n        if (response.code === 200) {\r\n          // 过滤零值记录\r\n          this.processedData = this.filterZeroValueRecords(response.data)\r\n          this.showResult = true\r\n        } else {\r\n          this.$message.error(response.msg || \"汇总数据失败\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"数据汇总失败:\", error)\r\n        this.$message.error(\"汇总处理失败：\" + (error.message || \"未知错误\"))\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 过滤零值记录\r\n     * @param {Array} data - 原始数据\r\n     * @returns {Array} 过滤后的数据\r\n     */\r\n    filterZeroValueRecords(data) {\r\n      // 找出设置了hideZeroValues为true的字段\r\n      const zeroFilterFields = this.config.fields\r\n        .filter(field => field.hideZeroValues === true)\r\n        .map(field => ({\r\n          key: field.fieldKey,\r\n          aggProp: this.getResultProp(field)\r\n        }));\r\n\r\n      // 如果没有需要过滤的字段，直接返回原始数据\r\n      if (zeroFilterFields.length === 0) {\r\n        return data;\r\n      }\r\n\r\n      // 过滤数据\r\n      return data.filter(record => {\r\n        // 检查每个需要过滤零值的字段\r\n        for (const field of zeroFilterFields) {\r\n          const value = record[field.aggProp];\r\n          // 如果字段值为0，过滤掉这条记录\r\n          if (value === 0 || value === \"0\" || value === \"0.00\") {\r\n            return false;\r\n          }\r\n        }\r\n        // 所有字段都不为零，保留这条记录\r\n        return true;\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 获取结果数据的属性名\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 属性名\r\n     */\r\n    getResultProp(field) {\r\n      // 如果有汇总方式，属性名为 fieldKey_aggregation\r\n      if (field.aggregation && field.aggregation !== \"none\") {\r\n        return `${field.fieldKey}_${field.aggregation}`\r\n      }\r\n      return field.fieldKey\r\n    },\r\n\r\n    /**\r\n     * 获取结果表格的列标题\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 列标题\r\n     */\r\n    getResultLabel(field) {\r\n      const baseLabel = this.getFieldLabel(field.fieldKey)\r\n\r\n      // 如果有汇总方式，在标签中添加汇总方式信息\r\n      if (field.aggregation && field.aggregation !== \"none\") {\r\n        // 获取汇总方式的中文名称\r\n        const aggregationLabel = this.aggregationOptions.find(opt => opt.value === field.aggregation)?.label || field.aggregation\r\n        return `${baseLabel}(${aggregationLabel})`\r\n      }\r\n\r\n      return baseLabel\r\n    },\r\n\r\n    /**\r\n     * 格式化单元格的值\r\n     * @param {*} value - 原始值\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 格式化后的值\r\n     */\r\n    formatCellValue(value, field) {\r\n      if (value == null) return \"-\"\r\n\r\n      const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n      if (!fieldConfig) return value\r\n\r\n      // 处理自定义 display 方法\r\n      if (fieldConfig.display && typeof this[fieldConfig.display] === \"function\") {\r\n        // 调用组件中定义的方法\r\n        return this[fieldConfig.display](value)\r\n      }\r\n\r\n      // 根据字段类型进行格式化\r\n      switch (fieldConfig.display) {\r\n        case \"number\":\r\n          const numValue = Number(value)\r\n          if (isNaN(numValue)) return \"-\"\r\n\r\n          switch (field.format) {\r\n            case \"decimal\":\r\n              return numValue.toFixed(2)\r\n            case \"percent\":\r\n              return (numValue * 100).toFixed(2) + \"%\"\r\n            case \"currency\":\r\n              return \"¥\" + numValue.toFixed(2)\r\n            case \"usd\":\r\n              return \"$\" + numValue.toFixed(2)\r\n            case \"hideZero\":\r\n              return numValue === 0 ? \"-\" : numValue.toFixed(2)\r\n            default:\r\n              return numValue.toFixed(2)\r\n          }\r\n\r\n        case \"date\":\r\n          return moment(value).format(field.format || \"YYYY-MM-DD\")\r\n\r\n        case \"boolean\":\r\n          if (field.aggregation === \"avg\") {\r\n            return (Number(value) * 100).toFixed(2) + \"%\"\r\n          }\r\n          return value ? \"是\" : \"否\"\r\n\r\n        default:\r\n          return value\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 格式化分组键\r\n     * @param {Object|string} groupKey - 分组键\r\n     * @returns {string} 格式化后的分组键\r\n     */\r\n    formatGroupKey(groupKey) {\r\n      if (typeof groupKey === \"object\" && groupKey !== null) {\r\n        if (groupKey.primary !== undefined && groupKey.date !== undefined) {\r\n          // 获取主分组字段的配置\r\n          const primaryFieldConfig = this.fieldLabelMap[this.config.primaryField]\r\n          let primaryValue = groupKey.primary\r\n\r\n          // 如果主分组字段有自定义 display 方法，应用它\r\n          if (primaryFieldConfig && primaryFieldConfig.display &&\r\n            typeof this[primaryFieldConfig.display] === \"function\") {\r\n            primaryValue = this[primaryFieldConfig.display](primaryValue)\r\n          }\r\n\r\n          // 日期值在前，主值在后\r\n          return `${groupKey.date} ${primaryValue}`\r\n        }\r\n      }\r\n\r\n      // 如果是简单值，检查是否需要应用自定义 display 方法\r\n      if (this.config.primaryField) {\r\n        const fieldConfig = this.fieldLabelMap[this.config.primaryField]\r\n        if (fieldConfig && fieldConfig.display &&\r\n          typeof this[fieldConfig.display] === \"function\") {\r\n          return this[fieldConfig.display](groupKey)\r\n        }\r\n      }\r\n\r\n      return String(groupKey || \"\")\r\n    },\r\n\r\n    /**\r\n     * 计算表格合计行\r\n     * @param {Object} param0 - 包含列信息和数据的对象\r\n     * @returns {Array} 合计行数据\r\n     */\r\n    getSummary({columns, data}) {\r\n      const sums = []\r\n\r\n      columns.forEach((column, index) => {\r\n        // 第一列显示\"合计\"文本\r\n        if (index === 0) {\r\n          sums[index] = \"合计\"\r\n          return\r\n        }\r\n\r\n        // 使用索引获取当前列对应的字段配置\r\n        const fieldIndex = index - 1\r\n        const field = this.config.fields[fieldIndex]\r\n\r\n        if (!field || !field.fieldKey) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 检查字段是否配置了汇总方式\r\n        if (!field.aggregation || field.aggregation === \"none\") {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 获取字段配置\r\n        const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n\r\n        // 对于不是数字类型的字段但有percentage显示方法的特殊处理\r\n        if (!fieldConfig) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        if (fieldConfig.display !== \"number\" &&\r\n          fieldConfig.display !== \"percentage\" &&\r\n          typeof this[fieldConfig.display] !== \"function\") {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 获取列数据并转换为数字\r\n        const values = data.map(item => {\r\n          const prop = this.getResultProp(field)\r\n          const val = Number(item[prop])\r\n          return isNaN(val) ? 0 : val // 处理非数字值\r\n        }).filter(val => !isNaN(val))\r\n\r\n        if (values.length === 0) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 根据汇总方式计算结果\r\n        let sum = 0\r\n        switch (field.aggregation) {\r\n          case \"sum\":\r\n            sum = values.reduce((a, b) => a + b, 0)\r\n            break\r\n          case \"avg\":\r\n            sum = values.reduce((a, b) => a + b, 0) / values.length\r\n            break\r\n          case \"max\":\r\n            sum = Math.max(...values)\r\n            break\r\n          case \"min\":\r\n            sum = Math.min(...values)\r\n            break\r\n          case \"variance\":\r\n            const mean = values.reduce((a, b) => a + b, 0) / values.length\r\n            sum = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length\r\n            break\r\n          default:\r\n            sum = values.reduce((a, b) => a + b, 0)\r\n        }\r\n\r\n        // 根据字段显示类型和格式设置来格式化结果\r\n        if (fieldConfig.display === \"percentage\" || fieldConfig.display === \"percent\") {\r\n          // 使用percentage方法格式化\r\n          sums[index] = this.percentage(sum)\r\n        } else if (field.format === \"decimal\") {\r\n          sums[index] = sum.toFixed(2)\r\n        } else if (field.format === \"percent\") {\r\n          sums[index] = (sum * 100).toFixed(2) + \"%\"\r\n        } else if (field.format === \"currency\") {\r\n          sums[index] = \"¥\" + sum.toFixed(2)\r\n        } else if (field.format === \"usd\") {\r\n          sums[index] = \"$\" + sum.toFixed(2)\r\n        } else {\r\n          sums[index] = sum.toFixed(2)\r\n        }\r\n      })\r\n\r\n      return sums\r\n    },\r\n\r\n    /**\r\n     * 保存当前配置\r\n     */\r\n    async saveConfig() {\r\n      try {\r\n        // 验证配置名称\r\n        if (!this.config.name) {\r\n          this.$message.warning(\"请输入速查名称\")\r\n          return\r\n        }\r\n\r\n        // 验证分组依据和分组日期至少填写一个\r\n        if (!this.config.primaryField && !this.config.dateField) {\r\n          this.$message.warning(\"请至少选择分组依据或分组日期其中之一\")\r\n          return\r\n        }\r\n\r\n        if (!this.config.fields.length) {\r\n          this.$message.warning(\"请添加至少一个字段\")\r\n          return\r\n        }\r\n\r\n        // 验证字段配置是否完整\r\n        const incompleteField = this.config.fields.find(field => !field.fieldKey)\r\n        if (incompleteField) {\r\n          this.$message.warning(\"请完成所有字段的配置\")\r\n          return\r\n        }\r\n\r\n        // 构造符合 AggregatorConfigDTO 的数据结构\r\n        const configToSave = {\r\n          name: this.config.name,\r\n          type: this.configType,\r\n          config: this.config\r\n        }\r\n\r\n        // 发送请求\r\n        await saveAggregatorConfig(configToSave)\r\n\r\n        this.$message.success(\"配置保存成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"保存配置失败：\" + (err.message || \"未知错误\"))\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 加载保存的配置列表\r\n     */\r\n    async loadConfigs() {\r\n      this.configLoading = true\r\n      this.configDialogVisible = true\r\n      try {\r\n        const result = await loadAggregatorConfigs({configType: this.configType})\r\n        this.savedConfigs = result.rows\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\r\n          err.response?.data?.message ||\r\n          err.message ||\r\n          \"加载配置列表失败，请稍后重试\"\r\n        )\r\n      } finally {\r\n        this.configLoading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 选择并加载配置\r\n     * @param {Object} row - 选中的配置行\r\n     */\r\n    async handleConfigSelect(row) {\r\n      try {\r\n        // 解析配置JSON\r\n        var config = JSON.parse(row.config)\r\n        config.name = row.name\r\n        this.config = config\r\n        // this.config.name = row.name\r\n\r\n        this.configDialogVisible = false\r\n        this.$message.success(\"配置加载成功\")\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\"加载配置失败：\" + err.message)\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 删除配置\r\n     * @param {Object} row - 要删除的配置行\r\n     */\r\n    async deleteConfig(row) {\r\n      try {\r\n        await this.$confirm(\"确认删除该配置？\", \"提示\", {\r\n          type: \"warning\"\r\n        })\r\n\r\n        await deleteAggregatorConfig(row.id)\r\n        this.savedConfigs = this.savedConfigs.filter(config => config.id !== row.id)\r\n        this.$message.success(\"配置删除成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"删除配置失败：\" + err.message)\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 打印表格\r\n     */\r\n    printTable() {\r\n      // 实现与原组件相同的打印功能\r\n      const printWindow = window.open(\"\", \"_blank\")\r\n      const table = this.$refs.resultTable.$el.cloneNode(true)\r\n      const title = \"汇总数据\"\r\n      const date = new Date().toLocaleDateString()\r\n\r\n      // 公司标志和标题的HTML模板\r\n      const headerTemplate = `\r\n        <div class=\"company-header\">\r\n          <div class=\"company-logo\">\r\n            <img src=\"/logo.png\" alt=\"Rich Shipping Logo\" />\r\n            <div class=\"company-name\">\r\n              <div class=\"company-name-cn\">广州瑞旗国际货运代理有限公司</div>\r\n              <div class=\"company-name-en\">GUANGZHOU RICH SHIPPING INT'L CO.,LTD.</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"document-title\">\r\n            <div class=\"title-cn\"></div>\r\n            <div class=\"title-en\"></div>\r\n          </div>\r\n        </div>\r\n      `\r\n      printWindow.document.write(`\r\n        <html lang=\"\">\r\n          <head>\r\n            <title>${title}</title>\r\n            <style>\r\n              /* 基础样式 */\r\n              body {\r\n                margin: 0;\r\n                padding: 0;\r\n                font-family: Arial, sans-serif;\r\n              }\r\n\r\n              /* 打印样式 - 必须放在这里才能生效 */\r\n              @media print {\r\n                @page {\r\n                  size: ${this.isLandscape ? \"landscape\" : \"portrait\"};\r\n                  margin: 1.5cm 1cm 1cm 1cm;\r\n                }\r\n\r\n                /* 重要：使用重复表头技术 */\r\n                thead {\r\n                  display: table-header-group;\r\n                }\r\n\r\n                /* 页眉作为表格的一部分，放在thead中 */\r\n                .page-header {\r\n                  display: table-header-group;\r\n                }\r\n\r\n                /* 内容部分 */\r\n                .page-content {\r\n                  display: table-row-group;\r\n                }\r\n\r\n                /* 避免元素内部分页 */\r\n                .company-header, .header-content {\r\n                  page-break-inside: avoid;\r\n                }\r\n\r\n                /* 表格样式 */\r\n                table.main-table {\r\n                  width: 100%;\r\n                  border-collapse: collapse;\r\n                  border: none;\r\n                }\r\n\r\n                /* 确保表头在每页都显示 */\r\n                table.data-table thead {\r\n                  display: table-header-group;\r\n                }\r\n\r\n                /* 避免行内分页 */\r\n                table.data-table tr {\r\n                  page-break-inside: avoid;\r\n                }\r\n              }\r\n\r\n              /* 表格样式 */\r\n              table.data-table {\r\n                border-collapse: collapse;\r\n                width: 100%;\r\n                margin-top: 20px;\r\n                table-layout: fixed;\r\n              }\r\n\r\n              table.data-table th, table.data-table td {\r\n                border: 1px solid #ddd;\r\n                padding: 8px;\r\n                text-align: left;\r\n                font-size: 12px;\r\n                word-wrap: break-word;\r\n                word-break: break-all;\r\n                white-space: normal;\r\n              }\r\n\r\n              table.data-table th {\r\n                background-color: #f2f2f2;\r\n              }\r\n\r\n              /* Element UI 表格样式模拟 */\r\n              .el-table {\r\n                border-collapse: collapse;\r\n                width: 100%;\r\n                table-layout: fixed;\r\n              }\r\n\r\n              .el-table th, .el-table td {\r\n                border: 1px solid #ddd;\r\n                padding: 8px;\r\n                text-align: left;\r\n                font-size: 12px;\r\n                word-wrap: break-word;\r\n                word-break: break-all;\r\n                white-space: normal;\r\n              }\r\n\r\n              .el-table th {\r\n                background-color: #f2f2f2;\r\n                font-weight: bold;\r\n              }\r\n\r\n              .el-table__footer {\r\n                background-color: #f8f8f9;\r\n                font-weight: bold;\r\n              }\r\n\r\n              .el-table__footer td {\r\n                border: 1px solid #ddd;\r\n                padding: 8px;\r\n              }\r\n\r\n              /* 公司标题和标志样式 */\r\n              .company-header {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                border-bottom: 2px solid #000;\r\n                padding-bottom: 10px;\r\n                width: 100%;\r\n              }\r\n\r\n              .company-logo {\r\n                display: flex;\r\n                align-items: center;\r\n              }\r\n\r\n              .company-logo img {\r\n                height: 50px;\r\n                margin-right: 10px;\r\n              }\r\n\r\n              .company-name {\r\n                display: flex;\r\n                flex-direction: column;\r\n              }\r\n\r\n              .company-name-cn {\r\n                font-size: 18px;\r\n                font-weight: bold;\r\n                color: #ff0000;\r\n              }\r\n\r\n              .company-name-en {\r\n                font-size: 14px;\r\n              }\r\n\r\n              .document-title {\r\n                text-align: right;\r\n              }\r\n\r\n              .title-cn {\r\n                font-size: 18px;\r\n                font-weight: bold;\r\n              }\r\n\r\n              .title-en {\r\n                font-size: 16px;\r\n                font-weight: bold;\r\n              }\r\n\r\n              /* 清除表格边框 */\r\n              table.main-table, table.main-table td {\r\n                border: none;\r\n              }\r\n\r\n              /* 页眉容器 */\r\n              .header-container {\r\n                width: 100%;\r\n                margin-bottom: 20px;\r\n              }\r\n            </style>\r\n          </head>\r\n          <body>\r\n            <!-- 使用表格布局确保页眉在每页重复 -->\r\n            <table class=\"main-table\">\r\n              <thead class=\"page-header\">\r\n                <tr>\r\n                  <td>\r\n                    <div class=\"header-container\">\r\n                      ${headerTemplate}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </thead>\r\n              <tbody class=\"page-content\">\r\n                <tr>\r\n                  <td>\r\n                    <!-- 保留原始表格的类名并添加data-table类 -->\r\n                    ${table.outerHTML.replace('<table', '<table class=\"el-table data-table\"')}\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </body>\r\n        </html>\r\n      `)\r\n\r\n      printWindow.document.close()\r\n\r\n      setTimeout(() => {\r\n        try {\r\n          printWindow.focus();\r\n          printWindow.print();\r\n        } catch (e) {\r\n          console.error(\"打印过程中发生错误:\", e);\r\n        }\r\n      }, 1000)\r\n    },\r\n\r\n    /**\r\n     * 导出PDF\r\n     */\r\n    async exportToPDF() {\r\n      try {\r\n        this.loading = true\r\n        const element = this.$refs.resultTable.$el\r\n        const opt = {\r\n          margin: [0.8, 0.8, 0.8, 0.8],\r\n          filename: \"汇总数据.pdf\",\r\n          image: {type: \"jpeg\", quality: 0.98},\r\n          html2canvas: {scale: 2},\r\n          jsPDF: {\r\n            unit: \"in\",\r\n            format: \"a3\",\r\n            orientation: this.isLandscape ? \"landscape\" : \"portrait\"\r\n          },\r\n          pagebreak: {mode: [\"avoid-all\", \"css\", \"legacy\"]},\r\n          header: [\r\n            {text: \"汇总数据\", style: \"headerStyle\"},\r\n            {text: new Date().toLocaleDateString(), style: \"headerStyle\", alignment: \"right\"}\r\n          ],\r\n          footer: {\r\n            height: \"20px\",\r\n            contents: {\r\n              default: \"<span style=\\\"float:right\\\">{{page}}/{{pages}}</span>\"\r\n            }\r\n          }\r\n        }\r\n\r\n        await html2pdf().set(opt).from(element).save()\r\n        this.$message.success(\"PDF导出成功\")\r\n      } catch (error) {\r\n        this.$message.error(\"PDF导出失败：\" + error.message)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 获取人员姓名\r\n     * @param {Number} id - 人员ID\r\n     * @returns {String} 人员姓名\r\n     */\r\n    getName(id) {\r\n      if (id !== null) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff && staff !== undefined) {\r\n          return staff.staffShortName + staff.staffFamilyEnName\r\n        }\r\n      }\r\n      return \"\"\r\n    },\r\n\r\n    /**\r\n     * 格式化百分比值\r\n     * @param {*} value - 要格式化的值\r\n     * @returns {string} 格式化后的百分比\r\n     */\r\n    percentage(value) {\r\n      if (value == null || value === '') {\r\n        return '-';\r\n      }\r\n\r\n      // 处理已经带有%的情况\r\n      if (typeof value === 'string' && value.includes('%')) {\r\n        return value;\r\n      }\r\n\r\n      // 将数值转换为百分比格式\r\n      const numValue = Number(value);\r\n      if (isNaN(numValue)) {\r\n        return '-';\r\n      }\r\n\r\n      // 如果值已经是百分比形式(例如0.25表示25%)，则直接乘以100\r\n      // 如果值已经是整数形式(例如25表示25%)，则不需要乘以100\r\n      const isDecimal = numValue > 0 && numValue <= 1;\r\n      const percentValue = isDecimal ? numValue * 100 : numValue;\r\n\r\n      // 格式化为2位小数的百分比\r\n      return percentValue.toFixed(2) + '%';\r\n    },\r\n\r\n    /**\r\n     * 获取列对齐方式\r\n     * @param {string} fieldKey - 字段键\r\n     * @returns {string} 对齐方式\r\n     */\r\n    getColumnAlign(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      return fieldConfig && fieldConfig.align ? fieldConfig.align : 'left'\r\n    },\r\n\r\n    /**\r\n     * 获取列宽度\r\n     * @param {string} fieldKey - 字段键\r\n     * @returns {string|number} 列宽度\r\n     */\r\n    getColumnWidth(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      return fieldConfig && fieldConfig.width ? fieldConfig.width : ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.data-aggregator {\r\n  padding: 20px;\r\n}\r\n\r\n.config-card, .result-card {\r\n  height: 100%;\r\n  overflow: auto;\r\n}\r\n\r\n.result-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-with-operations {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.operations {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.filter-tags {\r\n  margin-top: 10px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 5px;\r\n}\r\n\r\n.el-tag {\r\n  margin-right: 5px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.field-config-table {\r\n  border: 1px solid #EBEEF5;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.table-header,\r\n.table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px;\r\n  border-bottom: 1px solid #EBEEF5;\r\n}\r\n\r\n.table-header {\r\n  background-color: #F5F7FA;\r\n  font-weight: bold;\r\n}\r\n\r\n.col {\r\n  flex: 1;\r\n  padding: 0 5px;\r\n  min-width: 120px;\r\n}\r\n\r\n.col:first-child {\r\n  flex: 0 0 60px;\r\n  min-width: 60px;\r\n}\r\n\r\n.col-operation {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-input-number {\r\n  width: 100%;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqXA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAJ,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAI,IAAA;EACAC,KAAA;IACA;IACAC,aAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAC,cAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,QAAA;MACAC,OAAA;IACA;;IACA;IACAI,iBAAA;MACAP,IAAA,EAAAQ,QAAA;MACAN,QAAA;IACA;IACAO,UAAA;MACAT,IAAA,EAAAM,MAAA;MACAJ,QAAA;IACA;EACA;EACAQ,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,MAAA;QACAf,IAAA;QACAgB,YAAA;QACAC,YAAA;UACAC,KAAA;UACAC,aAAA;QACA;QACAC,SAAA;QACAC,WAAA;UACAC,eAAA;UACAC,UAAA;QACA;QACAC,WAAA;QACAC,MAAA;QACAC,OAAA;MACA;;MACAL,WAAA,GACA;QAAAM,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,kBAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAE,eAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAG,OAAA;MACAC,mBAAA;MACAC,mBAAA;MACAC,YAAA;MACAC,aAAA;MACAC,WAAA;MACAC,UAAA;MACAC,aAAA;MACAC,aAAA;QACAC,KAAA;QACAC,QAAA;QACAb,KAAA;MACA;IACA;EACA;EACAc,QAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA;MACA,OAAAvC,MAAA,CAAAwC,IAAA,MAAA1C,aAAA;IACA;IAEA;IACA2C,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA,YAAAH,eAAA,CAAAI,MAAA,WAAAP,KAAA;QACA;QACA,OAAAM,KAAA,CAAA5C,aAAA,CAAAsC,KAAA,KAAAM,KAAA,CAAA5C,aAAA,CAAAsC,KAAA,EAAAQ,OAAA;MACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAA;MACA,SAAAlC,MAAA,CAAAC,YAAA,SAAAD,MAAA,CAAAK,SAAA;QACA,UAAA8B,MAAA,MAAAC,aAAA,MAAApC,MAAA,CAAAK,SAAA,QAAA8B,MAAA,MAAAC,aAAA,MAAApC,MAAA,CAAAC,YAAA;MACA,gBAAAD,MAAA,CAAAC,YAAA;QACA,YAAAmC,aAAA,MAAApC,MAAA,CAAAC,YAAA;MACA,gBAAAD,MAAA,CAAAK,SAAA;QACA,YAAA+B,aAAA,MAAApC,MAAA,CAAAK,SAAA;MACA;MACA;IACA;EACA;EACAgC,OAAA;IACA;AACA;AACA;AACA;AACA;IACAD,aAAA,WAAAA,cAAAX,KAAA;MAAA,IAAAa,qBAAA;MACA,SAAAA,qBAAA,QAAAnD,aAAA,CAAAsC,KAAA,eAAAa,qBAAA,uBAAAA,qBAAA,CAAArD,IAAA,KAAAwC,KAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAc,gBAAA,WAAAA,iBAAAC,EAAA;MACA,IAAAd,QAAA,QAAAX,eAAA,CAAA0B,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA7B,KAAA,KAAA2B,EAAA;MAAA;MACA,OAAAd,QAAA,GAAAA,QAAA,CAAAd,KAAA,GAAA4B,EAAA;IACA;IAEA;AACA;AACA;IACAG,gBAAA,WAAAA,iBAAA;MACA,KAAAnB,aAAA;QACAC,KAAA;QACAC,QAAA;QACAb,KAAA;MACA;MACA,KAAAK,mBAAA;IACA;IAEA;AACA;AACA;IACA0B,SAAA,WAAAA,UAAA;MACA,UAAApB,aAAA,CAAAC,KAAA;QACA,KAAAoB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,UAAAtB,aAAA,CAAAX,KAAA,SAAAW,aAAA,CAAAX,KAAA;QACA,KAAAgC,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,UAAA9C,MAAA,CAAAW,OAAA;QACA,KAAAoC,IAAA,MAAA/C,MAAA;MACA;;MAEA;MACA,KAAAA,MAAA,CAAAW,OAAA,CAAAqC,IAAA,KAAAC,cAAA,CAAA1D,OAAA,WAAAiC,aAAA;;MAEA;MACA,KAAAA,aAAA;QACAC,KAAA;QACAC,QAAA;QACAb,KAAA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAqC,YAAA,WAAAA,aAAAC,KAAA;MACA,KAAAnD,MAAA,CAAAW,OAAA,CAAAyC,MAAA,CAAAD,KAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAE,eAAA,WAAAA,gBAAAC,QAAA;MACA,IAAAC,WAAA,QAAApE,aAAA,CAAAmE,QAAA;MACA,KAAAC,WAAA;;MAEA;MACA,IAAAA,WAAA,CAAAtB,OAAA,gBAAAsB,WAAA,CAAAtB,OAAA;QACA;MACA;MAEA,OAAAsB,WAAA,CAAAtB,OAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAuB,cAAA,WAAAA,eAAAF,QAAA;MACA,IAAAC,WAAA,QAAApE,aAAA,CAAAmE,QAAA;MACA,QAAAC,WAAA,aAAAA,WAAA,uBAAAA,WAAA,CAAAE,UAAA;IACA;IAEA;AACA;AACA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAA1D,MAAA,CAAAU,MAAA,CAAAsC,IAAA;QACAM,QAAA;QACAK,WAAA;QACAC,MAAA;QACAC,IAAA;QACAC,cAAA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC,WAAA,WAAAA,YAAAZ,KAAA;MACA,KAAAnD,MAAA,CAAAU,MAAA,CAAA0C,MAAA,CAAAD,KAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAa,SAAA,WAAAA,UAAAb,KAAA,EAAAc,SAAA;MACA,IAAAvD,MAAA,OAAAwD,mBAAA,CAAA3E,OAAA,OAAAS,MAAA,CAAAU,MAAA;;MAEA,IAAAuD,SAAA,aAAAd,KAAA;QACA;QAAA,IAAAgB,IAAA,GACA,CAAAzD,MAAA,CAAAyC,KAAA,OAAAzC,MAAA,CAAAyC,KAAA;QAAAzC,MAAA,CAAAyC,KAAA,IAAAgB,IAAA;QAAAzD,MAAA,CAAAyC,KAAA,QAAAgB,IAAA;MACA,WAAAF,SAAA,eAAAd,KAAA,GAAAzC,MAAA,CAAA0D,MAAA;QACA;QAAA,IAAAC,KAAA,GACA,CAAA3D,MAAA,CAAAyC,KAAA,OAAAzC,MAAA,CAAAyC,KAAA;QAAAzC,MAAA,CAAAyC,KAAA,IAAAkB,KAAA;QAAA3D,MAAA,CAAAyC,KAAA,QAAAkB,KAAA;MACA;;MAEA;MACA,KAAAtB,IAAA,MAAA/C,MAAA,YAAAU,MAAA;IACA;IAEA;AACA;AACA;AACA;IACA4D,iBAAA,WAAAA,kBAAAnB,KAAA;MACA,IAAA1B,KAAA,QAAAzB,MAAA,CAAAU,MAAA,CAAAyC,KAAA;MACA,IAAAI,WAAA,QAAApE,aAAA,CAAAsC,KAAA,CAAA6B,QAAA;MACA,IAAAC,WAAA;QACA;QACA9B,KAAA,CAAAmC,MAAA,QAAAW,gBAAA,CAAAhB,WAAA,CAAAtB,OAAA;QACAR,KAAA,CAAAkC,WAAA,GAAAJ,WAAA,CAAAE,UAAA;QACAhC,KAAA,CAAAoC,IAAA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAU,gBAAA,WAAAA,iBAAAC,WAAA;MACA,QAAAA,WAAA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IAEA;AACA;AACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAzE,MAAA;QACAf,IAAA;QACAgB,YAAA;QACAC,YAAA;UACAC,KAAA;UACAC,aAAA;QACA;QACAC,SAAA;QACAC,WAAA;UACAC,eAAA;UACAC,UAAA;QACA;QACAC,WAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA,KAAAW,UAAA;IACA;IAEA;AACA;AACA;AACA;IACAoD,aAAA,WAAAA,cAAA;MACA,aAAA1E,MAAA,CAAAM,WAAA,CAAAE,UAAA;QACA;UACA;QACA;UACA;QACA;QACA;UACA;MACA;IACA;IAEA;AACA;AACA;IACAmE,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAAtF,OAAA,oBAAAuF,oBAAA,CAAAvF,OAAA,IAAAwF,IAAA,UAAAC,QAAA;QAAA,IAAAC,eAAA,EAAAC,MAAA,EAAAC,QAAA;QAAA,WAAAL,oBAAA,CAAAvF,OAAA,IAAA6F,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,MAEA,CAAAZ,MAAA,CAAA5E,MAAA,CAAAC,YAAA,KAAA2E,MAAA,CAAA5E,MAAA,CAAAK,SAAA;gBAAAiF,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAZ,MAAA,CAAA/B,QAAA,CAAAC,OAAA;cAAA,OAAAwC,QAAA,CAAAG,MAAA;YAAA;cAAA,IAKAb,MAAA,CAAA5E,MAAA,CAAAU,MAAA,CAAA0D,MAAA;gBAAAkB,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAZ,MAAA,CAAA/B,QAAA,CAAAC,OAAA;cAAA,OAAAwC,QAAA,CAAAG,MAAA;YAAA;cAIA;cACAR,eAAA,GAAAL,MAAA,CAAA5E,MAAA,CAAAU,MAAA,CAAA+B,IAAA,WAAAhB,KAAA;gBAAA,QAAAA,KAAA,CAAA6B,QAAA;cAAA;cAAA,KACA2B,eAAA;gBAAAK,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAZ,MAAA,CAAA/B,QAAA,CAAAC,OAAA;cAAA,OAAAwC,QAAA,CAAAG,MAAA;YAAA;cAAA,IAKAb,MAAA,CAAAjF,iBAAA;gBAAA2F,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAZ,MAAA,CAAA/B,QAAA,CAAA6C,KAAA;cAAA,OAAAJ,QAAA,CAAAG,MAAA;YAAA;cAAAH,QAAA,CAAAC,IAAA;cAKAX,MAAA,CAAA5D,OAAA;;cAEA;cACAkE,MAAA;gBACAzF,cAAA,EAAAmF,MAAA,CAAAnF,cAAA;gBACAO,MAAA;kBACAC,YAAA,EAAA2E,MAAA,CAAA5E,MAAA,CAAAC,YAAA;kBACAC,YAAA,EAAA0E,MAAA,CAAA5E,MAAA,CAAAE,YAAA;kBACAG,SAAA,EAAAuE,MAAA,CAAA5E,MAAA,CAAAK,SAAA;kBACAC,WAAA,EAAAsE,MAAA,CAAA5E,MAAA,CAAAM,WAAA;kBACAG,WAAA,EAAAmE,MAAA,CAAA5E,MAAA,CAAAS,WAAA;kBACAC,MAAA,EAAAkE,MAAA,CAAA5E,MAAA,CAAAU,MAAA;kBACAC,OAAA,EAAAiE,MAAA,CAAA5E,MAAA,CAAAW,OAAA;gBACA;cACA,GAEA;cAAA2E,QAAA,CAAAE,IAAA;cAAA,OACAZ,MAAA,CAAAjF,iBAAA,CAAAuF,MAAA;YAAA;cAAAC,QAAA,GAAAG,QAAA,CAAAK,IAAA;cAEA,IAAAR,QAAA,CAAAS,IAAA;gBACA;gBACAhB,MAAA,CAAArD,aAAA,GAAAqD,MAAA,CAAAiB,sBAAA,CAAAV,QAAA,CAAArF,IAAA;gBACA8E,MAAA,CAAAtD,UAAA;cACA;gBACAsD,MAAA,CAAA/B,QAAA,CAAA6C,KAAA,CAAAP,QAAA,CAAAW,GAAA;cACA;cAAAR,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAS,EAAA,GAAAT,QAAA;cAEAU,OAAA,CAAAN,KAAA,YAAAJ,QAAA,CAAAS,EAAA;cACAnB,MAAA,CAAA/B,QAAA,CAAA6C,KAAA,cAAAJ,QAAA,CAAAS,EAAA,CAAAE,OAAA;YAAA;cAAAX,QAAA,CAAAC,IAAA;cAEAX,MAAA,CAAA5D,OAAA;cAAA,OAAAsE,QAAA,CAAAY,MAAA;YAAA;YAAA;cAAA,OAAAZ,QAAA,CAAAa,IAAA;UAAA;QAAA,GAAAnB,OAAA;MAAA;IAEA;IAEA;AACA;AACA;AACA;AACA;IACAa,sBAAA,WAAAA,uBAAA/F,IAAA;MAAA,IAAAsG,MAAA;MACA;MACA,IAAAC,gBAAA,QAAArG,MAAA,CAAAU,MAAA,CACAsB,MAAA,WAAAP,KAAA;QAAA,OAAAA,KAAA,CAAAqC,cAAA;MAAA,GACAwC,GAAA,WAAA7E,KAAA;QAAA;UACA8E,GAAA,EAAA9E,KAAA,CAAA6B,QAAA;UACAkD,OAAA,EAAAJ,MAAA,CAAAK,aAAA,CAAAhF,KAAA;QACA;MAAA;;MAEA;MACA,IAAA4E,gBAAA,CAAAjC,MAAA;QACA,OAAAtE,IAAA;MACA;;MAEA;MACA,OAAAA,IAAA,CAAAkC,MAAA,WAAA0E,MAAA;QACA;QAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAArH,OAAA,EACA8G,gBAAA;UAAAQ,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAAvF,KAAA,GAAAoF,KAAA,CAAAhG,KAAA;YACA,IAAAA,KAAA,GAAA6F,MAAA,CAAAjF,KAAA,CAAA+E,OAAA;YACA;YACA,IAAA3F,KAAA,UAAAA,KAAA,YAAAA,KAAA;cACA;YACA;UACA;UACA;QAAA,SAAAoG,GAAA;UAAAN,SAAA,CAAAO,CAAA,CAAAD,GAAA;QAAA;UAAAN,SAAA,CAAAQ,CAAA;QAAA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAV,aAAA,WAAAA,cAAAhF,KAAA;MACA;MACA,IAAAA,KAAA,CAAAkC,WAAA,IAAAlC,KAAA,CAAAkC,WAAA;QACA,UAAAxB,MAAA,CAAAV,KAAA,CAAA6B,QAAA,OAAAnB,MAAA,CAAAV,KAAA,CAAAkC,WAAA;MACA;MACA,OAAAlC,KAAA,CAAA6B,QAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACA8D,cAAA,WAAAA,eAAA3F,KAAA;MACA,IAAA4F,SAAA,QAAAjF,aAAA,CAAAX,KAAA,CAAA6B,QAAA;;MAEA;MACA,IAAA7B,KAAA,CAAAkC,WAAA,IAAAlC,KAAA,CAAAkC,WAAA;QAAA,IAAA2D,qBAAA;QACA;QACA,IAAAC,gBAAA,KAAAD,qBAAA,QAAAxG,kBAAA,CAAA2B,IAAA,WAAA+E,GAAA;UAAA,OAAAA,GAAA,CAAA3G,KAAA,KAAAY,KAAA,CAAAkC,WAAA;QAAA,gBAAA2D,qBAAA,uBAAAA,qBAAA,CAAA1G,KAAA,KAAAa,KAAA,CAAAkC,WAAA;QACA,UAAAxB,MAAA,CAAAkF,SAAA,OAAAlF,MAAA,CAAAoF,gBAAA;MACA;MAEA,OAAAF,SAAA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAI,eAAA,WAAAA,gBAAA5G,KAAA,EAAAY,KAAA;MACA,IAAAZ,KAAA;MAEA,IAAA0C,WAAA,QAAApE,aAAA,CAAAsC,KAAA,CAAA6B,QAAA;MACA,KAAAC,WAAA,SAAA1C,KAAA;;MAEA;MACA,IAAA0C,WAAA,CAAAtB,OAAA,gBAAAsB,WAAA,CAAAtB,OAAA;QACA;QACA,YAAAsB,WAAA,CAAAtB,OAAA,EAAApB,KAAA;MACA;;MAEA;MACA,QAAA0C,WAAA,CAAAtB,OAAA;QACA;UACA,IAAAyF,QAAA,GAAAC,MAAA,CAAA9G,KAAA;UACA,IAAA+G,KAAA,CAAAF,QAAA;UAEA,QAAAjG,KAAA,CAAAmC,MAAA;YACA;cACA,OAAA8D,QAAA,CAAAG,OAAA;YACA;cACA,QAAAH,QAAA,QAAAG,OAAA;YACA;cACA,aAAAH,QAAA,CAAAG,OAAA;YACA;cACA,aAAAH,QAAA,CAAAG,OAAA;YACA;cACA,OAAAH,QAAA,eAAAA,QAAA,CAAAG,OAAA;YACA;cACA,OAAAH,QAAA,CAAAG,OAAA;UACA;QAEA;UACA,WAAAC,eAAA,EAAAjH,KAAA,EAAA+C,MAAA,CAAAnC,KAAA,CAAAmC,MAAA;QAEA;UACA,IAAAnC,KAAA,CAAAkC,WAAA;YACA,QAAAgE,MAAA,CAAA9G,KAAA,SAAAgH,OAAA;UACA;UACA,OAAAhH,KAAA;QAEA;UACA,OAAAA,KAAA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAkH,cAAA,WAAAA,eAAAC,QAAA;MACA,QAAAC,QAAA,CAAA1I,OAAA,EAAAyI,QAAA,kBAAAA,QAAA;QACA,IAAAA,QAAA,CAAAE,OAAA,KAAAC,SAAA,IAAAH,QAAA,CAAAI,IAAA,KAAAD,SAAA;UACA;UACA,IAAAE,kBAAA,QAAAlJ,aAAA,MAAAa,MAAA,CAAAC,YAAA;UACA,IAAAqI,YAAA,GAAAN,QAAA,CAAAE,OAAA;;UAEA;UACA,IAAAG,kBAAA,IAAAA,kBAAA,CAAApG,OAAA,IACA,YAAAoG,kBAAA,CAAApG,OAAA;YACAqG,YAAA,QAAAD,kBAAA,CAAApG,OAAA,EAAAqG,YAAA;UACA;;UAEA;UACA,UAAAnG,MAAA,CAAA6F,QAAA,CAAAI,IAAA,OAAAjG,MAAA,CAAAmG,YAAA;QACA;MACA;;MAEA;MACA,SAAAtI,MAAA,CAAAC,YAAA;QACA,IAAAsD,WAAA,QAAApE,aAAA,MAAAa,MAAA,CAAAC,YAAA;QACA,IAAAsD,WAAA,IAAAA,WAAA,CAAAtB,OAAA,IACA,YAAAsB,WAAA,CAAAtB,OAAA;UACA,YAAAsB,WAAA,CAAAtB,OAAA,EAAA+F,QAAA;QACA;MACA;MAEA,OAAAtI,MAAA,CAAAsI,QAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAO,UAAA,WAAAA,WAAAC,KAAA;MAAA,IAAAC,MAAA;MAAA,IAAAC,OAAA,GAAAF,KAAA,CAAAE,OAAA;QAAA5I,IAAA,GAAA0I,KAAA,CAAA1I,IAAA;MACA,IAAA6I,IAAA;MAEAD,OAAA,CAAAE,OAAA,WAAAC,MAAA,EAAA1F,KAAA;QACA;QACA,IAAAA,KAAA;UACAwF,IAAA,CAAAxF,KAAA;UACA;QACA;;QAEA;QACA,IAAA2F,UAAA,GAAA3F,KAAA;QACA,IAAA1B,KAAA,GAAAgH,MAAA,CAAAzI,MAAA,CAAAU,MAAA,CAAAoI,UAAA;QAEA,KAAArH,KAAA,KAAAA,KAAA,CAAA6B,QAAA;UACAqF,IAAA,CAAAxF,KAAA;UACA;QACA;;QAEA;QACA,KAAA1B,KAAA,CAAAkC,WAAA,IAAAlC,KAAA,CAAAkC,WAAA;UACAgF,IAAA,CAAAxF,KAAA;UACA;QACA;;QAEA;QACA,IAAAI,WAAA,GAAAkF,MAAA,CAAAtJ,aAAA,CAAAsC,KAAA,CAAA6B,QAAA;;QAEA;QACA,KAAAC,WAAA;UACAoF,IAAA,CAAAxF,KAAA;UACA;QACA;QAEA,IAAAI,WAAA,CAAAtB,OAAA,iBACAsB,WAAA,CAAAtB,OAAA,qBACA,OAAAwG,MAAA,CAAAlF,WAAA,CAAAtB,OAAA;UACA0G,IAAA,CAAAxF,KAAA;UACA;QACA;;QAEA;QACA,IAAA4F,MAAA,GAAAjJ,IAAA,CAAAwG,GAAA,WAAA5D,IAAA;UACA,IAAAsG,IAAA,GAAAP,MAAA,CAAAhC,aAAA,CAAAhF,KAAA;UACA,IAAAwH,GAAA,GAAAtB,MAAA,CAAAjF,IAAA,CAAAsG,IAAA;UACA,OAAApB,KAAA,CAAAqB,GAAA,QAAAA,GAAA;QACA,GAAAjH,MAAA,WAAAiH,GAAA;UAAA,QAAArB,KAAA,CAAAqB,GAAA;QAAA;QAEA,IAAAF,MAAA,CAAA3E,MAAA;UACAuE,IAAA,CAAAxF,KAAA;UACA;QACA;;QAEA;QACA,IAAA+F,GAAA;QACA,QAAAzH,KAAA,CAAAkC,WAAA;UACA;YACAuF,GAAA,GAAAH,MAAA,CAAAI,MAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAD,CAAA,GAAAC,CAAA;YAAA;YACA;UACA;YACAH,GAAA,GAAAH,MAAA,CAAAI,MAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAD,CAAA,GAAAC,CAAA;YAAA,QAAAN,MAAA,CAAA3E,MAAA;YACA;UACA;YACA8E,GAAA,GAAAI,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAApF,mBAAA,CAAA3E,OAAA,EAAAwJ,MAAA;YACA;UACA;YACAG,GAAA,GAAAI,IAAA,CAAAG,GAAA,CAAAD,KAAA,CAAAF,IAAA,MAAApF,mBAAA,CAAA3E,OAAA,EAAAwJ,MAAA;YACA;UACA;YACA,IAAAW,IAAA,GAAAX,MAAA,CAAAI,MAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAD,CAAA,GAAAC,CAAA;YAAA,QAAAN,MAAA,CAAA3E,MAAA;YACA8E,GAAA,GAAAH,MAAA,CAAAI,MAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAD,CAAA,GAAAE,IAAA,CAAAK,GAAA,CAAAN,CAAA,GAAAK,IAAA;YAAA,QAAAX,MAAA,CAAA3E,MAAA;YACA;UACA;YACA8E,GAAA,GAAAH,MAAA,CAAAI,MAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAD,CAAA,GAAAC,CAAA;YAAA;QACA;;QAEA;QACA,IAAA9F,WAAA,CAAAtB,OAAA,qBAAAsB,WAAA,CAAAtB,OAAA;UACA;UACA0G,IAAA,CAAAxF,KAAA,IAAAsF,MAAA,CAAAmB,UAAA,CAAAV,GAAA;QACA,WAAAzH,KAAA,CAAAmC,MAAA;UACA+E,IAAA,CAAAxF,KAAA,IAAA+F,GAAA,CAAArB,OAAA;QACA,WAAApG,KAAA,CAAAmC,MAAA;UACA+E,IAAA,CAAAxF,KAAA,KAAA+F,GAAA,QAAArB,OAAA;QACA,WAAApG,KAAA,CAAAmC,MAAA;UACA+E,IAAA,CAAAxF,KAAA,UAAA+F,GAAA,CAAArB,OAAA;QACA,WAAApG,KAAA,CAAAmC,MAAA;UACA+E,IAAA,CAAAxF,KAAA,UAAA+F,GAAA,CAAArB,OAAA;QACA;UACAc,IAAA,CAAAxF,KAAA,IAAA+F,GAAA,CAAArB,OAAA;QACA;MACA;MAEA,OAAAc,IAAA;IACA;IAEA;AACA;AACA;IACAkB,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjF,kBAAA,CAAAtF,OAAA,oBAAAuF,oBAAA,CAAAvF,OAAA,IAAAwF,IAAA,UAAAgF,SAAA;QAAA,IAAA9E,eAAA,EAAA+E,YAAA;QAAA,WAAAlF,oBAAA,CAAAvF,OAAA,IAAA6F,IAAA,UAAA6E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3E,IAAA,GAAA2E,SAAA,CAAA1E,IAAA;YAAA;cAAA0E,SAAA,CAAA3E,IAAA;cAAA,IAGAuE,MAAA,CAAA9J,MAAA,CAAAf,IAAA;gBAAAiL,SAAA,CAAA1E,IAAA;gBAAA;cAAA;cACAsE,MAAA,CAAAjH,QAAA,CAAAC,OAAA;cAAA,OAAAoH,SAAA,CAAAzE,MAAA;YAAA;cAAA,MAKA,CAAAqE,MAAA,CAAA9J,MAAA,CAAAC,YAAA,KAAA6J,MAAA,CAAA9J,MAAA,CAAAK,SAAA;gBAAA6J,SAAA,CAAA1E,IAAA;gBAAA;cAAA;cACAsE,MAAA,CAAAjH,QAAA,CAAAC,OAAA;cAAA,OAAAoH,SAAA,CAAAzE,MAAA;YAAA;cAAA,IAIAqE,MAAA,CAAA9J,MAAA,CAAAU,MAAA,CAAA0D,MAAA;gBAAA8F,SAAA,CAAA1E,IAAA;gBAAA;cAAA;cACAsE,MAAA,CAAAjH,QAAA,CAAAC,OAAA;cAAA,OAAAoH,SAAA,CAAAzE,MAAA;YAAA;cAIA;cACAR,eAAA,GAAA6E,MAAA,CAAA9J,MAAA,CAAAU,MAAA,CAAA+B,IAAA,WAAAhB,KAAA;gBAAA,QAAAA,KAAA,CAAA6B,QAAA;cAAA;cAAA,KACA2B,eAAA;gBAAAiF,SAAA,CAAA1E,IAAA;gBAAA;cAAA;cACAsE,MAAA,CAAAjH,QAAA,CAAAC,OAAA;cAAA,OAAAoH,SAAA,CAAAzE,MAAA;YAAA;cAIA;cACAuE,YAAA;gBACA/K,IAAA,EAAA6K,MAAA,CAAA9J,MAAA,CAAAf,IAAA;gBACAG,IAAA,EAAA0K,MAAA,CAAAjK,UAAA;gBACAG,MAAA,EAAA8J,MAAA,CAAA9J;cACA,GAEA;cAAAkK,SAAA,CAAA1E,IAAA;cAAA,OACA,IAAA2E,gCAAA,EAAAH,YAAA;YAAA;cAEAF,MAAA,CAAAjH,QAAA,CAAAuH,OAAA;cAAAF,SAAA,CAAA1E,IAAA;cAAA;YAAA;cAAA0E,SAAA,CAAA3E,IAAA;cAAA2E,SAAA,CAAAnE,EAAA,GAAAmE,SAAA;cAEA,IAAAA,SAAA,CAAAnE,EAAA;gBACA+D,MAAA,CAAAjH,QAAA,CAAA6C,KAAA,cAAAwE,SAAA,CAAAnE,EAAA,CAAAE,OAAA;cACA;YAAA;YAAA;cAAA,OAAAiE,SAAA,CAAA/D,IAAA;UAAA;QAAA,GAAA4D,QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAM,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAzF,kBAAA,CAAAtF,OAAA,oBAAAuF,oBAAA,CAAAvF,OAAA,IAAAwF,IAAA,UAAAwF,SAAA;QAAA,IAAAC,MAAA,EAAAC,aAAA;QAAA,WAAA3F,oBAAA,CAAAvF,OAAA,IAAA6F,IAAA,UAAAsF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApF,IAAA,GAAAoF,SAAA,CAAAnF,IAAA;YAAA;cACA8E,MAAA,CAAAlJ,aAAA;cACAkJ,MAAA,CAAArJ,mBAAA;cAAA0J,SAAA,CAAApF,IAAA;cAAAoF,SAAA,CAAAnF,IAAA;cAAA,OAEA,IAAAoF,iCAAA;gBAAA/K,UAAA,EAAAyK,MAAA,CAAAzK;cAAA;YAAA;cAAA2K,MAAA,GAAAG,SAAA,CAAAhF,IAAA;cACA2E,MAAA,CAAAnJ,YAAA,GAAAqJ,MAAA,CAAAK,IAAA;cAAAF,SAAA,CAAAnF,IAAA;cAAA;YAAA;cAAAmF,SAAA,CAAApF,IAAA;cAAAoF,SAAA,CAAA5E,EAAA,GAAA4E,SAAA;cAEA3E,OAAA,CAAAN,KAAA,YAAAiF,SAAA,CAAA5E,EAAA;cACAuE,MAAA,CAAAzH,QAAA,CAAA6C,KAAA,CACA,EAAA+E,aAAA,GAAAE,SAAA,CAAA5E,EAAA,CAAAZ,QAAA,cAAAsF,aAAA,gBAAAA,aAAA,GAAAA,aAAA,CAAA3K,IAAA,cAAA2K,aAAA,uBAAAA,aAAA,CAAAxE,OAAA,KACA0E,SAAA,CAAA5E,EAAA,CAAAE,OAAA,IACA,gBACA;YAAA;cAAA0E,SAAA,CAAApF,IAAA;cAEA+E,MAAA,CAAAlJ,aAAA;cAAA,OAAAuJ,SAAA,CAAAzE,MAAA;YAAA;YAAA;cAAA,OAAAyE,SAAA,CAAAxE,IAAA;UAAA;QAAA,GAAAoE,QAAA;MAAA;IAEA;IAEA;AACA;AACA;AACA;IACAO,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MAAA,WAAAnG,kBAAA,CAAAtF,OAAA,oBAAAuF,oBAAA,CAAAvF,OAAA,IAAAwF,IAAA,UAAAkG,SAAA;QAAA,IAAAjL,MAAA;QAAA,WAAA8E,oBAAA,CAAAvF,OAAA,IAAA6F,IAAA,UAAA8F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5F,IAAA,GAAA4F,SAAA,CAAA3F,IAAA;YAAA;cACA;gBACA;gBACAxF,MAAA,GAAAoL,IAAA,CAAAC,KAAA,CAAAN,GAAA,CAAA/K,MAAA;gBACAA,MAAA,CAAAf,IAAA,GAAA8L,GAAA,CAAA9L,IAAA;gBACA+L,MAAA,CAAAhL,MAAA,GAAAA,MAAA;gBACA;;gBAEAgL,MAAA,CAAA/J,mBAAA;gBACA+J,MAAA,CAAAnI,QAAA,CAAAuH,OAAA;cACA,SAAAnD,GAAA;gBACAjB,OAAA,CAAAN,KAAA,YAAAuB,GAAA;gBACA+D,MAAA,CAAAnI,QAAA,CAAA6C,KAAA,aAAAuB,GAAA,CAAAhB,OAAA;cACA;YAAA;YAAA;cAAA,OAAAkF,SAAA,CAAAhF,IAAA;UAAA;QAAA,GAAA8E,QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAK,YAAA,WAAAA,aAAAP,GAAA;MAAA,IAAAQ,MAAA;MAAA,WAAA1G,kBAAA,CAAAtF,OAAA,oBAAAuF,oBAAA,CAAAvF,OAAA,IAAAwF,IAAA,UAAAyG,SAAA;QAAA,WAAA1G,oBAAA,CAAAvF,OAAA,IAAA6F,IAAA,UAAAqG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnG,IAAA,GAAAmG,SAAA,CAAAlG,IAAA;YAAA;cAAAkG,SAAA,CAAAnG,IAAA;cAAAmG,SAAA,CAAAlG,IAAA;cAAA,OAEA+F,MAAA,CAAAI,QAAA;gBACAvM,IAAA;cACA;YAAA;cAAAsM,SAAA,CAAAlG,IAAA;cAAA,OAEA,IAAAoG,kCAAA,EAAAb,GAAA,CAAAc,EAAA;YAAA;cACAN,MAAA,CAAApK,YAAA,GAAAoK,MAAA,CAAApK,YAAA,CAAAa,MAAA,WAAAhC,MAAA;gBAAA,OAAAA,MAAA,CAAA6L,EAAA,KAAAd,GAAA,CAAAc,EAAA;cAAA;cACAN,MAAA,CAAA1I,QAAA,CAAAuH,OAAA;cAAAsB,SAAA,CAAAlG,IAAA;cAAA;YAAA;cAAAkG,SAAA,CAAAnG,IAAA;cAAAmG,SAAA,CAAA3F,EAAA,GAAA2F,SAAA;cAEA,IAAAA,SAAA,CAAA3F,EAAA;gBACAwF,MAAA,CAAA1I,QAAA,CAAA6C,KAAA,aAAAgG,SAAA,CAAA3F,EAAA,CAAAE,OAAA;cACA;YAAA;YAAA;cAAA,OAAAyF,SAAA,CAAAvF,IAAA;UAAA;QAAA,GAAAqF,QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAM,UAAA,WAAAA,WAAA;MACA;MACA,IAAAC,WAAA,GAAAC,MAAA,CAAAC,IAAA;MACA,IAAAC,KAAA,QAAAC,KAAA,CAAAC,WAAA,CAAAC,GAAA,CAAAC,SAAA;MACA,IAAAC,KAAA;MACA,IAAAnE,IAAA,OAAAoE,IAAA,GAAAC,kBAAA;;MAEA;MACA,IAAAC,cAAA,unBAcA;MACAX,WAAA,CAAAY,QAAA,CAAAC,KAAA,qEAAAzK,MAAA,CAGAoK,KAAA,kaAAApK,MAAA,CAYA,KAAAd,WAAA,g8JAAAc,MAAA,CAoKAuK,cAAA,mVAAAvK,MAAA,CASA+J,KAAA,CAAAW,SAAA,CAAAC,OAAA,+LAOA;MAEAf,WAAA,CAAAY,QAAA,CAAAI,KAAA;MAEAC,UAAA;QACA;UACAjB,WAAA,CAAAkB,KAAA;UACAlB,WAAA,CAAAmB,KAAA;QACA,SAAAhG,CAAA;UACAlB,OAAA,CAAAN,KAAA,eAAAwB,CAAA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAiG,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAvI,kBAAA,CAAAtF,OAAA,oBAAAuF,oBAAA,CAAAvF,OAAA,IAAAwF,IAAA,UAAAsI,SAAA;QAAA,IAAAC,OAAA,EAAA9F,GAAA;QAAA,WAAA1C,oBAAA,CAAAvF,OAAA,IAAA6F,IAAA,UAAAmI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjI,IAAA,GAAAiI,SAAA,CAAAhI,IAAA;YAAA;cAAAgI,SAAA,CAAAjI,IAAA;cAEA6H,MAAA,CAAApM,OAAA;cACAsM,OAAA,GAAAF,MAAA,CAAAjB,KAAA,CAAAC,WAAA,CAAAC,GAAA;cACA7E,GAAA;gBACAiG,MAAA;gBACAC,QAAA;gBACAC,KAAA;kBAAAvO,IAAA;kBAAAwO,OAAA;gBAAA;gBACAC,WAAA;kBAAAC,KAAA;gBAAA;gBACAC,KAAA;kBACAC,IAAA;kBACApK,MAAA;kBACAqK,WAAA,EAAAb,MAAA,CAAA/L,WAAA;gBACA;gBACA6M,SAAA;kBAAAC,IAAA;gBAAA;gBACAC,MAAA,GACA;kBAAAC,IAAA;kBAAAC,KAAA;gBAAA,GACA;kBAAAD,IAAA,MAAA7B,IAAA,GAAAC,kBAAA;kBAAA6B,KAAA;kBAAAC,SAAA;gBAAA,EACA;gBACAC,MAAA;kBACAC,MAAA;kBACAC,QAAA;oBACAnP,OAAA;kBACA;gBACA;cACA;cAAAiO,SAAA,CAAAhI,IAAA;cAAA,OAEA,IAAAmJ,iBAAA,IAAAC,GAAA,CAAApH,GAAA,EAAAqH,IAAA,CAAAvB,OAAA,EAAAwB,IAAA;YAAA;cACA1B,MAAA,CAAAvK,QAAA,CAAAuH,OAAA;cAAAoD,SAAA,CAAAhI,IAAA;cAAA;YAAA;cAAAgI,SAAA,CAAAjI,IAAA;cAAAiI,SAAA,CAAAzH,EAAA,GAAAyH,SAAA;cAEAJ,MAAA,CAAAvK,QAAA,CAAA6C,KAAA,cAAA8H,SAAA,CAAAzH,EAAA,CAAAE,OAAA;YAAA;cAAAuH,SAAA,CAAAjI,IAAA;cAEA6H,MAAA,CAAApM,OAAA;cAAA,OAAAwM,SAAA,CAAAtH,MAAA;YAAA;YAAA;cAAA,OAAAsH,SAAA,CAAArH,IAAA;UAAA;QAAA,GAAAkH,QAAA;MAAA;IAEA;IAEA;AACA;AACA;AACA;AACA;IACA0B,OAAA,WAAAA,QAAAlD,EAAA;MACA,IAAAA,EAAA;QACA,IAAAmD,KAAA,QAAAC,MAAA,CAAAC,KAAA,CAAApP,IAAA,CAAAqP,cAAA,CAAAnN,MAAA,WAAAoN,OAAA;UAAA,OAAAA,OAAA,CAAAC,OAAA,IAAAxD,EAAA;QAAA;QACA,IAAAmD,KAAA,IAAAA,KAAA,KAAA7G,SAAA;UACA,OAAA6G,KAAA,CAAAM,cAAA,GAAAN,KAAA,CAAAO,iBAAA;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACA3F,UAAA,WAAAA,WAAA/I,KAAA;MACA,IAAAA,KAAA,YAAAA,KAAA;QACA;MACA;;MAEA;MACA,WAAAA,KAAA,iBAAAA,KAAA,CAAA2O,QAAA;QACA,OAAA3O,KAAA;MACA;;MAEA;MACA,IAAA6G,QAAA,GAAAC,MAAA,CAAA9G,KAAA;MACA,IAAA+G,KAAA,CAAAF,QAAA;QACA;MACA;;MAEA;MACA;MACA,IAAA+H,SAAA,GAAA/H,QAAA,QAAAA,QAAA;MACA,IAAAgI,YAAA,GAAAD,SAAA,GAAA/H,QAAA,SAAAA,QAAA;;MAEA;MACA,OAAAgI,YAAA,CAAA7H,OAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACA8H,cAAA,WAAAA,eAAArM,QAAA;MACA,IAAAC,WAAA,QAAApE,aAAA,CAAAmE,QAAA;MACA,OAAAC,WAAA,IAAAA,WAAA,CAAAqM,KAAA,GAAArM,WAAA,CAAAqM,KAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC,cAAA,WAAAA,eAAAvM,QAAA;MACA,IAAAC,WAAA,QAAApE,aAAA,CAAAmE,QAAA;MACA,OAAAC,WAAA,IAAAA,WAAA,CAAAuM,KAAA,GAAAvM,WAAA,CAAAuM,KAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAxQ,OAAA,GAAAyQ,SAAA"}]}