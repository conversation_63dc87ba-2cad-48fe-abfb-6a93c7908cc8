package com.rich.system.service.processor;

import com.rich.common.core.domain.entity.RsRct;
import com.rich.common.core.domain.entity.RsCharge;
import com.rich.common.core.domain.entity.RsOpLog;
import com.rich.common.core.domain.entity.RsServiceInstances;
import com.rich.system.service.context.ServiceProcessingContext;
import com.rich.system.service.enums.ServiceTypeEnum;
import com.rich.system.mapper.OptimizedRsChargeMapper;
import com.rich.system.mapper.RsServiceInstancesMapper;
import com.rich.system.mapper.RsOpLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;

import java.util.List;
import java.util.Date;

/**
 * 抽象服务处理器基类
 * 提供通用的服务处理逻辑
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractServiceProcessor implements ServiceProcessor {
    
    @Autowired
    protected OptimizedRsChargeMapper optimizedRsChargeMapper;
    
    @Autowired
    protected RsServiceInstancesMapper rsServiceInstancesMapper;
    
    @Autowired
    protected RsOpLogMapper rsOpLogMapper;
    
    /**
     * 模板方法：处理服务的标准流程
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public final void process(RsRct rsRct, ServiceProcessingContext context) throws Exception {
        ServiceTypeEnum serviceType = getSupportedServiceType();
        String serviceName = serviceType.getDisplayName();
        
        try {
            log.debug("开始处理服务: {}", serviceName);
            context.setServiceStatus(serviceName, ServiceProcessingContext.ServiceProcessingStatus.PROCESSING);
            
            // 预处理验证
            preProcess(rsRct, context);
            
            // 执行具体的服务处理逻辑
            doProcess(rsRct, context);
            
            // 后处理
            postProcess(rsRct, context, true);
            
            context.setServiceStatus(serviceName, ServiceProcessingContext.ServiceProcessingStatus.COMPLETED);
            log.debug("完成处理服务: {}", serviceName);
            
        } catch (Exception e) {
            log.error("处理服务失败: {}, 错误: {}", serviceName, e.getMessage(), e);
            context.recordError(serviceName, e);
            
            // 后处理（失败情况）
            postProcess(rsRct, context, false);
            
            throw e;
        }
    }
    
    /**
     * 具体的服务处理逻辑，由子类实现
     * 
     * @param rsRct 操作单数据
     * @param context 处理上下文
     * @throws Exception 处理异常
     */
    protected abstract void doProcess(RsRct rsRct, ServiceProcessingContext context) throws Exception;
    
    /**
     * 处理服务实例
     * 
     * @param serviceInstance 服务实例
     * @param rsRct 操作单数据
     * @param context 处理上下文
     * @return 处理后的服务实例
     */
    protected RsServiceInstances processServiceInstance(RsServiceInstances serviceInstance, 
                                                       RsRct rsRct, 
                                                       ServiceProcessingContext context) {
        ServiceTypeEnum serviceType = getSupportedServiceType();
        
        // 设置基本信息
        serviceInstance.setServiceBelongTo(serviceType.getServiceBelongTo());
        serviceInstance.setServiceTypeId(serviceType.getServiceTypeId());
        serviceInstance.setRctId(rsRct.getRctId());
        serviceInstance.setRctNo(rsRct.getRctNo());
        
        // 检查是否已存在
        RsServiceInstances existing = context.getExistingService(serviceType.getServiceBelongTo());
        if (existing != null) {
            serviceInstance.setServiceId(existing.getServiceId());
            rsServiceInstancesMapper.updateRsServiceInstances(serviceInstance);
        } else {
            rsServiceInstancesMapper.insertRsServiceInstances(serviceInstance);
        }
        
        return serviceInstance;
    }
    
    /**
     * 批量处理费用记录
     * 
     * @param charges 费用记录列表
     * @param serviceInstance 服务实例
     * @param rsRct 操作单数据
     */
    protected void processCharges(List<RsCharge> charges, 
                                 RsServiceInstances serviceInstance, 
                                 RsRct rsRct) {
        if (charges == null || charges.isEmpty()) {
            return;
        }
        
        ServiceTypeEnum serviceType = getSupportedServiceType();
        
        // 设置费用记录的基本信息
        for (RsCharge charge : charges) {
            charge.setServiceId(serviceInstance.getServiceId());
            charge.setSqdRctId(rsRct.getRctId());
            charge.setSqdServiceTypeId(serviceType.getServiceTypeId());
            charge.setPaymentTitleCode(serviceInstance.getPaymentTitleCode());
            charge.setIsRecievingOrPaying(1L);
            charge.setSqdRctNo(rsRct.getRctNo());
            
            if (charge.getIsAccountConfirmed() == null) {
                charge.setIsAccountConfirmed("0");
            }
            
            charge.setCurrencyRateCalculateDate(rsRct.getRctCreateTime());
            
            if (charge.getChargeId() == null || "0".equals(charge.getIsAccountConfirmed())) {
                charge.setSqdDnCurrencyBalance(charge.getSubtotal());
                charge.setDnCurrencyBalance(charge.getSubtotal());
            }
        }
        
        // 批量处理费用记录
        optimizedRsChargeMapper.batchUpsertCharges(charges);
    }
    
    /**
     * 批量处理操作日志
     * 
     * @param opLogs 操作日志列表
     * @param serviceInstance 服务实例
     * @param rsRct 操作单数据
     */
    protected void processOpLogs(List<RsOpLog> opLogs, 
                                RsServiceInstances serviceInstance, 
                                RsRct rsRct) {
        if (opLogs == null || opLogs.isEmpty()) {
            return;
        }
        
        ServiceTypeEnum serviceType = getSupportedServiceType();
        Long serviceId = serviceInstance.getServiceId();
        
        // 先删除现有的操作日志
        rsOpLogMapper.deleteRsOpLogByServiceId(serviceId);
        
        // 设置操作日志的基本信息
        for (RsOpLog opLog : opLogs) {
            opLog.setServiceId(serviceId);
            opLog.setSqdRctId(rsRct.getRctId());
            opLog.setSqdServiceTypeId(serviceType.getServiceTypeId());
            opLog.setServerSystemTime(new Date());
        }
        
        // 批量插入操作日志
        for (RsOpLog opLog : opLogs) {
            rsOpLogMapper.insertRsOpLog(opLog);
        }
    }
    
    /**
     * 获取或创建服务实例
     * 
     * @param rsRct 操作单数据
     * @param context 处理上下文
     * @return 服务实例
     */
    protected RsServiceInstances getOrCreateServiceInstance(RsRct rsRct, ServiceProcessingContext context) {
        ServiceTypeEnum serviceType = getSupportedServiceType();
        String serviceBelongTo = serviceType.getServiceBelongTo();
        
        RsServiceInstances existing = context.getExistingService(serviceBelongTo);
        if (existing != null) {
            return existing;
        }
        
        // 创建新的服务实例
        RsServiceInstances newInstance = new RsServiceInstances();
        newInstance.setServiceBelongTo(serviceBelongTo);
        newInstance.setServiceTypeId(serviceType.getServiceTypeId());
        newInstance.setRctId(rsRct.getRctId());
        newInstance.setRctNo(rsRct.getRctNo());
        
        return newInstance;
    }
    
    /**
     * 验证必要的数据
     * 
     * @param rsRct 操作单数据
     * @param context 处理上下文
     * @throws Exception 验证失败异常
     */
    @Override
    public void preProcess(RsRct rsRct, ServiceProcessingContext context) throws Exception {
        if (rsRct == null) {
            throw new IllegalArgumentException("操作单数据不能为空");
        }
        
        if (rsRct.getRctId() == null) {
            throw new IllegalArgumentException("操作单ID不能为空");
        }
        
        if (context == null) {
            throw new IllegalArgumentException("处理上下文不能为空");
        }
    }
    
    /**
     * 记录处理结果
     * 
     * @param rsRct 操作单数据
     * @param context 处理上下文
     * @param success 是否成功
     */
    @Override
    public void postProcess(RsRct rsRct, ServiceProcessingContext context, boolean success) {
        ServiceTypeEnum serviceType = getSupportedServiceType();
        String serviceName = serviceType.getDisplayName();
        
        if (success) {
            log.info("服务处理成功: {} (操作单: {})", serviceName, rsRct.getRctNo());
        } else {
            log.warn("服务处理失败: {} (操作单: {})", serviceName, rsRct.getRctNo());
        }
    }
}
