{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\RailwayComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\RailwayComponent.vue", "mtime": 1754646305904}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_audit", "_interopRequireDefault", "require", "_logisticsProgress", "_chargeList", "name", "components", "Audit", "LogisticsProgress", "ChargeList", "props", "serviceItem", "type", "Object", "default", "_default", "railwayServices", "Array", "Set", "form", "branchInfo", "Boolean", "logisticsInfo", "chargeInfo", "auditInfo", "disabled", "booking", "psaVerify", "supplierList", "companyList", "foldState", "serviceInstance", "serviceObject", "formDisable", "payable", "computed", "isDisabled", "methods", "getSupplierEmail", "serviceTypeId", "getServiceInstance", "supplierId", "supplier", "find", "v", "companyId", "staffEmail", "getAgreementDisplay", "agreementTypeCode", "agreementNo", "changeFold", "$emit", "getFold", "getServiceObject", "getPayable", "getFormDisable", "changeServiceObject", "auditCharge", "event", "generateFreight", "type1", "type2", "item", "psaBookingCancel", "copyFreight", "calculateCharge", "deleteLogItem", "rsOpLogList", "filter", "updateLogList", "deleteAllCharge", "rsChargeList", "deleteChargeItem", "exports", "_default2"], "sources": ["src/views/system/document/serviceComponents/RailwayComponent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"railway-component\">\r\n    <!--铁路-->\r\n    <div class=\"railway-item\">\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <!--标题栏-->\r\n          <div class=\"service-bar\">\r\n            <a\r\n              :class=\"[\r\n                'service-toggle-icon',\r\n                getFold(serviceItem.serviceTypeId) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <h3 class=\"service-title\" @click=\"changeFold(serviceItem.serviceTypeId)\">\r\n              铁路-{{ serviceItem.serviceShortName }}\r\n            </h3>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"getServiceInstance(serviceItem.serviceTypeId)\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(serviceItem.serviceTypeId)\"\r\n              :rs-charge-list=\"serviceItem.rsChargeList\"\r\n              @auditFee=\"auditCharge(serviceItem.serviceTypeId, $event)\"\r\n              @return=\"changeServiceObject(serviceItem.serviceTypeId, $event)\"\r\n            />\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"getFold(serviceItem.serviceTypeId)\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(3, serviceItem.serviceTypeId, getServiceObject(serviceItem.serviceTypeId))\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(serviceItem.serviceTypeId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <template #reference>\r\n                    <el-input\r\n                      :value=\"getServiceInstance(serviceItem.serviceTypeId).supplierName\"\r\n                      class=\"disable-form\"\r\n                      disabled\r\n                    />\r\n                  </template>\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(serviceItem.serviceTypeId)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--主表信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"商务单号\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input :value=\"form.sqdPsaNo\" class=\"disable-form\" disabled/>\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button\r\n                          size=\"mini\"\r\n                          style=\"color: red\"\r\n                          type=\"text\"\r\n                          @click=\"psaBookingCancel\"\r\n                        >\r\n                          取消\r\n                        </el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getFormDisable(serviceItem.serviceTypeId) || disabled || psaVerify\"\r\n                :logistics-progress-data=\"getServiceObject(serviceItem.serviceTypeId).rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"20\"\r\n                @deleteItem=\"deleteLogItem(serviceItem.serviceTypeId, $event)\"\r\n                @return=\"updateLogList(serviceItem.serviceTypeId, $event)\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"getServiceObject(serviceItem.serviceTypeId).rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"getFormDisable(serviceItem.serviceTypeId) || disabled\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"getServiceObject(serviceItem.serviceTypeId).payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"getServiceObject(serviceItem.serviceTypeId).payableRMBTax\"\r\n              :pay-detail-u-s-d=\"getServiceObject(serviceItem.serviceTypeId).payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"getServiceObject(serviceItem.serviceTypeId).payableUSDTax\"\r\n              :service-type-id=\"20\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"deleteAllCharge(serviceItem.serviceTypeId)\"\r\n              @deleteItem=\"deleteChargeItem(serviceItem.serviceTypeId, $event)\"\r\n              @return=\"calculateCharge(serviceItem.serviceTypeId, $event, getServiceObject(serviceItem.serviceTypeId))\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\n\r\nexport default {\r\n  name: \"RailwayComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    serviceItem: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 铁路服务数据集合\r\n    railwayServices: {\r\n      type: [Array, Set],\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 新增属性，不再依赖$parent\r\n    foldState: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    serviceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    serviceObject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    formDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    payable: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(serviceTypeId) {\r\n      const serviceInstance = this.getServiceInstance(serviceTypeId)\r\n      if (!serviceInstance || !serviceInstance.supplierId) return ''\r\n\r\n      const supplier = this.supplierList.find(v => v.companyId === serviceInstance.supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceTypeId) {\r\n      const serviceInstance = this.getServiceInstance(serviceTypeId)\r\n      if (!serviceInstance) return ''\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 事件转发给父组件\r\n    changeFold(serviceTypeId) {\r\n      this.$emit(\"changeFold\", serviceTypeId)\r\n    },\r\n    getFold(serviceTypeId) {\r\n      return this.foldState\r\n    },\r\n    getServiceInstance(serviceTypeId) {\r\n      return this.serviceInstance\r\n    },\r\n    getServiceObject(serviceTypeId) {\r\n      return this.serviceObject\r\n    },\r\n    getPayable(serviceTypeId) {\r\n      return this.payable\r\n    },\r\n    getFormDisable(serviceTypeId) {\r\n      return this.formDisable\r\n    },\r\n    changeServiceObject(serviceTypeId, serviceObject) {\r\n      this.$emit(\"changeServiceObject\", serviceTypeId, serviceObject)\r\n    },\r\n    auditCharge(serviceTypeId, event) {\r\n      this.$emit(\"auditCharge\", serviceTypeId, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    psaBookingCancel() {\r\n      this.$emit(\"psaBookingCancel\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceTypeId, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceTypeId, event, item)\r\n    },\r\n    // 物流进度相关方法\r\n    deleteLogItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsOpLogList) {\r\n        serviceObject.rsOpLogList = serviceObject.rsOpLogList.filter(item => item !== event)\r\n      }\r\n    },\r\n    updateLogList(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsOpLogList = event\r\n      }\r\n    },\r\n    // 费用列表相关方法\r\n    deleteAllCharge(serviceTypeId) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsChargeList = []\r\n      }\r\n    },\r\n    deleteChargeItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsChargeList) {\r\n        serviceObject.rsChargeList = serviceObject.rsChargeList.filter(item => item !== event)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// Railway组件特定样式\r\n.railway-component {\r\n  width: 100%;\r\n\r\n  .railway-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title {\r\n      margin: 0;\r\n      width: 250px;\r\n      text-align: left;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;AA4JA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAG,IAAA;EACAC,UAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,UAAA,EAAAA;EACA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAC,eAAA;MACAJ,IAAA,GAAAK,KAAA,EAAAC,GAAA;MACAJ,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAI,IAAA;MACAP,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAK,UAAA;MACAR,IAAA,EAAAS,OAAA;MACAP,OAAA;IACA;IACAQ,aAAA;MACAV,IAAA,EAAAS,OAAA;MACAP,OAAA;IACA;IACAS,UAAA;MACAX,IAAA,EAAAS,OAAA;MACAP,OAAA;IACA;IACAU,SAAA;MACAZ,IAAA,EAAAS,OAAA;MACAP,OAAA;IACA;IACA;IACAW,QAAA;MACAb,IAAA,EAAAS,OAAA;MACAP,OAAA;IACA;IACAY,OAAA;MACAd,IAAA,EAAAS,OAAA;MACAP,OAAA;IACA;IACAa,SAAA;MACAf,IAAA,EAAAS,OAAA;MACAP,OAAA;IACA;IACA;IACAc,YAAA;MACAhB,IAAA,EAAAK,KAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAc,WAAA;MACAjB,IAAA,EAAAK,KAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAe,SAAA;MACAlB,IAAA,EAAAS,OAAA;MACAP,OAAA;IACA;IACAiB,eAAA;MACAnB,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAiB,aAAA;MACApB,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAkB,WAAA;MACArB,IAAA,EAAAS,OAAA;MACAP,OAAA;IACA;IACAoB,OAAA;MACAtB,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAqB,QAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAX,QAAA,SAAAE,SAAA;IACA;EACA;EACAU,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,aAAA;MACA,IAAAR,eAAA,QAAAS,kBAAA,CAAAD,aAAA;MACA,KAAAR,eAAA,KAAAA,eAAA,CAAAU,UAAA;MAEA,IAAAC,QAAA,QAAAd,YAAA,CAAAe,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,SAAA,KAAAd,eAAA,CAAAU,UAAA;MAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAI,UAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAR,aAAA;MACA,IAAAR,eAAA,QAAAS,kBAAA,CAAAD,aAAA;MACA,KAAAR,eAAA;MACA,OAAAA,eAAA,CAAAiB,iBAAA,GAAAjB,eAAA,CAAAkB,WAAA;IACA;IACA;IACAC,UAAA,WAAAA,WAAAX,aAAA;MACA,KAAAY,KAAA,eAAAZ,aAAA;IACA;IACAa,OAAA,WAAAA,QAAAb,aAAA;MACA,YAAAT,SAAA;IACA;IACAU,kBAAA,WAAAA,mBAAAD,aAAA;MACA,YAAAR,eAAA;IACA;IACAsB,gBAAA,WAAAA,iBAAAd,aAAA;MACA,YAAAP,aAAA;IACA;IACAsB,UAAA,WAAAA,WAAAf,aAAA;MACA,YAAAL,OAAA;IACA;IACAqB,cAAA,WAAAA,eAAAhB,aAAA;MACA,YAAAN,WAAA;IACA;IACAuB,mBAAA,WAAAA,oBAAAjB,aAAA,EAAAP,aAAA;MACA,KAAAmB,KAAA,wBAAAZ,aAAA,EAAAP,aAAA;IACA;IACAyB,WAAA,WAAAA,YAAAlB,aAAA,EAAAmB,KAAA;MACA,KAAAP,KAAA,gBAAAZ,aAAA,EAAAmB,KAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA,EAAAC,KAAA,EAAAC,IAAA;MACA,KAAAX,KAAA,oBAAAS,KAAA,EAAAC,KAAA,EAAAC,IAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAZ,KAAA;IACA;IACAa,WAAA,WAAAA,YAAAN,KAAA;MACA,KAAAP,KAAA,gBAAAO,KAAA;IACA;IACAO,eAAA,WAAAA,gBAAA1B,aAAA,EAAAmB,KAAA,EAAAI,IAAA;MACA,KAAAX,KAAA,oBAAAZ,aAAA,EAAAmB,KAAA,EAAAI,IAAA;IACA;IACA;IACAI,aAAA,WAAAA,cAAA3B,aAAA,EAAAmB,KAAA;MACA,IAAA1B,aAAA,QAAAqB,gBAAA,CAAAd,aAAA;MACA,IAAAP,aAAA,IAAAA,aAAA,CAAAmC,WAAA;QACAnC,aAAA,CAAAmC,WAAA,GAAAnC,aAAA,CAAAmC,WAAA,CAAAC,MAAA,WAAAN,IAAA;UAAA,OAAAA,IAAA,KAAAJ,KAAA;QAAA;MACA;IACA;IACAW,aAAA,WAAAA,cAAA9B,aAAA,EAAAmB,KAAA;MACA,IAAA1B,aAAA,QAAAqB,gBAAA,CAAAd,aAAA;MACA,IAAAP,aAAA;QACAA,aAAA,CAAAmC,WAAA,GAAAT,KAAA;MACA;IACA;IACA;IACAY,eAAA,WAAAA,gBAAA/B,aAAA;MACA,IAAAP,aAAA,QAAAqB,gBAAA,CAAAd,aAAA;MACA,IAAAP,aAAA;QACAA,aAAA,CAAAuC,YAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAAjC,aAAA,EAAAmB,KAAA;MACA,IAAA1B,aAAA,QAAAqB,gBAAA,CAAAd,aAAA;MACA,IAAAP,aAAA,IAAAA,aAAA,CAAAuC,YAAA;QACAvC,aAAA,CAAAuC,YAAA,GAAAvC,aAAA,CAAAuC,YAAA,CAAAH,MAAA,WAAAN,IAAA;UAAA,OAAAA,IAAA,KAAAJ,KAAA;QAAA;MACA;IACA;EACA;AACA;AAAAe,OAAA,CAAA3D,OAAA,GAAA4D,SAAA"}]}