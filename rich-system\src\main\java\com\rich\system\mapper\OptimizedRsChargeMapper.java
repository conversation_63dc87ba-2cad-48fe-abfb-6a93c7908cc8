package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsCharge;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 优化的费用明细Mapper接口
 * 提供批量操作和优化查询功能
 * 
 * <AUTHOR>
 */
@Mapper
public interface OptimizedRsChargeMapper {
    
    /**
     * 批量插入费用记录
     * 
     * @param charges 费用记录列表
     * @return 影响行数
     */
    int batchInsertCharges(@Param("charges") List<RsCharge> charges);
    
    /**
     * 批量更新费用记录
     * 
     * @param charges 费用记录列表
     * @return 影响行数
     */
    int batchUpdateCharges(@Param("charges") List<RsCharge> charges);
    
    /**
     * 批量UPSERT费用记录
     * 使用MySQL的INSERT ... ON DUPLICATE KEY UPDATE语法
     * 
     * @param charges 费用记录列表
     * @return 影响行数
     */
    int batchUpsertCharges(@Param("charges") List<RsCharge> charges);
    
    /**
     * 删除孤立的费用记录
     * 删除指定操作单下不在活跃服务ID列表中的费用记录
     * 
     * @param rctId 操作单ID
     * @param activeServiceIds 活跃的服务ID集合
     * @return 删除的记录数
     */
    int deleteOrphanedCharges(@Param("rctId") Long rctId, 
                             @Param("activeServiceIds") Set<Long> activeServiceIds);
    
    /**
     * 按服务ID批量删除费用记录
     * 
     * @param serviceIds 服务ID列表
     * @return 删除的记录数
     */
    int batchDeleteChargesByServiceIds(@Param("serviceIds") List<Long> serviceIds);
    
    /**
     * 查询指定操作单的所有费用记录，按服务ID分组
     * 
     * @param rctId 操作单ID
     * @return 费用记录列表
     */
    List<RsCharge> selectChargesByRctIdGroupByService(@Param("rctId") Long rctId);
    
    /**
     * 查询指定服务ID列表的费用记录
     * 
     * @param serviceIds 服务ID列表
     * @return 费用记录列表
     */
    List<RsCharge> selectChargesByServiceIds(@Param("serviceIds") List<Long> serviceIds);
    
    /**
     * 统计指定操作单的费用记录数量
     * 
     * @param rctId 操作单ID
     * @return 费用记录数量
     */
    int countChargesByRctId(@Param("rctId") Long rctId);
    
    /**
     * 检查费用记录是否存在
     * 
     * @param chargeId 费用ID
     * @return 是否存在
     */
    boolean existsChargeById(@Param("chargeId") Long chargeId);
    
    /**
     * 批量检查费用记录是否存在
     * 
     * @param chargeIds 费用ID列表
     * @return 存在的费用ID列表
     */
    List<Long> batchCheckChargesExist(@Param("chargeIds") List<Long> chargeIds);
    
    /**
     * 软删除费用记录（标记为删除状态）
     * 
     * @param rctId 操作单ID
     * @param serviceIds 服务ID列表
     * @return 影响行数
     */
    int softDeleteCharges(@Param("rctId") Long rctId, 
                         @Param("serviceIds") List<Long> serviceIds);
    
    /**
     * 恢复软删除的费用记录
     * 
     * @param rctId 操作单ID
     * @param serviceIds 服务ID列表
     * @return 影响行数
     */
    int restoreSoftDeletedCharges(@Param("rctId") Long rctId, 
                                 @Param("serviceIds") List<Long> serviceIds);
    
    /**
     * 获取费用记录的汇总信息
     * 
     * @param rctId 操作单ID
     * @return 汇总信息Map，包含总金额、记录数等
     */
    java.util.Map<String, Object> getChargeSummary(@Param("rctId") Long rctId);
    
    /**
     * 按服务类型统计费用
     * 
     * @param rctId 操作单ID
     * @return 按服务类型分组的费用统计
     */
    List<java.util.Map<String, Object>> getChargeStatsByServiceType(@Param("rctId") Long rctId);
}
