package com.rich.system.service.cache;

import com.rich.common.core.domain.entity.RsServiceInstances;
import com.rich.system.mapper.RsServiceInstancesMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务实例缓存管理器
 * 提供服务实例的缓存功能，减少数据库查询
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ServiceInstanceCacheManager {
    
    @Autowired
    private RsServiceInstancesMapper rsServiceInstancesMapper;
    
    /**
     * 本地缓存，用于快速访问
     */
    private final Map<String, Map<String, RsServiceInstances>> localCache = new ConcurrentHashMap<>();
    
    /**
     * 获取操作单的服务实例映射（带缓存）
     * 
     * @param rctId 操作单ID
     * @return 服务实例映射 (serviceBelongTo -> RsServiceInstances)
     */
    @Cacheable(value = "serviceInstances", key = "#rctId", unless = "#result == null or #result.isEmpty()")
    public Map<String, RsServiceInstances> getServiceInstancesMap(Long rctId) {
        log.debug("从数据库查询操作单服务实例: {}", rctId);
        
        List<RsServiceInstances> instances = rsServiceInstancesMapper.selectRsServiceInstancesByRctId(rctId);
        
        if (instances == null || instances.isEmpty()) {
            log.debug("操作单 {} 没有找到服务实例", rctId);
            return new ConcurrentHashMap<>();
        }
        
        Map<String, RsServiceInstances> instanceMap = instances.stream()
                .collect(Collectors.toConcurrentMap(
                        RsServiceInstances::getServiceBelongTo,
                        Function.identity(),
                        (existing, replacement) -> {
                            log.warn("发现重复的服务归属标识: {}, 使用最新的实例", 
                                    existing.getServiceBelongTo());
                            return replacement;
                        }
                ));
        
        // 同时更新本地缓存
        localCache.put(rctId.toString(), instanceMap);
        
        log.debug("缓存操作单 {} 的 {} 个服务实例", rctId, instanceMap.size());
        
        return instanceMap;
    }
    
    /**
     * 获取单个服务实例（带缓存）
     * 
     * @param rctId 操作单ID
     * @param serviceBelongTo 服务归属标识
     * @return 服务实例，如果不存在则返回null
     */
    public RsServiceInstances getServiceInstance(Long rctId, String serviceBelongTo) {
        Map<String, RsServiceInstances> instanceMap = getServiceInstancesMap(rctId);
        return instanceMap.get(serviceBelongTo);
    }
    
    /**
     * 更新服务实例缓存
     * 
     * @param rctId 操作单ID
     * @param serviceInstance 服务实例
     */
    @CachePut(value = "serviceInstances", key = "#rctId")
    public Map<String, RsServiceInstances> updateServiceInstanceCache(Long rctId, 
                                                                     RsServiceInstances serviceInstance) {
        Map<String, RsServiceInstances> instanceMap = getServiceInstancesMap(rctId);
        instanceMap.put(serviceInstance.getServiceBelongTo(), serviceInstance);
        
        // 同时更新本地缓存
        localCache.put(rctId.toString(), instanceMap);
        
        log.debug("更新服务实例缓存: {} -> {}", rctId, serviceInstance.getServiceBelongTo());
        
        return instanceMap;
    }
    
    /**
     * 批量更新服务实例缓存
     * 
     * @param rctId 操作单ID
     * @param serviceInstances 服务实例列表
     */
    @CachePut(value = "serviceInstances", key = "#rctId")
    public Map<String, RsServiceInstances> batchUpdateServiceInstanceCache(Long rctId, 
                                                                           List<RsServiceInstances> serviceInstances) {
        Map<String, RsServiceInstances> instanceMap = new ConcurrentHashMap<>();
        
        for (RsServiceInstances instance : serviceInstances) {
            instanceMap.put(instance.getServiceBelongTo(), instance);
        }
        
        // 同时更新本地缓存
        localCache.put(rctId.toString(), instanceMap);
        
        log.debug("批量更新服务实例缓存: {} -> {} 个实例", rctId, instanceMap.size());
        
        return instanceMap;
    }
    
    /**
     * 清除操作单的服务实例缓存
     * 
     * @param rctId 操作单ID
     */
    @CacheEvict(value = "serviceInstances", key = "#rctId")
    public void evictServiceInstancesCache(Long rctId) {
        localCache.remove(rctId.toString());
        log.debug("清除操作单服务实例缓存: {}", rctId);
    }
    
    /**
     * 清除所有服务实例缓存
     */
    @CacheEvict(value = "serviceInstances", allEntries = true)
    public void evictAllServiceInstancesCache() {
        localCache.clear();
        log.info("清除所有服务实例缓存");
    }
    
    /**
     * 预热缓存
     * 
     * @param rctIds 操作单ID列表
     */
    public void warmUpCache(List<Long> rctIds) {
        log.info("开始预热服务实例缓存，操作单数量: {}", rctIds.size());
        
        for (Long rctId : rctIds) {
            try {
                getServiceInstancesMap(rctId);
            } catch (Exception e) {
                log.warn("预热操作单 {} 的缓存失败", rctId, e);
            }
        }
        
        log.info("服务实例缓存预热完成");
    }
    
    /**
     * 从本地缓存快速获取（不触发数据库查询）
     * 
     * @param rctId 操作单ID
     * @return 服务实例映射，如果本地缓存中不存在则返回null
     */
    public Map<String, RsServiceInstances> getFromLocalCache(Long rctId) {
        return localCache.get(rctId.toString());
    }
    
    /**
     * 检查本地缓存是否存在
     * 
     * @param rctId 操作单ID
     * @return 是否存在于本地缓存
     */
    public boolean existsInLocalCache(Long rctId) {
        return localCache.containsKey(rctId.toString());
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public CacheStatistics getCacheStatistics() {
        return CacheStatistics.builder()
                .localCacheSize(localCache.size())
                .localCacheKeys(localCache.keySet())
                .build();
    }
    
    /**
     * 缓存统计信息
     */
    @lombok.Data
    @lombok.Builder
    public static class CacheStatistics {
        private int localCacheSize;
        private java.util.Set<String> localCacheKeys;
    }
}
