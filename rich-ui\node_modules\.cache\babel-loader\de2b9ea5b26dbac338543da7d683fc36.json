{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\SeaFclComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\SeaFclComponent.vue", "mtime": 1754646305906}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_audit", "_interopRequireDefault", "require", "_logisticsProgress", "_chargeList", "_vueTreeselect", "name", "components", "Audit", "LogisticsProgress", "ChargeList", "Treeselect", "props", "seaFclList", "type", "Array", "default", "_default", "form", "Object", "branchInfo", "Boolean", "logisticsInfo", "chargeInfo", "auditInfo", "disabled", "booking", "psaVerify", "supplierList", "carrierList", "companyList", "carrierNormalizer", "Function", "getPayableFunc", "getBookingStatusFunc", "getServiceInstanceDisableFunc", "getServiceObjectFunc", "data", "bookingBillConfig", "file", "templateList", "computed", "isDisabled", "methods", "getSupplierEmail", "supplierId", "supplier", "find", "v", "companyId", "staffEmail", "getAgreementDisplay", "serviceInstance", "agreementTypeCode", "agreementNo", "handleBookingBill", "item", "template", "$emit", "changeServiceFold", "addSeaFCL", "deleteRsOpFclSea", "openChargeSelect", "auditCharge", "event", "getBookingBill", "generateFreight", "type1", "type2", "index", "selectPsaBookingOpen", "selectCarrier", "addProgress", "logList", "handleSettledRate", "psaBookingCancel", "copyFreight", "calculateCharge", "serviceType", "getPayable", "getBookingStatus", "status", "getServiceInstanceDisable", "getServiceObject", "serviceTypeId", "exports", "_default2"], "sources": ["src/views/system/document/serviceComponents/SeaFclComponent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"sea-fcl-component\">\r\n    <!--整柜海运-->\r\n    <div v-for=\"(item, index) in seaFclList\" :key=\"`sea-fcl-${index}`\" class=\"sea-fcl-item\">\r\n      <!--标题栏-->\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <div class=\"service-bar\">\r\n            <a :class=\"[\r\n                'service-toggle-icon',\r\n                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeServiceFold(item.rsServiceInstances)\">\r\n                海运-FCL\r\n              </h3>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"addSeaFCL\"\r\n              >[+]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"deleteRsOpFclSea(item)\"\r\n              >[-]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"openChargeSelect(item)\"\r\n              >[CN...]\r\n              </el-button>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"item.rsServiceInstances\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(1)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @auditFee=\"auditCharge(item,$event)\"\r\n              @return=\"item = $event\"\r\n            />\r\n            <div class=\"booking-bill-container\">\r\n              <el-popover\r\n                v-for=\"(billConfig, billIndex) in bookingBillConfig\"\r\n                :key=\"`bill-${billIndex}`\"\r\n                placement=\"top\"\r\n                trigger=\"click\"\r\n                width=\"100\"\r\n              >\r\n                <el-button\r\n                  v-for=\"(template, templateIndex) in billConfig.templateList\"\r\n                  :key=\"`template-${templateIndex}`\"\r\n                  @click=\"handleBookingBill(item, template)\"\r\n                >\r\n                  {{ template }}\r\n                </el-button>\r\n                <a\r\n                  slot=\"reference\"\r\n                  class=\"booking-bill-link\"\r\n                  target=\"_blank\"\r\n                >\r\n                  [{{ billConfig.file }}]\r\n                </a>\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"item.rsServiceInstances.serviceFold\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNo\"\r\n                  :class=\"{ 'disable-form': form.sqdPsaNo }\"\r\n                  :disabled=\"!!form.sqdPsaNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(1, 1, index)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(item.rsServiceInstances.supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"item.rsServiceInstances.supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.rsServiceInstances)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"订舱状态\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input\r\n                      :value=\"getBookingStatus(item.bookingStatus)\"\r\n                      class=\"disable-form\"\r\n                      disabled\r\n                      placeholder=\"订舱状态\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-button\r\n                      :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                      class=\"cancel-btn\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"psaBookingCancel(item)\"\r\n                    >\r\n                      取消\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"商务单号\" prop=\"supplierId\">\r\n                    <el-input :class=\"item.sqdPsaNo?'disable-form':''\" :disabled=\"item.sqdPsaNo?true:false\"\r\n                              :value=\"item.sqdPsaNo\"\r\n                              @focus=\"selectPsaBookingOpen(item)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"SO号码\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.soNo\"\r\n                              :class=\"disabled || getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"SO号码\" style=\"width: 100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"提单号码\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.blNo\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"提单号码\" style=\"width: 100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"9\">\r\n                  <el-form-item label=\"柜号概览\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.sqdContainersSealsSum\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"柜号概览\"\r\n                              style=\"width: 100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"船公司\" prop=\"carrierIds\">\r\n                    <treeselect v-model=\"item.carrierId\"\r\n                                :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                :disable-branch-nodes=\"true\"\r\n                                :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                :disabled-fuzzy-matching=\"true\" :flat=\"false\"\r\n                                :flatten-search-results=\"true\" :multiple=\"false\"\r\n                                :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\"\r\n                                placeholder=\"选择承运人\" @select=\"selectCarrier(item,$event)\"\r\n                    >\r\n                      <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                        {{\r\n                          (node.raw.carrierIntlCode != null) ? node.raw.carrierIntlCode : \" \"\r\n                        }}\r\n                      </div>\r\n                      <label slot=\"option-label\"\r\n                             slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                             :class=\"labelClassName\"\r\n                      >\r\n                        {{\r\n                          node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                        }}\r\n                        <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                      </label>\r\n                    </treeselect>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程船名\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.firstVessel\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"头程船名船次\" style=\"width: 100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"二程船名\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.secondVessel\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"二程船名船次\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"9\">\r\n                  <el-form-item label=\"船期\" style=\"padding-right: 0;\">\r\n                    <el-input v-model=\"item.inquiryScheduleSummary\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"航班时效\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程开船\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.firstCyOpenTime\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"头程开船\" style=\"width:100%\"\r\n                              @change=\"addProgress(getServiceObject(item.serviceTypeId).rsOpLogList,15)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程截重\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.firstCyClosingTime\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              clearable placeholder=\"头程截重\"\r\n                              style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截关时间\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.cvClosingTime\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"截关时间\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"ETD\" style=\"padding-right: 0;\">\r\n                    <el-date-picker v-model=\"item.etd\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ATA\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"ETA\" style=\"padding-right: 0;\">\r\n                    <el-date-picker v-model=\"item.eta\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ATA\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截补料\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.siClosingTime\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"截补料\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截VGM\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.sqdVgmStatus\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"截VGM\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"AMS/ENS\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.sqdAmsEnsPostStatus\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"AMS/ENS\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"结算价\" prop=\"revenueTons\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input v-model=\"item.settledRate\"\r\n                                  :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                  :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                  placeholder=\"price1/price2/price3\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button type=\"text\" @click=\"handleSettledRate(item)\">确定</el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"19\">\r\n                  <el-form-item label=\"订舱备注\" prop=\"revenueTons\">\r\n                    <div style=\"display: flex\">\r\n                      <el-input v-model=\"item.bookingChargeRemark\"\r\n                                :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                                :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                placeholder=\"订舱费用备注\" type=\"textarea\"\r\n                      />\r\n                      <el-input v-model=\"item.bookingAgentRemark\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                                :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                placeholder=\"订舱备注\"\r\n                                type=\"textarea\"\r\n                      />\r\n                    </div>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getServiceInstanceDisable(item.rsServiceInstances) || disabled || psaVerify\"\r\n                :logistics-progress-data=\"item.rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\" :process-type=\"4\" :service-type=\"1\"\r\n                @deleteItem=\"item.rsOpLogList=item.rsOpLogList.filter(item=>{return item!=$event})\"\r\n                @return=\"item.rsOpLogList=$event\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list :a-t-d=\"form.podEta\"\r\n                         :charge-data=\"item.rsChargeList\"\r\n                         :company-list=\"companyList\"\r\n                         :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)|| disabled\" :hiddenSupplier=\"booking\"\r\n                         :is-receivable=\"false\" :open-charge-list=\"true\"\r\n                         :pay-detail-r-m-b=\"item.payableRMB\"\r\n                         :pay-detail-r-m-b-tax=\"item.payableRMBTax\"\r\n                         :pay-detail-u-s-d=\"item.payableUSD\"\r\n                         :pay-detail-u-s-d-tax=\"item.payableUSDTax\"\r\n                         :service-type-id=\"1\" @copyFreight=\"copyFreight($event)\"\r\n                         @deleteAll=\"item.rsChargeList=[]\"\r\n                         @deleteItem=\"item.rsChargeList=item.rsChargeList.filter(item=>{return item!=$event})\"\r\n                         @return=\"calculateCharge(1,$event,item)\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\r\n\r\nexport default {\r\n  name: \"SeaFclComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    Treeselect\r\n  },\r\n  props: {\r\n    // 整柜海运数据列表\r\n    seaFclList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    carrierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 方法函数\r\n    carrierNormalizer: {\r\n      type: Function,\r\n      default: () => {\r\n      }\r\n    },\r\n    // 添加父组件方法的props\r\n    getPayableFunc: {\r\n      type: Function,\r\n      default: null\r\n    },\r\n    getBookingStatusFunc: {\r\n      type: Function,\r\n      default: null\r\n    },\r\n    getServiceInstanceDisableFunc: {\r\n      type: Function,\r\n      default: null\r\n    },\r\n    getServiceObjectFunc: {\r\n      type: Function,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      bookingBillConfig: [{\r\n        file: \"订舱单\",\r\n        templateList: [\"bookingOrder1\"]\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 处理订舱单生成\r\n    handleBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceInstance) {\r\n      this.$emit(\"changeServiceFold\", serviceInstance)\r\n    },\r\n    addSeaFCL() {\r\n      this.$emit(\"addSeaFCL\")\r\n    },\r\n    deleteRsOpFclSea(item) {\r\n      this.$emit(\"deleteRsOpFclSea\", item)\r\n    },\r\n    openChargeSelect(item) {\r\n      this.$emit(\"openChargeSelect\", item)\r\n    },\r\n    auditCharge(item, event) {\r\n      this.$emit(\"auditCharge\", item, event)\r\n    },\r\n    getBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    generateFreight(type1, type2, index) {\r\n      // 使用索引从seaFclList获取对应项目\r\n      const item = this.seaFclList[index];\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    selectPsaBookingOpen(item) {\r\n      this.$emit(\"selectPsaBookingOpen\", item)\r\n    },\r\n    selectCarrier(item, event) {\r\n      this.$emit(\"selectCarrier\", item, event)\r\n    },\r\n    addProgress(logList, type) {\r\n      this.$emit(\"addProgress\", logList, type)\r\n    },\r\n    handleSettledRate(item) {\r\n      this.$emit(\"handleSettledRate\", item)\r\n    },\r\n    psaBookingCancel(item) {\r\n      this.$emit(\"psaBookingCancel\", item)\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceType, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceType, event, item)\r\n    },\r\n    getPayable(type) {\r\n      return this.getPayableFunc ? this.getPayableFunc(type) : null\r\n    },\r\n    getBookingStatus(status) {\r\n      return this.getBookingStatusFunc ? this.getBookingStatusFunc(status) : \"\"\r\n    },\r\n    getServiceInstanceDisable(serviceInstance) {\r\n      return this.getServiceInstanceDisableFunc ? this.getServiceInstanceDisableFunc(serviceInstance) : false\r\n    },\r\n    getServiceObject(serviceTypeId) {\r\n      return this.getServiceObjectFunc ? this.getServiceObjectFunc(serviceTypeId) : {}\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// SeaFcl组件特定样式\r\n.sea-fcl-component {\r\n  width: 100%;\r\n\r\n  .sea-fcl-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .service-action-btn {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .booking-bill-container {\r\n      margin-left: auto;\r\n\r\n      .booking-bill-link {\r\n        color: blue;\r\n        padding: 0;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .cancel-btn {\r\n        color: red;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AAyZA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAI,IAAA;EACAC,UAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA;EACA;EACAC,KAAA;IACA;IACAC,UAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAC,IAAA;MACAJ,IAAA,EAAAK,MAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAG,UAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAM,aAAA;MACAR,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAO,UAAA;MACAT,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAQ,SAAA;MACAV,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAS,QAAA;MACAX,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAU,OAAA;MACAZ,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAW,SAAA;MACAb,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAY,YAAA;MACAd,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAY,WAAA;MACAf,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAa,WAAA;MACAhB,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAc,iBAAA;MACAjB,IAAA,EAAAkB,QAAA;MACAhB,OAAA,WAAAC,SAAA,GACA;IACA;IACA;IACAgB,cAAA;MACAnB,IAAA,EAAAkB,QAAA;MACAhB,OAAA;IACA;IACAkB,oBAAA;MACApB,IAAA,EAAAkB,QAAA;MACAhB,OAAA;IACA;IACAmB,6BAAA;MACArB,IAAA,EAAAkB,QAAA;MACAhB,OAAA;IACA;IACAoB,oBAAA;MACAtB,IAAA,EAAAkB,QAAA;MACAhB,OAAA;IACA;EACA;EACAqB,IAAA,WAAAA,KAAA;IACA;MACAC,iBAAA;QACAC,IAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAjB,QAAA,SAAAE,SAAA;IACA;EACA;EACAgB,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,UAAA;MACA,IAAAC,QAAA,QAAAlB,YAAA,CAAAmB,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,SAAA,KAAAJ,UAAA;MAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAI,UAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,eAAA;MACA,OAAAA,eAAA,CAAAC,iBAAA,GAAAD,eAAA,CAAAE,WAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,IAAA,EAAAC,QAAA;MACA,KAAAC,KAAA,mBAAAF,IAAA,EAAAC,QAAA;IACA;IACA;IACAE,iBAAA,WAAAA,kBAAAP,eAAA;MACA,KAAAM,KAAA,sBAAAN,eAAA;IACA;IACAQ,SAAA,WAAAA,UAAA;MACA,KAAAF,KAAA;IACA;IACAG,gBAAA,WAAAA,iBAAAL,IAAA;MACA,KAAAE,KAAA,qBAAAF,IAAA;IACA;IACAM,gBAAA,WAAAA,iBAAAN,IAAA;MACA,KAAAE,KAAA,qBAAAF,IAAA;IACA;IACAO,WAAA,WAAAA,YAAAP,IAAA,EAAAQ,KAAA;MACA,KAAAN,KAAA,gBAAAF,IAAA,EAAAQ,KAAA;IACA;IACAC,cAAA,WAAAA,eAAAT,IAAA,EAAAC,QAAA;MACA,KAAAC,KAAA,mBAAAF,IAAA,EAAAC,QAAA;IACA;IACAS,eAAA,WAAAA,gBAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA;MACA;MACA,IAAAb,IAAA,QAAA3C,UAAA,CAAAwD,KAAA;MACA,KAAAX,KAAA,oBAAAS,KAAA,EAAAC,KAAA,EAAAZ,IAAA;IACA;IACAc,oBAAA,WAAAA,qBAAAd,IAAA;MACA,KAAAE,KAAA,yBAAAF,IAAA;IACA;IACAe,aAAA,WAAAA,cAAAf,IAAA,EAAAQ,KAAA;MACA,KAAAN,KAAA,kBAAAF,IAAA,EAAAQ,KAAA;IACA;IACAQ,WAAA,WAAAA,YAAAC,OAAA,EAAA3D,IAAA;MACA,KAAA4C,KAAA,gBAAAe,OAAA,EAAA3D,IAAA;IACA;IACA4D,iBAAA,WAAAA,kBAAAlB,IAAA;MACA,KAAAE,KAAA,sBAAAF,IAAA;IACA;IACAmB,gBAAA,WAAAA,iBAAAnB,IAAA;MACA,KAAAE,KAAA,qBAAAF,IAAA;IACA;IACAoB,WAAA,WAAAA,YAAAZ,KAAA;MACA,KAAAN,KAAA,gBAAAM,KAAA;IACA;IACAa,eAAA,WAAAA,gBAAAC,WAAA,EAAAd,KAAA,EAAAR,IAAA;MACA,KAAAE,KAAA,oBAAAoB,WAAA,EAAAd,KAAA,EAAAR,IAAA;IACA;IACAuB,UAAA,WAAAA,WAAAjE,IAAA;MACA,YAAAmB,cAAA,QAAAA,cAAA,CAAAnB,IAAA;IACA;IACAkE,gBAAA,WAAAA,iBAAAC,MAAA;MACA,YAAA/C,oBAAA,QAAAA,oBAAA,CAAA+C,MAAA;IACA;IACAC,yBAAA,WAAAA,0BAAA9B,eAAA;MACA,YAAAjB,6BAAA,QAAAA,6BAAA,CAAAiB,eAAA;IACA;IACA+B,gBAAA,WAAAA,iBAAAC,aAAA;MACA,YAAAhD,oBAAA,QAAAA,oBAAA,CAAAgD,aAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAArE,OAAA,GAAAsE,SAAA"}]}