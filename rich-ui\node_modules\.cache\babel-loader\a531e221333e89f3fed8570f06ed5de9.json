{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\components\\VatinvoiceDialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\components\\VatinvoiceDialog.vue", "mtime": 1754645302076}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_company", "require", "_account", "_FileUpload", "_interopRequireDefault", "_vueTreeselect", "debounce", "fn", "delay", "timer", "context", "args", "arguments", "clearTimeout", "setTimeout", "apply", "_default2", "name", "components", "FileUpload", "Treeselect", "props", "visible", "type", "Boolean", "default", "title", "String", "form", "Object", "_default", "rules", "invoiceItems", "Array", "companyList", "bankAccountList", "data", "dialogVisible", "formData", "invoiceItemList", "debouncedFetchCompanyInfo", "companyBankList", "invoicingItemOptions", "id", "invoicingItemName", "children", "computed", "availableBankList", "length", "created", "fetchCompanyInfo", "watch", "handler", "val", "JSON", "parse", "stringify", "invoiceAttachment", "invoiceBelongsTo", "autoFillCompanyInfo", "immediate", "newVal", "oldVal", "methods", "invoicingItemNormalizer", "node", "_this", "normalized", "label", "map", "child", "submitForm", "_this2", "$refs", "validate", "valid", "$emit", "handleCancel", "handleClose", "searchAvailableInvoice", "console", "log", "fetchCompanyBankAccounts", "_this3", "cooperatorId", "$message", "warning", "listAccount", "belongToCompany", "then", "response", "code", "rows", "info", "catch", "error", "handleBankAccountChange", "bankCode", "selectedAccount", "find", "item", "cooperatorBankFullname", "bankName", "cooperator<PERSON><PERSON>k<PERSON><PERSON>unt", "bankAccount", "companyId", "_this4", "getCompany", "companyInfo", "cooperatorCompanyTitle", "companyLocalName", "cooperatorVatSerialNo", "taxNo", "companyCode", "companyInfoMap", "richCompanyTitle", "richVatSerialNo", "isDisabled", "invoiceStatus", "taxLocked", "actualPayDate", "handleRichBankCodeChange", "row", "richBankFullname", "rich<PERSON><PERSON><PERSON><PERSON><PERSON>unt", "getInvoiceStatusType", "status", "statusMap", "getInvoiceStatusText", "exports"], "sources": ["src/views/system/vatinvoice/components/VatinvoiceDialog.vue"], "sourcesContent": ["<template>\r\n  <el-dialog\r\n    v-dialogDrag\r\n    v-dialogDragWidth\r\n    :close-on-click-modal=\"false\"\r\n    :modal-append-to-body=\"false\"\r\n    :title=\"title\"\r\n    :visible.sync=\"dialogVisible\"\r\n    append-to-body\r\n    width=\"80%\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <el-form ref=\"form\" :model=\"formData\" :rules=\"rules\" class=\"edit\" label-width=\"80px\" size=\"mini\">\r\n      <!-- 第一行 - 基本信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票流水号\">\r\n            <el-input v-model=\"formData.invoiceCodeNo\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                      placeholder=\"发票流水号\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"进销标志\">\r\n            <el-select v-model=\"formData.saleBuy\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"进销标志\"\r\n                       style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"销项\" value=\"sale\"/>\r\n              <el-option label=\"进项\" value=\"buy\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票性质\">\r\n            <!-- 主营业务收入/非主营收入/营业外收入/成本/费用 -->\r\n            <el-select v-model=\"formData.taxClass\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"发票性质\"\r\n                       style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"主营业务收入\" value=\"主营业务收入\"/>\r\n              <el-option label=\"非主营业务收入\" value=\"非主营业务收入\"/>\r\n              <el-option label=\"营业外收入\" value=\"营业外收入\"/>\r\n              <el-option label=\"成本\" value=\"成本\"/>\r\n              <el-option label=\"费用\" value=\"费用\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票类型\">\r\n            <el-select v-model=\"formData.invoiceType\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"发票类型\" style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"增值税专用发票\" value=\"增值税专用发票\"/>\r\n              <el-option label=\"普通发票\" value=\"普通发票\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"合并发票\">\r\n            <el-row>\r\n              <el-col :span=\"8\">\r\n                <el-checkbox v-model=\"formData.mergeInvoice\" :class=\"{'disable-form': isDisabled()}\"\r\n                             :disabled=\"isDisabled()\" false-label=\"0\"\r\n                             true-label=\"1\"\r\n                >√\r\n                </el-checkbox>\r\n              </el-col>\r\n              <el-col :span=\"16\">\r\n                <el-button size=\"mini\" type=\"primary\" @click=\"searchAvailableInvoice\">检索可用发票</el-button>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票号码\">\r\n            <el-input v-model=\"formData.invoiceOfficalNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                      :disabled=\"isDisabled()\"\r\n                      placeholder=\"发票号码\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第二行 - 公司和账户信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"16\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"所属公司\">\r\n                <el-input v-model=\"formData.invoiceBelongsTo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"所属公司\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"我司账户\">\r\n                <tree-select :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"formData.richBankCode\" :placeholder=\"'银行账户'\"\r\n                             :type=\"'companyAccount'\" @return=\"formData.richBankCode=$event\"\r\n                             :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                             @returnData=\"handleRichBankCodeChange\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"对方公司\">\r\n                <company-select :load-options=\"companyList\"\r\n                                :multiple=\"false\" :no-parent=\"true\"\r\n                                :pass=\"formData.cooperatorId\" :placeholder=\"''\"\r\n                                @return=\"formData.cooperatorId=$event\"\r\n                                :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"对方账户\">\r\n                <el-select v-model=\"formData.cooperatorBankCode\" :class=\"{'disable-form': isDisabled()}\"\r\n                           :disabled=\"isDisabled()\"\r\n                           placeholder=\"对方账户\" style=\"width: 100%\"\r\n                           @change=\"handleBankAccountChange\" @click.native=\"fetchCompanyBankAccounts\"\r\n                >\r\n                  <el-option v-for=\"item in availableBankList\" :key=\"item.bankAccId\"\r\n                             :label=\"item.bankAccCode+ '('+item.bankAccount+')'\" :value=\"item.bankAccCode\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发票抬头\">\r\n                <el-input v-model=\"formData.richCompanyTitle\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司发票抬头\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发票抬头\">\r\n                <el-input v-model=\"formData.cooperatorCompanyTitle\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方发票抬头\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n\r\n        <el-col :span=\"8\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"formData.officalChargeNameSummary\" :minrows=\"3\" :rows=\"2\"\r\n                        placeholder=\"发票项目汇总\" type=\"textarea\"\r\n                        :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n              />\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"formData.relatedOrderNo\" :minrows=\"3\" :rows=\"2\" placeholder=\"相关订单号\"\r\n                        type=\"textarea\"\r\n                        :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n              />\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第四行 - 税号信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"16\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"税号\">\r\n                <el-input v-model=\"formData.richVatSerialNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司纳税人识别号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"税号\">\r\n                <el-input v-model=\"formData.cooperatorVatSerialNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\" placeholder=\"对方纳税人识别号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行全称\">\r\n                <el-input v-model=\"formData.richBankFullname\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司银行全称\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行全称\">\r\n                <el-input v-model=\"formData.cooperatorBankFullname\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方银行全称\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行账号\">\r\n                <el-input v-model=\"formData.richBankAccount\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司账号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行账号\">\r\n                <el-input v-model=\"formData.cooperatorBankAccount\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方账号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n\r\n        <el-col :span=\"4\">\r\n          <el-input v-model=\"formData.invoiceRemark\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                    :minrows=\"4\" :rows=\"4\"\r\n                    placeholder=\"备注\" type=\"textarea\"\r\n          />\r\n        </el-col>\r\n\r\n        <el-col :span=\"4\">\r\n          <el-col>\r\n            <el-form-item label=\"期望支付日\">\r\n              <el-date-picker v-model=\"formData.expectedPayDate\"\r\n                              clearable\r\n                              placeholder=\"期望支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"批复支付日\">\r\n              <el-date-picker v-model=\"formData.approvedPayDate\"\r\n                              clearable\r\n                              placeholder=\"批复支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"实际支付日\">\r\n              <el-date-picker v-model=\"formData.actualPayDate\"\r\n                              clearable\r\n                              placeholder=\"实际支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-col>\r\n\r\n      </el-row>\r\n\r\n      <!-- 第七行 - 发票信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票汇率\">\r\n            <el-input v-model=\"formData.invoiceExchangeRate\" :class=\"{'disable-form': isDisabled()}\"\r\n                      :disabled=\"isDisabled()\"\r\n                      placeholder=\"发票汇率\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票币种\">\r\n            <tree-select :pass=\"formData.invoiceCurrencyCode\"\r\n                         :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                         style=\"width: 100%\" @return=\"formData.invoiceCurrencyCode=$event\"\r\n                         :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"发票金额\">\r\n            <el-input v-model=\"formData.invoiceNetAmount\" :class=\"{'disable-form': isDisabled()}\"\r\n                      :disabled=\"isDisabled()\"\r\n                      placeholder=\"金额计算公式\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票状态\">\r\n            <el-row>\r\n              <el-col :offset=\"1\" :span=\"8\">\r\n                <el-tag :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                        :type=\"getInvoiceStatusType(formData.invoiceStatus)\"\r\n                >{{ getInvoiceStatusText(formData.invoiceStatus) }}\r\n                </el-tag>\r\n              </el-col>\r\n              <el-col :span=\"10\">\r\n                <el-popover\r\n                  placement=\"top-start\"\r\n                  title=\"发票附件管理\"\r\n                  trigger=\"hover\"\r\n                  width=\"300\"\r\n                >\r\n                  <div>\r\n                    <file-upload\r\n                      :class=\"isDisabled()?'disable-form':''\"\r\n                      :file-type=\"['pdf']\"\r\n                      :is-disabled=\"isDisabled()\"\r\n                      :is-tip-flex=\"true\"\r\n                      :value=\"formData.invoiceAttachment\"\r\n                      @input=\"formData.invoiceAttachment=$event\"\r\n                    />\r\n                  </div>\r\n                  <template #reference>\r\n                    <el-button size=\"mini\" type=\"primary\">\r\n                      <i class=\"el-icon-upload\"></i>\r\n                      发票附件\r\n                      <i v-if=\"formData.invoiceAttachment && formData.invoiceAttachment.trim()\" class=\"el-icon-check\"\r\n                         style=\"color: #67C23A; margin-left: 5px;\"></i>\r\n                    </el-button>\r\n                  </template>\r\n                </el-popover>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"报税月份\">\r\n            <el-input v-model=\"formData.belongsToMonth\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                      class=\"yellow-bg\" placeholder=\"2025/7/31\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-divider></el-divider>\r\n\r\n      <!-- 发票明细表格 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"24\">\r\n          <el-table :data=\"formData.rsCharges\" border size=\"mini\" style=\"width: 100%\">\r\n            <el-table-column align=\"center\" type=\"selection\" width=\"35\"/>\r\n            <el-table-column align=\"center\" label=\"账单编号\" prop=\"debitNoteId\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"RCT号\" prop=\"sqdRctNo\"/>\r\n            <el-table-column align=\"center\" label=\"所属服务\" prop=\"serviceLocalName\" width=\"100\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.sqdServiceTypeId == 0 ? \"客户应收\" : scope.row.serviceLocalName }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"费用名称\" prop=\"chargeName\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"备注\" prop=\"chargeRemark\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"收付标志\" prop=\"isReceivingOrPaying\" width=\"80\">\r\n              <template #default=\"scope\">{{ scope.row.isReceivingOrPaying == 0 ? \"应收\" : \"应付\" }}</template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"报价币种\" prop=\"quoteCurrency\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"单价\" prop=\"dnUnitRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"数量\" prop=\"dnAmount\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"单位\" prop=\"dnUnitCode\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"结算汇率\" prop=\"basicCurrencyRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"结算币种\" prop=\"dnCurrencyCode\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"税率\" prop=\"dutyRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"含税小计\" prop=\"subtotal\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"开票项目名称\" prop=\"invoicingItem\" width=\"150\">\r\n              <template #default=\"scope\">\r\n                <treeselect v-model=\"scope.row.invoicingItem\"\r\n                            :class=\"{'disable-form': isDisabled()}\"\r\n                            :default-expand-level=\"1\"\r\n                            :disable-branch-nodes=\"false\"\r\n                            :disabled=\"isDisabled()\"\r\n                            :normalizer=\"invoicingItemNormalizer\"\r\n                            :options=\"invoicingItemOptions\"\r\n                            :show-count=\"true\"\r\n                            :z-index=\"9999\"\r\n                            append-to-body\r\n                            placeholder=\"开票项目名称\"\r\n                            searchable\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{ node.raw.invoicingItemName || node.label }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\">\r\n                    {{ node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"税收编码\" prop=\"taxCode\" width=\"100\"/>\r\n          </el-table>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 操作按钮组 -->\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n        <el-col :span=\"3\">\r\n          <el-button icon=\"el-icon-check\" size=\"mini\" type=\"primary\">√默认对冲</el-button>\r\n          <div>已选总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">智选</el-button>\r\n          <div>未选总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">反选</el-button>\r\n          <div>全部总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">申请开票</el-button>\r\n          <div>申请人+时间</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">信息审核</el-button>\r\n          <div>确认人+时间</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"success\">发送开票</el-button>\r\n          <div>开票人+时间</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"warning\">打印</el-button>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"info\">报税锁定</el-button>\r\n          <div>报税人+时间</div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <template #footer>\r\n      <div class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">{{ formData.invoiceId ? \"更 新\" : \"确 定\" }}</el-button>\r\n        <el-button @click=\"handleCancel\">取 消</el-button>\r\n      </div>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport {getCompany} from \"@/api/system/company\"\r\nimport {listAccount} from \"@/api/system/account\"\r\nimport FileUpload from \"@/components/FileUpload\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\r\n\r\n// 防抖函数\r\nfunction debounce(fn, delay) {\r\n  let timer = null\r\n  return function () {\r\n    const context = this\r\n    const args = arguments\r\n    clearTimeout(timer)\r\n    timer = setTimeout(function () {\r\n      fn.apply(context, args)\r\n    }, delay)\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: \"VatinvoiceDialog\",\r\n  components: {\r\n    FileUpload,\r\n    Treeselect\r\n  },\r\n  props: {\r\n    // 是否显示对话框\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 标题\r\n    title: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 表单验证规则\r\n    rules: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 发票明细列表\r\n    invoiceItems: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 银行账户列表\r\n    bankAccountList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 内部对话框可见性状态\r\n      dialogVisible: false,\r\n      // 表单数据的副本\r\n      formData: {},\r\n      // 发票明细列表的副本\r\n      invoiceItemList: [],\r\n      // 防抖后的获取公司信息方法\r\n      debouncedFetchCompanyInfo: null,\r\n      // 公司银行账户列表\r\n      companyBankList: [],\r\n      // 开票项目选项数据\r\n      invoicingItemOptions: [\r\n        {\r\n          id: \"1\",\r\n          invoicingItemName: \"经纪代理服务\",\r\n          children: [\r\n            {id: \"1-1\", invoicingItemName: \"代理运费\"},\r\n            {id: \"1-2\", invoicingItemName: \"国际货物运输代理服务费\"},\r\n            {id: \"1-3\", invoicingItemName: \"代理港杂费\"},\r\n            {id: \"1-4\", invoicingItemName: \"国际货物运输代理服务\"},\r\n            {id: \"1-5\", invoicingItemName: \"代理报关服务费\"},\r\n            {id: \"1-6\", invoicingItemName: \"代理服务费\"},\r\n            {id: \"1-7\", invoicingItemName: \"代理报关费\"},\r\n            {id: \"1-8\", invoicingItemName: \"代理拖车费\"},\r\n            {id: \"1-9\", invoicingItemName: \"国际货物运输代理服务-代理运费\"},\r\n            {id: \"1-10\", invoicingItemName: \"代理国内运费\"},\r\n            {id: \"1-11\", invoicingItemName: \"国际货物运输代理海运费\"},\r\n            {id: \"1-12\", invoicingItemName: \"代理运费费\"},\r\n            {id: \"1-13\", invoicingItemName: \"运输代理费\"},\r\n            {id: \"1-14\", invoicingItemName: \"货物运输代理服务费\"},\r\n            {id: \"1-15\", invoicingItemName: \"国际货物运输代理港杂费\"},\r\n            {id: \"1-16\", invoicingItemName: \"国际货物运输代理运费\"},\r\n            {id: \"1-17\", invoicingItemName: \"货物运输代理费\"},\r\n            {id: \"1-18\", invoicingItemName: \"国际货物运输代理费\"},\r\n            {id: \"1-19\", invoicingItemName: \"代理杂费\"},\r\n            {id: \"1-20\", invoicingItemName: \"代理文件费\"},\r\n            {id: \"1-21\", invoicingItemName: \"代理设备交接单费用\"},\r\n            {id: \"1-22\", invoicingItemName: \"代理舱单申报费\"},\r\n            {id: \"1-23\", invoicingItemName: \"代理操作费\"},\r\n            {id: \"1-24\", invoicingItemName: \"代理封条费\"},\r\n            {id: \"1-25\", invoicingItemName: \"代理码头操作费\"},\r\n            {id: \"1-26\", invoicingItemName: \"代理电放费\"},\r\n            {id: \"1-27\", invoicingItemName: \"代理核重费\"}\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    // 可用的银行账户列表：优先使用companyBankList，为空时使用传入的bankAccountList\r\n    availableBankList() {\r\n      return this.companyBankList.length > 0 ? this.companyBankList : this.bankAccountList\r\n    }\r\n  },\r\n\r\n  created() {\r\n    // 创建防抖版本的fetchCompanyInfo方法，设置300ms延迟\r\n    this.debouncedFetchCompanyInfo = debounce(this.fetchCompanyInfo, 300)\r\n  },\r\n  watch: {\r\n    visible: {\r\n      handler(val) {\r\n        this.dialogVisible = val\r\n        if (val) {\r\n          // 当对话框显示时，复制传入的数据\r\n          this.formData = JSON.parse(JSON.stringify(this.form))\r\n          this.invoiceItemList = JSON.parse(JSON.stringify(this.invoiceItems))\r\n\r\n          // 确保发票附件字段存在\r\n          if (!this.formData.invoiceAttachment) {\r\n            this.formData.invoiceAttachment = \"\"\r\n          }\r\n\r\n          // 如果所属公司已有值，自动填充相关信息\r\n          if (this.formData.invoiceBelongsTo) {\r\n            this.autoFillCompanyInfo(this.formData.invoiceBelongsTo)\r\n          }\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    // 监听对方公司ID变化\r\n    \"formData.cooperatorId\": {\r\n      handler(newVal, oldVal) {\r\n        // 只有当值真正变化时才触发查询\r\n        if (newVal && newVal !== oldVal) {\r\n          this.debouncedFetchCompanyInfo(newVal)\r\n        }\r\n      }\r\n    },\r\n    // 监听所属公司变化，自动填充我司发票抬头和税号\r\n    \"formData.invoiceBelongsTo\": {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          this.autoFillCompanyInfo(newVal)\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 开票项目数据标准化函数\r\n    invoicingItemNormalizer(node) {\r\n      const normalized = {\r\n        id: node.invoicingItemName, // 使用invoicingItemName作为id\r\n        label: node.invoicingItemName,\r\n        invoicingItemName: node.invoicingItemName\r\n      }\r\n\r\n      if (node.children && node.children.length > 0) {\r\n        normalized.children = node.children.map(child => this.invoicingItemNormalizer(child))\r\n      }\r\n\r\n      return normalized\r\n    },\r\n    // 提交表单\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          this.$emit(\"submit\", this.formData)\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    handleCancel() {\r\n      this.$emit(\"cancel\")\r\n      this.handleClose()\r\n    },\r\n    // 关闭对话框\r\n    handleClose() {\r\n      this.$emit(\"update:visible\", false)\r\n    },\r\n    searchAvailableInvoice() {\r\n      console.log(\"检索可用发票\")\r\n    },\r\n\r\n    // 获取公司银行账户列表\r\n    fetchCompanyBankAccounts() {\r\n      // 检查是否有选择对方公司\r\n      if (!this.formData.cooperatorId) {\r\n        this.$message.warning(\"请先选择对方公司\")\r\n        return\r\n      }\r\n\r\n      // 调用API获取该公司的银行账户\r\n      listAccount({belongToCompany: this.formData.cooperatorId}).then(response => {\r\n        if (response.code === 200) {\r\n          this.companyBankList = response.rows || []\r\n          // 如果没有账户，显示提示\r\n          if (this.companyBankList.length === 0) {\r\n            this.$message.info(\"该公司没有银行账户记录\")\r\n          }\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取公司银行账户失败\", error)\r\n        this.$message.error(\"获取公司银行账户失败\")\r\n      })\r\n    },\r\n\r\n    // 处理银行账户选择变化\r\n    handleBankAccountChange(bankCode) {\r\n      // 根据选择的bankCode找到对应的银行账户信息\r\n      const selectedAccount = this.availableBankList.find(item => item.bankCode === bankCode)\r\n\r\n      if (selectedAccount) {\r\n        // 自动填充银行全称和银行账号\r\n        this.formData.cooperatorBankFullname = selectedAccount.bankName || \"\"\r\n        this.formData.cooperatorBankAccount = selectedAccount.bankAccount || \"\"\r\n      }\r\n    },\r\n    // 获取公司信息\r\n    fetchCompanyInfo(companyId) {\r\n      getCompany(companyId).then(response => {\r\n        if (response.code === 200) {\r\n          const companyInfo = response.data\r\n          // 更新表单中与对方公司相关的字段\r\n          this.formData.cooperatorCompanyTitle = companyInfo.companyLocalName || \"\"\r\n          this.formData.cooperatorVatSerialNo = companyInfo.taxNo || \"\"\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取公司信息失败\", error)\r\n      })\r\n    },\r\n\r\n    // 自动填充我司发票抬头和税号\r\n    autoFillCompanyInfo(companyCode) {\r\n      // 根据所属公司代码(GZRS/HKRS/SZRS/GZCF)自动填充我司发票抬头和税号\r\n      const companyInfoMap = {\r\n        \"GZRS\": {\r\n          title: \"广州瑞旗国际货运代理有限公司\",\r\n          taxNo: \"91440101MA59UQXX7B\"\r\n        },\r\n        \"HKRS\": {\r\n          title: \"香港瑞旗国际货运代理有限公司\",\r\n          taxNo: \"HK12345678\"\r\n        },\r\n        \"SZRS\": {\r\n          title: \"深圳市瑞旗国际货运代理有限公司\",\r\n          taxNo: \"91440300MA5G9UB57Q\"\r\n        },\r\n        \"GZCF\": {\r\n          title: \"广州正泽国际货运代理有限公司\",\r\n          taxNo: \"91440101MA9XRGLH0F\"\r\n        }\r\n      }\r\n\r\n      // 获取对应公司的信息\r\n      const companyInfo = companyInfoMap[companyCode]\r\n\r\n      // 如果找到对应的公司信息，则填充表单\r\n      if (companyInfo) {\r\n        this.formData.richCompanyTitle = companyInfo.title\r\n        this.formData.richVatSerialNo = companyInfo.taxNo\r\n      }\r\n    },\r\n\r\n    // 判断表单项是否禁用\r\n    isDisabled() {\r\n      // 根据以下条件判断表单是否应该禁用：\r\n      // 1. 如果发票状态为已开票\r\n      if (this.formData.invoiceStatus === \"1\") {\r\n        return true\r\n      }\r\n\r\n      // 2. 如果报税已锁定\r\n      if (this.formData.taxLocked === \"1\") {\r\n        return true\r\n      }\r\n\r\n      // 3. 如果已支付\r\n      if (this.formData.actualPayDate) {\r\n        return true\r\n      }\r\n\r\n      // 如果没有禁用条件，则表单可编辑\r\n      return false\r\n    },\r\n    handleRichBankCodeChange(row) {\r\n      this.formData.richBankFullname = row.bankName\r\n      this.formData.richBankAccount = row.bankAccount\r\n    },\r\n    // 获取发票状态类型\r\n    getInvoiceStatusType(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"info\",\r\n        \"issued\": \"success\",\r\n        \"applied\": \"warning\",\r\n        \"canceled\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n    // 获取发票状态文本\r\n    getInvoiceStatusText(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"未开票\",\r\n        \"issued\": \"已开票\",\r\n        \"applied\": \"已申请\",\r\n        \"canceled\": \"已作废\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.yellow-bg {\r\n  background-color: #fffbe6;\r\n}\r\n\r\n.disable-form {\r\n  background-color: #f5f7fa;\r\n  border-color: #e4e7ed;\r\n  color: #c0c4cc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.uploaded-file-preview {\r\n  margin-top: 10px;\r\n  padding: 10px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.preview-title {\r\n  font-weight: bold;\r\n  color: #495057;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.file-links {\r\n  display: flex;\r\n  gap: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.file-links .el-link {\r\n  font-size: 12px;\r\n}\r\n\r\n/* treeselect组件样式 */\r\n:deep(.vue-treeselect__menu) {\r\n  z-index: 9999 !important;\r\n  position: fixed !important;\r\n}\r\n\r\n:deep(.vue-treeselect__menu-container) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n:deep(.vue-treeselect__dropdown) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n/* 确保下拉框在表格之上 */\r\n:deep(.vue-treeselect__menu-arrow) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n:deep(.vue-treeselect__list) {\r\n  z-index: 9999 !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AA4cA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,cAAA,GAAAD,sBAAA,CAAAH,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA,SAAAK,SAAAC,EAAA,EAAAC,KAAA;EACA,IAAAC,KAAA;EACA;IACA,IAAAC,OAAA;IACA,IAAAC,IAAA,GAAAC,SAAA;IACAC,YAAA,CAAAJ,KAAA;IACAA,KAAA,GAAAK,UAAA;MACAP,EAAA,CAAAQ,KAAA,CAAAL,OAAA,EAAAC,IAAA;IACA,GAAAH,KAAA;EACA;AACA;AAAA,IAAAQ,SAAA,GAEA;EACAC,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA;EACA;EACAC,KAAA;IACA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAG,IAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA,WAAAK,SAAA;QAAA;MAAA;IACA;IACA;IACAC,KAAA;MACAR,IAAA,EAAAM,MAAA;MACAJ,OAAA,WAAAK,SAAA;QAAA;MAAA;IACA;IACA;IACAE,YAAA;MACAT,IAAA,EAAAU,KAAA;MACAR,OAAA,WAAAK,SAAA;QAAA;MAAA;IACA;IACAI,WAAA;MACAX,IAAA,EAAAU,KAAA;MACAR,OAAA,WAAAK,SAAA;QAAA;MAAA;IACA;IACA;IACAK,eAAA;MACAZ,IAAA,EAAAU,KAAA;MACAR,OAAA,WAAAK,SAAA;QAAA;MAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,aAAA;MACA;MACAC,QAAA;MACA;MACAC,eAAA;MACA;MACAC,yBAAA;MACA;MACAC,eAAA;MACA;MACAC,oBAAA,GACA;QACAC,EAAA;QACAC,iBAAA;QACAC,QAAA,GACA;UAAAF,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA;MAEA;IAEA;EACA;EACAE,QAAA;IACA;IACAC,iBAAA,WAAAA,kBAAA;MACA,YAAAN,eAAA,CAAAO,MAAA,YAAAP,eAAA,QAAAN,eAAA;IACA;EACA;EAEAc,OAAA,WAAAA,QAAA;IACA;IACA,KAAAT,yBAAA,GAAAlC,QAAA,MAAA4C,gBAAA;EACA;EACAC,KAAA;IACA7B,OAAA;MACA8B,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAAhB,aAAA,GAAAgB,GAAA;QACA,IAAAA,GAAA;UACA;UACA,KAAAf,QAAA,GAAAgB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA5B,IAAA;UACA,KAAAW,eAAA,GAAAe,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAxB,YAAA;;UAEA;UACA,UAAAM,QAAA,CAAAmB,iBAAA;YACA,KAAAnB,QAAA,CAAAmB,iBAAA;UACA;;UAEA;UACA,SAAAnB,QAAA,CAAAoB,gBAAA;YACA,KAAAC,mBAAA,MAAArB,QAAA,CAAAoB,gBAAA;UACA;QACA;MACA;MACAE,SAAA;IACA;IACA;IACA;MACAR,OAAA,WAAAA,QAAAS,MAAA,EAAAC,MAAA;QACA;QACA,IAAAD,MAAA,IAAAA,MAAA,KAAAC,MAAA;UACA,KAAAtB,yBAAA,CAAAqB,MAAA;QACA;MACA;IACA;IACA;IACA;MACAT,OAAA,WAAAA,QAAAS,MAAA;QACA,IAAAA,MAAA;UACA,KAAAF,mBAAA,CAAAE,MAAA;QACA;MACA;IACA;EACA;EACAE,OAAA;IACA;IACAC,uBAAA,WAAAA,wBAAAC,IAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,UAAA;QACAxB,EAAA,EAAAsB,IAAA,CAAArB,iBAAA;QAAA;QACAwB,KAAA,EAAAH,IAAA,CAAArB,iBAAA;QACAA,iBAAA,EAAAqB,IAAA,CAAArB;MACA;MAEA,IAAAqB,IAAA,CAAApB,QAAA,IAAAoB,IAAA,CAAApB,QAAA,CAAAG,MAAA;QACAmB,UAAA,CAAAtB,QAAA,GAAAoB,IAAA,CAAApB,QAAA,CAAAwB,GAAA,WAAAC,KAAA;UAAA,OAAAJ,KAAA,CAAAF,uBAAA,CAAAM,KAAA;QAAA;MACA;MAEA,OAAAH,UAAA;IACA;IACA;IACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA7C,IAAA,CAAA8C,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAI,KAAA,WAAAJ,MAAA,CAAAlC,QAAA;QACA;MACA;IACA;IACA;IACAuC,YAAA,WAAAA,aAAA;MACA,KAAAD,KAAA;MACA,KAAAE,WAAA;IACA;IACA;IACAA,WAAA,WAAAA,YAAA;MACA,KAAAF,KAAA;IACA;IACAG,sBAAA,WAAAA,uBAAA;MACAC,OAAA,CAAAC,GAAA;IACA;IAEA;IACAC,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA7C,QAAA,CAAA8C,YAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,IAAAC,oBAAA;QAAAC,eAAA,OAAAlD,QAAA,CAAA8C;MAAA,GAAAK,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAR,MAAA,CAAA1C,eAAA,GAAAiD,QAAA,CAAAE,IAAA;UACA;UACA,IAAAT,MAAA,CAAA1C,eAAA,CAAAO,MAAA;YACAmC,MAAA,CAAAE,QAAA,CAAAQ,IAAA;UACA;QACA;MACA,GAAAC,KAAA,WAAAC,KAAA;QACAf,OAAA,CAAAe,KAAA,eAAAA,KAAA;QACAZ,MAAA,CAAAE,QAAA,CAAAU,KAAA;MACA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAAC,QAAA;MACA;MACA,IAAAC,eAAA,QAAAnD,iBAAA,CAAAoD,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAH,QAAA,KAAAA,QAAA;MAAA;MAEA,IAAAC,eAAA;QACA;QACA,KAAA5D,QAAA,CAAA+D,sBAAA,GAAAH,eAAA,CAAAI,QAAA;QACA,KAAAhE,QAAA,CAAAiE,qBAAA,GAAAL,eAAA,CAAAM,WAAA;MACA;IACA;IACA;IACAtD,gBAAA,WAAAA,iBAAAuD,SAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,mBAAA,EAAAF,SAAA,EAAAhB,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACA,IAAAiB,WAAA,GAAAlB,QAAA,CAAAtD,IAAA;UACA;UACAsE,MAAA,CAAApE,QAAA,CAAAuE,sBAAA,GAAAD,WAAA,CAAAE,gBAAA;UACAJ,MAAA,CAAApE,QAAA,CAAAyE,qBAAA,GAAAH,WAAA,CAAAI,KAAA;QACA;MACA,GAAAlB,KAAA,WAAAC,KAAA;QACAf,OAAA,CAAAe,KAAA,aAAAA,KAAA;MACA;IACA;IAEA;IACApC,mBAAA,WAAAA,oBAAAsD,WAAA;MACA;MACA,IAAAC,cAAA;QACA;UACAxF,KAAA;UACAsF,KAAA;QACA;QACA;UACAtF,KAAA;UACAsF,KAAA;QACA;QACA;UACAtF,KAAA;UACAsF,KAAA;QACA;QACA;UACAtF,KAAA;UACAsF,KAAA;QACA;MACA;;MAEA;MACA,IAAAJ,WAAA,GAAAM,cAAA,CAAAD,WAAA;;MAEA;MACA,IAAAL,WAAA;QACA,KAAAtE,QAAA,CAAA6E,gBAAA,GAAAP,WAAA,CAAAlF,KAAA;QACA,KAAAY,QAAA,CAAA8E,eAAA,GAAAR,WAAA,CAAAI,KAAA;MACA;IACA;IAEA;IACAK,UAAA,WAAAA,WAAA;MACA;MACA;MACA,SAAA/E,QAAA,CAAAgF,aAAA;QACA;MACA;;MAEA;MACA,SAAAhF,QAAA,CAAAiF,SAAA;QACA;MACA;;MAEA;MACA,SAAAjF,QAAA,CAAAkF,aAAA;QACA;MACA;;MAEA;MACA;IACA;IACAC,wBAAA,WAAAA,yBAAAC,GAAA;MACA,KAAApF,QAAA,CAAAqF,gBAAA,GAAAD,GAAA,CAAApB,QAAA;MACA,KAAAhE,QAAA,CAAAsF,eAAA,GAAAF,GAAA,CAAAlB,WAAA;IACA;IACA;IACAqB,oBAAA,WAAAA,qBAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IACA;IACAE,oBAAA,WAAAA,qBAAAF,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;EACA;AACA;AAAAG,OAAA,CAAAxG,OAAA,GAAAT,SAAA"}]}