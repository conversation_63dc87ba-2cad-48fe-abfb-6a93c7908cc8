package com.rich.system.service.context;

import com.rich.common.core.domain.entity.RsRct;
import com.rich.common.core.domain.entity.RsServiceInstances;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 服务处理上下文
 * 用于管理服务处理过程中的状态和数据
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceProcessingContext {
    
    /**
     * 操作单信息
     */
    private RsRct rsRct;
    
    /**
     * 现有服务实例映射 (serviceBelongTo -> RsServiceInstances)
     */
    private Map<String, RsServiceInstances> existingServiceInstances;
    
    /**
     * 现有服务类型ID列表
     */
    private List<Long> existingServiceTypeIds;
    
    /**
     * 处理状态跟踪
     */
    private Map<String, ServiceProcessingStatus> processingStatus;
    
    /**
     * 错误信息收集
     */
    private Map<String, Exception> errors;
    
    /**
     * 处理统计
     */
    private ProcessingStatistics statistics;
    
    /**
     * 服务处理状态枚举
     */
    public enum ServiceProcessingStatus {
        PENDING,     // 待处理
        PROCESSING,  // 处理中
        COMPLETED,   // 已完成
        FAILED,      // 失败
        SKIPPED      // 跳过
    }
    
    /**
     * 处理统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessingStatistics {
        private AtomicInteger totalServices = new AtomicInteger(0);
        private AtomicInteger processedServices = new AtomicInteger(0);
        private AtomicInteger failedServices = new AtomicInteger(0);
        private AtomicInteger skippedServices = new AtomicInteger(0);
        private long startTime;
        private long endTime;
        
        public void incrementTotal() {
            totalServices.incrementAndGet();
        }
        
        public void incrementProcessed() {
            processedServices.incrementAndGet();
        }
        
        public void incrementFailed() {
            failedServices.incrementAndGet();
        }
        
        public void incrementSkipped() {
            skippedServices.incrementAndGet();
        }
        
        public long getDuration() {
            return endTime - startTime;
        }
        
        public double getSuccessRate() {
            int total = totalServices.get();
            if (total == 0) return 0.0;
            return (double) processedServices.get() / total * 100;
        }
    }
    
    /**
     * 初始化上下文
     */
    public static ServiceProcessingContext initialize(RsRct rsRct, 
                                                     List<RsServiceInstances> existingInstances) {
        Map<String, RsServiceInstances> instanceMap = new HashMap<>();
        List<Long> serviceTypeIds = new ArrayList<>();
        
        if (existingInstances != null) {
            for (RsServiceInstances instance : existingInstances) {
                instanceMap.put(instance.getServiceBelongTo(), instance);
                serviceTypeIds.add(instance.getServiceTypeId());
            }
        }
        
        return ServiceProcessingContext.builder()
                .rsRct(rsRct)
                .existingServiceInstances(instanceMap)
                .existingServiceTypeIds(serviceTypeIds)
                .processingStatus(new ConcurrentHashMap<>())
                .errors(new ConcurrentHashMap<>())
                .statistics(ProcessingStatistics.builder()
                        .startTime(System.currentTimeMillis())
                        .build())
                .build();
    }
    
    /**
     * 设置服务处理状态
     */
    public void setServiceStatus(String serviceName, ServiceProcessingStatus status) {
        processingStatus.put(serviceName, status);
        
        switch (status) {
            case COMPLETED:
                statistics.incrementProcessed();
                break;
            case FAILED:
                statistics.incrementFailed();
                break;
            case SKIPPED:
                statistics.incrementSkipped();
                break;
        }
    }
    
    /**
     * 记录错误
     */
    public void recordError(String serviceName, Exception error) {
        errors.put(serviceName, error);
        setServiceStatus(serviceName, ServiceProcessingStatus.FAILED);
    }
    
    /**
     * 完成处理
     */
    public void complete() {
        statistics.setEndTime(System.currentTimeMillis());
    }
    
    /**
     * 检查是否有错误
     */
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    /**
     * 获取错误摘要
     */
    public String getErrorSummary() {
        if (errors.isEmpty()) {
            return "无错误";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("处理过程中发生 ").append(errors.size()).append(" 个错误:\n");
        
        errors.forEach((serviceName, error) -> {
            summary.append("- ").append(serviceName).append(": ")
                   .append(error.getMessage()).append("\n");
        });
        
        return summary.toString();
    }
    
    /**
     * 获取处理摘要
     */
    public String getProcessingSummary() {
        ProcessingStatistics stats = this.statistics;
        return String.format(
            "处理摘要: 总计=%d, 成功=%d, 失败=%d, 跳过=%d, 成功率=%.2f%%, 耗时=%dms",
            stats.getTotalServices().get(),
            stats.getProcessedServices().get(),
            stats.getFailedServices().get(),
            stats.getSkippedServices().get(),
            stats.getSuccessRate(),
            stats.getDuration()
        );
    }
    
    /**
     * 检查服务是否存在
     */
    public boolean hasExistingService(String serviceBelongTo) {
        return existingServiceInstances.containsKey(serviceBelongTo);
    }
    
    /**
     * 获取现有服务实例
     */
    public RsServiceInstances getExistingService(String serviceBelongTo) {
        return existingServiceInstances.get(serviceBelongTo);
    }
    
    /**
     * 添加服务到统计
     */
    public void addServiceToStatistics(String serviceName) {
        statistics.incrementTotal();
        setServiceStatus(serviceName, ServiceProcessingStatus.PENDING);
    }
}
