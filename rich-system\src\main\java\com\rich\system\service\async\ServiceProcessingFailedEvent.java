package com.rich.system.service.async;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.ApplicationEvent;

/**
 * 服务处理失败事件
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ServiceProcessingFailedEvent extends ApplicationEvent {
    
    /**
     * 操作单ID
     */
    private Long rctId;
    
    /**
     * 操作单号
     */
    private String rctNo;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 异常信息
     */
    private Exception exception;
    
    /**
     * 处理耗时（毫秒）
     */
    private long duration;
    
    /**
     * 失败时间
     */
    private long failedTime;
    
    public ServiceProcessingFailedEvent(Long rctId, String rctNo, String serviceName, 
                                       Exception exception, long duration) {
        super(serviceName);
        this.rctId = rctId;
        this.rctNo = rctNo;
        this.serviceName = serviceName;
        this.exception = exception;
        this.duration = duration;
        this.failedTime = System.currentTimeMillis();
    }
}
