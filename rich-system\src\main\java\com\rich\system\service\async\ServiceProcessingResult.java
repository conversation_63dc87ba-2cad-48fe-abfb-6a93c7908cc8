package com.rich.system.service.async;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 服务处理结果
 * 封装异步服务处理的结果信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceProcessingResult {
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 异常对象
     */
    private Exception exception;
    
    /**
     * 处理耗时（毫秒）
     */
    private long duration;
    
    /**
     * 结果类型
     */
    private ResultType resultType;
    
    /**
     * 额外信息
     */
    private java.util.Map<String, Object> additionalInfo;
    
    /**
     * 结果类型枚举
     */
    public enum ResultType {
        SUCCESS,    // 成功
        FAILURE,    // 失败
        TIMEOUT,    // 超时
        SKIPPED     // 跳过
    }
    
    /**
     * 创建成功结果
     * 
     * @param serviceName 服务名称
     * @param duration 处理耗时
     * @return 成功结果
     */
    public static ServiceProcessingResult success(String serviceName, long duration) {
        return ServiceProcessingResult.builder()
                .serviceName(serviceName)
                .success(true)
                .duration(duration)
                .resultType(ResultType.SUCCESS)
                .build();
    }
    
    /**
     * 创建成功结果（带额外信息）
     * 
     * @param serviceName 服务名称
     * @param duration 处理耗时
     * @param additionalInfo 额外信息
     * @return 成功结果
     */
    public static ServiceProcessingResult success(String serviceName, 
                                                 long duration, 
                                                 java.util.Map<String, Object> additionalInfo) {
        return ServiceProcessingResult.builder()
                .serviceName(serviceName)
                .success(true)
                .duration(duration)
                .resultType(ResultType.SUCCESS)
                .additionalInfo(additionalInfo)
                .build();
    }
    
    /**
     * 创建失败结果
     * 
     * @param serviceName 服务名称
     * @param exception 异常
     * @param duration 处理耗时
     * @return 失败结果
     */
    public static ServiceProcessingResult failure(String serviceName, 
                                                 Exception exception, 
                                                 long duration) {
        return ServiceProcessingResult.builder()
                .serviceName(serviceName)
                .success(false)
                .exception(exception)
                .errorMessage(exception.getMessage())
                .duration(duration)
                .resultType(ResultType.FAILURE)
                .build();
    }
    
    /**
     * 创建失败结果（带错误消息）
     * 
     * @param serviceName 服务名称
     * @param errorMessage 错误消息
     * @param duration 处理耗时
     * @return 失败结果
     */
    public static ServiceProcessingResult failure(String serviceName, 
                                                 String errorMessage, 
                                                 long duration) {
        return ServiceProcessingResult.builder()
                .serviceName(serviceName)
                .success(false)
                .errorMessage(errorMessage)
                .duration(duration)
                .resultType(ResultType.FAILURE)
                .build();
    }
    
    /**
     * 创建超时结果
     * 
     * @param serviceName 服务名称
     * @param timeoutMs 超时时间（毫秒）
     * @return 超时结果
     */
    public static ServiceProcessingResult timeout(String serviceName, long timeoutMs) {
        return ServiceProcessingResult.builder()
                .serviceName(serviceName)
                .success(false)
                .errorMessage("处理超时: " + timeoutMs + "ms")
                .duration(timeoutMs)
                .resultType(ResultType.TIMEOUT)
                .build();
    }
    
    /**
     * 创建跳过结果
     * 
     * @param serviceName 服务名称
     * @param reason 跳过原因
     * @return 跳过结果
     */
    public static ServiceProcessingResult skipped(String serviceName, String reason) {
        return ServiceProcessingResult.builder()
                .serviceName(serviceName)
                .success(true)
                .errorMessage(reason)
                .duration(0)
                .resultType(ResultType.SKIPPED)
                .build();
    }
    
    /**
     * 获取结果摘要
     * 
     * @return 结果摘要字符串
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("服务: ").append(serviceName);
        summary.append(", 结果: ").append(resultType);
        summary.append(", 耗时: ").append(duration).append("ms");
        
        if (!success && errorMessage != null) {
            summary.append(", 错误: ").append(errorMessage);
        }
        
        return summary.toString();
    }
    
    /**
     * 检查是否为成功结果
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return success && resultType == ResultType.SUCCESS;
    }
    
    /**
     * 检查是否为失败结果
     * 
     * @return 是否失败
     */
    public boolean isFailure() {
        return !success && resultType == ResultType.FAILURE;
    }
    
    /**
     * 检查是否为超时结果
     * 
     * @return 是否超时
     */
    public boolean isTimeout() {
        return resultType == ResultType.TIMEOUT;
    }
    
    /**
     * 检查是否为跳过结果
     * 
     * @return 是否跳过
     */
    public boolean isSkipped() {
        return resultType == ResultType.SKIPPED;
    }
    
    /**
     * 添加额外信息
     * 
     * @param key 键
     * @param value 值
     */
    public void addAdditionalInfo(String key, Object value) {
        if (additionalInfo == null) {
            additionalInfo = new java.util.HashMap<>();
        }
        additionalInfo.put(key, value);
    }
    
    /**
     * 获取额外信息
     * 
     * @param key 键
     * @return 值
     */
    public Object getAdditionalInfo(String key) {
        return additionalInfo != null ? additionalInfo.get(key) : null;
    }
}
