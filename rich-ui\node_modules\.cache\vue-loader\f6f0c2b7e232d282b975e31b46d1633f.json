{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\booking\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\booking\\index.vue", "mtime": 1754646305887}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYm9va2luZyBmcm9tICJAL3ZpZXdzL3N5c3RlbS9ib29raW5nL2luZGV4Ig0KaW1wb3J0IFRyZWVzZWxlY3QgZnJvbSAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QiDQppbXBvcnQgc3RvcmUgZnJvbSAiQC9zdG9yZSINCmltcG9ydCB7DQogIGRlbFJjdCwNCiAgbGlzdEFnZ3JlZ2F0b3JCb29raW5nLA0KICBsaXN0QWdncmVnYXRvclJjdCwNCiAgbGlzdFJjdCwNCiAgbGlzdFZlcmlmeUFnZ3JlZ2F0b3JMaXN0LA0KICBsaXN0VmVyaWZ5TGlzdCwNCiAgb3ANCn0gZnJvbSAiQC9hcGkvc3lzdGVtL3JjdCINCmltcG9ydCBwaW55aW4gZnJvbSAianMtcGlueWluIg0KaW1wb3J0IGN1cnJlbmN5IGZyb20gImN1cnJlbmN5LmpzIg0KaW1wb3J0IHtwYXJzZVRpbWV9IGZyb20gIi4uLy4uLy4uL3V0aWxzL3JpY2giDQppbXBvcnQgbW9tZW50IGZyb20gIm1vbWVudC9tb21lbnQiDQppbXBvcnQgQ29tcGFueVNlbGVjdCBmcm9tICJAL2NvbXBvbmVudHMvQ29tcGFueVNlbGVjdC9pbmRleC52dWUiDQppbXBvcnQgRGF0YUFnZ3JlZ2F0b3IgZnJvbSAiQC92aWV3cy9zeXN0ZW0vRGF0YUFnZ3JlZ2F0b3IvaW5kZXgudnVlIg0KaW1wb3J0IHtyY3RGaWVsZExhYmVsTWFwfSBmcm9tICJAL2NvbmZpZy9yY3RGaWVsZExhYmVsTWFwIg0KaW1wb3J0IERhdGFBZ2dyZWdhdG9yQmFja0dyb3VuZCBmcm9tICJAL3ZpZXdzL3N5c3RlbS9EYXRhQWdncmVnYXRvckJhY2tHcm91bmQvaW5kZXgudnVlIg0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJib29raW5nTGlzdCIsDQogIGNvbXBvbmVudHM6IHtEYXRhQWdncmVnYXRvckJhY2tHcm91bmQsIERhdGFBZ2dyZWdhdG9yLCBDb21wYW55U2VsZWN0LCBUcmVlc2VsZWN0fSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgc2hvd0xlZnQ6IDMsDQogICAgICBzaG93UmlnaHQ6IDI0LA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiBmYWxzZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDmk43kvZzljZXliJfooajooajmoLzmlbDmja4NCiAgICAgIHNhbGVzSWQ6IG51bGwsDQogICAgICB2ZXJpZnlQc2FJZDogbnVsbCwNCiAgICAgIHNhbGVzQXNzaXN0YW50SWQ6IG51bGwsDQogICAgICBvcElkOiBudWxsLA0KICAgICAgYmVsb25nTGlzdDogW10sDQogICAgICBvcExpc3Q6IFtdLA0KICAgICAgYnVzaW5lc3NMaXN0OiBbXSwNCiAgICAgIHJjdExpc3Q6IFtdLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMjANCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgc3RhdGlzdGljc09wOiBbXSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHt9LA0KICAgICAgb3BlbkFnZ3JlZ2F0b3I6IGZhbHNlLA0KICAgICAgZmllbGRMYWJlbE1hcDogcmN0RmllbGRMYWJlbE1hcCwNCiAgICAgIGFnZ3JlZ2F0b3JSY3RMaXN0OiBbXQ0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBzaG93U2VhcmNoKG4pIHsNCiAgICAgIGlmIChuID09PSB0cnVlKSB7DQogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjENCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDMNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjQNCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDANCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgbGV0IGxvYWQgPSBmYWxzZQ0KICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5ubykgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5uZXdCb29raW5nTm8gPSB0aGlzLiRyb3V0ZS5xdWVyeS5ubw0KICAgICAgdGhpcy5nZXRMaXN0KCkudGhlbigoKSA9PiB7DQogICAgICAgIGxvYWQgPSB0cnVlDQogICAgICB9KQ0KICAgIH0gZWxzZSB7DQogICAgICB0aGlzLmdldExpc3QoKS50aGVuKCgpID0+IHsNCiAgICAgICAgbG9hZCA9IHRydWUNCiAgICAgIH0pDQogICAgfQ0KICAgIGlmIChsb2FkKSB7DQogICAgICB0aGlzLmxvYWRTYWxlcygpDQogICAgICB0aGlzLmxvYWRPcCgpDQogICAgICB0aGlzLmxvYWRCdXNpbmVzc2VzKCkNCiAgICB9DQogICAgdGhpcy5sb2FkU3RhZmZMaXN0KCkNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBtb21lbnQoKSB7DQogICAgICByZXR1cm4gbW9tZW50DQogICAgfQ0KICB9LA0KICBwcm9wczogWyJ0eXBlIl0sDQogIG1ldGhvZHM6IHsNCiAgICBsaXN0QWdncmVnYXRvckJvb2tpbmcocGFyYW1zKSB7DQogICAgICBwYXJhbXMuY29uZmlnID0gSlNPTi5zdHJpbmdpZnkocGFyYW1zLmNvbmZpZykNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zID0gcGFyYW1zOw0KICAgICAgcmV0dXJuIGxpc3RBZ2dyZWdhdG9yQm9va2luZyh0aGlzLnF1ZXJ5UGFyYW1zKQ0KICAgIH0sDQogICAgaGFuZGxlT3BlbkFnZ3JlZ2F0b3IoKSB7DQogICAgICB0aGlzLm9wZW5BZ2dyZWdhdG9yID0gdHJ1ZQ0KICAgIH0sDQoNCiAgICBoYW5kbGVTdGF0aXN0aWNzKCkgew0KICAgICAgb3AoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5zdGF0aXN0aWNzT3AgPSByZXNwb25zZS5kYXRhDQogICAgICB9KQ0KICAgIH0sDQogICAgcGFyc2VUaW1lLA0KICAgIGdldEJhZGdlKHJvdykgew0KICAgICAgaWYgKCgodGhpcy50eXBlID09PSAiYm9va2luZyIgJiYgcm93LnNxZFNoaXBwaW5nQm9va2luZ1N0YXR1cyA9PSAiMCIpIHx8ICh0aGlzLnR5cGUgPT09ICJwc2EiICYmIHJvdy5zcWRTaGlwcGluZ0Jvb2tpbmdTdGF0dXMgPT0gIjEiICYmIHJvdy5wc2FWZXJpZnkgPT0gIjAiKSkpIHsNCiAgICAgICAgcmV0dXJuICJuZXciDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gIiINCiAgICAgIH0NCiAgICB9LA0KICAgIGhpZGRlbkRlbGV0ZShyb3cpIHsNCiAgICAgIGlmICh0aGlzLnR5cGUgPT09ICJib29raW5nIiAmJiByb3cuc3FkU2hpcHBpbmdCb29raW5nU3RhdHVzID09IDApIHsNCiAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnR5cGUgPT09ICJwc2EiKSB7DQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQogICAgY3VycmVuY3ksDQogICAgZ2V0UmVsZWFzZVR5cGUoaWQpIHsNCiAgICAgIGlmIChpZCA9PSAxKSByZXR1cm4gIuaciOe7kyINCiAgICAgIGlmIChpZCA9PSAyKSByZXR1cm4gIuaKvOaUviINCiAgICAgIGlmIChpZCA9PSAzKSByZXR1cm4gIuelqOe7kyINCiAgICAgIGlmIChpZCA9PSA0KSByZXR1cm4gIuetvuaUviINCiAgICAgIGlmIChpZCA9PSA1KSByZXR1cm4gIuiuoumHkSINCiAgICAgIGlmIChpZCA9PSA2KSByZXR1cm4gIumihOS7mCINCiAgICAgIGlmIChpZCA9PSA3KSByZXR1cm4gIuaJo+i0pyINCiAgICAgIGlmIChpZCA9PSA5KSByZXR1cm4gIuWxhemXtCINCiAgICAgIHJldHVybiAiIg0KICAgIH0sDQogICAgaGFuZGxlVmVyaWZ5KHJvdykgew0KICAgICAgdGhpcy4kdGFiLm9wZW5QYWdlKCLmk43kvZzljZUiLCAiL29wcHJvY2Vzcy9vcGRldGFpbCIsIHtySWQ6IHRoaXMuaWRzWzBdLCBwc2FWZXJpZnk6IHRydWV9KQ0KICAgIH0sDQogICAgZ2V0TmFtZShpZCkgew0KICAgICAgaWYgKGlkKSB7DQogICAgICAgIGxldCBzdGFmZiA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKHJzU3RhZmYgPT4gcnNTdGFmZi5zdGFmZklkID09IGlkKVswXQ0KICAgICAgICBpZiAoc3RhZmYpIHsNCiAgICAgICAgICByZXR1cm4gc3RhZmYuc3RhZmZGYW1pbHlMb2NhbE5hbWUgKyBzdGFmZi5zdGFmZkdpdmluZ0xvY2FsTmFtZSArIHN0YWZmLnN0YWZmU2hvcnROYW1lDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIHNxZERvY0RlbGl2ZXJ5V2F5KHR5cGUpIHsNCiAgICAgIGlmICh0eXBlID09IDEpIHJldHVybiAiIOWig+WkluW/q+mAkiINCiAgICAgIGlmICh0eXBlID09IDIpIHJldHVybiAiIOWig+WGheW/q+mAkiINCiAgICAgIGlmICh0eXBlID09IDMpIHJldHVybiAiIOi3keiFvyINCiAgICAgIGlmICh0eXBlID09IDQpIHJldHVybiAiIOS4muWKoemAgei+viINCiAgICAgIGlmICh0eXBlID09IDUpIHJldHVybiAiIOWuouaIt+iHquWPliINCiAgICAgIGlmICh0eXBlID09IDYpIHJldHVybiAiIFFRIg0KICAgICAgaWYgKHR5cGUgPT0gNykgcmV0dXJuICIg5b6u5L+hIg0KICAgICAgaWYgKHR5cGUgPT0gOCkgcmV0dXJuICIg55S16YKuIg0KICAgICAgaWYgKHR5cGUgPT0gOSkgcmV0dXJuICIg5YWs5LyX5Y+3Ig0KICAgICAgaWYgKHR5cGUgPT0gMTApIHJldHVybiAiIOaJv+i/kOS6uuezu+e7nyINCiAgICAgIGlmICh0eXBlID09IDExKSByZXR1cm4gIiDorqLoiLHlj6Pns7vnu58iDQogICAgICBpZiAodHlwZSA9PSAxMikgcmV0dXJuICIg56ys5LiJ5pa557O757ufIg0KICAgIH0sDQogICAgbG9naXN0aWNzUGF5bWVudFRlcm1zKHYpIHsNCiAgICAgIGlmICh2ID09IDEpIHJldHVybiAi5pyI57uTIg0KICAgICAgaWYgKHYgPT0gMikgcmV0dXJuICLmirzljZUiDQogICAgICBpZiAodiA9PSAzKSByZXR1cm4gIuatpOelqOe7k+a4hSINCiAgICAgIGlmICh2ID09IDQpIHJldHVybiAi57uP55CG562+5Y2VIg0KICAgICAgaWYgKHYgPT0gNSkgcmV0dXJuICLpooTmlLborqLph5EiDQogICAgICBpZiAodiA9PSA2KSByZXR1cm4gIuWFqOminemihOS7mCINCiAgICAgIGlmICh2ID09IDcpIHJldHVybiAi5omj6LSnIg0KICAgICAgaWYgKHYgPT0gOCkgcmV0dXJuICLog4zpnaDog4wiDQogICAgfSwNCiAgICBlbWVyZ2VuY3lMZXZlbCh2KSB7DQogICAgICBpZiAodiA9PSAwKSByZXR1cm4gIumihOWumiINCiAgICAgIGlmICh2ID09IDEpIHJldHVybiAi5b2T5aSpIg0KICAgICAgaWYgKHYgPT0gMikgcmV0dXJuICLluLjop4QiDQogICAgICBpZiAodiA9PSAzKSByZXR1cm4gIue0p+aApSINCiAgICAgIGlmICh2ID09IDQpIHJldHVybiAi56uL5Y2zIg0KICAgIH0sDQogICAgZGlmZmljdWx0eUxldmVsKHYpIHsNCiAgICAgIGlmICh2ID09IDApIHJldHVybiAi566A5piTIg0KICAgICAgaWYgKHYgPT0gMSkgcmV0dXJuICLmoIflh4YiDQogICAgICBpZiAodiA9PSAyKSByZXR1cm4gIumrmOe6pyINCiAgICAgIGlmICh2ID09IDMpIHJldHVybiAi54m55YirIg0KICAgIH0sDQogICAgcHJvY2Vzc1N0YXR1cyh2KSB7DQogICAgICBpZiAodiA9PSAxKSByZXR1cm4gIuetieW+hSINCiAgICAgIGlmICh2ID09IDIpIHJldHVybiAi6L+b6KGMIg0KICAgICAgaWYgKHYgPT0gMykgcmV0dXJuICLlj5jmm7QiDQogICAgICBpZiAodiA9PSA0KSByZXR1cm4gIuW8guW4uCINCiAgICAgIGlmICh2ID09IDUpIHJldHVybiAi6LSo5oq8Ig0KICAgICAgaWYgKHYgPT0gNikgcmV0dXJuICLnoa7orqQiDQogICAgICBpZiAodiA9PSA3KSByZXR1cm4gIuWujOaIkCINCiAgICAgIGlmICh2ID09IDgpIHJldHVybiAi5Y+W5raIIg0KICAgICAgaWYgKHYgPT0gOSkgcmV0dXJuICLpqbPlm54iDQogICAgICBpZiAodiA9PSAxMCkgcmV0dXJuICLlm57mlLYiDQogICAgfSwNCiAgICBsb2FkU2FsZXMoKSB7DQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5zYWxlc0xpc3QubGVuZ3RoID09IDAgfHwgdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5yZWRpc0xpc3Quc2FsZXNMaXN0KSB7DQogICAgICAgIHN0b3JlLmRpc3BhdGNoKCJnZXRTYWxlc0xpc3QiKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmJlbG9uZ0xpc3QgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnNhbGVzTGlzdA0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5iZWxvbmdMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5zYWxlc0xpc3QNCiAgICAgIH0NCiAgICB9LA0KICAgIGxvYWRCdXNpbmVzc2VzKCkgew0KICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYnVzaW5lc3Nlc0xpc3QubGVuZ3RoID09IDAgfHwgdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5yZWRpc0xpc3QuYnVzaW5lc3Nlc0xpc3QpIHsNCiAgICAgICAgc3RvcmUuZGlzcGF0Y2goImdldEJ1c2luZXNzZXNMaXN0IikudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5idXNpbmVzc0xpc3QgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmJ1c2luZXNzZXNMaXN0DQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmJ1c2luZXNzTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYnVzaW5lc3Nlc0xpc3QNCiAgICAgIH0NCiAgICB9LA0KICAgIGxvYWRPcCgpIHsNCiAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLm9wTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5vcExpc3QpIHsNCiAgICAgICAgc3RvcmUuZGlzcGF0Y2goImdldE9wTGlzdCIpLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMub3BMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5vcExpc3QNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMub3BMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5vcExpc3QNCiAgICAgIH0NCiAgICB9LA0KICAgIGxvYWRTdGFmZkxpc3QoKSB7DQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5hbGxSc1N0YWZmTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5hbGxSc1N0YWZmTGlzdCkgew0KICAgICAgICBzdG9yZS5kaXNwYXRjaCgiZ2V0QWxsUnNTdGFmZkxpc3QiKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLnN0YWZmTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc3RhZmZMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5hbGxSc1N0YWZmTGlzdA0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOafpeivouaTjeS9nOWNleWIl+ihqOWIl+ihqCAqLw0KICAgIGFzeW5jIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICBpZiAodGhpcy50eXBlID09PSAicHNhIikgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNxZFNoaXBwaW5nQm9va2luZ1N0YXR1cyA9IDENCiAgICAgIH0NCiAgICAgIC8qIGlmICh0aGlzLnR5cGUgPT09ICdib29raW5nJykgIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zcWRTaGlwcGluZ0Jvb2tpbmdTdGF0dXMgPSAwDQogICAgICB9ICovDQoNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGVybWlzc2lvbkxldmVsID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5wZXJtaXNzaW9uTGV2ZWxMaXN0LkMNCiAgICAgIGF3YWl0IGxpc3RWZXJpZnlMaXN0KHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnJjdExpc3QgPSByZXNwb25zZS5yb3dzDQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbA0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDENCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc2VhcmNoVmFsdWUgPSAxDQogICAgICB0aGlzLmdldExpc3QoKQ0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIikNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc2VhcmNoVmFsdWUgPSBudWxsDQogICAgICB0aGlzLmdldExpc3QoKQ0KICAgIH0sDQogICAgLy8gaGFuZGxlU3RhdHVzQ2hhbmdlKHJvdykgew0KICAgIC8vICAgbGV0IHRleHQgPSByb3cuc3RhdHVzID09PSAiMCIgPyAi5ZCv55SoIiA6ICLlgZznlKgiOw0KICAgIC8vICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu6K6k6KaBIicgKyB0ZXh0ICsgJ+WQl++8nycpLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgIC8vICAgICByZXR1cm4gY2hhbmdlU3RhdHVzKHJvdy5yY3RJZCwgcm93LnN0YXR1cyk7DQogICAgLy8gICB9KS50aGVuKCgpID0+IHsNCiAgICAvLyAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyh0ZXh0ICsgIuaIkOWKnyIpOw0KICAgIC8vICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgew0KICAgIC8vICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIjEiIDogIjAiOw0KICAgIC8vICAgfSk7DQogICAgLy8gfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLnJjdElkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLiR0YWIub3BlblBhZ2UoIuaTjeS9nOWNlSIsICIvb3Bwcm9jZXNzL29wZGV0YWlsIiwge30pDQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy4kdGFiLm9wZW5QYWdlKCLmk43kvZzljZUiLCAiL29wcHJvY2Vzcy9vcGRldGFpbCIsIHtySWQ6IHJvdy5yY3RJZH0pDQogICAgfSwNCiAgICBkYmNsaWNrKHJvdywgY29sdW1uLCBldmVudCkgew0KICAgICAgLy8g5bey57uP6K6i6Iix5LqG55qE5LiN5Y+v5Lul5L+u5pS5DQogICAgICAvKiBpZiAodGhpcy50eXBlID09PSAnYm9va2luZycgJiYgcm93LnNxZFNoaXBwaW5nQm9va2luZ1N0YXR1cyA9PSAxKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfSAqLw0KICAgICAgLyogaWYgKHRoaXMudHlwZSA9PT0gJ3BzYScgJiYgcm93LnBzYVZlcmlmeVN0YXR1c0lkID09IDEpIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9ICovDQogICAgICBpZiAodGhpcy50eXBlID09PSAiYm9va2luZyIpIHsNCiAgICAgICAgdGhpcy4kdGFiLm9wZW5QYWdlKCLorqLoiLHljZXmmI7nu4YiLCAiL3NhbGVzcXVvdGF0aW9uL2Jvb2tpbmdEZXRhaWwiLCB7cklkOiByb3cucmN0SWQsIGJvb2tpbmc6IHRydWV9KQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMudHlwZSA9PT0gInBzYSIpIHsNCiAgICAgICAgdGhpcy4kdGFiLm9wZW5QYWdlKCLllYbliqHlrqHmoLjmmI7nu4YiLCAiL3BzYVZlcmlmeS9wc2FEZXRhaWwiLCB7cklkOiByb3cucmN0SWQsIHBzYVZlcmlmeTogdHJ1ZX0pDQogICAgICB9DQogICAgfSwNCiAgICB0YWJsZVJvd0NsYXNzTmFtZSh7cm93LCByb3dJbmRleH0pIHsNCiAgICAgIGlmICh0aGlzLnR5cGUgPT09ICJib29raW5nIiAmJiByb3cuc3FkU2hpcHBpbmdCb29raW5nU3RhdHVzID09IDApIHsNCiAgICAgICAgcmV0dXJuICJ1bmNvbmZpcm1lZCINCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnR5cGUgPT09ICJwc2EiICYmIHJvdy5wc2FWZXJpZnkgIT0gMSkgew0KICAgICAgICByZXR1cm4gInVuY29uZmlybWVkIg0KICAgICAgfQ0KICAgICAgcmV0dXJuICIiDQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgcmN0SWRzID0gcm93LnJjdElkIHx8IHRoaXMuaWRzDQogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbnoa7orqTliKDpmaTmk43kvZzljZXliJfooajnvJblj7fkuLpcIiIgKyByY3RJZHMgKyAiXCLnmoTmlbDmja7pobnvvJ8iLCAi5o+Q56S6Iiwge2N1c3RvbUNsYXNzOiAibW9kYWwtY29uZmlybSJ9KS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGRlbFJjdChyY3RJZHMpDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIikNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgic3lzdGVtL3JjdC9ib29raW5nRXhwb3J0Iiwgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgcmN0XyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0sDQogICAgc3RhZmZOb3JtYWxpemVyKG5vZGUpIHsNCiAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmICFub2RlLmNoaWxkcmVuLmxlbmd0aCkgew0KICAgICAgICBkZWxldGUgbm9kZS5jaGlsZHJlbg0KICAgICAgfQ0KICAgICAgbGV0IGwNCiAgICAgIGlmIChub2RlLnN0YWZmKSB7DQogICAgICAgIGlmIChub2RlLnN0YWZmLnN0YWZmRmFtaWx5TG9jYWxOYW1lID09IG51bGwgJiYgbm9kZS5zdGFmZi5zdGFmZkdpdmluZ0xvY2FsTmFtZSA9PSBudWxsKSB7DQogICAgICAgICAgaWYgKG5vZGUucm9sZS5yb2xlTG9jYWxOYW1lICE9IG51bGwpIHsNCiAgICAgICAgICAgIGwgPSBub2RlLnJvbGUucm9sZUxvY2FsTmFtZSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMobm9kZS5yb2xlLnJvbGVMb2NhbE5hbWUpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGwgPSBub2RlLmRlcHQuZGVwdExvY2FsTmFtZSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMobm9kZS5kZXB0LmRlcHRMb2NhbE5hbWUpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGwgPSBub2RlLnN0YWZmLnN0YWZmQ29kZSArICIgIiArIG5vZGUuc3RhZmYuc3RhZmZGYW1pbHlMb2NhbE5hbWUgKyBub2RlLnN0YWZmLnN0YWZmR2l2aW5nTG9jYWxOYW1lICsgIiAiICsgbm9kZS5zdGFmZi5zdGFmZkdpdmluZ0VuTmFtZSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMobm9kZS5zdGFmZi5zdGFmZkZhbWlseUxvY2FsTmFtZSArIG5vZGUuc3RhZmYuc3RhZmZHaXZpbmdMb2NhbE5hbWUpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGlmIChub2RlLnJvbGVJZCkgew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGlkOiBub2RlLnJvbGVJZCwNCiAgICAgICAgICBsYWJlbDogbCwNCiAgICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbiwNCiAgICAgICAgICBpc0Rpc2FibGVkOiBub2RlLnN0YWZmSWQgPT0gbnVsbCAmJiBub2RlLmNoaWxkcmVuID09IHVuZGVmaW5lZA0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGlkOiBub2RlLmRlcHRJZCwNCiAgICAgICAgICBsYWJlbDogbCwNCiAgICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbiwNCiAgICAgICAgICBpc0Rpc2FibGVkOiBub2RlLnN0YWZmSWQgPT0gbnVsbCAmJiBub2RlLmNoaWxkcmVuID09IHVuZGVmaW5lZA0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/booking", "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <el-row :gutter=\"20\" style=\"margin: 0;padding: 0;\">\r\n      <!--搜索条件-->\r\n      <el-col :span=\"showLeft\">\r\n        <el-form :model=\"queryParams\" class=\"query\" ref=\"queryForm\" size=\"mini\" :inline=\"true\" v-show=\"showSearch\">\r\n          <el-form-item label=\"单号\" prop=\"rctNo\">\r\n            <el-input v-model=\"queryParams.rctNo\" placeholder=\"操作单号\" @keydown.enter.native=\"handleQuery\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"操作\" prop=\"rctOpDate\">\r\n            <el-date-picker v-model=\"queryParams.rctOpDate\" clearable\r\n                            placeholder=\"操作日期\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"ATD\" prop=\"rctOpDate\">\r\n            <el-date-picker v-model=\"queryParams.ATDDate\" :default-time=\"['00:00:00', '23:59:59']\"\r\n                            clearable\r\n                            placeholder=\"ATD\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"ETD\" prop=\"rctOpDate\">\r\n            <el-date-picker v-model=\"queryParams.ETDDate\" clearable\r\n                            placeholder=\"ETD\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"紧急\" prop=\"urgencyDegree\">\r\n            <el-input v-model=\"queryParams.urgencyDegree\" placeholder=\"紧急程度\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"审核\" prop=\"pasVerifyTime\">\r\n            <el-date-picker v-model=\"queryParams.pasVerifyTime\" clearable\r\n                            placeholder=\"商务审核时间\"\r\n                            style=\"width:100%\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"标记\" prop=\"isOpAllotted\">\r\n            <el-select v-model=\"queryParams.isOpAllotted\" clearable placeholder=\"操作分配标记\" style=\"width: 100%\">\r\n              <el-option label=\"未分配\" value=\"0\">未分配</el-option>\r\n              <el-option label=\"已分配\" value=\"1\">已分配</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"客户\" prop=\"clientId\">\r\n            <company-select :multiple=\"false\" :no-parent=\"true\"\r\n                            :pass=\"queryParams.clientId\" :placeholder=\"'客户'\" :role-client=\"'1'\"\r\n                            :role-control=\"true\" :roleTypeId=\"1\" @return=\"queryParams.clientId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"放货\" prop=\"releaseTypeId\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\"\r\n                         :multiple=\"false\" :pass=\"queryParams.releaseTypeId\"\r\n                         :placeholder=\"'放货方式'\" :type=\"'releaseType'\"\r\n                         @return=\"queryParams.releaseTypeId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进度\" prop=\"processStatusId\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"queryParams.processStatusId\" :placeholder=\"'进度状态'\"\r\n                         :type=\"'processStatus'\" @return=\"queryParams.processStatusId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"物流\" prop=\"logisticsTypeId\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"queryParams.logisticsTypeId\" :placeholder=\"'物流类型'\"\r\n                         :type=\"'serviceType'\" @return=\"queryParams.logisticsTypeId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"启运\" prop=\"polIds\">\r\n            <location-select :multiple=\"true\" :no-parent=\"true\"\r\n                             :pass=\"queryParams.polIds\" :placeholder=\"'启运港'\" @return=\"queryParams.polIds=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"目的\" prop=\"destinationPortIds\">\r\n            <location-select :en=\"true\" :multiple=\"true\"\r\n                             :pass=\"queryParams.destinationPortIds\" :placeholder=\"'目的港'\"\r\n                             @return=\"queryParams.destinationPortIds=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"货量\" prop=\"revenueTons\">\r\n            <el-input v-model=\"queryParams.revenueTons\" placeholder=\"计费货量\" style=\"width: 100%\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"业务\" prop=\"salesId\">\r\n            <treeselect v-model=\"salesId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"belongList\" :show-count=\"true\" placeholder=\"业务员\"\r\n                        @open=\"loadSales\" @select=\"queryParams.salesId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.salesId = null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"助理\" prop=\"salesAssistantId\">\r\n            <treeselect v-model=\"salesAssistantId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"belongList\" :show-count=\"true\" placeholder=\"业务助理\"\r\n                        @open=\"loadSales\" @select=\"queryParams.salesAssistantId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.salesAssistantId=null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"标记\" prop=\"isPsaVerified\">\r\n            <el-select v-model=\"queryParams.isPsaVerified\" clearable placeholder=\"商务审核标记\" style=\"width: 100%\">\r\n              <el-option label=\"已审\" value=\"0\">已审</el-option>\r\n              <el-option label=\"未审\" value=\"1\">未审</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"商务\" prop=\"verifyPsaId\">\r\n            <treeselect v-model=\"verifyPsaId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :options=\"businessList\"\r\n                        :show-count=\"true\" placeholder=\"商务\"\r\n                        @open=\"loadBusinesses\" @select=\"queryParams.verifyPsaId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.verifyPsaId=null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"操作\" prop=\"opId\">\r\n            <treeselect v-model=\"opId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"opList.filter(v => {return v.role.roleLocalName=='操作员'})\"\r\n                        :show-count=\"true\" placeholder=\"操作员\"\r\n                        @open=\"loadOp\" @select=\"queryParams.opId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.opId = null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <!--顶部操作按钮-->\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <!--<el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:booking:add']\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>-->\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:booking:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <!--<el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:booking:remove']\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>-->\r\n\r\n          <el-col :span=\"1.5\">\r\n            <el-col :span=\"2\">\r\n              <el-popover\r\n                placement=\"right\"\r\n                trigger=\"click\"\r\n                width=\"400\"\r\n              >\r\n                <el-table :data=\"statisticsOp\">\r\n                  <el-table-column label=\"操作\" property=\"opName\" width=\"100\"></el-table-column>\r\n                  <el-table-column label=\"已派单\" property=\"number\" width=\"300\"></el-table-column>\r\n                </el-table>\r\n                <el-button v-if=\"type==='psa'\" slot=\"reference\" @click=\"handleStatistics\">派单统计</el-button>\r\n              </el-popover>\r\n            </el-col>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button @click=\"handleOpenAggregator\">数据汇总</el-button>\r\n            <el-dialog v-dialogDrag v-dialogDragWidth\r\n                       :visible.sync=\"openAggregator\" append-to-body width=\"80%\"\r\n            >\r\n              <data-aggregator-back-ground :aggregate-function=\"listAggregatorBooking\"\r\n                                           :config-type=\"'booking-agg'\" :data-source-type=\"'rct'\"\r\n                                           :field-label-map=\"fieldLabelMap\"/>\r\n            </el-dialog>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n        <!--表格-->\r\n        <el-table v-loading=\"loading\" :data=\"rctList\"\r\n                  stripe @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"dbclick\"\r\n        >\r\n          <el-table-column align=\"left\" label=\"序号\" type=\"index\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <el-badge\r\n                :value=\"getBadge(scope.row)\"\r\n                class=\"item\"\r\n              >\r\n                <div style=\"width: 15px\">{{ scope.$index + 1 }}</div>\r\n              </el-badge>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"操作单号\" prop=\"clientId\" show-overflow-tooltip width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"column-text highlight-text\" style=\"font-size: 18px;height: 23px\"\r\n              >{{ scope.row.rctNo }}\r\n              </div>\r\n              <div class=\"unHighlight-text\" style=\"width: 100px;\">{{\r\n                  parseTime(scope.row.rctCreateTime, \"{y}.{m}.{d}\") + \" \" + emergencyLevel(scope.row.emergencyLevel)\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"委托单位\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"column-text highlight-text\">\r\n                {{ scope.row.clientSummary ? scope.row.clientSummary.split(\"/\")[1] : null }}\r\n              </div>\r\n              <div class=\"unHighlight-text\" style=\"height: 23px\">\r\n                {{\r\n                  (scope.row.orderBelongsTo ? scope.row.orderBelongsTo : \"\") + \" \" + (scope.row.releaseType ? getReleaseType(scope.row.releaseType) : \"\") + \" \" + scope.row.paymentNode\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"物流类型\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" font-weight: 600;\">{{ scope.row.logisticsTypeEnName }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" color: #b7bbc2;height: 23px\">\r\n                  {{ scope.row.impExpType === \"1\" ? \"出口\" : \"\" }}\r\n                  {{ scope.row.impExpType === \"2\" ? \"进口\" : \"\" }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"启运港\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box \" style=\" font-size: 15px\">\r\n                  {{ scope.row.pol ? scope.row.pol.split(\"(\")[0] : scope.row.pol }}\r\n                </p>\r\n                <p class=\"unHighlight-text\">\r\n                  {{ scope.row.pol ? \"(\" + scope.row.pol.split(\"(\")[1] : \"\" }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"目的港\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"width: 95px;overflow: hidden\">\r\n                <p class=\"column-text bottom-box highlight-text\" style=\" \">\r\n                  {{ scope.row.destinationPort ? scope.row.destinationPort.split(\"(\")[0] : scope.row.destinationPort }}\r\n                </p>\r\n                <p class=\"unHighlight-text\">\r\n                  {{ scope.row.destinationPort ? \"(\" + scope.row.destinationPort.split(\"(\")[1] : \"\" }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"计费货量\" width=\"140\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box highlight-text\" style=\" \">{{ scope.row.revenueTon }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" \">{{ scope.row.goodsNameSummary }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"提单\" width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{\r\n                    (scope.row.blTypeCode ? scope.row.blTypeCode : \"\")\r\n                  }}\r\n                </p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" height: 23px\">\r\n                  {{ (scope.row.blFormCode ? scope.row.blFormCode : \"\") }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"订舱\" show-overflow-tooltip width=\"90\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text bottom-box\"\r\n                   style=\"text-overflow: ellipsis; white-space: nowrap;font-weight: 600;  font-size: 13px\"\r\n                >{{\r\n                    scope.row.carrierEnName\r\n                  }} <span class=\"column-text unHighlight-text\" style=\" font-size: 12px\">{{\r\n                    \"(\" + scope.row.agreementTypeCode + \")\"\r\n                    }}</span></p>\r\n                <p class=\"column-text top-box\" style=\"height: 23px \">{{ scope.row.supplierName }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"入仓与SO号\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{ scope.row.warehousingNo }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" \">{{ scope.row.soNo }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"提单与柜号\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{ scope.row.blNo }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" height: 23px\">{{ scope.row.sqdContainersSealsSum }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"订单状态\" show-overflow-tooltip width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{ processStatus(scope.row.processStatusId) }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"物流进度\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{\r\n                    (scope.row.podEta ? (\"ATD: \" + parseTime(scope.row.podEta, \"{m}-{d}\")) : (\"ETD: \" + parseTime(scope.row.etd, \"{m}-{d}\")))\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box\" style=\"height: 23px \">{{\r\n                    (scope.row.destinationPortEta ? (\"ATA: \" + parseTime(scope.row.destinationPortEta, \"{m}-{d}\")) : (\"ETA: \" + parseTime(scope.row.eta, \"{m}-{d}\")))\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"文件进度\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{\r\n                    (scope.row.docStatusA ? (scope.row.docStatusA.split(\":\")[0] + \": \" + moment(scope.row.docStatusA).format(\"MM.DD\")) : \"\")\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" \">\r\n                  {{\r\n                    (scope.row.docStatusB ? (scope.row.docStatusB.split(\":\")[0] + \": \" + moment(scope.row.docStatusB).format(\"MM.DD\")) : \"\")\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"收款\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{\r\n                    currency(scope.row.dnInRmb, {\r\n                      separator: \",\",\r\n                      symbol: \"¥\",\r\n                      precision: 2\r\n                    }).format()\r\n                  }}\r\n                </p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\"height: 23px \">{{\r\n                    currency(currency(scope.row.dnUsdBalance).value, {\r\n                      separator: \",\",\r\n                      symbol: \"$\",\r\n                      precision: 2\r\n                    }).format() + \" / \" + currency(currency(scope.row.dnRmbBalance).value, {\r\n                      separator: \",\",\r\n                      symbol: \"¥\",\r\n                      precision: 2\r\n                    }).format()\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"主服务付款\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box \">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{\r\n                    currency(scope.row.cnInRmb, {\r\n                      separator: \",\",\r\n                      symbol: \"¥\",\r\n                      precision: 2\r\n                    }).format()\r\n                  }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"业绩\" width=\"100\">\r\n            <template slot=\"header\" slot-scope=\"scope\">\r\n              <div style=\"margin-right: 5px\">\r\n                业绩\r\n              </div>\r\n            </template>\r\n            <template slot-scope=\"scope\">\r\n              <div :class=\"currency(scope.row.sqdProfitRmbSumVat).divide(scope.row.sqdDnRmbSumVat).value<0?'warning':''\"\r\n                   class=\"flex-box\"\r\n                   style=\"margin-right: 5px\"\r\n              >\r\n                <p class=\"column-text top-box\" style=\"height: 23px \">{{\r\n                    currency(scope.row.profitInRmb, {separator: \",\", symbol: \"¥\", precision: 2}).format()\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" height: 23px\">{{\r\n                    currency(scope.row.profitInRmb).divide(scope.row.cnInRmb).multiply(100).value + \"%\"\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"业务/助理\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\" style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box\" style=\"overflow: hidden\">{{\r\n                    (getName(scope.row.salesId) + (scope.row.salesId ? (\"/\" + getName(scope.row.salesAssistantId)) : getName(scope.row.salesAssistantId))) ? getName(scope.row.salesId) + (scope.row.salesId ? (\"/\" + getName(scope.row.salesAssistantId)) : getName(scope.row.salesAssistantId)) : null\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\"height: 23px \">\r\n                  {{ parseTime(scope.row.newBookingTime, \"{m}.{d}\") }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"商务审核\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\" style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box\" style=\" width: 55px;overflow: hidden \">{{\r\n                    getName(scope.row.verifyPsaId)\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" \">\r\n                  {{\r\n                    parseTime(scope.row.psaVerifyTime, \"{m}.{d}\") + \" \" + processStatus(scope.row.psaVerifyStatusId)\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"操作员\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{ getName(scope.row.opId) }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" \">\r\n                  {{\r\n                    parseTime(scope.row.rctCreateTime, \"{m}.{d}\") + \" \" + processStatus(scope.row.processStatusId)\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column v-if=\"type==='booking'\" align=\"left\" class-name=\"small-padding fixed-width\" label=\"操作\"\r\n                           width=\"50\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:booking:remove']\"\r\n                v-if=\"hiddenDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport booking from \"@/views/system/booking/index\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport store from \"@/store\"\r\nimport {\r\n  delRct,\r\n  listAggregatorBooking,\r\n  listAggregatorRct,\r\n  listRct,\r\n  listVerifyAggregatorList,\r\n  listVerifyList,\r\n  op\r\n} from \"@/api/system/rct\"\r\nimport pinyin from \"js-pinyin\"\r\nimport currency from \"currency.js\"\r\nimport {parseTime} from \"../../../utils/rich\"\r\nimport moment from \"moment/moment\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport DataAggregator from \"@/views/system/DataAggregator/index.vue\"\r\nimport {rctFieldLabelMap} from \"@/config/rctFieldLabelMap\"\r\nimport DataAggregatorBackGround from \"@/views/system/DataAggregatorBackGround/index.vue\"\r\n\r\nexport default {\r\n  name: \"bookingList\",\r\n  components: {DataAggregatorBackGround, DataAggregator, CompanySelect, Treeselect},\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 操作单列表表格数据\r\n      salesId: null,\r\n      verifyPsaId: null,\r\n      salesAssistantId: null,\r\n      opId: null,\r\n      belongList: [],\r\n      opList: [],\r\n      businessList: [],\r\n      rctList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      statisticsOp: [],\r\n      // 表单校验\r\n      rules: {},\r\n      openAggregator: false,\r\n      fieldLabelMap: rctFieldLabelMap,\r\n      aggregatorRctList: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    let load = false\r\n    if (this.$route.query.no) {\r\n      this.queryParams.newBookingNo = this.$route.query.no\r\n      this.getList().then(() => {\r\n        load = true\r\n      })\r\n    } else {\r\n      this.getList().then(() => {\r\n        load = true\r\n      })\r\n    }\r\n    if (load) {\r\n      this.loadSales()\r\n      this.loadOp()\r\n      this.loadBusinesses()\r\n    }\r\n    this.loadStaffList()\r\n  },\r\n  computed: {\r\n    moment() {\r\n      return moment\r\n    }\r\n  },\r\n  props: [\"type\"],\r\n  methods: {\r\n    listAggregatorBooking(params) {\r\n      params.config = JSON.stringify(params.config)\r\n      this.queryParams.params = params;\r\n      return listAggregatorBooking(this.queryParams)\r\n    },\r\n    handleOpenAggregator() {\r\n      this.openAggregator = true\r\n    },\r\n\r\n    handleStatistics() {\r\n      op().then(response => {\r\n        this.statisticsOp = response.data\r\n      })\r\n    },\r\n    parseTime,\r\n    getBadge(row) {\r\n      if (((this.type === \"booking\" && row.sqdShippingBookingStatus == \"0\") || (this.type === \"psa\" && row.sqdShippingBookingStatus == \"1\" && row.psaVerify == \"0\"))) {\r\n        return \"new\"\r\n      } else {\r\n        return \"\"\r\n      }\r\n    },\r\n    hiddenDelete(row) {\r\n      if (this.type === \"booking\" && row.sqdShippingBookingStatus == 0) {\r\n        return true\r\n      }\r\n      if (this.type === \"psa\") {\r\n        return false\r\n      }\r\n    },\r\n    currency,\r\n    getReleaseType(id) {\r\n      if (id == 1) return \"月结\"\r\n      if (id == 2) return \"押放\"\r\n      if (id == 3) return \"票结\"\r\n      if (id == 4) return \"签放\"\r\n      if (id == 5) return \"订金\"\r\n      if (id == 6) return \"预付\"\r\n      if (id == 7) return \"扣货\"\r\n      if (id == 9) return \"居间\"\r\n      return \"\"\r\n    },\r\n    handleVerify(row) {\r\n      this.$tab.openPage(\"操作单\", \"/opprocess/opdetail\", {rId: this.ids[0], psaVerify: true})\r\n    },\r\n    getName(id) {\r\n      if (id) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff) {\r\n          return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName\r\n        }\r\n      }\r\n    },\r\n    sqdDocDeliveryWay(type) {\r\n      if (type == 1) return \" 境外快递\"\r\n      if (type == 2) return \" 境内快递\"\r\n      if (type == 3) return \" 跑腿\"\r\n      if (type == 4) return \" 业务送达\"\r\n      if (type == 5) return \" 客户自取\"\r\n      if (type == 6) return \" QQ\"\r\n      if (type == 7) return \" 微信\"\r\n      if (type == 8) return \" 电邮\"\r\n      if (type == 9) return \" 公众号\"\r\n      if (type == 10) return \" 承运人系统\"\r\n      if (type == 11) return \" 订舱口系统\"\r\n      if (type == 12) return \" 第三方系统\"\r\n    },\r\n    logisticsPaymentTerms(v) {\r\n      if (v == 1) return \"月结\"\r\n      if (v == 2) return \"押单\"\r\n      if (v == 3) return \"此票结清\"\r\n      if (v == 4) return \"经理签单\"\r\n      if (v == 5) return \"预收订金\"\r\n      if (v == 6) return \"全额预付\"\r\n      if (v == 7) return \"扣货\"\r\n      if (v == 8) return \"背靠背\"\r\n    },\r\n    emergencyLevel(v) {\r\n      if (v == 0) return \"预定\"\r\n      if (v == 1) return \"当天\"\r\n      if (v == 2) return \"常规\"\r\n      if (v == 3) return \"紧急\"\r\n      if (v == 4) return \"立即\"\r\n    },\r\n    difficultyLevel(v) {\r\n      if (v == 0) return \"简易\"\r\n      if (v == 1) return \"标准\"\r\n      if (v == 2) return \"高级\"\r\n      if (v == 3) return \"特别\"\r\n    },\r\n    processStatus(v) {\r\n      if (v == 1) return \"等待\"\r\n      if (v == 2) return \"进行\"\r\n      if (v == 3) return \"变更\"\r\n      if (v == 4) return \"异常\"\r\n      if (v == 5) return \"质押\"\r\n      if (v == 6) return \"确认\"\r\n      if (v == 7) return \"完成\"\r\n      if (v == 8) return \"取消\"\r\n      if (v == 9) return \"驳回\"\r\n      if (v == 10) return \"回收\"\r\n    },\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch(\"getSalesList\").then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        store.dispatch(\"getBusinessesList\").then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    loadOp() {\r\n      if (this.$store.state.data.opList.length == 0 || this.$store.state.data.redisList.opList) {\r\n        store.dispatch(\"getOpList\").then(() => {\r\n          this.opList = this.$store.state.data.opList\r\n        })\r\n      } else {\r\n        this.opList = this.$store.state.data.opList\r\n      }\r\n    },\r\n    loadStaffList() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList\r\n      }\r\n    },\r\n    /** 查询操作单列表列表 */\r\n    async getList() {\r\n      this.loading = true\r\n      if (this.type === \"psa\") {\r\n        this.queryParams.sqdShippingBookingStatus = 1\r\n      }\r\n      /* if (this.type === 'booking')  {\r\n        this.queryParams.sqdShippingBookingStatus = 0\r\n      } */\r\n\r\n      this.queryParams.permissionLevel = this.$store.state.user.permissionLevelList.C\r\n      await listVerifyList(this.queryParams).then(response => {\r\n        this.rctList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.queryParams.searchValue = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.queryParams.searchValue = null\r\n      this.getList()\r\n    },\r\n    // handleStatusChange(row) {\r\n    //   let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n    //   this.$modal.confirm('确认要\"' + text + '吗？').then(function () {\r\n    //     return changeStatus(row.rctId, row.status);\r\n    //   }).then(() => {\r\n    //     this.$modal.msgSuccess(text + \"成功\");\r\n    //   }).catch(function () {\r\n    //     row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n    //   });\r\n    // },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.rctId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.$tab.openPage(\"操作单\", \"/opprocess/opdetail\", {})\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.$tab.openPage(\"操作单\", \"/opprocess/opdetail\", {rId: row.rctId})\r\n    },\r\n    dbclick(row, column, event) {\r\n      // 已经订舱了的不可以修改\r\n      /* if (this.type === 'booking' && row.sqdShippingBookingStatus == 1) {\r\n        return\r\n      } */\r\n      /* if (this.type === 'psa' && row.psaVerifyStatusId == 1) {\r\n        return\r\n      } */\r\n      if (this.type === \"booking\") {\r\n        this.$tab.openPage(\"订舱单明细\", \"/salesquotation/bookingDetail\", {rId: row.rctId, booking: true})\r\n      }\r\n      if (this.type === \"psa\") {\r\n        this.$tab.openPage(\"商务审核明细\", \"/psaVerify/psaDetail\", {rId: row.rctId, psaVerify: true})\r\n      }\r\n    },\r\n    tableRowClassName({row, rowIndex}) {\r\n      if (this.type === \"booking\" && row.sqdShippingBookingStatus == 0) {\r\n        return \"unconfirmed\"\r\n      }\r\n      if (this.type === \"psa\" && row.psaVerify != 1) {\r\n        return \"unconfirmed\"\r\n      }\r\n      return \"\"\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const rctIds = row.rctId || this.ids\r\n      this.$confirm(\"是否确认删除操作单列表编号为\\\"\" + rctIds + \"\\\"的数据项？\", \"提示\", {customClass: \"modal-confirm\"}).then(function () {\r\n        return delRct(rctIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/rct/bookingExport\", {\r\n        ...this.queryParams\r\n      }, `rct_${new Date().getTime()}.xlsx`)\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.column-text {\r\n  margin: 0;\r\n  padding: 0;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.highlight-text {\r\n  font-weight: 600;\r\n  font-size: 15px\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n\r\n::v-deep .el-table .success-row {\r\n  background: #f8f8f9;\r\n}\r\n\r\n.red {\r\n  color: rgb(103, 194, 58);\r\n}\r\n\r\n.item {\r\n  margin-top: 10px;\r\n  margin-right: 40px;\r\n}\r\n\r\n::v-deep .el-badge__content.is-fixed {\r\n  font-size: 12px;\r\n  top: 0px;\r\n  right: 2px;\r\n}\r\n</style>\r\n"]}]}