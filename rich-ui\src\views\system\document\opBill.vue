<template>
  <div class="container" style="margin: 15px;width: auto">
    <el-form ref="form" :model="form" :rules="rules" class="edit" label-width="63px">
      <el-row>
        <el-col>
          <!--主表信息-->
          <el-row :gutter="10">
            <el-col :span="3">
              <div class="rs-application-title">
                {{ op ? "操作单" : "订舱单" }}
              </div>
            </el-col>
            <el-col :span="3">
              <input v-model="form.rctNo" class="rct-no" disabled
                     placeholder="操作单号(RCT)" size="large" @focus="generateRct(false)"
              />
              <el-form-item label="操作日期" prop="rctOpDate">
                <el-date-picker v-model="form.rctCreateTime" clearable
                                placeholder="操作日期"
                                style="width:100%"
                                type="datetime"
                                :class="'disable-form'" disabled
                                value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item label="紧急程度" prop="urgencyDegree">
                <tree-select :class="psaVerify|| disabled?'disable-form':''" :flat="false" :multiple="false"
                             :disabled="psaVerify || disabled" :pass="form.emergencyLevel" :placeholder="'紧急程度'"
                             :type="'emergencyLevel'" @return="form.emergencyLevel=$event"
                />
              </el-form-item>
              <el-form-item label="简易程度" prop="orderDifficulty">
                <tree-select :class="psaVerify|| disabled?'disable-form':''" :flat="false" :multiple="false"
                             :disabled="psaVerify || disabled" :pass="form.difficultyLevel" :placeholder="'简易程度'"
                             :type="'difficultyLevel'" @return="form.difficultyLevel=$event"
                />
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item label="订单所属" prop="orderBelongsTo">
                <tree-select :class="psaVerify|| disabled?'disable-form':''" :disabled="psaVerify || disabled"
                             :flat="false" :multiple="false" :pass="form.orderBelongsTo" :placeholder="'收付路径'"
                             :type="'rsPaymentTitle'" @return="form.orderBelongsTo=$event"
                />
              </el-form-item>
              <el-form-item label="放货方式">
                <tree-select :d-load="false" :disabled="psaVerify || disabled" :flat="false"
                             :multiple="false" :pass="form.releaseType"
                             :placeholder="'放货方式'" :type="'releaseType'"
                             :class="psaVerify || disabled?'disable-form':''" @return="form.releaseType=$event"
                />
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <div class="form-box">
                <div class="form-item">
                  物流文件:
                </div>
                <div class="form-content">
                  {{ form.transportStatusA }}
                </div>
              </div>
              <div class="form-box">
                <div class="form-item">
                  付款节点:
                </div>
                <div class="form-content">
                  {{ form.paymentNode }}
                </div>
              </div>
            </el-col>
            <el-col :span="3">
              <div class="form-box">
                <div class="form-item">
                  付款进度:
                </div>
                <div class="form-content">
                  {{ }}
                </div>
              </div>
              <!--<div class="form-box">
                <div class="form-item">
                  状态日期:
                </div>
                <div class="form-content">
                  {{ form.processStatusTime + " " + (form.processStatusId ? getProcess(form.processStatusId) : "") }}
                </div>
              </div>-->
              <div class="form-box">
                <div class="form-item">
                  收款进度:
                </div>
                <div class="form-content">
                  {{ }}
                </div>
              </div>
            </el-col>
          </el-row>
          <div class="parting-bar"/>
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="委托单位" prop="clientId">
                <el-input v-model="form.clientName" :disabled="psaVerify || disabled" placeholder="委托单位"
                          :class="psaVerify || disabled?'disable-form':''" @focus="selectCompany"
                />
              </el-form-item>
              <el-col :span="12" style="padding-left: 0;">
                <el-form-item label="客户单号" prop="clientJobNo">
                  <el-input v-model="form.clientJobNo" :class="psaVerify || disabled?'disable-form':''"
                            :disabled="psaVerify || disabled"
                            placeholder="客户单号"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" style="padding-right: 0;">
                <el-form-item label="合同号" prop="clientInvoiceNo">
                  <el-input v-model="form.clientContractNo" :class="psaVerify || disabled?'disable-form':''"
                            :disabled="psaVerify || disabled"
                            placeholder="合同号"
                  />
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="6">
              <el-col :span="12" style="padding: 0;">
                <el-form-item label="联系人" prop="clientContact">
                  <el-input v-model="form.clientContact" :class="psaVerify || disabled?'disable-form':''"
                            :disabled="psaVerify || disabled"
                            placeholder="联系人"
                  />
                </el-form-item>
                <el-form-item label="发票号" prop="clientInvoiceNo">
                  <el-input v-model="form.clientInvoiceNo" :class="psaVerify || disabled?'disable-form':''"
                            :disabled="psaVerify || disabled"
                            placeholder="(委托单位)发票号"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="电话" prop="clientContactTel">
                  <el-input v-model="form.clientContactTel" :class="psaVerify || disabled?'disable-form':''"
                            :disabled="psaVerify || disabled"
                            placeholder="电话"
                  />
                </el-form-item>
                <el-form-item label="客户角色" prop="clientContactTel">
                  <!--<el-input v-model="form.clientRole" :disabled="psaVerify || disabled" placeholder="电话"/>-->
                  <tree-select :flat="false" :multiple="true" :pass="form.roleIds" :type="'companyRole'"
                               :disabled="psaVerify || disabled"
                               :class="psaVerify || disabled?'disable-form':''" class="sss" @return="getCompanyRoleIds"
                  />
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="6">
              <el-form-item label="邮箱" prop="clientContactEmail">
                <el-input v-model="form.clientContactEmail" :class="psaVerify || disabled?'disable-form':''"
                          :disabled="psaVerify || disabled"
                          placeholder="邮箱"
                />
              </el-form-item>
              <el-form-item label="关联工厂" prop="relationClientIdList">
                <company-select :class="psaVerify || disabled?'disable-form':''" :load-options="companyList"
                                :disabled="psaVerify || disabled" :multiple="true" :no-parent="true"
                                :pass="RelationClientIdList"
                                :role-control="!checkRole(['Operator'])"
                                @deselect="handleDeselectCompanyIds"
                                @return="selectRelationClient($event)"
                                @returnData="handleSelectCompanyIds($event)"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div class="parting-bar"/>
          <el-row>
            <el-row :gutter="10">
              <el-col :span="18">
                <el-col :span="4">
                  <el-form-item label="货名概要" prop="goodsNameSummary">
                    <el-input v-model="form.goodsNameSummary" :class="psaVerify || disabled?'disable-form':''"
                              :disabled="psaVerify || disabled"
                              placeholder="货名概要"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="货物特征" prop="cargoTypeIds">
                    <tree-select :class="psaVerify || disabled?'disable-form':''" :disabled="psaVerify || disabled"
                                 :flat="false" :multiple="true" :pass="cargoTypeCodes"
                                 :placeholder="'货物特征'" :type="'cargoTypeCode'"
                                 @return="cargoTypeCodes=$event"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="件数" prop="packageQuantity">
                    <div style="display: flex">
                      <el-input-number v-model="form.packageQuantity" :controls="false"
                                       :disabled="psaVerify || disabled"
                                       placeholder="总件数" style="width: 100%;flex: 2"
                                       :class="psaVerify || disabled?'disable-form':''"
                      />
                      <el-input class="disable-form" disabled style="flex: 1" value="PKG"/>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="毛重" prop="grossWeight">
                    <div style="display: flex">
                      <el-input v-model="grossWeight" :disabled="psaVerify || disabled" placeholder="总毛重"
                                style="width: 64%;flex: 2" @change.native="autoCompletion('grossWeight')"
                                :class="psaVerify || disabled?'disable-form':''"
                      />
                      <el-input class="disable-form" disabled style="flex: 1" value="KGS"/>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="体积" prop="volume">
                    <div style="display: flex">
                      <el-input-number v-model="form.goodsVolume" :controls="false" :disabled="psaVerify || disabled"
                                       :precision="2"
                                       :step="0.01" placeholder="体积" style="width: 64%;flex: 2"
                                       :class="psaVerify || disabled?'disable-form':''"
                      />
                      <el-input class="disable-form" disabled style="flex: 1" value="CBM"/>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="货值" prop="goodsValue">
                    <div style="display: flex">
                      <el-input v-model="goodsValue" :disabled="psaVerify || disabled" placeholder="总货值"
                                style="width: 64%" @change.native="autoCompletion('goodsValue')"
                                :class="psaVerify || disabled?'disable-form':''"
                      />
                      <tree-select :disabled="psaVerify || disabled" :pass="form.goodsCurrencyCode"
                                   :placeholder="'货值币种'" :type="'currency'" style="width: 36%"
                                   :class="psaVerify || disabled?'disable-form':''"
                                   @return="form.goodsCurrencyCode=$event"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="18">
                <el-col :span="4">
                  <el-form-item label="物流类型" prop="logisticsTypeId">
                    <tree-select :class="psaVerify || disabled?'disable-form':''" :dbn="true"
                                 :disabled="psaVerify || disabled" :flat="false"
                                 :main="true" :multiple="false" :pass="form.logisticsTypeId"
                                 :placeholder="'物流类型'" :type="'mainServiceType'"
                                 @return="form.logisticsTypeId=$event"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="进出口" prop="impExpType">
                    <el-select v-model="form.impExpType" :class="psaVerify || disabled?'disable-form':''"
                               :disabled="psaVerify || disabled" clearable
                               filterable
                               placeholder="进出口" style="width: 100%"
                    >
                      <el-option label="出口" value="1">出口</el-option>
                      <el-option label="进口" value="2">进口</el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="运输条款" prop="logisticsTermsId">
                    <tree-select :class="psaVerify || disabled?'disable-form':''" :disabled="psaVerify || disabled"
                                 :flat="false" :multiple="false"
                                 :pass="form.logisticsTerms" :placeholder="'运输条款'"
                                 :type="'transportationTerms'" @return="form.logisticsTerms=$event"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="贸易条款" prop="tradingTermsId">
                    <tree-select :class="psaVerify || disabled?'disable-form':''" :disabled="psaVerify || disabled"
                                 :flat="false" :multiple="false"
                                 :pass="form.tradingTerms" :placeholder="'贸易条款'"
                                 :type="'tradingTerms'" @return="form.tradingTerms=$event"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="收汇方式" prop="tradingPaymentChannelId">
                    <tree-select :class="psaVerify || disabled?'disable-form':''" :disabled="psaVerify || disabled"
                                 :flat="false" :multiple="false"
                                 :pass="form.tradingPaymentChannel" :placeholder="'贸易付款方式'"
                                 :type="'paymentChannels'" @return="form.tradingPaymentChannel=$event"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="运费付于" prop="tradingPaymentChannelId">
                    <tree-select :disabled="psaVerify || disabled" :flat="false"
                                 :multiple="false" :pass="form.freightPaidWayCode"
                                 :class="psaVerify || disabled?'disable-form':''" :placeholder="'运费付于'"
                                 :type="'freightPaidWay'" @return="form.freightPaidWayCode=$event"
                    />
                  </el-form-item>
                </el-col>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="18">
                <el-col :span="4">
                  <el-form-item label="启运港" prop="polId">
                    <location-select :check-port="logisticsType" :class="psaVerify || disabled?'disable-form':''"
                                     :disabled="psaVerify || disabled" :en="true"
                                     :load-options="locationOptions" :multiple="false" :pass="form.polId"
                                     :placeholder="'启运港'" @return="form.polId=$event"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="中转港" prop="transitPortId">
                    <location-select :check-port="logisticsType" :class="psaVerify || disabled?'disable-form':''"
                                     :disabled="psaVerify || disabled" :en="true" :load-options="locationOptions"
                                     :multiple="false" :pass="form.transitPortId"
                                     :placeholder="'中转港'"
                                     @return="form.transitPortId=$event"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="卸货港" prop="podId" style="margin: 0;padding: 0;">
                    <location-select :check-port="logisticsType" :class="psaVerify || disabled?'disable-form':''"
                                     :disabled="psaVerify || disabled" :en="true"
                                     :load-options="locationOptions" :multiple="false" :pass="form.podId"
                                     :placeholder="'卸货港'" @return="form.podId=$event"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="目的港" prop="destinationPortId">
                    <location-select :check-port="logisticsType" :disabled="psaVerify || disabled" :en="true"
                                     :load-options="locationOptions" :multiple="false"
                                     :pass="form.destinationPortId" :placeholder="'目的港'"
                                     :class="psaVerify || disabled?'disable-form':''"
                                     @return="form.destinationPortId=$event"
                    />
                  </el-form-item>
                </el-col>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="18">
                <el-col :span="6">
                  <el-form-item label="计费货量" prop="revenueTon">
                    <el-input v-model="form.revenueTon" :class="psaVerify || disabled?'disable-form':''"
                              :disabled="psaVerify || disabled"
                              placeholder="计费货量" style="width: 100%"
                              @focus="editRevenueTon"
                    />
                    <el-dialog
                      v-dialogDrag
                      v-dialogDragWidth
                      :close-on-click-modal="false" :modal-append-to-body="false"
                      :visible.sync="openGenerateRevenueTons" append-to-body
                      title="修改计费货量" width="350px"
                    >
                      <el-row>
                        <el-col :span="23">
                          <el-col :span="12">
                            <el-input-number v-model="form.countA" :controls="false" :min="1"
                                             placeholder="数量" style="width: 100%;"
                            />
                            <el-input-number v-model="form.countB" :controls="false" :min="1"
                                             placeholder="数量" style="width: 100%;"
                            />
                            <el-input-number v-model="form.countC" :controls="false" :min="1"
                                             placeholder="数量" style="width: 100%;"
                            />
                          </el-col>
                          <el-col :span="12">
                            <tree-select :pass="form.unitCodeA" :type="'unit'" placeholder="选择柜型"
                                         @returnData="form.unitCodeA = $event.unitCode"
                            />
                            <tree-select :pass="form.unitCodeB" :type="'unit'" placeholder="选择柜型"
                                         @returnData="form.unitCodeB = $event.unitCode"
                            />
                            <tree-select :pass="form.unitCodeC" :type="'unit'" placeholder="选择柜型"
                                         @returnData="form.unitCodeC = $event.unitCode"
                            />
                          </el-col>
                        </el-col>
                      </el-row>
                      <span slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="revenueTonConfirm">确 定</el-button>
                      </span>
                    </el-dialog>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="箱型特征">
                    <tree-select :class="psaVerify || disabled?'disable-form':''" :dbn="true"
                                 :disabled="psaVerify || disabled"
                                 :disable-branch-nodes="true" :flat="false" :multiple="true"
                                 :pass="ctnrTypeCodeIds" :placeholder="'服务类型'" :type="'ctnrType'"
                                 @return="ctnrTypeCodeIds=$event"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="服务列表">
                    <tree-select :class="psaVerify || disabled?'disable-form':''" :dbn="true"
                                 :disabled="psaVerify || disabled"
                                 :flat="false" :multiple="true"
                                 :pass="form.serviceTypeIds" :placeholder="'服务类型'" :type="'serviceType'"
                                 @return="getServiceTypeList"
                    />
                  </el-form-item>
                </el-col>
              </el-col>
            </el-row>
          </el-row>
          <div class="parting-bar"/>
          <el-row :gutter="10">
            <el-row :gutter="10">
              <el-col :span="3">
                <el-form-item label="提单类型">
                  <tree-select :class="psaVerify || disabled?'disable-form':''" :disabled="psaVerify || disabled"
                               :flat="false"
                               :multiple="false" :pass="form.blTypeCode"
                               :placeholder="'提单类型'" :type="'blType'" @return="form.blTypeCode=$event"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="出单方式" prop="blFormCode">
                  <tree-select :class="psaVerify || disabled?'disable-form':''" :disabled="psaVerify || disabled"
                               :flat="false" :multiple="false" :pass="form.blFormCode"
                               :placeholder="'出单方式'" :type="'blForm'" @return="form.blFormCode=$event"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="交单方式" prop="goodsNameSummary">
                  <tree-select :class="psaVerify || disabled?'disable-form':''" :disabled="psaVerify || disabled"
                               :flat="false"
                               :multiple="false" :pass="form.sqdDocDeliveryWay"
                               :placeholder="'货代单交单方式'"
                               :type="'docReleaseWay'" @return="form.sqdDocDeliveryWay=$event"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <div style="height: 32px;width: 50%; padding: 0 0 4px;display: flex">
                  <el-checkbox-button v-model="form.noTransferAllowed" :disabled="psaVerify || disabled"
                                      label="基础信息"
                                      style="width: 100px"
                  >
                    <i
                      :class="form.noTransferAllowed?'el-icon-check':'el-icon-minus'"
                    /> {{ form.noTransferAllowed ? "不可中转" : "接受中转" }}
                  </el-checkbox-button>
                  <el-checkbox-button v-model="form.noDividedAllowed" :disabled="psaVerify || disabled"
                                      label="订单信息"
                                      style="width: 100px"
                  >
                    <i
                      :class="form.noDividedAllowed?'el-icon-check':'el-icon-minus'"
                    /> {{ form.noDividedAllowed ? "不可分批" : "接受分批" }}
                  </el-checkbox-button>
                  <el-checkbox-button v-model="form.noAgreementShowed" :disabled="psaVerify || disabled"
                                      label="分支信息"
                                      style="width: 100px"
                  ><i
                    :class="form.noAgreementShowed?'el-icon-check':'el-icon-minus'"
                  /> {{ form.noAgreementShowed ? "不可套约" : "接受套约" }}
                  </el-checkbox-button>
                  <el-checkbox-button v-model="form.isCustomsIntransitShowed" :disabled="psaVerify || disabled"
                                      label="分支信息"
                                      style="width: 100px"
                  ><i
                    :class="form.isCustomsIntransitShowed?'el-icon-check':'el-icon-minus'"
                  /> 属地清关
                  </el-checkbox-button>
                </div>
              </el-col>
              <el-col :span="5">
                <el-form-item label="唛头" prop="goodsNameSummary">
                  <el-input v-model="form.shippingMark" :class="psaVerify || disabled?'disable-form':''"
                            :disabled="psaVerify || disabled"
                            show-word-limit style="padding: 0;margin: 0;"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row style="margin-bottom: 3px">
              <el-col :span="4">
                <div class="select-label">发货人
                  <div class="select-btn-group">
                    <el-button style="color: blue" type="text" @click="handleAddCommon('common')">[↗]</el-button>
                    <el-button style="color: blue" type="text" @click="openCommonUsed">[...]</el-button>
                  </div>
                </div>
                <el-input v-model="form.bookingShipper" :autosize="{ minRows: 5, maxRows: 8}"
                          :class="psaVerify || disabled?'disable-form':''"
                          :disabled="psaVerify || disabled" maxlength="500" show-word-limit
                          style="padding: 0;margin: 0;"
                          type="textarea"
                />
              </el-col>
              <el-col :span="4">
                <div class="custom-form-label">收货人</div>
                <el-input v-model="form.bookingConsignee" :autosize="{ minRows: 5, maxRows: 8}"
                          :class="psaVerify || disabled?'disable-form':''"
                          :disabled="psaVerify || disabled" maxlength="500" show-word-limit
                          style="padding: 0;margin: 0;"
                          type="textarea"
                />
              </el-col>
              <el-col :span="4">
                <div class="custom-form-label">通知人</div>
                <el-input v-model="form.bookingNotifyParty" :autosize="{ minRows: 5, maxRows: 8}"
                          :class="psaVerify || disabled?'disable-form':''" :disabled="psaVerify || disabled"
                          maxlength="500" show-word-limit style="padding: 0;margin: 0;"
                          type="textarea"
                />
              </el-col>
              <el-col :span="3">
                <div class="custom-form-label" style="border-right: solid 1px #DCDFE6;">第二通知人</div>
                <el-input v-model="form.secondBookingNotifyParty"
                          :autosize="{ minRows: 5, maxRows: 8}" :class="psaVerify || disabled?'disable-form':''"
                          :disabled="psaVerify || disabled" maxlength="500"
                          show-word-limit style="padding: 0;margin: 0;" type="textarea"
                />
              </el-col>
              <el-col :span="3">
                <div class="custom-form-label" style="border-right: solid 1px #DCDFE6;">代理</div>
                <el-input v-model="form.bookingAgent"
                          :class="psaVerify || disabled?'disable-form':''" :disabled="psaVerify || disabled"
                          :autosize="{ minRows: 5, maxRows: 8}" maxlength="500"
                          show-word-limit style="padding: 0;margin: 0;" type="textarea"
                />
              </el-col>
            </el-row>
          </el-row>
          <div class="parting-bar"/>
          <div style="margin-top: 10px;margin-bottom: 10px">
            <!--<el-checkbox-button v-if="!booking" v-model="serviceInfo" label="分支信息" style="width: 100px"><i
                :class="serviceInfo?'el-icon-check':'el-icon-minus'"
            /> 服务信息
            </el-checkbox-button>-->
            <el-checkbox-button v-model="branchInfo" label="分支信息" style="width: 100px"><i
              :class="branchInfo?'el-icon-check':'el-icon-minus'"
            /> 基础信息
            </el-checkbox-button>
            <el-checkbox-button v-model="logisticsInfo" label="物流进度" style="width: 100px"><i
              :class="logisticsInfo?'el-icon-check':'el-icon-minus'"
            /> 物流进度
            </el-checkbox-button>
            <!--<el-checkbox-button v-model="docInfo" label="文件列表" style="width: 100px"><i
                :class="docInfo?'el-icon-check':'el-icon-minus'"
            /> 文件列表
            </el-checkbox-button>-->
            <el-checkbox-button v-model="chargeInfo" label="费用列表" style="width: 100px"><i
              :class="chargeInfo?'el-icon-check':'el-icon-minus'"
            /> 费用列表
            </el-checkbox-button>
            <el-checkbox-button v-model="auditInfo" label="审核信息" style="width: 100px"><i
              :class="auditInfo?'el-icon-check':'el-icon-minus'"
            /> 审核信息
            </el-checkbox-button>
          </div>
          <div class="parting-bar"/>
          <!--文件进度-->
          <el-row>
            <el-form ref="fileProcessForm" :model="form" class="file-process" label-position="top" label-width="80px">
              <el-row>
                <el-col :span="1">
                  <el-form-item label="期望赎单">
                    <el-input
                      v-model="form.opAskingBlGetTime"
                      style="width: 100%"
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="1">
                  <el-form-item label="期望放单">
                    <el-input
                      v-model="form.opAskingBlReleaseTime"
                      style="width: 100%"
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="1">
                  <el-form-item label="预计赎单">
                    <el-input
                      v-model="form.accPromissBlGetTime"
                      style="width: 100%"
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="1">
                  <el-form-item label="提单赎回">
                    <el-input
                      v-model="form.actualBlGotTime"
                      style="width: 100%"
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="1">
                  <el-form-item label="预计放单" label-width="100%">
                    <el-input
                      v-model="form.accPromissBlReleaseTime"
                      style="width: 100%"
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="1">
                  <el-form-item label="已放提单">
                    <el-input
                      v-model="form.actualBlReleaseTime"
                      style="width: 100%"
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="1">
                  <el-form-item label="已发代理">
                    <el-input
                      v-model="form.agentNoticeTime"
                      style="width: 100%"
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :offset="2" :span="4">
                  <el-form-item label="ATD" prop="revenueTons">
                    <el-date-picker v-model="form.podEta"
                                    clearable
                                    placeholder="ATA" style="width:100%"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="ATA" prop="revenueTons">
                    <el-date-picker v-model="form.destinationPortEta"
                                    clearable
                                    placeholder="ATA" style="width:100%"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>

              </el-row>
            </el-form>
          </el-row>
        </el-col>
      </el-row>

      <!--提单信息-->
      <bill-of-lading-info
        :bookingMessageForm="bookingMessageForm"
        :bookingMessageList="bookingMessageList"
        :bookingMessageStatus="bookingMessageStatus"
        :openBookingMessage="openBookingMessage"
        :auditInfo="auditInfo"
        :branchInfo="branchInfo"
        :chargeInfo="chargeInfo"
        :companyList="companyList"
        :disabled="disabled"
        :form="form"
        :logisticsInfo="logisticsInfo"
        :psaVerify="psaVerify"
        :rsClientMessage="rsClientMessage"
        :rsClientMessagePayableRMB="rsClientMessagePayableRMB"
        :rsClientMessagePayableTaxRMB="rsClientMessagePayableTaxRMB"
        :rsClientMessagePayableTaxUSD="rsClientMessagePayableTaxUSD"
        :rsClientMessagePayableUSD="rsClientMessagePayableUSD"
        :rsClientMessageProfitRMB="rsClientMessageProfitRMB"
        :rsClientMessageProfitTaxRMB="rsClientMessageProfitTaxRMB"
        :rsClientMessageProfitTaxUSD="rsClientMessageProfitTaxUSD"
        :rsClientMessageProfitUSD="rsClientMessageProfitUSD"
        :rsClientMessageReceivableRMB="rsClientMessageReceivableRMB"
        :rsClientMessageReceivableTaxRMB="rsClientMessageReceivableTaxRMB"
        :rsClientMessageReceivableTaxUSD="rsClientMessageReceivableTaxUSD"
        :rsClientMessageReceivableUSD="rsClientMessageReceivableUSD"
        @confirmed="confirmed"
        @copyFreight="copyFreight"
        @getBillOfLading="getBillOfLading"
        @getChargeListBill="getChargeListBill"
        @getOpBill="getOpBill"
        @handleAddCommon="handleAddCommon"
        @handleProfit="handleProfit"
        @handleReceiveSelected="handleReceiveSelected"
        @handleSelectionChange="handleSelectionChange"
        @openChargeSelect="openChargeSelect"
        @openReleaseUsed="openReleaseUsed"
        @rsClientMessageCharge="rsClientMessageCharge"
        @bookingMessageConfirm="bookingMessageConfirm"
        @closeBookingMessage="closeBookingMessage"
        @deleteBookingMessage="deleteBookingMessage"
        @handleAddBookingMessage="addBookingMessage"
        @handleBookingMessageUpdate="handleBookingMessageUpdate"
      />

      <!--子服务-->
      <!--整柜海运-->
      <sea-fcl-component
        :audit-info="auditInfo"
        :booking="booking"
        :branch-info="branchInfo"
        :carrier-list="carrierList"
        :carrier-normalizer="carrierNormalizer"
        :charge-info="chargeInfo"
        :company-list="companyList"
        :disabled="disabled"
        :form="form"
        :logistics-info="logisticsInfo"
        :psa-verify="psaVerify"
        :sea-fcl-list="form.rsOpSeaFclList"
        :supplier-list="supplierList"
        @addProgress="addProgress"
        @addSeaFCL="addSeaFCL"
        @auditCharge="auditCharge"
        @calculateCharge="calculateCharge"
        @changeServiceFold="changeServiceFold"
        @copyFreight="copyFreight"
        @deleteRsOpFclSea="deleteRsOpFclSea"
        @generateFreight="generateFreight"
        @getBookingBill="getBookingBill"
        @getBookingStatus="getBookingStatus"
        @getPayable="getPayable"
        @getServiceInstanceDisable="getServiceInstanceDisable"
        @getServiceObject="getServiceObject"
        @handleSettledRate="handleSettledRate"
        @openChargeSelect="openChargeSelect"
        @psaBookingCancel="psaBookingCancel"
        @selectCarrier="selectCarrier"
        @selectPsaBookingOpen="selectPsaBookingOpen"
      />

      <!--拼柜海运-->
      <sea-lcl-component
        :audit-info="auditInfo"
        :booking="booking"
        :branch-info="branchInfo"
        :carrier-list="carrierList"
        :carrier-normalizer="carrierNormalizer"
        :charge-info="chargeInfo"
        :company-list="companyList"
        :disabled="disabled"
        :form="form"
        :logistics-info="logisticsInfo"
        :psa-verify="psaVerify"
        :sea-lcl-list="form.rsOpSeaLclList"
        :supplier-list="supplierList"
        @addProgress="addProgress"
        @addSeaLCL="addSeaLCL"
        @auditCharge="auditCharge"
        @calculateCharge="calculateCharge"
        @changeServiceFold="changeServiceFold"
        @copyFreight="copyFreight"
        @deleteRsOpLclSea="deleteRsOpLclSea"
        @generateFreight="generateFreight"
        @getBookingBill="getBookingBill"
        @getBookingStatus="getBookingStatus"
        @getFormDisable="getFormDisable"
        @getPayable="getPayable"
        @getServiceInstanceDisable="getServiceInstanceDisable"
        @getServiceObject="getServiceObject"
        @handleSettledRate="handleSettledRate"
        @openChargeSelect="openChargeSelect"
        @psaBookingCancel="psaBookingCancel"
        @selectCarrier="selectCarrier"
        @selectPsaBookingOpen="selectPsaBookingOpen"
      />

      <!--空运-->
      <air-component
        :air-list="form.rsOpAirList"
        :form="form"
        :branch-info="branchInfo"
        :logistics-info="logisticsInfo"
        :charge-info="chargeInfo"
        :audit-info="auditInfo"
        :disabled="disabled"
        :booking="booking"
        :psa-verify="psaVerify"
        :supplier-list="supplierList"
        :carrier-list="carrierList"
        :company-list="companyList"
        :carrier-normalizer="carrierNormalizer"
        @changeServiceFold="changeServiceFold"
        @addAir="addAir"
        @deleteRsOpAir="deleteRsOpAir"
        @openChargeSelect="openChargeSelect"
        @auditCharge="auditCharge"
        @getBookingBill="getBookingBill"
        @generateFreight="generateFreight"
        @selectPsaBookingOpen="selectPsaBookingOpen"
        @selectCarrier="selectCarrier"
        @addProgress="addProgress"
        @psaBookingCancel="psaBookingCancel"
        @copyFreight="copyFreight"
        @calculateCharge="calculateCharge"
      />

      <!--铁路-->
      <railway-component
        v-for="item in RAIL"
        :key="item.serviceTypeId"
        :fold-state="getFold(item.serviceTypeId)"
        :form="form"
        :branch-info="branchInfo"
        :logistics-info="logisticsInfo"
        :charge-info="chargeInfo"
        :audit-info="auditInfo"
        :disabled="disabled"
        :booking="booking"
        :psa-verify="psaVerify"
        :supplier-list="supplierList"
        :company-list="companyList"
        :form-disable="getFormDisable(item.serviceTypeId)"
        :payable="getPayable(item.serviceTypeId)"
        :service-instance="getServiceInstance(item.serviceTypeId)"
        :service-item="item"
        :service-object="getServiceObject(item.serviceTypeId)"
        @changeFold="changeFold"
        @changeServiceObject="changeServiceObject"
        @auditCharge="auditCharge"
        @generateFreight="generateFreight"
        @psaBookingCancel="psaBookingCancel"
        @copyFreight="copyFreight"
        @calculateCharge="calculateCharge"
      />

      <!--快递-->
      <express-component
        v-for="item in EXPRESS"
        :key="item.serviceTypeId"
        :fold-state="getFold(item.serviceTypeId)"
        :form="form"
        :form-disable="getFormDisable(item.serviceTypeId)"
        :payable="getPayable(item.serviceTypeId)"
        :service-instance="getServiceInstance(item.serviceTypeId)"
        :service-item="item"
        :service-object="getServiceObject(item.serviceTypeId)"
        :branch-info="branchInfo"
        :service-info="serviceInfo"
        :logistics-info="logisticsInfo"
        :charge-info="chargeInfo"
        :audit-info="auditInfo"
        :disabled="disabled"
        :booking="booking"
        :psa-verify="psaVerify"
        :supplier-list="supplierList"
        :company-list="companyList"
        @changeFold="changeFold"
        @changeServiceObject="changeServiceObject"
        @auditCharge="auditCharge"
        @generateFreight="generateFreight"
        @psaBookingCancel="psaBookingCancel"
        @copyFreight="copyFreight"
        @calculateCharge="calculateCharge"
      />

      <!--整柜拖车-->
      <ctnr-truck-component
        :ctnr-truck-list="form.rsOpCtnrTruckList"
        :form="form"
        :branch-info="branchInfo"
        :logistics-info="logisticsInfo"
        :charge-info="chargeInfo"
        :audit-info="auditInfo"
        :disabled="disabled"
        :booking="booking"
        :psa-verify="psaVerify"
        :supplier-list="supplierList"
        :company-list="companyList"
        :location-options="locationOptions"
        @changeServiceFold="changeServiceFold"
        @addCtnrTruck="addCtnrTruck"
        @deleteRsOpCtnrTruck="deleteRsOpCtnrTruck"
        @openChargeSelect="openChargeSelect"
        @auditCharge="auditCharge"
        @getDispatchingBill="getDispatchingBill"
        @generateFreight="generateFreight"
        @handleAddCommon="handleAddCommon"
        @openDispatchCommon="openDispatchCommon"
        @copyFreight="copyFreight"
        @calculateCharge="calculateCharge"
      />

      <!--散货拖车-->
      <bulk-truck-component
        :bulk-truck-list="form.rsOpBulkTruckList"
        :form="form"
        :branch-info="branchInfo"
        :logistics-info="logisticsInfo"
        :charge-info="chargeInfo"
        :audit-info="auditInfo"
        :disabled="disabled"
        :booking="booking"
        :psa-verify="psaVerify"
        :supplier-list="supplierList"
        :company-list="companyList"
        :location-options="locationOptions"
        @changeServiceFold="changeServiceFold"
        @addBulkTruck="addBulkTruck"
        @deleteRsOpBulkTruck="deleteRsOpBulkTruck"
        @openChargeSelect="openChargeSelect"
        @auditCharge="auditCharge"
        @getDispatchingBill="getDispatchingBill"
        @generateFreight="generateFreight"
        @handleAddCommon="handleAddCommon"
        @openDispatchCommon="openDispatchCommon"
        @copyFreight="copyFreight"
        @calculateCharge="calculateCharge"
      />

      <!--单证报关-->
      <doc-declare-component
        :doc-declare-list="form.rsOpDocDeclareList"
        :form="form"
        :branch-info="branchInfo"
        :logistics-info="logisticsInfo"
        :charge-info="chargeInfo"
        :audit-info="auditInfo"
        :disabled="disabled"
        :booking="booking"
        :psa-verify="psaVerify"
        :supplier-list="supplierList"
        :company-list="companyList"
        @changeServiceFold="changeServiceFold"
        @addDocDeclare="addDocDeclare"
        @deleteRsOpDocDeclare="deleteRsOpDocDeclare"
        @openChargeSelect="openChargeSelect"
        @auditCharge="auditCharge"
        @getBookingBill="getBookingBill"
        @generateFreight="generateFreight"
        @selectPsaBookingOpen="selectPsaBookingOpen"
        @psaBookingCancel="psaBookingCancel"
        @copyFreight="copyFreight"
        @calculateCharge="calculateCharge"
      />

      <!--全包报关-->
      <free-declare-component
        :free-declare-list="form.rsOpFreeDeclareList"
        :form="form"
        :branch-info="branchInfo"
        :logistics-info="logisticsInfo"
        :charge-info="chargeInfo"
        :audit-info="auditInfo"
        :disabled="disabled"
        :booking="booking"
        :psa-verify="psaVerify"
        :supplier-list="supplierList"
        :company-list="companyList"
        @changeServiceFold="changeServiceFold"
        @addFreeDeclare="addFreeDeclare"
        @deleteRsOpFreeDeclare="deleteRsOpFreeDeclare"
        @openChargeSelect="openChargeSelect"
        @auditCharge="auditCharge"
        @getBookingBill="getBookingBill"
        @generateFreight="generateFreight"
        @copyFreight="copyFreight"
        @calculateCharge="calculateCharge"
      />

      <!--清关-->
      <clearance-component
        v-for="item in CLEAR"
        :key="item.serviceTypeId"
        :fold-state="getFold(item.serviceTypeId)"
        :form="form"
        :branch-info="branchInfo"
        :logistics-info="logisticsInfo"
        :charge-info="chargeInfo"
        :audit-info="auditInfo"
        :disabled="disabled"
        :booking="booking"
        :psa-verify="psaVerify"
        :supplier-list="supplierList"
        :company-list="companyList"
        :form-disable="getFormDisable(item.serviceTypeId)"
        :payable="getPayable(item.serviceTypeId)"
        :service-instance="getServiceInstance(item.serviceTypeId)"
        :service-item="item"
        :service-object="getServiceObject(item.serviceTypeId)"
        @changeFold="changeFold"
        @changeServiceObject="changeServiceObject"
        @auditCharge="auditCharge"
        @getBookingBill="getBookingBill"
        @generateFreight="generateFreight"
        @psaBookingCancel="psaBookingCancel"
        @copyFreight="copyFreight"
        @calculateCharge="calculateCharge"
      />

      <!--仓储-->
      <whs-component
        v-for="item in WHS"
        :key="item.serviceTypeId"
        :fold-state="getFold(item.serviceTypeId)"
        :audit-info="auditInfo"
        :booking="booking"
        :branch-info="branchInfo"
        :charge-info="chargeInfo"
        :company-list="companyList"
        :disabled="disabled"
        :form="form"
        :logistics-info="logisticsInfo"
        :psa-verify="psaVerify"
        :rs-op-warehouse-payable="rsOpWarehousePayable"
        :supplier-list="supplierList"
        :whs-services="WHS"
        :form-disable="getFormDisable(item.serviceTypeId)"
        :payable="getPayable(item.serviceTypeId)"
        :service-instance="getServiceInstance(item.serviceTypeId)"
        :service-item="item"
        :service-object="getServiceObject(item.serviceTypeId)"
        @auditCharge="auditCharge"
        @calculateCharge="calculateCharge"
        @changeFold="changeFold"
        @changeServiceObject="changeServiceObject"
        @copyFreight="copyFreight"
        @generateFreight="generateFreight"
        @getBookingBill="getBookingBill"
        @outboundPlan="outboundPlan"
        @psaBookingCancel="psaBookingCancel"
      />

      <!--拓展服务-->
      <extend-service-component
        v-for="item in EXTEND"
        :key="item.serviceTypeId"
        :fold-state="getFold(item.serviceTypeId)"
        :audit-info="auditInfo"
        :booking="booking"
        :branch-info="branchInfo"
        :charge-info="chargeInfo"
        :company-list="companyList"
        :disabled="disabled"
        :extend-service-list="extendServiceList"
        :form="form"
        :logistics-info="logisticsInfo"
        :psa-verify="psaVerify"
        :supplier-list="supplierList"
        :form-disable="getFormDisable(item.serviceTypeId)"
        :payable="getPayable(item.serviceTypeId)"
        :service-instance="getServiceInstance(item.serviceTypeId)"
        :service-item="item"
        :service-object="getServiceObject(item.serviceTypeId)"
        @changeFold="changeFold"
        @changeServiceObject="changeServiceObject"
        @auditCharge="auditCharge"
        @calculateCharge="calculateCharge"
        @changeServiceFold="changeExtendServiceFold"
        @copyFreight="copyFreight"
        @generateFreight="generateFreight"
        @psaBookingCancel="psaBookingCancel"
      />

      <!--备注-->
      <div>
        <el-row :gutter="10" class="spc" style="margin-bottom:15px;display: -webkit-box">
          <!--报价-->
          <el-col :span="4">
            <el-form-item label="报价单号">
              <el-input v-model="form.qoutationNo" :disabled="true" placeholder="报价单号"
                        class="disable-form" style="width: 100%"
              />
            </el-form-item>
            <el-input v-model="form.qoutationSketch" :autosize="{ minRows: 10, maxRows: 20}"
                      :class="!booking ||disabled ?'disable-form':''"
                      placeholder="内容" style="padding-bottom: 2px;" type="textarea"
                      :disabled="!booking ||disabled"
            />
            <el-form-item label="业务员" prop="salesId">
              <treeselect v-model="salesId" :class="form.sqdShippingBookingStatus==1?'disable-form':''"
                          :disabled="form.sqdShippingBookingStatus==1"
                          :disabled-fuzzy-matching="true" :flatten-search-results="true" :normalizer="staffNormalizer"
                          :options="belongList" :show-count="true" placeholder="业务员"
                          @input="$event==undefined?form.salesId = null:null"
                          :disabled-branch-nodes="true"
                          @select="form.salesId = $event.staffId"
              >
                <div slot="value-label" slot-scope="{node}">
                  {{
                    node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + " " + node.raw.staff.staffGivingEnName : ""
                  }}
                </div>
                <label slot="option-label"
                       slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                       :class="labelClassName"
                >
                  {{ node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label }}
                  <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                </label>
              </treeselect>
            </el-form-item>
            <el-form-item label="报价日期" prop="quotationTime">
              <el-date-picker v-model="form.qoutationTime"
                              :class=" 'disable-form'" disabled
                              placeholder="报价日期" style="width:100%"
                              clearable type="date"
                              value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <!--订舱-->
          <el-col :span="5">
            <el-row>
              <el-col :span="16">
                <el-form-item label="订舱单号">
                  <el-input v-model="form.newBookingNo" :disabled="true" class="disable-form"
                            placeholder="订舱单号" style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8" align="center">
                <el-button :disabled=" !booking || form.sqdShippingBookingStatus==1 " type="success"
                           @click="submitForm('booking')"
                >
                  <i
                    :class="form.sqdShippingBookingStatus !=null && form.sqdShippingBookingStatus != 0?'el-icon-check':''"
                  />
                  {{
                    form.sqdShippingBookingStatus == null || form.sqdShippingBookingStatus == 0 ? "提交订舱" : "已订舱"
                  }}
                </el-button>
              </el-col>
            </el-row>

            <el-input v-model="form.newBookingRemark" :autosize="{ minRows: 10, maxRows: 20}"
                      :class="!booking||disabled?'disable-form':''"
                      placeholder="业务订舱备注" type="textarea"
                      :disabled="!booking||disabled"
            />
            <el-col :span="16">
              <el-form-item label="业务助理" prop="salesAssistantId">
                <treeselect v-model="salesAssistantId" :class="!booking|| disabled?'disable-form':''"
                            :disabled-branch-nodes="true"
                            :disabled-fuzzy-matching="true" :flatten-search-results="true" :normalizer="staffNormalizer"
                            :disabled="!booking" :options="belongList" :show-count="true"
                            placeholder="业务助理"
                            @input="$event==undefined?form.salesAssistantId=null:null"
                            @select="form.salesAssistantId = $event.staffId"
                >
                  <div slot="value-label" slot-scope="{node}">
                    {{
                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + " " + node.raw.staff.staffGivingEnName : ""
                    }}
                  </div>
                  <label slot="option-label"
                         slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                         :class="labelClassName"
                  >
                    {{ node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label }}
                    <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                  </label>
                </treeselect>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="订舱日期" prop="newBookingTime">
                <el-date-picker v-model="form.newBookingTime" :class="'disable-form'"
                                clearable
                                placeholder="订舱申请单日期" style="width:100%"
                                disabled type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-col>
          <!--商务审核-->
          <el-col :span="4">
            <el-row>
              <el-col :span="16">
                <el-form-item label="审核意见">
                  <tree-select :class="!psaVerify|| form.psaVerify == 1?'disable-form':''"
                               :disabled="!psaVerify|| form.psaVerify == 1" :flat="false" :multiple="false"
                               :pass="form.psaVerifyStatusId" :placeholder="'审核意见'"
                               :type="'processStatus'"
                               :type-id="1" @return="form.psaVerifyStatusId=$event"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8" align="center">
                <el-button :disabled="!psaVerify || form.psaVerify == 1" type="success"
                           @click="submitForm('psa')"
                >
                  <i :class="form.psaVerify == 1?'el-icon-check':''"/>{{
                    form.psaVerify != 1 ? "提交审核" : "已审核"
                  }}
                </el-button>
              </el-col>
            </el-row>

            <el-form-item label="" label-width="100" prop="inquiryInnerRemarkSum">
              <el-input v-model="form.inquiryInnerRemarkSum" :autosize="{ minRows: 10, maxRows: 20}" maxlength="150"
                        :class="!psaVerify || form.psaVerify == 1?'disable-form':''"
                        :disabled="!psaVerify || form.psaVerify == 1" placeholder="内容"
                        show-word-limit
                        type="textarea"
              />
            </el-form-item>
            <el-col :span="16">
              <el-form-item label="商务审核" prop="verifyPsaId">
                <treeselect v-model="verifyPsaId" :class="!booking ||disabled?'disable-form':''"
                            :disabled-branch-nodes="true"
                            :disabled="!booking ||disabled" :disabled-fuzzy-matching="true"
                            :flatten-search-results="true"
                            :normalizer="businessesNormalizer" :options="businessList" :show-count="true"
                            placeholder="商务" @input="$event==undefined?form.verifyPsaId=null:null"
                            @select="form.verifyPsaId = $event.staffId"
                >
                  <div slot="value-label" slot-scope="{node}">
                    {{
                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + " " + node.raw.staff.staffGivingEnName : ""
                    }}
                  </div>
                  <label slot="option-label"
                         slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                         :class="labelClassName"
                  >
                    {{ node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label }}
                    <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                  </label>
                </treeselect>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="审核时间" prop="psaVerifyTime">
                <el-date-picker v-model="form.psaVerifyTime" class="disable-form"
                                clearable
                                disabled placeholder="商务审核时间"
                                style="width:100%" type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                />
              </el-form-item>
            </el-col>
          </el-col>
          <!--<el-col :span="4">
            <el-form-item label="派单意见">
              <el-input v-model="form.newBookingNo" :disabled="psaVerify || disabled" placeholder="订舱单号"
                        style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="" label-width="100" prop="opLeaderNotice">
              <el-input v-model="form.opLeaderNotice" :autosize="{ minRows: 10, maxRows: 20}" :disabled="psaVerify || disabled"
                        maxlength="150" placeholder="内容" show-word-limit type="textarea"
              />
            </el-form-item>
            <el-form-item label="派单操作" prop="opId">
              <treeselect v-model="opId" :disabled-branch-nodes="true" :disabled="psaVerify || disabled"
                          :disabled-fuzzy-matching="true" :flatten-search-results="true"
                          :normalizer="staffNormalizer"
                          :options="opList.filter(v => {return v.role.roleLocalName=='操作员'})" :show-count="true"
                          placeholder="操作员"
                          @input="$event==undefined?form.opId = null:null"
                          @select="form.opId = $event.staffId"
              >
                <div slot="value-label" slot-scope="{node}">
                  {{
                    node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''
                  }}
                </div>
                <label slot="option-label"
                       slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                       :class="labelClassName"
                >
                  {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}
                  <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                </label>
              </treeselect>
            </el-form-item>
            <el-form-item label="派单日期" prop="processStatusTime">
              <el-date-picker  clearable
                              placeholder="状态日期"
                              style="width:100%"
                              type="date"
                              value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>-->
          <!--操作-->
          <el-col :span="5">
            <el-row>
              <el-col :span="12">
                <el-form-item label="订单状态">
                  <tree-select :flat="false" :multiple="false" :pass="form.processStatusId" :placeholder="'订单状态'"
                               :disabled="!op || disabled" :type="'processStatus'" :type-id="2"
                               :class="!op || disabled?'disable-form':''" @return="form.processStatusId=$event"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" align="center">
                <el-row>
                  <el-col :span="12">
                    <el-button :disabled="!op || form.opAccept == 1 || finance" type="success"
                               @click="submitForm('opConfirm')"
                    ><i :class="form.opAccept == 1?'el-icon-check':''"/>{{
                        form.opAccept != 1 ? "确认接单" : "已接单"
                      }}
                    </el-button>
                  </el-col>
                  <el-col :span="12">
                    <el-button v-if="op && form.opAccept == 0" type="warning" @click="turnDown"
                    >{{
                        "驳回"
                      }}
                    </el-button>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
            <el-form-item label="" label-width="100" prop="opInnerRemark">
              <el-input v-model="form.opInnerRemark" :autosize="{ minRows: 10, maxRows: 20}"
                        :class="!op|| disabled?'disable-form':''"
                        :disabled="!op" placeholder="内容"
                        show-word-limit type="textarea"
              />
            </el-form-item>
            <el-col :span="16">
              <el-form-item label="操作员" prop="opId">
                <treeselect v-model="opId" :disabled-branch-nodes="true"
                            :disabled-fuzzy-matching="true" :flatten-search-results="true"
                            :normalizer="businessesNormalizer"
                            :options="opList.filter(v => {return v.role.roleLocalName=='操作员'})" :show-count="true"
                            :class="!psaVerify || disabled || form.psaVerify == 1?'disable-form':''"
                            :disabled="!psaVerify || form.psaVerify == 1 || disabled"
                            placeholder="操作员"
                            @input="$event==undefined?form.opId = null:null"
                            @select="form.opId = $event.staffId"
                >
                  <div slot="value-label" slot-scope="{node}">
                    {{
                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + " " + node.raw.staff.staffGivingEnName : ""
                    }}
                  </div>
                  <label slot="option-label"
                         slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                         :class="labelClassName"
                  >
                    {{ node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label }}
                    <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                  </label>
                </treeselect>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="状态日期" prop="statusUpdateTime">
                <el-date-picker v-model="form.statusUpdateTime" class="disable-form"
                                clearable disabled
                                placeholder="状态日期"
                                style="width:100%" type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-col>
        </el-row>
      </div>

      <!--费用选择弹出层-->
      <el-dialog
        v-dialogDrag
        v-dialogDragWidth
        :close-on-click-modal="false"
        :destroy-on-close="true" :modal-append-to-body="false"
        :visible.sync="openFreightSelect" append-to-body center
        custom-class="dialog"
        width="90%"
      >
        <FreightSelect :freight-select-data="freightSelectData" :type-id="freightSelectData.typeId"
                       @returnFreight="handleFreightSelect"
        />
      </el-dialog>

      <!--费用复制选择弹出层-->
      <el-dialog
        v-dialogDrag
        v-dialogDragWidth
        :close-on-click-modal="false"
        :destroy-on-close="true" :modal-append-to-body="false"
        :visible.sync="chargeOpen" append-to-body center
        custom-class="dialog"
        width="70%"
      >
        <charge-select :service-type-id="chargeSelectItem?chargeSelectItem.sqdServiceTypeId:null"
                       :client-id="form.clientId" :search-data="chargeSearchData" @returnCharge="handleChargeSelect"
        />
      </el-dialog>

      <!--委托单位选择弹出层-->
      <el-dialog
        v-dialogDrag
        v-dialogDragWidth :append-to-body="true"
        :close-on-click-modal="false"
        :destroy-on-close="true" :modal-append-to-body="false"
        :visible.sync="openCompanySelect" center
        custom-class="dialog"
        width="90%"
      >
        <select-company :roleTypeId="'1'" @return="selectCompanyData"/>
      </el-dialog>

      <!--商务订舱选择弹出层-->
      <el-dialog
        v-dialogDrag
        v-dialogDragWidth :append-to-body="true"
        :close-on-click-modal="false"
        :destroy-on-close="true" :modal-append-to-body="false"
        :visible.sync="openPsaBookingSelect" center
        custom-class="dialog"
        width="90%"
      >
        <psa-booking-list-select :psaBookingSelectData="psaBookingSelectData" @return="selectPsaBooking"/>
      </el-dialog>

      <!--通用信息选择弹出层-->
      <el-dialog
        v-dialogDrag
        v-dialogDragWidth
        :append-to-body="true"
        :close-on-click-modal="false"
        :destroy-on-close="true" :modal-append-to-body="false"
        :visible.sync="openCommonUsedSelect" center
        custom-class="dialog"
        width="90%"
      >
        <common-used-select :common-used-select-data="commonUsedSelectData" @return="selectCommonUsed"/>
      </el-dialog>

      <!--出仓计划-->
      <outbound-plan :open-outbound="openOutbound" :outbound-form-prop="outboundForm" :outboundData="outboundData"
                     @closeOutbound="openOutbound=false"
      ></outbound-plan>

    </el-form>

    <div ref="dragArea" class="drag-area">
      <div>{{ this.form.rctNo }}</div>
      <el-button
        v-if="(psaVerify&&form.psaVerify != 1) || (form.opAccept==1 &&this.$route.query.type==='op') || form.sqdShippingBookingStatus!=1"
        type="primary" @click="submitForm()"
      >{{ "保存更改" }}
      </el-button>
      <el-button
        v-if="booking && form.rctId && form.sqdShippingBookingStatus==='1'" type="primary" @click="saveAs()"
      >{{ "另存为" }}
      </el-button>
    </div>
    <!-- 预览 -->
    <print-preview ref="preView"/>
  </div>
</template>

<script>
import document from "@/views/system/document/index"
import OrderDifficultySelect from "@/components/OrderDifficultySelect/index.vue"
import CompanySelect from "@/components/CompanySelect/index.vue"
import store from "@/store"
import Treeselect from "@riophae/vue-treeselect"
import pinyin from "js-pinyin"
import DocList from "@/views/system/document/docList.vue"
import ChargeList from "@/views/system/document/chargeList.vue"
import LogisticsProgress from "@/views/system/document/logisticsProgress.vue"
import Audit from "@/views/system/document/audit.vue"
import {parseTime} from "@/utils/rich"
import LogisticsNoInfo from "@/views/system/document/logisticsNoInfo.vue"
import opHistory from "@/views/system/document/opHistory.vue"
import receivablePayable from "@/views/system/document/receivablePayable.vue"
import {
  addRct,
  getRct,
  getRctCFMon,
  getRctMon,
  getRctRSWHMon,
  saveAllService,
  saveAsAllService,
  saveAsRct,
  updateRct
} from "@/api/system/rct"
import UrgencyDegreeSelect from "@/components/UrgencyDegreeSelect/index.vue"
import ProgressStatus from "@/components/ProgressStatus/index.vue"
import {checkRole} from "@/utils/permission"
import currency from "currency.js"
import _ from "lodash"
import FreightSelect from "@/views/system/freight/freightSelect.vue"
import {getQuotation, queryLocal} from "@/api/system/quotation"
import SelectCompany from "@/views/system/company/selectCompany.vue"
import {getBooking} from "@/api/system/booking"
import ProgressName from "@/components/ProgressName/index.vue"
import PrintTemplate from "@/views/system/print/PrintTemplate.vue"
import {defaultElementTypeProvider, hiprint} from "../../../index"
import printPreview from "@/views/print/demo/design/preview.vue"
import dispatchBill from "../../../print-template/dispatchBill"
import booking from "../../../print-template/booking"
import CFLBooking from "../../../print-template/CFLBbooking"
import debitNode from "../../../print-template/debitNode"
import debitNodeEn from "../../../print-template/debitNodeEn"
import debitNodeEnHKRMBToUSD from "../../../print-template/debitNodeEnHKRMBToUSD"
import debitNodeUSDToRMB from "../../../print-template/debitNodeUSDToRMB"
import debitNodeZSUSD from "../../../print-template/debitNodeZSUSD"
import debitNodeCFL from "../../../print-template/debitNodeCFL"
import debitNodeCFLToRMB from "../../../print-template/debitNodeCFLToRMB"
import FCLBill from "../../../print-template/FCLBill"
import AirBill from "../../../print-template/AirBill"
import moment from "moment"
import {addPsarct, updatePsarct} from "@/api/system/psarct"
import PsaBookingListSelect from "@/views/system/booking/psaBookingListSelect.vue"
import {addClientsinfo} from "@/api/system/clientsinfo"
import CommonUsedSelect from "@/views/system/commonused/commonUsedSelect.vue"
import {locationOptions} from "@/api/system/location"
import billOfLading from "@/print-template/billOfLading"
import billOfLadingRelease from "@/print-template/billOfLadingRelease"
import {updateCharge} from "@/api/system/rsCharge"
import {updateServiceinstances} from "@/api/system/serviceinstances"
import toWords from "num-words"
import ChargeSelect from "@/views/system/document/chargeSelect.vue"
import DatePickerItem from "@/views/system/DatePickerItem/index.vue"
import OutboundPlan from "@/views/system/document/outboundPlan.vue"
import SeaFclComponent from "@/views/system/document/serviceComponents/SeaFclComponent.vue"
import SeaLclComponent from "@/views/system/document/serviceComponents/SeaLclComponent.vue"
import AirComponent from "@/views/system/document/serviceComponents/AirComponent.vue"
import RailwayComponent from "@/views/system/document/serviceComponents/RailwayComponent.vue"
import ExpressComponent from "@/views/system/document/serviceComponents/ExpressComponent.vue"
import CtnrTruckComponent from "@/views/system/document/serviceComponents/CtnrTruckComponent.vue"
import BulkTruckComponent from "@/views/system/document/serviceComponents/BulkTruckComponent.vue"
import DocDeclareComponent from "@/views/system/document/serviceComponents/DocDeclareComponent.vue"
import FreeDeclareComponent from "@/views/system/document/serviceComponents/FreeDeclareComponent.vue"
import ClearanceComponent from "@/views/system/document/serviceComponents/ClearanceComponent.vue"
import WhsComponent from "@/views/system/document/serviceComponents/WhsComponent.vue"
import ExtendServiceComponent from "@/views/system/document/serviceComponents/ExtendServiceComponent.vue"
import BillOfLadingInfo from "@/views/system/document/serviceComponents/BillOfLadingInfo.vue"

// 导入提取的mixins和utils
import chargeCalculatorMixin from "@/views/system/document/mixins/chargeCalculator.js"
import computedPropsMixin from "@/views/system/document/mixins/computedProps.js"
import formValidatorMixin from "@/views/system/document/mixins/formValidator.js"
import opDataHandlerMixin from "@/views/system/document/mixins/opDataHandler.js"
import serviceManagerMixin from "@/views/system/document/mixins/serviceManager.js"
import {createServiceInstance, createServiceObject} from "@/views/system/document/utils/serviceFactory.js"

let hiprintTemplate

// 添加防抖函数工具
const debounce = function (func, wait = 500) {
  let timeout
  return function (...args) {
    const context = this
    clearTimeout(timeout)
    timeout = setTimeout(() => {
      func.apply(context, args)
    }, wait)
  }
}

export default {
  name: "op",
  props: ["type"],
  components: {
    OutboundPlan,
    DatePickerItem,
    ChargeSelect,
    CommonUsedSelect,
    PsaBookingListSelect,
    printPreview,
    PrintTemplate,
    ProgressName,
    SelectCompany,
    FreightSelect,
    ProgressStatus,
    UrgencyDegreeSelect,
    receivablePayable, opHistory, LogisticsNoInfo,
    Audit,
    LogisticsProgress,
    ChargeList,
    DocList,
    Treeselect,
    CompanySelect,
    OrderDifficultySelect,
    document,
    SeaFclComponent,
    SeaLclComponent,
    AirComponent,
    RailwayComponent,
    ExpressComponent,
    CtnrTruckComponent,
    BulkTruckComponent,
    DocDeclareComponent,
    FreeDeclareComponent,
    ClearanceComponent,
    WhsComponent,
    ExtendServiceComponent,
    BillOfLadingInfo
  },
  mixins: [
    chargeCalculatorMixin,
    computedPropsMixin,
    formValidatorMixin,
    opDataHandlerMixin,
    serviceManagerMixin
  ],
  data() {
    // 验证函数和服务实例创建函数已移动到mixins中

    // 常量配置
    const CONFIG = {
      RCT_SETTINGS: {
        leadingCharacter: "RCT",
        GZCFLeadingCharacter: "CFL",
        RSWHLeadingCharacter: "RSH",
        month: 1,
        noNum: 1,
        rctNo: null
      },
      SERVICE_SETS: {
        SEA: new Set(),
        SEAFCL: new Set(),
        SEALCL: new Set(),
        AIR: new Set(),
        RAIL: new Set(),
        EXPRESS: new Set(),
        TRUCK: new Set(),
        CUSTOM: new Set(),
        CLEAR: new Set(),
        WHS: new Set(),
        EXTEND: new Set()
      }
    }

    return {
      isSubmitting: false, // 添加提交状态控制变量
      form: {
        noTransferAllowed: false,
        noDividedAllowed: false,
        noAgreementShowed: false,
        isCustomsIntransitShowed: false,
        rsOpSeaFclList: [],
        rsOpSeaLclList: [],
        rsOpAirList: [],
        rsOpCtnrTruckList: [],
        rsOpBulkTruckList: [],
        rsOpDocDeclareList: [],
        rsOpFreeDeclareList: []
      },
      statusForm: {},
      // rules已移动到formValidatorMixin中
      rct: {...CONFIG.RCT_SETTINGS},

      // 为了兼容模板中的直接引用，保持根级别的属性
      psaVerify: false,
      op: false,
      finance: false,
      booking: false,
      openDocList: false,
      openGenerateRevenueTons: false,
      openBookingMessage: false,
      openFreightSelect: false,
      openCompanySelect: false,
      openCommonUsedSelect: false,
      openPsaBookingSelect: false,
      openGenerateRct: false,

      // UI面板显示状态（兼容模板直接引用）
      basicInfo: true,
      orderInfo: true,
      branchInfo: true,
      serviceInfo: false,
      logisticsInfo: false,
      docInfo: false,
      chargeInfo: false,
      auditInfo: false,
      clientMessage: true,
      basicLogistics: false,
      preCarriage: false,
      exportDeclaration: false,
      importClearance: false,

      // 服务折叠状态（兼容模板直接引用）
      rsOpSealFclFold: false,
      otherFold: true,
      rsOpSealLclFold: false,
      rsOpBulkShipFold: false,
      rsOpRoroShipFold: false,
      rsOpAirFold: false,
      rsOpRailFold: false,
      rsOpRailFclFold: false,
      rsOpRailLclFold: false,
      rsOpExpressFold: false,
      rsOpPortServiceFold: false,
      rsOpExportTruckFold: false,
      rsOpCtnrTruckFold: false,
      rsOpBulkTruckFold: false,
      rsOpExportCustomsClearanceFold: false,
      rsOpDocDeclareFold: false,
      rsOpDOAgentFold: false,
      rsOpClearAgentFold: false,
      rsOpFreeDeclareFold: false,
      rsOpImportCustomsClearanceFold: false,
      rsOpImportDispatchTruckFold: false,
      rsOpWarehouseFold: false,
      rsOpInspectionAndCertificateFold: false,
      rsOpLandFold: false,
      rsOpInsuranceFold: false,
      rsOpExpandServiceFold: false,
      rsOpWHSFold: false,
      rsOp3rdCertFold: false,
      rsOpINSFold: false,
      rsOpTradingFold: false,
      rsOpFumigationFold: false,
      rsOpCOFold: false,
      rsOpOtherFold: false,

      // 财务数据变量（兼容模板直接引用）
      rsClientMessageReceivable: 0,
      rsClientMessageReceivableRMB: 0,
      rsClientMessageReceivableUSD: 0,
      rsClientMessageReceivableTaxRMB: 0,
      rsClientMessageReceivableTaxUSD: 0,
      rsClientMessagePayable: 0,
      rsClientMessageChargeData: {},
      rsClientMessagePayableRMB: 0,
      rsClientMessagePayableUSD: 0,
      rsClientMessagePayableTaxRMB: 0,
      rsClientMessagePayableTaxUSD: 0,
      sqdUnreceivedRmbSum: 0,
      sqdUnreceivedUsdSum: 0,
      sqdUnpaidRmbSum: 0,
      sqdUnpaidUsdSum: 0,
      rsClientMessageProfitRMB: 0,
      rsClientMessageProfitUSD: 0,
      rsBasicLogisticsPayable: 0,
      rsPrecarriagePayable: 0,
      rsExportCustomsPayable: 0,
      rsImportCustomsPayable: 0,
      rsClientMessageProfitTaxRMB: 0,
      rsClientMessageProfitTaxUSD: 0,

      // 各项服务应付变量（兼容模板直接引用）
      rsOpSeaFclPayable: 0,
      rsOpSeaLclPayable: 0,
      rsOpOtherPayableUSD: 0,
      rsOpOtherPayableRMB: 0,
      rsOpBulkShipPayable: 0,
      rsOpRoroShipPayable: 0,
      rsOpAirPayableRMB: 0,
      rsOpAirPayableUSD: 0,
      rsOpRailPayable: 0,
      rsOpRailFclPayableRMB: 0,
      rsOpRailFclPayableUSD: 0,
      rsOpRailLclPayableRMB: 0,
      rsOpRailLclPayableUSD: 0,
      rsOpExpressPayableRMB: 0,
      rsOpExpressPayableUSD: 0,
      rsOpPortServicePayable: 0,
      rsOpExportTruckPayable: 0,
      rsOpExportCustomsClearancePayable: 0,
      rsOpImportCustomsClearancePayable: 0,
      rsOpImportDispatchTruckPayable: 0,
      rsOpWarehousePayable: 0,
      rsOpInspectionAndCertificatePayable: 0,
      rsOpLandPayable: 0,
      rsOpInsurancePayable: 0,
      rsOpCtnrTruckPayableRMB: 0,
      rsOpCtnrTruckPayableUSD: 0,
      rsOpBulkTruckPayableRMB: 0,
      rsOpBulkTruckPayableUSD: 0,
      rsOpDocDeclarePayable: 0,
      rsOpFreeDeclarePayable: 0,
      rsOpDOAgentPayableRMB: 0,
      rsOpDOAgentPayableUSD: 0,
      rsOpClearAgentPayableRMB: 0,
      rsOpClearAgentPayableUSD: 0,
      rsOpWHSPayableRMB: 0,
      rsOpWHSPayableUSD: 0,
      rsOp3rdCertPayableRMB: 0,
      rsOp3rdCertPayableUSD: 0,
      rsOpINSPayableRMB: 0,
      rsOpINSPayableUSD: 0,
      rsOpTradingPayableRMB: 0,
      rsOpTradingPayableUSD: 0,
      rsOpFumigationPayableRMB: 0,
      rsOpFumigationPayableUSD: 0,
      rsOpCOPayableRMB: 0,
      rsOpCOPayableUSD: 0,

      // 表单状态变量（兼容模板直接引用）
      outboundForm: null,
      openOutbound: false,
      outboundData: {},
      formType: null,
      serviceList: new Set(),
      companyList: [],
      grossWeight: null,
      goodsValue: null,
      locationOptions: [],
      logisticsType: null,
      carrierIds: [],
      carrierList: [],
      clientDocList: [],
      logisticsProgressList: [],
      belongList: [],
      businessList: [],
      opList: [],
      // 客户信息数据
      rsClientMessage: {
        rsChargeList: [],
        rsOpLogList: [],
        rsDocList: []
      },
      // new 子服务数据
      rsOpSeaFcl: createServiceObject(),
      rsOpSeaLcl: createServiceObject(),
      rsOpAir: createServiceObject(),
      rsOpRailFCL: createServiceObject(false),
      rsOpRailLCL: createServiceObject(false),
      rsOpRail: createServiceObject(false),
      rsOpExpress: createServiceObject(false),
      rsOpTruck: createServiceObject(false),
      rsOpCtnrTruck: createServiceObject(true, {rsOpTruckList: []}),
      rsOpBulkTruck: createServiceObject(true, {rsOpTruckList: []}),
      // 正单报关
      rsOpDocDeclare: createServiceObject(),
      // 全包报关
      rsOpFreeDeclare: createServiceObject(),
      rsOpImportDispatchTruck: createServiceObject(false),
      // 代理放单
      rsOpDOAgent: createServiceObject(false),
      rsOpClearAgent: createServiceObject(),
      rsOpWHS: createServiceObject(),
      rsOpWarehouse: createServiceObject(),
      rsOpInsurance: createServiceObject(),
      rsOpExpandService: createServiceObject(),
      rsOp3rdCert: createServiceObject(),
      rsOpINS: createServiceObject(),
      rsOpTrading: createServiceObject(),
      rsOpFumigation: createServiceObject(),
      rsOpCO: createServiceObject(),
      rsOpOther: createServiceObject(),
      // new 服务实例
      rsClientServiceInstance: createServiceInstance(),
      rsOpSeaFclServiceInstance: createServiceInstance(),
      rsOpSeaLclServiceInstance: createServiceInstance(),
      rsOpAirServiceInstance: createServiceInstance(),
      rsOpRailFclServiceInstance: createServiceInstance(),
      rsOpRailLclServiceInstance: createServiceInstance(),
      rsOpExpressServiceInstance: createServiceInstance(),
      rsOpCtnrTruckServiceInstance: createServiceInstance(),
      rsOpBulkTruckServiceInstance: createServiceInstance(),
      rsOpDocDeclareServiceInstance: createServiceInstance(),
      rsOpFreeDeclareServiceInstance: createServiceInstance(),
      rsOpDOAgentServiceInstance: createServiceInstance(),
      rsOpClearAgentServiceInstance: createServiceInstance(),
      rsOpWHSServiceInstance: createServiceInstance(),
      rsOp3rdCertServiceInstance: createServiceInstance(),
      rsOpINSServiceInstance: createServiceInstance(),
      rsOpTradingServiceInstance: createServiceInstance(),
      rsOpFumigationServiceInstance: createServiceInstance(),
      rsOpCOServiceInstance: createServiceInstance(),
      rsOpOtherServiceInstance: createServiceInstance(),
      list: new Set(),

      chargeOpen: false,
      chargeSearchData: {},
      chargeSelectItem: null,

      // 客户信息中的审核信息
      opConfirmedName: null,
      opConfirmedDate: null,
      accountConfirmedName: null,
      accountConfirmedDate: null,
      psaConfirmedName: null,
      psaConfirmedDate: null,
      salesConfirmedName: null,
      salesConfirmedDate: null,
      clientConfirmedName: null,
      clientConfirmedDate: null,
      RelationClientIdList: [],
      PaymentTitleCode: null,
      salesId: null,
      salesAssistantId: null,
      salesObserverId: null,
      verifyPsaId: null,
      bookingOpId: null,
      opId: null,
      docOpId: null,
      opObserverId: null,
      carrierId: null,
      basicServiceName: [],
      RelationClientList: [],
      relationClientLists: [],
      basicServiceId: [],
      freightSelectData: {},
      curFreightSelectRow: {},
      psaBookingSelectData: {},
      commonUsedSelectData: {},
      commonUsedType: null,
      ...CONFIG.SERVICE_SETS,
      bookingMessageTitle: null,
      supplierList: [],
      bookingMessageList: [],
      bookingMessageForm: {},
      bookingBillPrintRow: null,
      bookingMessageStatus: null,
      selectedPrintCharges: [],
      ctnrTypeCodeIds: [],
      cargoTypeCodes: [],
      showPsaRct: false,
      rsServiceInstances: {
        accountConfirmTime: null,
        agreementNo: "",
        agreementTypeCode: null,
        clientConfirmedTime: null,
        confirmAccountId: null,
        createBy: null,
        createByName: null,
        createTime: null,
        deleteBy: null,
        deleteByName: null,
        deleteStatus: null,
        deleteTime: null,
        inquiryInnerRemark: "",
        inquiryLeatestUpdatedTime: null,
        inquiryNo: null,
        inquiryNotice: null,
        inquiryPsaId: null,
        isAccountConfirmed: null,
        isDnClientConfirmed: null,
        isDnOpConfirmed: null,
        isDnPsaConfirmed: null,
        isDnSalesConfirmed: null,
        isDnSupplierConfirmed: null,
        logisticsPaymentTermsCode: null,
        maxWeight: null,
        opConfirmedTime: null,
        paymentTitleCode: null,
        permissionLevel: null,
        psaConfirmedTime: null,
        rctId: null,
        rctNo: null,
        remark: null,
        salesConfirmedTime: null,
        serviceBelongTo: null,
        serviceId: null,
        serviceTypeId: null,
        supplierConfirmedTime: null,
        supplierContact: null,
        supplierId: null,
        supplierName: null,
        supplierSummary: null,
        supplierTel: null,
        updateBy: null,
        updateByName: null,
        updateTime: null,
        serviceFold: false
      },
      curPsaRow: null
    }
  },
  watch: {
    "form.logisticsTypeId"(n) {
      if (this.$store.state.data.serviceTypeList.length == 0 || this.$store.state.data.redisList.serviceType) {
        store.dispatch("getServiceTypeList").then(() => {
          this.getType(n)
        })
      } else {
        this.getType(n)
      }
    },
    "rsClientMessage.rsChargeList": {
      handler(n, rsOpService) {
        this._debouncedCalculateFinancials(n)
      },
      immediate: false
    },
    "form.rsOpSeaFclList"(n) {
      this._updateServicePayables(n)
    },
    "form.rsOpSeaLclList"(n) {
      this._updateServicePayables(n)
    },
    "form.rsOpOther.rsChargeList"(n) {
      const result = this._calculateChargeAmount(n)
      this.rsOpOtherPayableRMB = result.rmbTaxTotal
      this.rsOpOtherPayableUSD = result.usdTaxTotal
    },
    "form.rsOpAirList"(n) {
      this._updateServicePayables(n)
    },
    "rsOpRailFCL.rsChargeList"(n) {
      const result = this._calculateChargeAmount(n)
      this.rsOpRailFclPayableRMB = result.rmbTaxTotal
      this.rsOpRailFclPayableUSD = result.usdTaxTotal
    },
    "rsOpRailLCL.rsChargeList"(n) {
      const result = this._calculateChargeAmount(n)
      this.rsOpRailLclPayableRMB = result.rmbTaxTotal
      this.rsOpRailLclPayableUSD = result.usdTaxTotal
    },
    "rsOpExpress.rsChargeList"(n) {
      const result = this._calculateChargeAmount(n)
      this.rsOpExpressPayableRMB = result.rmbTaxTotal
      this.rsOpExpressPayableUSD = result.usdTaxTotal
    },
    "form.rsOpCtnrTruckList"(n) {
      this._updateServicePayables(n)
    },
    "form.rsOpBulkTruckList"(n) {
      this._updateServicePayables(n)
    },
    "form.rsOpDocDeclareList"(n) {
      this._updateServicePayables(n)
    },
    "form.rsOpFreeDeclareList"(n) {
      this._updateServicePayables(n)
    },
    "rsOpDOAgent.rsChargeList"(n) {
      const result = this._calculateChargeAmount(n)
      this.rsOpDOAgentPayableRMB = result.rmbTaxTotal
      this.rsOpDOAgentPayableUSD = result.usdTaxTotal
    },
    "rsOpClearAgent.rsChargeList"(n) {
      const result = this._calculateChargeAmount(n)
      this.rsOpClearAgentPayableRMB = result.rmbTaxTotal
      this.rsOpClearAgentPayableUSD = result.usdTaxTotal
    },
    "rsOpWHS.rsChargeList"(n) {
      const result = this._calculateChargeAmount(n)
      this.rsOpWHSPayableRMB = result.rmbTaxTotal
      this.rsOpWHSPayableUSD = result.usdTaxTotal
    },
    "rsOp3rdCert.rsChargeList"(n) {
      const result = this._calculateChargeAmount(n)
      this.rsOp3rdCertPayableRMB = result.rmbTaxTotal
      this.rsOp3rdCertPayableUSD = result.usdTaxTotal
    },
    "rsOpINS.rsChargeList"(n) {
      const result = this._calculateChargeAmount(n)
      this.rsOpINSPayableRMB = result.rmbTaxTotal
      this.rsOpINSPayableUSD = result.usdTaxTotal
    },
    "rsOpTrading.rsChargeList"(n) {
      const result = this._calculateChargeAmount(n)
      this.rsOpTradingPayableRMB = result.rmbTaxTotal
      this.rsOpTradingPayableUSD = result.usdTaxTotal
    },
    "rsOpFumigation.rsChargeList"(n) {
      const result = this._calculateChargeAmount(n)
      this.rsOpFumigationPayableRMB = result.rmbTaxTotal
      this.rsOpFumigationPayableUSD = result.usdTaxTotal
    },
    "rsOpCO.rsChargeList"(n) {
      const result = this._calculateChargeAmount(n)
      this.rsOpCOPayableRMB = result.rmbTaxTotal
      this.rsOpCOPayableUSD = result.usdTaxTotal
    }
  },
  computed: {
    // 通用表单禁用逻辑检查
    ...(() => {
      const createFormDisableComputed = (serviceInstanceName) => {
        return function () {
          const instance = this[serviceInstanceName]
          if (!instance) return false

          return !!(
            instance.isDnOpConfirmed ||
            instance.isDnPsaConfirmed ||
            instance.isDnSupplierConfirmed ||
            instance.isAccountConfirmed ||
            instance.isDnClientConfirmed ||
            instance.isDnSalesConfirmed
          )
        }
      }

      // 服务实例映射表
      const serviceInstanceMap = {
        rsOpSealFclFormDisable: "rsOpSeaFclServiceInstance",
        rsOpSealLclFormDisable: "rsOpSeaLclServiceInstance",
        rsOpAirFormDisable: "rsOpAirServiceInstance",
        rsOpRailFclFormDisable: "rsOpRailFclServiceInstance",
        rsOpRailLclFormDisable: "rsOpRailLclServiceInstance",
        rsOpExpressFormDisable: "rsOpExpressServiceInstance",
        rsOpCtnrTruckFormDisable: "rsOpCtnrTruckServiceInstance",
        rsOpBulkTruckFormDisable: "rsOpBulkTruckServiceInstance",
        rsOpDocDeclareFormDisable: "rsOpImportCustomsClearanceServiceInstance",
        rsOpFreeDeclareFormDisable: "rsOpExportCustomsClearanceServiceInstance",
        rsOpDOAgentFormDisable: "rsOpImportCustomsClearanceServiceInstance",
        rsOpClearAgentFormDisable: "rsOpClearAgentServiceInstance",
        rsOpWHSFormDisable: "rsOpWHSServiceInstance",
        rsClientMessageFormDisable: "rsClientServiceInstance",
        rsOp3rdCertFormDisable: "rsOp3rdCertServiceInstance",
        rsOpINSFormDisable: "rsOpINSServiceInstance",
        rsOpTradingFormDisable: "rsOpTradingServiceInstance",
        rsOpCOFormDisable: "rsOpCOServiceInstance"
      }

      // 动态生成所有FormDisable计算属性
      const result = {}
      Object.keys(serviceInstanceMap).forEach(computedName => {
        result[computedName] = createFormDisableComputed(serviceInstanceMap[computedName])
      })

      return result
    })(),

    // 操作单号禁用逻辑（特殊逻辑，保持独立）
    rctNoDisable() {
      if (!this.form.rctNo || !this.form.createTime) return false

      const today = new Date()
      const currentDateStr = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, "0")}-${today.getDate().toString().padStart(2, "0")}`
      const formDateStr = this.form.createTime.split(" ")[0]

      return formDateStr !== currentDateStr
    },
    disabled() {
      if (this.form.opAccept == 0 && this.op) {
        return true
      }
      if (this.booking && this.form.sqdShippingBookingStatus == 1) {
        return true
      }
      return false
    },
    // 拓展服务列表
    extendServiceList() {
      const extendServices = []

      // 根据serviceTypeId映射拓展服务数据
      const extendServiceMap = {
        90: { // 3rdCert
          data: this.rsOp3rdCert,
          serviceShortName: "第三方认证",
          serviceFold: this.rsOp3rdCertFold
        },
        100: { // Insurance
          data: this.rsOpINS,
          serviceShortName: "保险",
          serviceFold: this.rsOpINSFold
        },
        101: { // Trading
          data: this.rsOpTrading,
          serviceShortName: "贸易",
          serviceFold: this.rsOpTradingFold
        },
        102: { // Fumigation
          data: this.rsOpFumigation,
          serviceShortName: "熏蒸",
          serviceFold: this.rsOpFumigationFold
        },
        103: { // CO
          data: this.rsOpCO,
          serviceShortName: "产地证",
          serviceFold: this.rsOpCOFold
        },
        104: { // Other
          data: this.rsOpOther,
          serviceShortName: "其他",
          serviceFold: this.rsOpOtherFold
        }
      }

      // 遍历EXTEND集合，构建拓展服务列表
      Array.from(this.EXTEND).forEach(item => {
        const serviceConfig = extendServiceMap[item.serviceTypeId]
        if (serviceConfig && serviceConfig.data) {
          extendServices.push({
            serviceTypeId: item.serviceTypeId,
            serviceShortName: serviceConfig.serviceShortName,
            serviceFold: serviceConfig.serviceFold,
            rsServiceInstances: serviceConfig.data.rsServiceInstances || {},
            rsChargeList: serviceConfig.data.rsChargeList || [],
            rsOpLogList: serviceConfig.data.rsOpLogList || [],
            rsDocList: serviceConfig.data.rsDocList || [],
            payableRMB: serviceConfig.data.payableRMB || 0,
            payableUSD: serviceConfig.data.payableUSD || 0,
            payableRMBTax: serviceConfig.data.payableRMBTax || 0,
            payableUSDTax: serviceConfig.data.payableUSDTax || 0
          })
        }
      })

      return extendServices
    }
  },
  beforeMount() {
    if (this.$store.state.data.exchangeRateList.length == 0 || this.$store.state.data.redisList.exchangeRateList) {
      store.dispatch("getExchangeRate")
    }

    if (this.$route.query.type === "op") {
      this.op = true
    }
    if (this.$store.state.user.deptNum.has(4)) {
      this.finance = true
    }
    if (this.$route.query.psaVerify) {
      this.psaVerify = true
    }
    if (this.$route.query.booking) {
      this.booking = true
    }

    this.loadSelection()
    this.reset()

    this.formType = "rct"
    // 如果是来自于操作单列表修改,则会通过路由传递操作单id--rId
    if (this.$route.query.rId) {
      this.formType = "rct"
      this.getRctDetail(this.$route.query.rId).then(() => {
      })
    } else if (this.$route.query.bId) {
      // 订舱单
      this.formType = "booking"
      this.getBookingDetail(this.$route.query.bId).then(() => {
      })
    } else if (this.$route.query.id) {
      // 如果是来自报价列表的订舱申请
      this.formType = "booking"
      this.getQuotation(this.$route.query.id)
    }
  },
  mounted() {
    // 初始化防抖函数
    this._debouncedCalculateFinancials = _.debounce(this._calculateFinancials, 300)

    // 初始化服务管理器
    this.serviceManager = this._createServiceManager()

    /**
     * 监听悬浮拖拽区域
     */
    this.$nextTick(() => {
      // 获取DOM元素
      let dragArea = this.$refs.dragArea
      // 缓存 clientX clientY 的对象: 用于判断是点击事件还是移动事件
      let clientOffset = {}
      // 绑定鼠标按下事件
      dragArea.addEventListener("mousedown", (event) => {
        let offsetX = dragArea.getBoundingClientRect().left // 获取当前的x轴距离
        let offsetY = dragArea.getBoundingClientRect().top // 获取当前的y轴距离
        let innerX = event.clientX - offsetX // 获取鼠标在方块内的x轴距
        let innerY = event.clientY - offsetY // 获取鼠标在方块内的y轴距
        // console.log(offsetX, offsetY, innerX, innerY);
        // 缓存 clientX clientY
        clientOffset.clientX = event.clientX
        clientOffset.clientY = event.clientY
        // 鼠标移动的时候不停的修改div的left和top值
        document.onmousemove = function (event) {
          dragArea.style.left = event.clientX - innerX + "px"
          dragArea.style.top = event.clientY - innerY + "px"
          // dragArea 距离顶部的距离
          let dragAreaTop = window.innerHeight - dragArea.getBoundingClientRect().height
          // dragArea 距离左部的距离
          let dragAreaLeft = window.innerWidth - dragArea.getBoundingClientRect().width
          // 边界判断处理
          // 1、设置左右不能动
          // dragArea.style.left = dragAreaLeft + "px";

          // 1.设置左侧边界
          if (dragArea.getBoundingClientRect().left <= 0) {
            dragArea.style.left = "0px"
          }
          // 2.设置右侧边界
          if (dragArea.getBoundingClientRect().left >= dragAreaLeft) {
            dragArea.style.left = dragAreaLeft + "px"
          }
          // 3、设置顶部边界
          if (dragArea.getBoundingClientRect().top <= 0) {
            dragArea.style.top = "0px"
          }
          // 4、设置底部边界
          if (dragArea.getBoundingClientRect().top >= dragAreaTop) {
            dragArea.style.top = dragAreaTop + "px"
          }
        }
        // 鼠标抬起时，清除绑定在文档上的mousemove和mouseup事件；否则鼠标抬起后还可以继续拖拽方块
        document.onmouseup = function () {
          document.onmousemove = null
          document.onmouseup = null
        }
      }, false)
      // 绑定鼠标松开事件
      dragArea.addEventListener("mouseup", (event) => {
        let clientX = event.clientX
        let clientY = event.clientY
        /* if (clientX === clientOffset.clientX && clientY === clientOffset.clientY) {
          console.log('click 事件');
        } else {
          console.log('drag 事件');
        } */
      })
    })

    // 初始化打印
    this.initPrint()
  },
  methods: {
    // 服务管理器 - 统一管理所有服务类型相关操作
    _createServiceManager() {
      return {
        // 服务类型配置映射表
        serviceTypeMap: {
          1: {
            property: "rsOpSeaFcl",
            listProperty: "rsOpSeaFclList",
            template: "rsOpSeaFcl",
            instance: "rsOpSeaFclServiceInstance",
            fold: "rsOpSealFclFold",
            payable: "rsOpSeaFclPayable"
          },
          2: {
            property: "rsOpSeaLcl",
            listProperty: "rsOpSeaLclList",
            template: "rsOpSeaLcl",
            instance: "rsOpSeaLclServiceInstance",
            fold: "rsOpSealLclFold",
            payable: "rsOpSeaLclPayable"
          },
          10: {
            property: "rsOpAir",
            listProperty: "rsOpAirList",
            template: "rsOpAir",
            instance: "rsOpAirServiceInstance",
            fold: "rsOpAirFold",
            payable: "rsOpAirPayable"
          },
          20: {
            property: "rsOpRailFCL",
            listProperty: null,
            template: "rsOpRailFCL",
            instance: "rsOpRailFclServiceInstance",
            fold: "rsOpRailFclFold",
            payable: "rsOpRailFclPayable"
          },
          21: {
            property: "rsOpRailLCL",
            listProperty: null,
            template: "rsOpRailLCL",
            instance: "rsOpRailLclServiceInstance",
            fold: "rsOpRailLclFold",
            payable: "rsOpRailLclPayable"
          },
          40: {
            property: "rsOpExpress",
            listProperty: null,
            template: "rsOpExpress",
            instance: "rsOpExpressServiceInstance",
            fold: "rsOpExpressFold",
            payable: "rsOpExpressPayable"
          },
          50: {
            property: "rsOpCtnrTruck",
            listProperty: "rsOpCtnrTruckList",
            template: "rsOpCtnrTruck",
            instance: "rsOpCtnrTruckServiceInstance",
            fold: "rsOpCtnrTruckFold",
            payable: "rsOpCtnrTruckPayable"
          },
          51: {
            property: "rsOpBulkTruck",
            listProperty: "rsOpBulkTruckList",
            template: "rsOpBulkTruck",
            instance: "rsOpBulkTruckServiceInstance",
            fold: "rsOpBulkTruckFold",
            payable: "rsOpBulkTruckPayable"
          },
          60: {
            property: "rsOpDocDeclare",
            listProperty: "rsOpDocDeclareList",
            template: "rsOpDocDeclare",
            instance: "rsOpImportCustomsClearanceServiceInstance",
            fold: "rsOpDocDeclareFold",
            payable: "rsOpDocDeclarePayable"
          },
          61: {
            property: "rsOpFreeDeclare",
            listProperty: "rsOpFreeDeclareList",
            template: "rsOpFreeDeclare",
            instance: "rsOpExportCustomsClearanceServiceInstance",
            fold: "rsOpFreeDeclareFold",
            payable: "rsOpFreeDeclarePayable"
          },
          70: {
            property: "rsOpDOAgent",
            listProperty: null,
            template: "rsOpDOAgent",
            instance: "rsOpImportCustomsClearanceServiceInstance",
            fold: "rsOpDOAgentFold",
            payable: "rsOpDOAgentServiceInstance"
          },
          71: {
            property: "rsOpClearAgent",
            listProperty: null,
            template: "rsOpClearAgent",
            instance: "rsOpClearAgentServiceInstance",
            fold: "rsOpClearAgentFold",
            payable: "rsOpClearAgentPayable"
          },
          80: {
            property: "rsOpWHS",
            listProperty: null,
            template: "rsOpWHS",
            instance: "rsOpWHSServiceInstance",
            fold: "rsOpWHSFold",
            payable: "rsOpWHSPayable"
          },
          90: {
            property: "rsOp3rdCert",
            listProperty: null,
            template: "rsOp3rdCert",
            instance: "rsOp3rdCertServiceInstance",
            fold: "rsOp3rdCertFold",
            payable: "rsOp3rdCertPayable"
          },
          100: {
            property: "rsOpINS",
            listProperty: null,
            template: "rsOpINS",
            instance: "rsOpINSServiceInstance",
            fold: "rsOpINSFold",
            payable: "rsOpINSPayable"
          },
          101: {
            property: "rsOpTrading",
            listProperty: null,
            template: "rsOpTrading",
            instance: "rsOpTradingServiceInstance",
            fold: "rsOpTradingFold",
            payable: "rsOpTradingPayable"
          },
          102: {
            property: "rsOpFumigation",
            listProperty: null,
            template: "rsOpFumigation",
            instance: "rsOpFumigationServiceInstance",
            fold: "rsOpFumigationFold",
            payable: "rsOpFumigationPayable"
          },
          103: {
            property: "rsOpCO",
            listProperty: null,
            template: "rsOpCO",
            instance: "rsOpCOServiceInstance",
            fold: "rsOpCOFold",
            payable: "rsOpCOPayable"
          },
          104: {
            property: "rsOpOther",
            listProperty: null,
            template: "rsOpOther",
            instance: "rsOpOtherServiceInstance",
            fold: "rsOpOtherFold",
            payable: "rsOpOtherPayable"
          }
        },

        // 获取服务配置
        getConfig(serviceTypeId) {
          return this.serviceTypeMap[serviceTypeId] || null
        },

        // 获取服务对象
        getServiceObject(serviceTypeId, context) {
          const config = this.getConfig(serviceTypeId)
          return config ? context[config.property] : null
        },

        // 获取服务实例
        getServiceInstance(serviceTypeId, context) {
          const config = this.getConfig(serviceTypeId)
          return config ? context[config.instance] : null
        },

        // 获取应付对象
        getPayable(serviceTypeId, context) {
          const config = this.getConfig(serviceTypeId)
          return config && config.payable ? context[config.payable] : null
        },

        // 切换折叠状态
        toggleFold(serviceTypeId, context) {
          const config = this.getConfig(serviceTypeId)
          if (config && config.fold) {
            context[config.fold] = !context[config.fold]
          }
        },

        // 获取折叠状态
        getFoldState(serviceTypeId, context) {
          const config = this.getConfig(serviceTypeId)
          return config && config.fold ? context[config.fold] : false
        },

        // 批量处理服务实例
        processServiceInstances(serviceList, context) {
          serviceList.forEach(serviceTypeId => {
            const config = this.getConfig(serviceTypeId)
            if (config && context.serviceList.has(serviceTypeId)) {
              if (context[config.property] && context[config.instance]) {
                context[config.property].rsServiceInstances = context[config.instance]
                context.form[config.property] = context[config.property]
              }
            }
          })
        },

        // 获取所有配置的服务类型ID
        getAllServiceTypeIds() {
          return Object.keys(this.serviceTypeMap).map(id => parseInt(id))
        }
      }
    },

    // 通用服务删除函数
    _createServiceDeleteHandler(listProperty, extraValidation = null, warningMessage = "请先删除相关费用") {
      return (item) => {
        // 通用验证：检查费用列表
        if (item.rsChargeList && item.rsChargeList.length > 0) {
          this.$message.warning(warningMessage)
          return
        }

        // 额外验证（如检查PSA订舱号）
        if (extraValidation && extraValidation(item)) {
          this.$message.warning("请先删除相关费用或取消订舱")
          return
        }

        // 执行删除
        this.form[listProperty] = this.form[listProperty].filter(v => v !== item)
      }
    },

    // 通用服务添加函数
    _createServiceAddHandler(serviceTemplateProperty, listProperty) {
      return () => {
        const serviceTemplate = this._.cloneDeep(this[serviceTemplateProperty])
        serviceTemplate.rsChargeList = []
        serviceTemplate.rsOpLogList = []
        serviceTemplate.rsDocList = []
        serviceTemplate.rsServiceInstances = this.rsServiceInstances
        this.form[listProperty].push(serviceTemplate)
      }
    },

    // 服务配置映射表
    _getServiceConfig() {
      return {
        freeDeclare: {
          listProperty: "rsOpFreeDeclareList",
          templateProperty: "rsOpFreeDeclare",
          deleteValidation: null
        },
        docDeclare: {
          listProperty: "rsOpDocDeclareList",
          templateProperty: "rsOpDocDeclare",
          deleteValidation: null
        },
        bulkTruck: {
          listProperty: "rsOpBulkTruckList",
          templateProperty: "rsOpBulkTruck",
          deleteValidation: null
        },
        ctnrTruck: {
          listProperty: "rsOpCtnrTruckList",
          templateProperty: "rsOpCtnrTruck",
          deleteValidation: null
        },
        air: {
          listProperty: "rsOpAirList",
          templateProperty: "rsOpAir",
          deleteValidation: null
        },
        seaLcl: {
          listProperty: "rsOpSeaLclList",
          templateProperty: "rsOpSeaLcl",
          deleteValidation: (item) => item.sqdPsaNo
        },
        seaFcl: {
          listProperty: "rsOpSeaFclList",
          templateProperty: "rsOpSeaFcl",
          deleteValidation: (item) => item.sqdPsaNo
        }
      }
    },

    // ===================== 费用计算函数 =====================

    // 通用费用计算函数
    _calculateChargeAmount(chargeList) {
      if (!chargeList || !Array.isArray(chargeList)) {
        return {
          rmbTotal: 0,
          usdTotal: 0,
          rmbTaxTotal: 0,
          usdTaxTotal: 0,
          rmbUnpaid: 0,
          usdUnpaid: 0
        }
      }

      let rmbTotal = 0, usdTotal = 0, rmbTaxTotal = 0, usdTaxTotal = 0, rmbUnpaid = 0, usdUnpaid = 0

      chargeList.forEach(charge => {
        if (charge.subtotal) {
          const amount = parseFloat(charge.subtotal) || 0
          const taxRate = parseFloat(charge.dutyRate) || 0
          const amountWithoutTax = amount / (1 + taxRate / 100)
          const unpaidBalance = parseFloat(charge.sqdDnCurrencyBalance) || 0

          if (charge.dnCurrencyCode === "USD") {
            usdTotal += amountWithoutTax
            usdTaxTotal += amount
            usdUnpaid += unpaidBalance
          } else {
            rmbTotal += amountWithoutTax
            rmbTaxTotal += amount
            rmbUnpaid += unpaidBalance
          }
        }
      })

      return {rmbTotal, usdTotal, rmbTaxTotal, usdTaxTotal, rmbUnpaid, usdUnpaid}
    },

    // 计算服务列表的费用汇总
    _calculateServiceListAmount(serviceList) {
      if (!serviceList || !Array.isArray(serviceList)) {
        return {
          rmbTotal: 0,
          usdTotal: 0,
          rmbUnpaid: 0,
          usdUnpaid: 0,
          rmbTaxTotal: 0,
          usdTaxTotal: 0
        }
      }

      let rmbTotal = 0, usdTotal = 0, rmbTaxTotal = 0, usdTaxTotal = 0, rmbUnpaid = 0, usdUnpaid = 0

      serviceList.forEach(service => {
        if (service && service.rsChargeList) {
          const result = this._calculateChargeAmount(service.rsChargeList)
          rmbTotal += result.rmbTotal
          usdTotal += result.usdTotal
          rmbTaxTotal += result.rmbTaxTotal
          usdTaxTotal += result.usdTaxTotal
          rmbUnpaid += result.rmbUnpaid
          usdUnpaid += result.usdUnpaid
        }
      })

      return {rmbTotal, usdTotal, rmbUnpaid, usdUnpaid, rmbTaxTotal, usdTaxTotal}
    },

    // 更新服务项目的应付金额
    _updateServicePayables(serviceList) {
      if (!serviceList || !Array.isArray(serviceList)) return

      serviceList.forEach(service => {
        const result = this._calculateChargeAmount(service.rsChargeList)
        service.payableRMB = result.rmbTaxTotal
        service.payableUSD = result.usdTaxTotal
      })
    },

    // 财务数据计算主函数
    _calculateFinancials(n) {
      // 使用通用函数计算客户信息应收
      const receivableResult = this._calculateChargeAmount(n)

      // 更新应收相关数据
      this.rsClientMessageReceivableRMB = receivableResult.rmbTotal
      this.rsClientMessageReceivableUSD = receivableResult.usdTotal
      this.rsClientMessageReceivableTaxRMB = receivableResult.rmbTaxTotal
      this.rsClientMessageReceivableTaxUSD = receivableResult.usdTaxTotal
      this.sqdUnreceivedRmbSum = receivableResult.rmbUnpaid
      this.sqdUnreceivedUsdSum = receivableResult.usdUnpaid

      // 计算所有服务的应付费用
      const allServiceLists = [
        this.form.rsOpSeaFclList,
        this.form.rsOpSeaLclList,
        this.form.rsOpAirList,
        this.form.rsOpCtnrTruckList,
        this.form.rsOpBulkTruckList,
        this.form.rsOpDocDeclareList,
        this.form.rsOpFreeDeclareList
      ]

      const singleServices = [
        this.rsOpRailFCL,
        this.rsOpRailLCL,
        this.rsOpExpress,
        this.rsOpDOAgent,
        this.rsOpClearAgent,
        this.rsOpWHS,
        this.rsOp3rdCert,
        this.rsOpINS,
        this.rsOpTrading,
        this.rsOpFumigation,
        this.rsOpCO,
        this.rsOpOther
      ]

      // 计算服务列表总应付
      let totalPayable = {rmbTotal: 0, usdTotal: 0, rmbTaxTotal: 0, usdTaxTotal: 0, rmbUnpaid: 0, usdUnpaid: 0}

      allServiceLists.forEach(serviceList => {
        if (serviceList) {
          const result = this._calculateServiceListAmount(serviceList)
          totalPayable.rmbTotal += result.rmbTotal
          totalPayable.usdTotal += result.usdTotal
          totalPayable.rmbTaxTotal += result.rmbTaxTotal
          totalPayable.usdTaxTotal += result.usdTaxTotal
          totalPayable.rmbUnpaid += result.rmbUnpaid
          totalPayable.usdUnpaid += result.usdUnpaid
        }
      })

      // 计算单个服务应付
      singleServices.forEach(service => {
        if (service && service.rsChargeList) {
          const result = this._calculateChargeAmount(service.rsChargeList)
          totalPayable.rmbTotal += result.rmbTotal
          totalPayable.usdTotal += result.usdTotal
          totalPayable.rmbTaxTotal += result.rmbTaxTotal
          totalPayable.usdTaxTotal += result.usdTaxTotal
          totalPayable.rmbUnpaid += result.rmbUnpaid
          totalPayable.usdUnpaid += result.usdUnpaid
        }
      })

      // 更新应付相关数据
      this.rsClientMessagePayableRMB = totalPayable.rmbTotal
      this.rsClientMessagePayableUSD = totalPayable.usdTotal
      this.sqdUnpaidRmbSum = totalPayable.rmbUnpaid
      this.sqdUnpaidUsdSum = totalPayable.usdUnpaid

      // 计算利润（不含税）
      this.rsClientMessageProfitRMB = receivableResult.rmbTotal - totalPayable.rmbTotal
      this.rsClientMessageProfitUSD = receivableResult.usdTotal - totalPayable.usdTotal

      // 计算利润（含税）
      if (n.length > 0) {
        this.rsClientMessageProfitTaxRMB = receivableResult.rmbTaxTotal - totalPayable.rmbTaxTotal
        this.rsClientMessageProfitTaxUSD = receivableResult.usdTaxTotal - totalPayable.usdTaxTotal
      } else {
        this.rsClientMessageProfitTaxRMB = receivableResult.rmbTotal - totalPayable.rmbTaxTotal
        this.rsClientMessageProfitTaxUSD = receivableResult.usdTotal - totalPayable.usdTaxTotal
      }
      // console.log(receivableResult.rmbTaxTotal,totalPayable)
    },

    outboundPlan() {
      this.outboundData.rctId = this.form.rctId
      this.outboundData.clientName = this.form.clientSummary.split("/")[1]
      this.outboundData.customerOrderNo = this.form.rctNo
      this.outboundData.outboundType = "整柜"
      this.outboundData.operator = this.$store.state.data.allRsStaffList.find(item => item.staffId === this.form.opId).staffGivingEnName || ""
      this.outboundData.containerNo = this.form.sqdContainersSealsSum || ""
      this.outboundData.orderDate = moment().format("yyyy-MM-DD HH:mm:ss")
      this.openOutbound = true
    },
    handleStatusChange() {
      // 将进度设置为点击的最新日期

    },
    handleProfit() {

    },
    // 将对象属性值转换为大写（保持属性名不变）
    convertObjectKeysToUpperCase(obj) {
      if (!obj || typeof obj !== "object") return obj

      return Object.fromEntries(
        Object.entries(obj).map(([key, value]) => [
          key,
          typeof value === "string" ? value.toUpperCase() : value
        ])
      )
    },
    openChargeSelect(serviceObject) {
      this.chargeSelectItem = serviceObject

      this.chargeSearchData = {
        locationOptions: this.locationOptions,
        polId: this.form.polId,
        destinationPortId: this.form.destinationPortId,
        carrierId: this.form.carrierId,
        supplierId: serviceObject.supplierId,
        supplierList: this.supplierList
      }

      this.chargeOpen = true
    },
    auditCharge(serviceObject, rsChargeList) {
      this.$set(serviceObject, "rsChargeList", rsChargeList)
    },
    // 检查服务实例是否禁用（与computed中的逻辑一致）
    getServiceInstanceDisable(serviceInstance) {
      if (!serviceInstance) return false

      return !!(
        serviceInstance.isDnOpConfirmed ||
        serviceInstance.isDnPsaConfirmed ||
        serviceInstance.isDnSupplierConfirmed ||
        serviceInstance.isDnClientConfirmed ||
        serviceInstance.isDnSalesConfirmed
      )
    },
    // ===================== 服务删除函数（使用通用工具函数） =====================
    deleteRsOpFreeDeclare(item) {
      const config = this._getServiceConfig().freeDeclare
      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)
    },
    deleteRsOpDocDeclare(item) {
      const config = this._getServiceConfig().docDeclare
      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)
    },
    deleteRsOpBulkTruck(item) {
      const config = this._getServiceConfig().bulkTruck
      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)
    },
    deleteRsOpCtnrTruck(item) {
      const config = this._getServiceConfig().ctnrTruck
      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)
    },
    deleteRsOpLclSea(item) {
      const config = this._getServiceConfig().seaLcl
      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)
    },
    deleteRsOpFclSea(item) {
      const config = this._getServiceConfig().seaFcl
      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)
    },
    deleteRsOpAir(item) {
      const config = this._getServiceConfig().air
      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)
    },
    updateServiceInstance(serviceInstance) {
      updateServiceinstances(serviceInstance)
    },
    // 获取订舱状态文本(草稿箱-0/已订舱-1/已放舱-2/已使用-3/已取消--1)
    getBookingStatus(status) {
      const statusMap = {
        "0": "草稿箱",
        "1": "已订舱",
        "2": "已放舱",
        "3": "已使用",
        "-1": "已取消"
      }
      return statusMap[status] || "未知状态"
    },
    // ===================== 服务添加函数（使用通用工具函数） =====================
    addFreeDeclare() {
      const config = this._getServiceConfig().freeDeclare
      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()
    },
    addDocDeclare() {
      const config = this._getServiceConfig().docDeclare
      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()
    },
    addBulkTruck() {
      const config = this._getServiceConfig().bulkTruck
      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()
    },
    addCtnrTruck() {
      const config = this._getServiceConfig().ctnrTruck
      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()
    },
    addAir() {
      const config = this._getServiceConfig().air
      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()
    },
    addSeaLCL() {
      const config = this._getServiceConfig().seaLcl
      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()
    },
    addSeaFCL() {
      const config = this._getServiceConfig().seaFcl
      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()
    },
    psaBookingCancel(serviceObject) {
      if (this.form.seaId === null) {
        this.$message.warning("当前商务舱位未选择")
        return
      }
      this.$confirm("确认取消？", "", {confirmButtonText: "确认取消"})
        .then(_ => {
          // serviceObject.seaId = this.form.seaId
          serviceObject.rctNo = null
          serviceObject.sqdPsaNo = null
          serviceObject.clientShortName = null
          serviceObject.salesId = null
          serviceObject.salesAssistantId = null
          serviceObject.distributionStatus = "0"
          serviceObject.bookingStatus = "-1"
          // serviceObject.opId = this.$store.state.user.sid
          serviceObject.bookingId = this.$store.state.user.sid
          serviceObject.newBookingTime = moment().format("yyyy-MM-DD HH:mm:ss")

          // TODO
          this.submitForm()
          this.showPsaRct = false
        })

    },
    selectCommonUsed(row) {
      if (this.commonUsedType === "common") {
        this.form.bookingShipper = row.bookingShipper
        this.form.bookingConsignee = row.bookingConsignee
        this.form.bookingNotifyParty = row.bookingNotifyParty
        this.form.bookingAgent = row.bookingAgent
      }
      if (this.commonUsedType === "release") {
        this.bookingMessageForm.bookingShipper = row.bookingShipper
        this.bookingMessageForm.bookingConsignee = row.bookingConsignee
        this.bookingMessageForm.bookingNotifyParty = row.bookingNotifyParty
        this.bookingMessageForm.bookingAgent = row.bookingAgent
      }
      if (this.commonUsedType === "dispatch") {
        this.form.precarriageAddress = row.precarriageAddress
        this.form.precarriageTel = row.precarriageTel
        this.form.precarriageContact = row.precarriageContact
        this.form.precarriageRemark = row.precarriageRemark
      }

      this.openCommonUsedSelect = false
    },
    openDispatchCommon() {
      this.commonUsedSelectData.destinationPortId = this.form.destinationPortId
      this.commonUsedSelectData.polId = this.form.polId
      this.commonUsedSelectData.clientId = this.form.clientId
      this.commonUsedSelectData.type = "dispatch"

      this.commonUsedType = "dispatch"

      this.openCommonUsedSelect = true
    },
    openCommonUsed() {
      this.commonUsedSelectData.destinationPortId = this.form.destinationPortId
      this.commonUsedSelectData.polId = this.form.polId
      this.commonUsedSelectData.clientId = this.form.clientId
      this.commonUsedSelectData.type = "common"

      this.commonUsedType = "common"

      this.openCommonUsedSelect = true
    },
    openReleaseUsed() {
      this.commonUsedSelectData.destinationPortId = this.form.destinationPortId
      this.commonUsedSelectData.polId = this.form.polId
      this.commonUsedSelectData.clientId = this.form.clientId
      this.commonUsedSelectData.type = "release"

      this.commonUsedType = "release"

      this.openCommonUsedSelect = true
    },
    handleAddCommon(type) {
      let data = {}
      data.polId = this.form.polId
      data.destinationPortId = this.form.destinationPortId
      data.clientId = this.form.clientId
      if (type === "common") {
        data.bookingShipper = this.form.bookingShipper
        data.bookingConsignee = this.form.bookingConsignee
        data.bookingNotifyParty = this.form.bookingNotifyParty
        data.bookingAgent = this.form.bookingAgent
        data.serviceTypeId = this.form.logisticsTypeId
      }
      if (type === "release") {
        data.bookingShipper = this.bookingMessageForm.bookingShipper
        data.bookingConsignee = this.bookingMessageForm.bookingConsignee
        data.bookingNotifyParty = this.bookingMessageForm.bookingNotifyParty
        data.bookingAgent = this.bookingMessageForm.bookingAgent
        data.serviceTypeId = this.form.logisticsTypeId
      }
      if (type === "dispatch") {
        data.precarriageAddress = this.form.precarriageAddress
        data.precarriageContact = this.form.precarriageContact
        data.precarriageTel = this.form.precarriageTel
        data.serviceTypeId = 5
        data.dispatchAddress
        data.dispatchContact
        data.dispatchTel
        data.dispatchRemark
      }

      addClientsinfo(data).then(response => {
        this.$modal.msgSuccess("信息新增成功")
      })
    },
    customMerge(obj1, obj2) {
      return this._.mergeWith({}, obj1, obj2, (objValue, srcValue) => {
        if (this._.isNull(srcValue)) {
          return objValue
        }
        if (this._.isBoolean(objValue)) {
          return objValue
        }
        return undefined  // 由merge处理
      })
    },
    /**
     * 返回选择得商务订舱数据
     * @param row
     */
    selectPsaBooking(row) {
      this.$confirm("确认选择？选中信息会覆盖当前数据", "", {confirmButtonText: "确认覆盖"})
        .then(_ => {
          this.$refs["form"].validate(valid => {
            if (valid) {
              // 如果当前已有商务单号(说明已经选择),要先取消再选择
              if (this.form.sqdPsaNo !== null) {
                this.$message.warning("请先取消当前订舱")
                return
              }

              let mergeRow = this.customMerge(this.form.rsOpSeaFclList.filter(item => item.seaId === this.curPsaRow.seaId)[0], row)

              if (row.carrierId) {
                mergeRow.carrierId = row.carrierId
              }

              // 将选定的数据覆盖当前数据
              this.form.rsOpSeaFclList = this.form.rsOpSeaFclList.map(item => {
                if (item.seaId === this.curPsaRow.seaId) {
                  item = mergeRow
                }
                return item
              })
              // selectRow.sqdPsaNo = row.psaNo
              let locationIds = [row.polId, row.destinationPortId]
              locationOptions({locationSelectList: locationIds}).then(response => {
                this.locationOptions ? this.locationOptions = Array.from(new Map(this.locationOptions.concat(response.data).map(item => [item.locationId, item])).values()) : this.locationOptions = response.data
                // 更新商务订舱表
                let data = {}
                data.seaId = row.seaId
                data.rctNo = this.form.rctNo
                data.clientShortName = this.form.clientName.split("/")[1]
                data.salesId = this.form.salesId
                data.salesAssistantId = this.form.salesAssistantId
                data.distributionStatus = "1"
                data.opId = this.$store.state.user.sid
                data.newBookingTime = moment().format("yyyy-MM-DD HH:mm:ss")

                updatePsarct(data).then(_ => {
                  this.submitForm()
                })
                this.showPsaRct = true

                this.openPsaBookingSelect = false
              })
            } else {
              this.$message.warning("请先完整填写操作单")
            }
          })

        })
        .catch(_ => {
        })
    },
    selectPsaBookingOpen(item) {
      this.curPsaRow = item
      this.psaBookingSelectData.destinationPortId = this.form.destinationPortId
      this.psaBookingSelectData.polId = this.form.polId
      this.psaBookingSelectData.locationOptions = this.locationOptions

      this.openPsaBookingSelect = true
    },
    addProgress(rsOpLogList, processId) {
      let data = {}
      if (processId === 15) {
        data.processId = processId
        data.processStatusId = 7
        data.processStatusTime = moment().format("yyyy-MM-DD HH:mm:ss")
        data.opId = this.$store.state.user.sid
        data.basProcess = {processShortName: "装船"}
        data.basProcessStatus = {processStatusShortName: "完成"}
      }
      if (processId === 18) {
        data.processId = processId
        data.processStatusId = 7
        data.processStatusTime = moment().format("yyyy-MM-DD HH:mm:ss")
        data.opId = this.$store.state.user.sid
        data.basProcess = {processShortName: "到港"}
        data.basProcessStatus = {processStatusShortName: "完成"}
      }

      rsOpLogList.push(data)
    },
    // 应收勾选
    handleReceiveSelected(rows) {
      this.selectedPrintCharges = rows
    },
    // 提单打印
    getBillOfLading(type) {
      hiprintTemplate = null
      let data = {}

      let printRow = this.bookingBillPrintRow ? this.bookingBillPrintRow[0] : null
      if (!printRow) {
        this.$message.warning("请先勾选一条提单信息")
        return
      }

      let prefix = "<div style=\"word-break: keep-all !important; \n" +
        "            word-wrap: break-word !important; \n" +
        "            white-space: pre-wrap !important;\">"
      let suffix = "</div>"

      data.bookingShipper = printRow.bookingShipper ? printRow.bookingShipper.replace(/\n/g, "</br>") : null
      data.bookingShipper = prefix + data.bookingShipper + suffix
      data.bookingConsignee = printRow.bookingConsignee ? printRow.bookingConsignee.replace(/\n/g, "</br>") : null
      data.bookingConsignee = prefix + data.bookingConsignee + suffix
      data.bookingNotifyParty = printRow.bookingNotifyParty ? printRow.bookingNotifyParty.replace(/\n/g, "</br>") : null
      data.bookingNotifyParty = prefix + data.bookingNotifyParty + suffix
      data.bookingAgent = printRow.bookingAgent ? printRow.bookingAgent.replace(/\n/g, "</br>") : null
      data.bookingAgent = prefix + data.bookingAgent + suffix
      data.containerNo = printRow.containerNo
      data.rctNo = this.form.rctNo
      data.containerType = printRow.containerType
      data.contractNo = printRow.contractNo
      data.sealNo = printRow.sealNo
      data.shippingMark = printRow.shippingMark
      let packageQuantitySum = 0
      if (printRow.packageQuantity) {
        printRow.packageQuantity.split(/\n/g).map(item => {
          // 提取packageQuantity中的数字
          let num = item.match(/\d+/g)
          packageQuantitySum += Number(num)
        })
        // 最后加上原始的单位
        // 提取数字和单位，例如从"2 CARTONS"中提取出数字2和单位CARTONS
        const firstPackage = printRow.packageQuantity.split(/\n/g)[0]
        const unit = firstPackage.replace(/\d+/g, "").trim() // 去除数字，保留单位
        data.packageQuantity = packageQuantitySum + " " + unit
      }

      data.recelp = printRow.city ? printRow.city : "GUANG ZHOU"
      data.goodsDescription = printRow.goodsDescription ? printRow.goodsDescription.replace(/\n/g, "</br>") : null
      data.goodsDescription = prefix + data.goodsDescription + suffix
      let goodsVolumeSum = 0
      printRow.goodsVolume ? printRow.goodsVolume.split(/\n/g).map(item => {
        goodsVolumeSum += Number(item.trim())
      }) : null
      data.goodsVolume = goodsVolumeSum + "CBM"
      let grossWeightSum = 0
      printRow.grossWeight ? printRow.grossWeight.split(/\n/g).map(item => {
        grossWeightSum += Number(item.trim())
      }) : null
      data.grossWeight = grossWeightSum + "KGS"
      data.blTypeCode = printRow.blTypeCode
      data.blFormCode = printRow.blFormCode
      data.sqdDocDeliveryWay = printRow.sqdDocDeliveryWay
      data.polName = printRow.polName
      data.podName = printRow.destinationPort
      data.destinationPort = printRow.destinationPort
      // 货名
      data.goodsNameSummary = this.form.goodsNameSummary
      // so号
      data.soNo = this.form.soNo

      data.blNumbers = printRow.blNumbers
      data.issueDate = printRow.issueDate
      data.mblNo = printRow.mBlNo
      data.issuePlace = printRow.issuePlace
      const options = {
        day: "2-digit",
        month: "short",
        year: "numeric"
      }
      data.onBoardDate = printRow.onBoardDate ? new Date(printRow.onBoardDate).toLocaleDateString("en-GB", options).toUpperCase() : null
      data.declaredValue = printRow.declaredValue
      data.logisticsTerms = this.form.logisticsTerms
      data.firstVessel = this.form.firstVessel
      data.payWay = printRow.payWay
      data.forwardingAgent = printRow.bookingAgent
      data.blRemark = printRow.blRemark ? printRow.blRemark.replace(/\n/g, "</br>") : null
      let bookingNo = ""
      this.form.soNo ? this.form.soNo.split("/").map(item => {
        bookingNo += item + "</br>"
      }) : null
      data.bookingNo = bookingNo
      if (printRow.containerNo) {
        let sealNoArr = printRow.sealNo ? printRow.sealNo.split(/\n/g) : null
        let packageQuantityArr = printRow.packageQuantity ? printRow.packageQuantity.split(/\n/g) : null
        let goodsVolumeArr = printRow.goodsVolume ? printRow.goodsVolume.split(/\n/g) : null
        let containerTypeArr = printRow.containerType ? printRow.containerType.split(/\n/g) : null
        let grossWeightArr = printRow.grossWeight ? printRow.grossWeight.split(/\n/g) : null
        let containers = 0
        containerTypeArr ? containerTypeArr.map(item => {
          let revenue = item.split("x")
          containers = Number(revenue[0]) + Number(containers)
        }) : null
        data.containerQuantity = toWords(containers) + "(" + this.form.logisticsTypeEnName + ")"
        printRow.goodsSummary = ""
        printRow.containerNo.split(/\n/g).map((item, index) => {
          printRow.goodsSummary += item + "/" + ((sealNoArr && sealNoArr.length > index) ? sealNoArr[index] : null) + "/" + ((containerTypeArr && containerTypeArr.length > index) ? containerTypeArr[index] : null) + "/" + ((packageQuantityArr && packageQuantityArr.length > index) ? packageQuantityArr[index] : null) + "/" + ((grossWeightArr && grossWeightArr.length > index) ? grossWeightArr[index] : null) + "KGS" + "/" + ((goodsVolumeArr && goodsVolumeArr.length > index) ? goodsVolumeArr[index] : null) + "CBM" + "</br>"
        })
      }
      // 出单地
      data.issuePlace = ""
      data.carrier = printRow.containerType ? printRow.containerType.split(/\n/g).map(item => {
        return item + "</br>"
      }) : null
      data.goodsSummary = printRow.goodsSummary

      data = this.convertObjectKeysToUpperCase(data)
      if (type === "套打提单") {
        hiprintTemplate = new hiprint.PrintTemplate({template: billOfLading})
      } else {
        hiprintTemplate = new hiprint.PrintTemplate({template: billOfLadingRelease})
      }

      // 打开预览组件
      this.$refs.preView.print(hiprintTemplate, data)
    },
    closeBookingMessage() {
      this.openBookingMessage = false
      this.bookingMessageForm = {}
    },
    deleteBookingMessage(row) {
      this.bookingMessageList = this.bookingMessageList.filter(item => item.rsBookingMessageId !== row.rsBookingMessageId)
    },
    handleBookingMessageUpdate(row) {
      this.bookingMessageStatus = "edit"
      this.bookingMessageForm = row
      this.openBookingMessage = true
    },
    handleSelectionChange(val) {
      // 勾选要打印的提单,只能勾选一项
      if (val.length > 1) {
        this.$message.warning("只能勾选一条提单信息")
        return
      }

      this.bookingBillPrintRow = val
    },
    addBookingMessage(bookingForm) {
      // 合并bookingForm和this.bookingMessageForm
      this.bookingMessageForm = {
        rctId: this.form.rctId,
        polName: this.form.polName,
        podName: this.form.podName,
        destinationPort: this.form.destinationPort
      }
      Object.assign(this.bookingMessageForm, bookingForm)
      this.bookingMessageStatus = "add"
      this.openBookingMessage = true
    },
    bookingMessageConfirm(bookingForm) {
      if (this.bookingMessageStatus === "add") {
        this.bookingMessageList.push(bookingForm)
      }

      this.openBookingMessage = false
    },
    // 订舱单打印
    getBookingBill(serviceObject) {
      let data = {}
      data.bookingShipper = this.form.bookingShipper
      data.bookingConsignee = this.form.bookingConsignee
      data.bookingNotifyParty = this.form.bookingNotifyParty
      data.logisticsTerms = this.form.logisticsTerms
      data.tradingTerms = this.form.tradingTerms
      data.clientName = serviceObject.supplierName
      data.clientContact = this.form.clientContact
      let str = this.form.destinationPort ? this.form.destinationPort.split("(") : []
      data.destinationPort1 = str[0]
      data.destinationPort2 = str[1] ? ("(" + str[1]) : ""
      let polSplitArr = this.form.polName ? this.form.polName.split("(") : []
      data.polName = polSplitArr[0]
      data.polName1 = polSplitArr[1] ? ("(" + polSplitArr[1]) : ""
      let podSplitArr = this.form.podName ? this.form.podName.split("(") : []
      data.podName = podSplitArr[0]
      data.podName1 = podSplitArr[1] ? ("(" + podSplitArr[1]) : ""
      data.rctNo = this.form.rctNo
      data.packageQuantity = this.form.packageQuantity + "PKG"
      data.grossWeight = this.form.grossWeight + " KGS"
      data.goodsVolume = this.form.goodsVolume + " CBM"
      data.noDividedAllowedYes = this.form.noDividedAllowed ? "√" : ""
      data.noDividedAllowedNo = this.form.noDividedAllowed ? "" : "√"
      data.noTransferAllowedYes = this.form.noTransferAllowed ? "√" : ""
      data.noTransferAllowedNo = this.form.noTransferAllowed ? "" : "√"
      data.oceanVessel = (serviceObject.firstVessel ? serviceObject.firstVessel : "") + " " + (serviceObject.inquiryScheduleSummary ? serviceObject.inquiryScheduleSummary : "")
      data.printDate = moment().format("YYYY/MM/DD")
      data.clientContact = this.form.clientContact
      data.freightPaidWayCode = this.form.freightPaidWayCode
      data.logisticsTypeEnName = this.form.logisticsTypeEnName
      data.clientContactTel = this.form.clientContactTel
      data.bookingAgentRemark = serviceObject.bookingAgentRemark
      let chargeListStr = ""
      serviceObject.bookingChargeRemark ? serviceObject.bookingChargeRemark.split("\n").filter(item => item !== "").map(item => chargeListStr += item + "<br>") : ""
      data.bookingChargeRemark = chargeListStr
      data.shippingMark = this.form.shippingMark
      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {
        store.dispatch("getAllRsStaffList")
      }

      let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.$store.state.user.sid)[0]
      if (staff) {
        data.curTel = staff.staffPhoneNum
        data.curName = staff.staffShortName
        data.curEmail = staff.staffEmailEnterprise
      }

      data.goodsNameSummary = this.form.goodsNameSummary
      data.vesselSummary = "by " + (this.form.carrierEnName ? this.form.carrierEnName : "") + "</br> "
      data.vesselSummary += "ETD: " + (serviceObject.etd ? serviceObject.etd : "")

      data.revenueTon = this.form.revenueTon
      data.curDate = moment().format("YYYYMMDD")
      data.companyName = "广州瑞旗国际货运代理有限公司"
      if (this.form.orderBelongsTo === "GZRS") {
        data.companyName = "广州瑞旗国际货运代理有限公司"
      } else if (this.form.orderBelongsTo === "HKRS") {
        data.companyName = "瑞旗国际（中国）有限公司"
      } else if (this.form.orderBelongsTo === "CFL") {
        data.companyName = "广州正泽国际货运代理有限公司"
      } else if (this.form.orderBelongsTo === "GZVS") {
        data.companyName = "广州外海国际供应链有限公司"
      } else if (this.form.orderBelongsTo === "CASH") {
        data.companyName = "公司现金账户"
      }

      if (this.form.rctNo && this.form.rctNo.startsWith("CFL")) {
        hiprintTemplate = new hiprint.PrintTemplate({template: CFLBooking})
      } else {
        hiprintTemplate = new hiprint.PrintTemplate({template: booking})
      }
      // 打开预览组件
      this.$refs.preView.print(hiprintTemplate, data)
    },
    getDispatchingBill(serviceObject) {
      hiprintTemplate = null
      let data = {}
      data.truckList = serviceObject.rsOpTruckList
      // data.company = (this.form.clientName.split("/")[2] === "null" || this.form.clientName.split("/")[2] === "") ? this.form.clientName.split("/")[1] : this.form.clientName.split("/")[2]
      data.company = serviceObject.supplierName
      data.clientContact = this.form.clientContact
      // 货名
      data.goodsNameSummary = this.form.goodsNameSummary
      // so号
      data.soNo = this.form.soNo
      data.packageQuantity = this.form.packageQuantity + " PACKAGES"
      data.grossWeight = this.form.grossWeight + " KGS"
      data.goodsVolume = this.form.goodsVolume + " CBM"
      data.revenueTon = this.form.revenueTon
      data.carrierEnName = this.form.sqdCarrier ? this.form.sqdCarrier : this.form.carrierEnName
      data.pol = this.form.pol
      data.pod = this.form.destinationPort
      data.podName = this.form.podName
      data.precarriageTime = moment(serviceObject.precarriageTime).format("yyyy-MM-DD HH:mm:ss")
      data.precarriageAddress = serviceObject.precarriageAddress
      data.precarriageContact = serviceObject.precarriageContact
      data.precarriageRemark = serviceObject.precarriageRemark
      data.backLocation
      data.REF = this.form.rctNo
      data.subtotal = 0
      serviceObject.rsChargeList.map(item => {
        item.subtotal ? data.subtotal = currency(item.subtotal).add(data.subtotal).value : null
      })

      // 用于组装pdf文件名
      data.pdfName = "[派车单]" + "-" + this.form.rctNo + "-" + this.form.revenueTon + "-" + serviceObject.rsServiceInstances.supplierSummary + "-" + moment().format("YYYYMMDD")

      hiprintTemplate = new hiprint.PrintTemplate({template: dispatchBill})
      // 打开预览组件
      this.$refs.preView.print(hiprintTemplate, data)
    },
    // 操作单打印
    getOpBill(type) {
      hiprintTemplate = null
      let data = {}
      data.title = this.form.logisticsTypeEnName
      data.chargeList = this.selectedPrintCharges
      data.rctNo = this.form.rctNo
      data.revenueTon = this.form.revenueTon
      data.printDate = moment().format("YYYY/MM/DD")
      data.company = this.form.clientName.split("/")[2] === "null" || this.form.clientName.split("/")[2] === "" ? this.form.clientName.split("/")[1] : this.form.clientName.split("/")[2]
      data.clientContact = this.form.clientContact
      let qoutationSketch = ""
      let qoutationSketchArr = this.form.qoutationSketch ? this.form.qoutationSketch.split("\n") : null
      qoutationSketchArr ? qoutationSketchArr.map(item => {
        qoutationSketch += item + "</br>"
      }) : null
      data.qoutationSketch = qoutationSketch
      data.contract = this.form.contract
      data.clientContactTel = this.form.clientContactTel
      data.revenueTon = this.form.revenueTon
      data.newBookingRemark = this.form.newBookingRemark
      data.carrierEnName = this.form.carrierEnName
      data.grossWeight = this.form.grossWeight + "KGS"
      data.tradingTerms = this.getTradingTerms(this.form.tradingTerms)
      data.goodsNameSummary = this.form.goodsNameSummary
      data.vessel = this.form.cvClosingTime ? this.form.cvClosingTime : "**"
      data.truk = this.form.truk
      data.pol = this.form.pol
      const index = this.form.pol ? this.form.pol.indexOf("(") : -1
      data.pol1 = index !== -1 ? this.form.pol.slice(0, index) : ""
      data.pol2 = index !== -1 ? this.form.pol.slice(index) : ""
      data.pod = this.form.destinationPort
      const index2 = this.form.destinationPort ? this.form.destinationPort.indexOf("(") : -1
      data.pod1 = index2 !== -1 ? this.form.destinationPort.slice(0, index2) : ""
      data.pod2 = index2 !== -1 ? this.form.destinationPort.slice(index2) : ""
      data.podName = this.form.podName
      const index3 = this.form.podName ? this.form.podName.indexOf("(") : -1
      data.podName1 = index3 !== -1 ? this.form.podName.slice(0, index3) : ""
      data.podName2 = index3 !== -1 ? this.form.podName.slice(index3) : ""
      data.soNo = this.form.soNo
      //放货方式
      data.releaseType = this.getReleaseType(this.form.releaseType)
      // 出单方式
      data.ORG = this.form.blFormCode === "ORG" ? "√" : ""
      data.TLX = this.form.blFormCode === "TLX" ? "√" : ""
      data.SWB = this.form.blFormCode === "SWB" ? "√" : ""
      data.sign = this.getName(this.form.salesId)
      data.signDate = this.form.rctCreateTime
      data.orderBelongsTo = this.form.orderBelongsTo
      data.packageQuantity = this.form.packageQuantity
      data.goodsVolume = this.form.goodsVolume + "CBM"
      data.firstVessel = this.form.firstVessel
      data.transType
      data.trcukAddr = ""
      this.form.rsOpCtnrTruckList ? this.form.rsOpCtnrTruckList.map(item => {
        item.precarriageAddress ? data.trcukAddr += (item.precarriageAddress + "</br>") : null
        return item
      }) : null
      this.form.rsOpBulkTruckList ? this.form.rsOpBulkTruckList.map(item => {
        item.precarriageAddress ? data.trcukAddr += (item.precarriageAddress + "</br>") : null
        return item
      }) : null
      let total = 0
      let USD = 0
      let RMB = 0
      this.selectedPrintCharges.map(item => {
        // total = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).add(total).value
        if (item.dnCurrencyCode === "USD") {
          USD = currency(USD).add(currency(item.dnUnitRate).multiply(item.dnAmount)).value
        }
        if (item.dnCurrencyCode === "RMB") {
          RMB = currency(RMB).add(currency(item.dnUnitRate).multiply(item.dnAmount)).value
        }
      })
      data.total = (USD ? ("USD: " + currency(USD).value + " ") : " ") + (RMB ? ("RMB: " + currency(RMB).value) : " ")

      data.pdfName = "[操作单]" + "-" + this.form.rctNo + moment().format("YYYY-MM-DD")
      data.bookingRemark = this.form.newBookingRemark
      data.secondVessel = this.form.secondVessel ? this.form.secondVessel : "**"
      data.includes

      data.tradingPaymentChannel = this.form.freightPaidWayCode
      data.clearType = this.form.serviceTypeIds.includes(60) ? "单证报关" : "无单报关"
      data.tradingTerms = this.form.tradingTerms
      data.supplierName = "**"
      if (this.form.rsOpSeaFclList && this.form.rsOpSeaFclList[0]) {
        data.supplierName = this.form.rsOpSeaFclList[0].supplierName ? this.form.rsOpSeaFclList[0].supplierName : "**"
      }
      if (this.form.rsOpSeaLclList && this.form.rsOpSeaLclList[0]) {
        data.supplierName = this.form.rsOpSeaLclList[0].supplierName ? this.form.rsOpSeaLclList[0].supplierName : "**"
      }

      data.carrierEnName = this.form.carrierEnName
      // 应收
      data.receive = ""
      this.rsClientMessage.rsChargeList ? this.rsClientMessage.rsChargeList.map(v => {
        data.receive = data.receive + v.chargeName + "/" + v.dnUnitCode + "  " + v.dnUnitRate + "/" + v.dnCurrencyCode + "</br>"
      }) : null
      // 应付
      data.pay = ""
      this.form.rsOpSeaFclList ? this.form.rsOpSeaFclList.map(item => {
        item.rsChargeList ? item.rsChargeList.map(v => {
          data.pay = data.pay + v.chargeName + "/" + v.dnUnitCode + "  " + v.dnUnitRate + "/" + v.dnCurrencyCode + "</br>"
        }) : null
      }) : null
      this.form.rsOpSeaLclList ? this.form.rsOpSeaLclList.map(item => {
        item.rsChargeList ? item.rsChargeList.map(v => {
          data.pay = data.pay + v.chargeName + "/" + v.dnUnitCode + "  " + v.dnUnitRate + "/" + v.dnCurrencyCode + "</br>"
        }) : null
      }) : null
      this.form.rsOpAirList ? this.form.rsOpAirList.map(item => {
        item.rsChargeList ? item.rsChargeList.map(v => {
          data.pay = data.pay + v.chargeName + "/" + v.dnUnitCode + "  " + v.dnUnitRate + "/" + v.dnCurrencyCode + "</br>"
        }) : null
      }) : null
      this.form.rsOpFreeDeclareList ? this.form.rsOpFreeDeclareList.map(item => {
        item.rsChargeList ? item.rsChargeList.map(v => {
          data.pay = data.pay + v.chargeName + "/" + v.dnUnitCode + "  " + v.dnUnitRate + "/" + v.dnCurrencyCode + "</br>"
        }) : null
      }) : null
      this.form.rsOpDocDeclareList ? this.form.rsOpDocDeclareList.map(item => {
        item.rsChargeList ? item.rsChargeList.map(v => {
          data.pay = data.pay + v.chargeName + "/" + v.dnUnitCode + "  " + v.dnUnitRate + "/" + v.dnCurrencyCode + "</br>"
        }) : null
      }) : null
      this.form.rsOpBulkTruckList ? this.form.rsOpBulkTruckList.map(item => {
        item.rsChargeList ? item.rsChargeList.map(v => {
          data.pay = data.pay + v.chargeName + "/" + v.dnUnitCode + "  " + v.dnUnitRate + "/" + v.dnCurrencyCode + "</br>"
        }) : null
      }) : null
      this.form.rsOpCtnrTruckList ? this.form.rsOpCtnrTruckList.map(item => {
        item.rsChargeList ? item.rsChargeList.map(v => {
          data.pay = data.pay + v.chargeName + "/" + v.dnUnitCode + "  " + v.dnUnitRate + "/" + v.dnCurrencyCode + "</br>"
        }) : null
      }) : null

      this.form.serviceTypeIds ? this.form.serviceTypeIds.map(v => {
        this.getServiceObject(v) ? this.getServiceObject(v).rsChargeList.map(v => {
          data.pay = data.pay + v.chargeName + "/" + v.dnUnitCode + "  " + v.dnUnitRate + "/" + v.dnCurrencyCode + "</br>"
        }) : null
      }) : null

      if (type === "整柜") {
        hiprintTemplate = new hiprint.PrintTemplate({template: FCLBill})
      } else if (type === "散货") {
        hiprintTemplate = new hiprint.PrintTemplate({template: FCLBill})
      } else if (type === "空运") {
        hiprintTemplate = new hiprint.PrintTemplate({template: AirBill})
      } else {
        hiprintTemplate = new hiprint.PrintTemplate({template: FCLBill})
      }

      // 打开预览组件
      this.$refs.preView.print(hiprintTemplate, data)
    },
    getReleaseType(id) {
      if (id == 1) return "月结"
      if (id == 2) return "押放"
      if (id == 3) return "票结"
      if (id == 4) return "签放"
      if (id == 5) return "订金"
      if (id == 6) return "预付"
      if (id == 7) return "扣货"
      if (id == 9) return "居间"
      return ""
    },
    getTradingTerms(id) {
      if (id == 1) return "EXW"
      if (id == 2) return "FCA"
      if (id == 3) return "FOB"
      if (id == 4) return "CNF"
      if (id == 5) return "CIF"
      if (id == 6) return "DDU"
      if (id == 7) return "DDP"
      return ""
    },
    // 费用清单打印
    getChargeListBill(type) {
      // 如果用户勾选了不同结算单位的费用,给出提醒
      let continuePrint = true
      this.selectedPrintCharges.map(item => {
        if (item.clearingCompanyId !== this.selectedPrintCharges[0].clearingCompanyId) {
          continuePrint = false
          return
        }
      })
      if (!continuePrint) {
        this.$message.warning("请选择同一结算单位的费用")
        return
      }

      hiprintTemplate = null
      let data = {}

      let chargeList
      if (this.$store.state.data.chargeList.length == 0 || this.$store.state.data.redisList.charge) {
        store.dispatch("getChargeList").then(() => {
          chargeList = this.$store.state.data.chargeList
        })
      } else {
        chargeList = this.$store.state.data.chargeList
      }

      data.rctNo = this.form.rctNo
      data.containerNo = this.form.rsOpSeaFclList ? this.form.rsOpSeaFclList[0].sqdContainersSealsSum : (this.form.rsOpSeaLclList ? this.form.rsOpSeaLclList[0].sqdContainersSealsSum : null)
      data.revenueTon = this.form.revenueTon
      data.contractNo = this.form.clientJobNo
      data.printDate = moment().format("YYYY/MM/DD")
      let searchV = this.companyList.filter(item => item.companyId === this.selectedPrintCharges[0].clearingCompanyId)[0]
      data.company = searchV ? searchV.companyLocalName : ""
      data.clientContact = this.form.clientContact
      data.carrierEnName = this.form.carrierEnName
      data.pol = this.form.pol
      data.pod = this.form.destinationPort
      data.soNo = (this.form.rsOpAirList && this.form.rsOpAirList.length > 0) ? this.form.rsOpAirList[0].soNo : this.form.soNo
      data.transType = this.form.serviceTypeIds.includes(1) ? "FCL" : "LCL"

      data.chargeList = this.selectedPrintCharges.map(item => {
        return {
          ...item,
          subtotal: (type === "CN-广州正泽[USD->RMB]" || type === "CN-广州瑞旗[USD->RMB]") ? currency(item.subtotal).multiply(item.basicCurrencyRate).value : ((type === "EN- 瑞旗香港账户[HSBC RMB->USD]" || type === "EN-广州瑞旗[RMB->USD]") ? currency(item.subtotal, {precision: 2}).divide(item.basicCurrencyRate).value : item.subtotal),
          dutyRate: currency(item.dutyRate, {symbol: ""}).format() + "%",
          chargeName: (type === "EN-广州瑞旗[RMB->USD]" || type === "EN-广州瑞旗[招行USD]" || type === "EN- 瑞旗香港账户[HSBC RMB->USD]" || type === "EN- 香港瑞旗[HSBC]") ? chargeList.filter(v => v.chargeId === item.dnChargeNameId)[0].chargeEnName.toUpperCase() : item.chargeName
        }
      })
      let total = 0
      let USD = 0
      let RMB = 0
      data.chargeList.forEach(item => {
        if (type === "EN-广州瑞旗[RMB->USD]" || type === "EN- 瑞旗香港账户[HSBC RMB->USD]") {
          if (item.dnCurrencyCode === "USD") {
            USD = currency(USD).add(item.subtotal).value
          }
          if (item.dnCurrencyCode === "RMB") {
            USD = currency(USD).add(item.subtotal).value
          }
        } else if (type === "CN-广州瑞旗[USD->RMB]" || type === "CN-广州正泽[USD->RMB]") {
          if (item.dnCurrencyCode === "USD") {
            RMB = currency(RMB).add(item.subtotal).value
          }
          if (item.dnCurrencyCode === "RMB") {
            RMB = currency(RMB).add(item.subtotal).value
          }
        } else {
          // total = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).add(total).value
          if (item.dnCurrencyCode === "USD") {
            USD = currency(USD).add(item.subtotal).value
          }
          if (item.dnCurrencyCode === "RMB") {
            RMB = currency(RMB).add(item.subtotal).value
          }
        }
      })
      data.USD = currency(USD).value
      data.RMB = currency(RMB).value
      // data.total = (USD ? ("USD: " + currency(USD).value + "</br>") : " ") + (RMB ? ("RMB: " + currency(RMB).value) : " ")

      data.pdfName = "[费用清单]" + "-" + this.form.rctNo + moment().format("YYYY-MM-DD")

      data.pol = (type === "EN- 瑞旗香港账户[HSBC RMB->USD]" || type === "EN-广州瑞旗[招行USD]" || "EN-广州瑞旗[RMB->USD]" === type || type === "EN- 香港瑞旗[HSBC]") ? pinyin.getFullChars(this.form.pol.slice(0, this.form.pol.indexOf("("))).toUpperCase() : data.pol

      if (type === "CN-广州瑞旗[招行USD+工行RMB]") {
        hiprintTemplate = new hiprint.PrintTemplate({template: debitNode})
      } else if (type === "CN-广州瑞旗[USD->RMB]") {
        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeUSDToRMB})
      } else if (type === "EN- 香港瑞旗[HSBC]") {
        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeEn})
      } else if (type === "EN-广州瑞旗[招行USD]") {
        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeZSUSD})
      } else if (type === "CN-广州正泽[招行USD+RMB]") {
        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeCFL})
      } else if (type === "EN-广州瑞旗[RMB->USD]") {
        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeZSUSD})
      } else if (type === "CN-广州正泽[USD->RMB]") {
        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeCFLToRMB})
      } else if (type === "EN- 瑞旗香港账户[HSBC RMB->USD]") {
        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeEnHKRMBToUSD})
      } else {
        hiprintTemplate = new hiprint.PrintTemplate({template: debitNode})
      }
      // 打开预览组件
      this.$refs.preView.print(hiprintTemplate, data)
    },
    // 费用清单打印
    checkRole,
    initPrint() {
      hiprint.init({
        providers: [new defaultElementTypeProvider()]
      })
    },
    editRevenueTon() {
      if (this.form.revenueTon && this.form.revenueTon.split("+").length > 0) {
        this.form.revenueTon.split("+").map((revenueTon, i) => {
          if (revenueTon.split("x").length > 0 && i === 0) {
            this.form.countA = Number(revenueTon.split("x")[0])
            this.form.unitCodeA = revenueTon.split("x")[1]
          }
          if (revenueTon.split("x").length > 0 && i === 1) {
            this.form.countB = Number(revenueTon.split("x")[0])
            this.form.unitCodeB = revenueTon.split("x")[1]
          }
          if (revenueTon.split("x").length > 0 && i === 2) {
            this.form.countC = Number(revenueTon.split("x")[0])
            this.form.unitCodeC = revenueTon.split("x")[1]
          }
        })
      }
      this.openGenerateRevenueTons = true
    },
    revenueTonConfirm() {
      let revenueString = []
      if (this.form.unitCodeA) {
        this.form.countA ? revenueString.push(this.form.countA + "x" + this.form.unitCodeA) : revenueString.push(1 + "x" + this.form.unitCodeA)
      }
      if (this.form.unitCodeB && this.form.countB != null) {
        this.form.countB ? revenueString.push(this.form.countB + "x" + this.form.unitCodeB) : revenueString.push(1 + "x" + this.form.unitCodeB)
      }
      if (this.form.unitCodeC) {
        this.form.countC ? revenueString.push(this.form.countC + "x" + this.form.unitCodeC) : revenueString.push(1 + "x" + this.form.unitCodeC)
      }
      this.form.revenueTon = revenueString.join("+")
      this.form.unitCodeA = null
      this.form.unitCodeB = null
      this.form.unitCodeC = null
      this.form.conutA = null
      this.form.conutB = null
      this.form.conutC = null
      this.openGenerateRevenueTons = false
    },
    changeServiceObject(serviceTypeId, serviceObject) {
      const serviceTypeMap = {
        1: "rsOpSeaFcl",
        2: "rsOpSeaLcl",
        10: "rsOpAir",
        20: "rsOpRailFCL",
        21: "rsOpRailLCL",
        40: "rsOpExpress",
        50: "rsOpCtnrTruck",
        51: "rsOpBulkTruck",
        60: "rsOpDocDeclare",
        61: "rsOpFreeDeclare",
        70: "rsOpDOAgent",
        71: "rsOpClearAgent",
        80: "rsOpWHS",
        90: "rsOp3rdCert",
        100: "rsOpINS",
        101: "rsOpTrading",
        102: "rsOpFumigation",
        103: "rsOpCO",
        104: "rsOpOther"
      }

      const targetProperty = serviceTypeMap[serviceTypeId]
      if (targetProperty) {
        this[targetProperty] = Object.assign({}, serviceObject, this[targetProperty] || {})
      }
    },
    getFormDisable(serviceTypeId) {
      const serviceTypeMap = {
        1: "rsOpSealFclFormDisable",
        2: "rsOpSealLclFormDisable",
        10: "rsOpAirFormDisable",
        20: "rsOpRailFclFormDisable",
        21: "rsOpRailLclFormDisable",
        40: "rsOpExpressFormDisable",
        50: "rsOpCtnrTruckFormDisable",
        51: "rsOpBulkTruckFormDisable",
        60: "rsOpDocDeclareFormDisable",
        61: "rsOpFreeDeclareFormDisable",
        70: "rsOpDOAgentFormDisable",
        71: "rsOpClearAgentFormDisable",
        80: "rsOpWHSFormDisable",
        90: "rsOp3rdCertFormDisable",
        100: "rsOpINSFormDisable",
        101: "rsOpTradingFormDisable",
        102: "rsOpFumigationFormDisable",
        103: "rsOpCOFormDisable"
      }

      const propertyName = serviceTypeMap[serviceTypeId]
      return propertyName ? this[propertyName] : undefined
    },
    changeServiceFold(serviceInstance) {
      serviceInstance.serviceFold = !serviceInstance.serviceFold
    },
    // 拓展服务折叠状态变更
    changeExtendServiceFold(serviceData) {
      const serviceTypeId = serviceData.serviceTypeId
      // 根据serviceTypeId更新对应的折叠状态
      switch (serviceTypeId) {
        case 90: // 3rdCert
          this.rsOp3rdCertFold = !this.rsOp3rdCertFold
          break
        case 100: // Insurance
          this.rsOpINSFold = !this.rsOpINSFold
          break
        case 101: // Trading
          this.rsOpTradingFold = !this.rsOpTradingFold
          break
        case 102: // Fumigation
          this.rsOpFumigationFold = !this.rsOpFumigationFold
          break
        case 103: // CO
          this.rsOpCOFold = !this.rsOpCOFold
          break
        case 104: // Other
          this.rsOpOtherFold = !this.rsOpOtherFold
          break
      }
    },
    // 切换折叠状态（使用服务管理器）
    changeFold(serviceTypeId) {
      this.serviceManager.toggleFold(serviceTypeId, this)
    },
    // 获取折叠状态（使用服务管理器）
    getFold(serviceTypeId) {
      return this.serviceManager.getFoldState(serviceTypeId, this)
    },
    addTuck(rsOpTuckList) {
      let obj = {}

      rsOpTuckList.push(obj)
    },
    // 获取服务对象（使用服务管理器）
    getServiceObject(serviceTypeId) {
      return this.serviceManager.getServiceObject(serviceTypeId, this)
    },
    // 获取应付对象（使用服务管理器）
    getPayable(serviceTypeId) {
      return this.serviceManager.getPayable(serviceTypeId, this)
    },
    // 获取服务实例（使用服务管理器）
    getServiceInstance(serviceTypeId) {
      return this.serviceManager.getServiceInstance(serviceTypeId, this)
    },
    getName(id) {
      if (id) {
        if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {
          store.dispatch("getAllRsStaffList")
        }

        if (id) {
          let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]
          if (staff) {
            return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName
          } else {
            return ""
          }
        }
      } else {
        return ""
      }
    },
    async getBookingDetail(id) {
      this.reset()
      await getBooking(id).then(response => {
        let rr = []
        if (response.data.relationClientIds) {
          response.data.relationClientIds.split(",").forEach(v => {
            rr.push(Number(v))
          })
        }
        this.relationClientIds = rr
        this.grossWeight = response.data.grossWeight
        this.goodsValue = response.data.goodsValue
        this.form = response.data
        this.form.relationClientIds = rr
        let cIds = new Set()
        if (cIds.size > 0) {
          cIds.forEach(c => {
            this.carrierIds.push(c)
          })
        }
        if (this.belongList != undefined) {
          for (const a of this.belongList) {
            if (a.children != undefined) {
              for (const b of a.children) {
                if (b.children != undefined) {
                  for (const c of b.children) {
                    if (c.staffId == response.data.salesId) {
                      this.salesId = c.deptId
                    }
                    if (c.staffId == response.data.salesAssistantId) {
                      this.salesAssistantId = c.deptId
                    }
                    if (c.staffId == response.data.salesObserverId) {
                      this.salesObserverId = c.deptId
                    }
                  }
                }
              }
            }
          }
        }
        if (this.opList != undefined) {
          for (const a of this.opList) {
            if (a.staffId == response.data.opId) {
              this.opId = a.roleId
            }

            if (a.children != undefined) {
              for (const b of a.children) {
                if (a.role.roleLocalName == "操作员" && b.staffId == response.data.opId) {
                  this.opId = b.roleId
                }
                if (a.role.roleLocalName == "订舱员" && b.staffId == response.data.bookingOpId) {
                  this.bookingOpId = b.roleId
                }
                if (a.role.roleLocalName == "单证员" && b.staffId == response.data.docOpId) {
                  this.docOpId = b.roleId
                }
                if (b.staffId == response.data.opObserverId) {
                  this.opObserverId = b.roleId
                }
              }
            }
          }
        }
        if (this.businessList != undefined) {
          for (const a of this.businessList) {
            /* if (a.children != undefined) {
              for (const b of a.children) {
                if (b.staffId == response.data.verifyPsaId) {
                  this.verifyPsaId = b.roleId
                }
              }
            } */
            if (a.staffId == response.data.verifyPsaId) {
              this.verifyPsaId = a.staffId
            }
          }
        }
        // 设置基础物流信息的值
        if (response.data.rsBookingLogisticsTypeBasicInfo != null) {
          this.logisticsBasicInfo = response.data.rsBookingLogisticsTypeBasicInfo
          this.logisticsReceivablePayableList = response.data.rsBookingLogisticsTypeBasicInfo.rsBookingReceivablePayableList
          /* this.logisticsReceivablePayableList.map(logisticsReceivablePayable=>{
            log
          }) */
        }
        // 设置前程运输的值
        if (response.data.rsBookingPreCarriageBasicInfo != null) {
          this.preCarriageBasicInfo = response.data.rsBookingPreCarriageBasicInfo
          this.preCarriageReceivablePayableList = response.data.rsBookingPreCarriageBasicInfo.rsBookingReceivablePayableList
        }
        // 设置出口报关的值
        if (response.data.rsBookingExportDeclarationBasicInfo != null) {
          this.exportDeclarationBasicInfo = response.data.rsBookingExportDeclarationBasicInfo
          this.exportDeclarationReceivablePayableList = response.data.rsBookingExportDeclarationBasicInfo.rsBookingReceivablePayableList
        }
        // 设置进口清关的值
        if (response.data.rsBookingImportClearanceBasicInfo != null) {
          this.importClearanceBasicInfo = response.data.rsBookingImportClearanceBasicInfo
          this.importClearanceReceivablePayableList = response.data.rsBookingImportClearanceBasicInfo.rsBookingReceivablePayableList
        }
        this.locationOptions = response.locationOptions
      })
    },
    // 从报价表中过来
    async getQuotation(id) {
      this.reset()
      await getQuotation(id).then(response => {
        let revenueTonArr = []
        response.midRevenueTonsList.map(item => {
          revenueTonArr.push(item.count + "x" + item.unit)
        })
        this.companyList = response.company ? [response.company] : []
        this.form.clientName = response.data.companyName
        this.form.company = response.data.company
        this.form.revenueTon = revenueTonArr.join("+")
        this.form.logisticsTypeId = response.data.logisticsTypeId
        this.form.salesId = response.data.staffId
        this.form.clientId = response.data.companyId
        this.form.clientRoleId = response.data.companyRoleId
        this.form.clientContactor = response.data.extStaffName
        this.form.clientContactorTel = response.data.extStaffPhoneNum
        this.form.clientContactorEmail = response.data.extStaffEmailEnterprise
        this.form.quotationNo = response.data.richNo
        this.form.quotationDate = new Date()
        this.form.impExpTypeId = response.data.imExPort
        this.form.goodsNameSummary = response.data.cargoName
        this.form.goodsValue = response.data.cargoPrice
        this.form.goodsCurrencyId = response.data.cargoCurrencyId
        this.form.grossWeight = response.data.grossWeight
        this.grossWeight = response.data.grossWeight
        this.form.weightUnitId = response.data.cargoUnitId
        this.form.polId = response.data.departureId
        this.form.destinationPortId = response.data.destinationId
        this.form.transitPortId = response.data.transportationTermsId
        this.form.revenueTons = response.data.revenueTons
        this.form.newBookingRemark = response.data.remark
        this.form.inquiryNo = response.data.richNo
        this.form.qoutationNo = response.data.richNo
        this.form.qoutationSketch = response.data.quotationSketch
        this.form.qoutationTime = new Date()
        if (this.belongList != undefined) {
          for (const a of this.belongList) {
            if (a.children != undefined) {
              for (const b of a.children) {
                if (b.children != undefined) {
                  for (const c of b.children) {
                    if (c.staffId == response.data.staffId) {
                      this.salesId = c.deptId
                    }
                  }
                }
              }
            }
          }
        }
        let cIds = new Set()
        for (const v of this.carrierList) {
          if (v.children != undefined && v.children.length > 0) {
            for (const a of v.children) {
              if (a.carrier != null && a.carrier.carrierId != null && a.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {
                if (response.data.carrierIds.includes(a.carrier.carrierId)) {
                  cIds.add(a.serviceTypeId)
                }
              }
              if (a.children != undefined && a.children.length > 0) {
                for (const b of a.children) {
                  if (b.carrier != null && b.carrier.carrierId != null && b.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {
                    if (response.data.carrierIds.includes(b.carrier.carrierId)) {
                      cIds.add(b.serviceTypeId)
                    }
                  }
                }
              }
            }
          }
        }
        if (cIds.size > 0) {
          cIds.forEach(c => {
            this.carrierIds.push(c)
          })
        }

        // 注意事项
        let characteristics = ""
        if (response.characteristics) {
          for (const c of response.characteristics) {
            characteristics += (c.serviceType != null ? c.serviceType : "")
              + (c.cargoType != null ? c.cargoType : "")
              + (c.company != null ? c.company : "")
              + (c.locationDeparture != null ? c.locationDeparture : "")
              + (c.locationDestination != null ? c.locationDestination : "")
              + (c.info != null ? c.info : "")
              + (c.essentialDetail != null ? c.essentialDetail : "") + "\n"
          }
        }
        this.form.inquiryNotice = characteristics
        this.form.serviceTypeIds = response.serviceTypeIds
        this.form.cargoTypeIds = response.cargoTypeIds
        this.cargoTypeCodes = response.cargoTypeCodeSum ? response.cargoTypeCodeSum.toString().split(",") : []
        this.locationOptions = response.locationOptions
        this.form.preCarriageRegionIds = response.locationLoadingIds
        this.form.clientRoleId = response.roleIds[0]

        for (const qf of response.quotationFreight) {
          // 一条费用拆分成两条(应收和应付)
          let chargePay = {}
          chargePay.showClient = false
          chargePay.showSupplier = false
          chargePay.showQuotationCharge = false
          chargePay.showCostCharge = false
          chargePay.showQuotationCurrency = false
          chargePay.showCostCurrency = false
          chargePay.showQuotationUnit = false
          chargePay.showCostUnit = false
          chargePay.showStrategy = false
          chargePay.showUnitRate = false
          chargePay.showAmount = false
          chargePay.showCurrencyRate = false
          chargePay.showDutyRate = false

          chargePay.companyName = qf.company
          chargePay.clearingCompanyId = qf.companyId
          chargePay.dnChargeNameId = qf.chargeId
          chargePay.chargeName = qf.charge
          chargePay.dnCurrencyCode = qf.quotationCurrencyCode
          chargePay.dnUnitRate = qf.inquiryRate
          chargePay.dnUnitCode = qf.unitCode
          chargePay.dnAmount = qf.inquiryAmount
          chargePay.basicCurrencyRate = qf.exchangeRate
          chargePay.dutyRate = qf.taxRate
          chargePay.subtotal = currency(chargePay.dnUnitRate).multiply(chargePay.dnAmount).multiply(chargePay.basicCurrencyRate).value

          let chargeReceive = {}
          chargeReceive.showClient = false
          chargeReceive.showSupplier = false
          chargeReceive.showQuotationCharge = false
          chargeReceive.showCostCharge = false
          chargeReceive.showQuotationCurrency = false
          chargeReceive.showCostCurrency = false
          chargeReceive.showQuotationUnit = false
          chargeReceive.showCostUnit = false
          chargeReceive.showStrategy = false
          chargeReceive.showUnitRate = false
          chargeReceive.showAmount = false
          chargeReceive.showCurrencyRate = false
          chargeReceive.showDutyRate = false

          if (this.form.clientName) {
            chargeReceive.clearingCompanyId = response.data.companyId
            chargeReceive.companyName = response.data.company
          }
          chargeReceive.dnChargeNameId = qf.chargeId
          chargeReceive.chargeName = qf.charge
          chargeReceive.dnCurrencyCode = qf.quotationCurrencyCode
          chargeReceive.dnUnitRate = qf.quotationRate
          chargeReceive.dnUnitCode = qf.unitCode
          chargeReceive.dnAmount = qf.quotationAmount
          chargeReceive.basicCurrencyRate = qf.exchangeRate
          chargeReceive.dutyRate = qf.taxRate
          chargeReceive.subtotal = currency(chargeReceive.dnUnitRate).multiply(chargeReceive.dnAmount).multiply(chargeReceive.basicCurrencyRate).value

          if (qf.serviceTypeId === 1) {
            this.rsOpSeaFcl.rsChargeList.push(chargePay)
            this.form.rsOpSeaFclList.map(item => {
              item.rsChargeList.push(chargePay)
            })
          }
          if (qf.serviceTypeId === 2) {
            this.rsOpSeaLcl.rsChargeList.push(chargePay)
            this.form.rsOpSeaLclList.map(item => {
              item.rsChargeList.push(chargePay)
            })
          }
          if (qf.serviceTypeId === 10) {
            this.rsOpAir.rsChargeList.push(chargePay)
            this.form.rsOpAirList.map(item => {
              item.rsChargeList.push(chargePay)
            })
          }
          if (qf.serviceTypeId === 20) {
            this.rsOpRailFCL.rsChargeList.push(chargePay)
          }
          if (qf.serviceTypeId === 21) {
            this.rsOpRailLCL.rsChargeList.push(chargePay)
          }
          if (qf.serviceTypeId === 40) {
            this.rsOpExpress.rsChargeList.push(chargePay)
          }
          if (qf.serviceTypeId === 50) {
            this.rsOpCtnrTruck.rsChargeList.push(chargePay)
            this.form.rsOpCtnrTruckList.map(item => {
              item.rsChargeList.push(chargePay)
            })
          }
          if (qf.serviceTypeId === 51) {
            this.rsOpBulkTruck.rsChargeList.push(chargePay)
            this.form.rsOpBulkTruckList.map(item => {
              item.rsChargeList.push(chargePay)
            })
          }
          if (qf.serviceTypeId === 60) {
            this.rsOpDocDeclare.rsChargeList.push(chargePay)
            this.form.rsOpDocDeclareList.map(item => {
              item.rsChargeList.push(chargePay)
            })
          }
          if (qf.serviceTypeId === 61) {
            this.rsOpFreeDeclare.rsChargeList.push(chargePay)
            this.form.rsOpFreeDeclareList.map(item => {
              item.rsChargeList.push(chargePay)
            })
          }
          if (qf.serviceTypeId === 70) {
            this.rsOpDOAgent.rsChargeList.push(chargePay)
          }
          if (qf.serviceTypeId === 71) {
            this.rsOpClearAgent.rsChargeList.push(chargePay)
          }
          if (qf.serviceTypeId === 80) {
            this.rsOpWHS.rsChargeList.push(chargePay)
          }
          if (qf.serviceTypeId === 90) {
            this.rsOp3rdCert.rsChargeList.push(chargePay)
          }
          if (qf.serviceTypeId === 100) {
            this.rsOpINS.rsChargeList.push(chargePay)
          }
          if (qf.serviceTypeId === 101) {
            this.rsOpTrading.rsChargeList.push(chargePay)
          }
          if (qf.serviceTypeId === 102) {
            this.rsOpFumigation.rsChargeList.push(chargePay)
          }
          if (qf.serviceTypeId === 103) {
            this.rsOpCO.rsChargeList.push(chargePay)
          }
          if (qf.serviceTypeId === 104) {
            this.rsOpOther.rsChargeList.push(chargePay)
          }

          this.rsClientMessage.rsChargeList.push(chargeReceive)
        }
      })
    },
    generateFreight(type, serviceTypeId, item) {
      console.log(item)
      if (!this.form.revenueTon) {
        this.$modal.msgWarning("请先录入计费货量")
        return
      }

      this.curFreightSelectRow = item

      this.freightSelectData.typeId = type
      if (type != 5) {
        this.freightSelectData.destinationPortId = this.form.destinationPortId
      }
      if (type == 5) {
        this.freightSelectData.precarriageRegionId = this.form.precarriageRegionId
      }
      this.freightSelectData.polId = this.form.polId
      this.freightSelectData.serviceTypeId = serviceTypeId
      this.freightSelectData.revenueTonList = this.form.revenueTon.split("+")
      this.freightSelectData.locationOptions = this.locationOptions

      this.openFreightSelect = true
    },
    currency,
    async getRctDetail(id) {
      this.reset()
      await getRct(id).then(response => {
        if (response.outboundRecord) {
          this.outboundForm = response.outboundRecord
        }

        this.form = response.data

        this.showPsaRct = response.data.psaRctId ? true : false

        this.form.serviceTypeIds = response.data.serviceTypeIds ? response.data.serviceTypeIds : this.form.serviceTypeIdList.split(",")

        this.bookingMessageList = response.data.bookingMessagesList ? response.data.bookingMessagesList : []

        this.form.noTransferAllowed = response.data.noTransferAllowed === "1"
        this.form.noDividedAllowed = response.data.noDividedAllowed === "1"
        this.form.noAgreementShowed = response.data.noAgreementShowed === "1"
        this.form.isCustomsIntransitShowed = response.data.isCustomsIntransitShowed === "1"
        this.grossWeight = response.data.grossWeight
        this.goodsValue = response.data.goodsValue
        let cIds = new Set()
        let rr = []
        // 关联客户
        if (response.data.relationClientIds) {
          response.data.relationClientIds.split(",").forEach(v => {
            rr.push(Number(v))
          })
        }
        this.relationClientIds = rr
        this.form.relationClientIds = rr
        if (this.belongList != undefined) {
          for (const a of this.belongList) {
            if (a.children != undefined) {
              for (const b of a.children) {
                if (b.children != undefined) {
                  for (const c of b.children) {
                    if (c.staffId == response.data.salesId) {
                      this.salesId = c.deptId
                    }
                    if (c.staffId == response.data.salesAssistantId) {
                      this.salesAssistantId = c.deptId
                    }
                    if (c.staffId == response.data.salesObserverId) {
                      this.salesObserverId = c.deptId
                    }
                  }
                }
              }
            }
          }
        }
        if (this.opList != undefined) {
          for (const a of this.opList) {
            if (a.staffId == response.data.opId) {
              this.opId = a.roleId
            }

            if (a.children != undefined) {
              for (const b of a.children) {
                if (a.role.roleLocalName == "操作员" && b.staffId == response.data.opId) {
                  this.opId = b.roleId
                }
                if (a.role.roleLocalName == "订舱员" && b.staffId == response.data.bookingOpId) {
                  this.bookingOpId = b.roleId
                }
                if (a.role.roleLocalName == "单证员" && b.staffId == response.data.docOpId) {
                  this.docOpId = b.roleId
                }
                if (b.staffId == response.data.opObserverId) {
                  this.opObserverId = b.roleId
                }
              }
            }
          }
        }

        this.verifyPsaId = response.data.verifyPsaId
        this.opId = response.data.opId

        if (response.data.rsClientMessage != null) {
          this.rsClientMessage = response.data.rsClientMessage
          this.rsClientServiceInstance = response.data.rsClientMessage.rsServiceInstances
        }
        if (response.data.serviceTypeIds.indexOf(20) !== -1 && response.data.rsOpRailFCL !== null) {
          this.rsOpRailFCL = response.data.rsOpRailFCL
          this.rsOpRailFclServiceInstance = response.data.rsOpRailFCL.rsServiceInstances
        }
        if (response.data.serviceTypeIds.indexOf(21) !== -1 && response.data.rsOpRailLCL !== null) {
          this.rsOpRailLCL = response.data.rsOpRailLCL
          this.rsOpRailLclServiceInstance = response.data.rsOpRailLCL.rsServiceInstances
        }
        if (response.data.serviceTypeIds.indexOf(40) !== -1 && response.data.rsOpExpress !== null) {
          this.rsOpExpress = response.data.rsOpExpress
          this.rsOpExpressServiceInstance = response.data.rsOpExpress.rsServiceInstances
        }
        if (response.data.serviceTypeIds.indexOf(70) !== -1 && response.data.rsOpDOAgent !== null) {
          this.rsOpDOAgent = response.data.rsOpDOAgent
          this.rsOpDOAgentServiceInstance = response.data.rsOpDOAgent.rsServiceInstances
        }
        if (response.data.serviceTypeIds.indexOf(71) !== -1 && response.data.rsOpClearAgent !== null) {
          this.rsOpClearAgent = response.data.rsOpClearAgent
          this.rsOpClearAgentServiceInstance = response.data.rsOpClearAgent.rsServiceInstances
        }
        if (response.data.serviceTypeIds.indexOf(80) !== -1 && response.data.rsOpWHS !== null) {
          this.rsOpWHS = response.data.rsOpWHS
          this.rsOpWHSServiceInstance = response.data.rsOpWHS.rsServiceInstances
        }
        if (response.data.serviceTypeIds.indexOf(90) !== -1 && response.data.rsOp3rdCert !== null) {
          this.rsOp3rdCert = response.data.rsOp3rdCert
          this.rsOp3rdCertServiceInstance = response.data.rsOp3rdCert.rsServiceInstances
        }

        if (response.data.serviceTypeIds.indexOf(100) !== -1 && response.data.rsOpINS !== null) {
          this.rsOpINS = response.data.rsOpINS
          this.rsOpINSServiceInstance = response.data.rsOpINS.rsServiceInstances
        }
        if (response.data.serviceTypeIds.indexOf(101) !== -1 && response.data.rsOpTrading !== null) {
          this.rsOpTrading = response.data.rsOpTrading
          this.rsOpTradingServiceInstance = response.data.rsOpTrading.rsServiceInstances
        }
        if (response.data.serviceTypeIds.indexOf(102) !== -1 && response.data.rsOpFumigation !== null) {
          this.rsOpFumigation = response.data.rsOpFumigation
          this.rsOpFumigationServiceInstance = response.data.rsOpFumigation.rsServiceInstances
        }
        if (response.data.serviceTypeIds.indexOf(103) !== -1 && response.data.rsOpCO !== null) {
          this.rsOpCO = response.data.rsOpCO
          this.rsOpCOServiceInstance = response.data.rsOpCO.rsServiceInstances
        }
        if (response.data.serviceTypeIds.indexOf(104) !== -1 && response.data.rsOpOther !== null) {
          this.rsOpOther = response.data.rsOpOther
          this.rsOpOtherServiceInstance = response.data.rsOpOther.rsServiceInstances
        }

        // 客户信息审核
        this.opConfirmedName = response.data.rsClientMessage.rsServiceInstances.isDnOpConfirmed ? this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isDnOpConfirmed)[0].staffFamilyLocalName + "" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isDnOpConfirmed)[0].staffGivingLocalName : null
        this.opConfirmedDate = response.data.rsClientMessage.rsServiceInstances.opConfirmedTime ? response.data.rsClientMessage.rsServiceInstances.opConfirmedTime : null
        this.accountConfirmedName = response.data.rsClientMessage.rsServiceInstances.isAccountConfirmed ? this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isAccountConfirmed)[0].staffFamilyLocalName + "" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isAccountConfirmed)[0].staffGivingLocalName : null
        this.accountConfirmedDate = response.data.rsClientMessage.rsServiceInstances.accountConfirmTime ? response.data.rsClientMessage.rsServiceInstances.accountConfirmTime : null
        this.clientConfirmedName = response.data.rsClientMessage.rsServiceInstances.isDnClientConfirmed ? this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isDnClientConfirmed)[0].staffFamilyLocalName + "" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isDnClientConfirmed)[0].staffGivingLocalName : null
        this.clientConfirmedDate = response.data.rsClientMessage.rsServiceInstances.clientConfirmedTime ? response.data.rsClientMessage.rsServiceInstances.clientConfirmedTime : null
        this.salesConfirmedName = response.data.rsClientMessage.rsServiceInstances.isDnSalesConfirmed ? this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isDnSalesConfirmed)[0].staffFamilyLocalName + "" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isDnSalesConfirmed)[0].staffGivingLocalName : null
        this.salesConfirmedDate = response.data.rsClientMessage.rsServiceInstances.salesConfirmedTime ? response.data.rsClientMessage.rsServiceInstances.salesConfirmedTime : null

        this.locationOptions = response.locationOptions
        this.RelationClientIdList = []
        response.data.relationClientIdList ? response.data.relationClientIdList.split(",").map(v => this.RelationClientIdList.push(Number(v))) : []

        this.relationClientLists.push(response.companyList)
        this.companyList = response.companyList.filter(v => v.companyId === response.data.clientId).concat(response.companyList.filter(v => v.companyId !== response.data.clientId))

        this.supplierList = response.supplierList

        /* let clientCompany = response.companyList.filter(v => v.companyId === response.data.clientId).length > 0 ? response.companyList.filter(v => v.companyId === response.data.clientId)[0] : null
        if (clientCompany) {
          this.form.clientName = clientCompany.companyTaxCode + "/" + clientCompany.companyShortName + "/" + clientCompany.companyLocalName
        } */
        this.form.clientName = response.data.clientSummary

        // 箱型特征
        this.ctnrTypeCodeIds = this.form.ctnrTypeCode ? this.form.ctnrTypeCode.split(",") : []
        this.cargoTypeCodes = this.form.cargoTypeCodeSum ? this.form.cargoTypeCodeSum.split(",") : []

        // 折叠信息以及显示信息
        if (response.data.messageDisplay) {
          response.data.messageDisplay.split(",").forEach((v, i) => {
            if (i === 0) v == 1 ? this.serviceInfo = true : this.serviceInfo = false
            if (i === 1) v == 1 ? this.orderInfo = true : this.orderInfo = false
            if (i === 2) v == 1 ? this.branchInfo = true : this.branchInfo = false
            if (i === 3) v == 1 ? this.logisticsInfo = true : this.logisticsInfo = false
            if (i === 4) v == 1 ? this.docInfo = true : this.docInfo = false
            if (i === 5) v == 1 ? this.chargeInfo = true : this.chargeInfo = false
            if (i === 6) v == 1 ? this.auditInfo = true : this.auditInfo = false
          })
        }
        if (response.data.serviceMessageFold) {
          response.data.serviceMessageFold.split(",").forEach((v, i) => {
            if (i === 0) v == 1 ? this.clientMessage = true : this.clientMessage = false
            if (i === 1) v == 1 ? this.rsOpSealFclFold = true : this.rsOpSealFclFold = false
            if (i === 2) v == 1 ? this.rsOpSealLclFold = true : this.rsOpSealLclFold = false
            if (i === 3) v == 1 ? this.rsOpAirFold = true : this.rsOpAirFold = false
            if (i === 4) v == 1 ? this.rsOpRailFclFold = true : this.rsOpRailFclFold = false
            if (i === 5) v == 1 ? this.rsOpRailLclFold = true : this.rsOpRailLclFold = false
            if (i === 6) v == 1 ? this.rsOpExpressFold = true : this.rsOpExpressFold = false
            if (i === 7) v == 1 ? this.rsOpCtnrTruckFold = true : this.rsOpCtnrTruckFold = false
            if (i === 8) v == 1 ? this.rsOpBulkTruckFold = true : this.rsOpBulkTruckFold = false
            if (i === 9) v == 1 ? this.rsOpDocDeclareFold = true : this.rsOpDocDeclareFold = false
            if (i === 10) v == 1 ? this.rsOpFreeDeclareFold = true : this.rsOpFreeDeclareFold = false
            if (i === 11) v == 1 ? this.rsOpDOAgentFold = true : this.rsOpDOAgentFold = false
            if (i === 12) v == 1 ? this.rsOpClearAgentFold = true : this.rsOpClearAgentFold = false
            if (i === 13) v == 1 ? this.rsOpWHSFold = true : this.rsOpWHSFold = false
            if (i === 14) v == 1 ? this.rsOp3rdCertFold = true : this.rsOp3rdCertFold = false
            if (i === 15) v == 1 ? this.rsOpINSFold = true : this.rsOpINSFold = false
            if (i === 16) v == 1 ? this.rsOpTradingFold = true : this.rsOpTradingFold = false
            if (i === 17) v == 1 ? this.rsOpFumigationFold = true : this.rsOpFumigationFold = false
            if (i === 18) v == 1 ? this.rsOpCOFold = true : this.rsOpCOFold = false
            if (i === 19) v == 1 ? this.rsOpOtherFold = true : this.rsOpOtherFold = false
          })
        }

        response.data.rsOpSeaFclList ? response.data.rsOpSeaFclList.map(item => {
          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false
          return item
        }) : null
        response.data.rsOpSeaLclList ? response.data.rsOpSeaLclList.map(item => {
          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false
          return item
        }) : null
        response.data.rsOpAirList ? response.data.rsOpAirList.map(item => {
          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false
          return item
        }) : null
        response.data.rsOpCtnrTruckList ? response.data.rsOpCtnrTruckList.map(item => {
          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false
          return item
        }) : null
        response.data.rsOpBulkTruckList ? response.data.rsOpBulkTruckList.map(item => {
          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false
          return item
        }) : null
        response.data.rsOpDocDeclareList ? response.data.rsOpDocDeclareList.map(item => {
          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false
          return item
        }) : null
        response.data.rsOpFreeDeclareList ? response.data.rsOpFreeDeclareList.map(item => {
          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false
          return item
        }) : null

        // 默认收发通
        // this.form.bookingShipper = response.data.bookingShipper ? response.data.bookingShipper : "GUANGZHOU RICH SHIPPING INT'L CO., LTD."
        this.form.bookingShipper = response.data.bookingShipper
          ? response.data.bookingShipper
          : (this.form.rctNo && this.form.rctNo.startsWith("CFL")
            ? "GUANGZHOU CHERISH FREIGHT INT'L CO.,LTD."
            : "GUANGZHOU RICH SHIPPING INT'L CO., LTD.")
        this.form.bookingConsignee = response.data.bookingConsignee ? response.data.bookingConsignee : "TO ORDER"
        this.form.bookingNotifyParty = response.data.bookingNotifyParty ? response.data.bookingNotifyParty : "SAME AS CONSIGNEE"
      })
    },
    // 操作单驳回
    turnDown() {
      this.$refs["form"].validate(valid => {
        this.form.opId = null
        this.form.psaVerifyStatusId = 0
        this.form.psaVerify = 0
        if (this.form.rctId != null) {
          this.form.noTransferAllowed = this.form.noTransferAllowed ? 1 : 0
          this.form.noDividedAllowed = this.form.noDividedAllowed ? 1 : 0
          this.form.noAgreementShowed = this.form.noAgreementShowed ? 1 : 0
          this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? 1 : 0
          updateRct(this.form).then(response => {
            this.form.noTransferAllowed = this.form.noTransferAllowed ? true : false
            this.form.noDividedAllowed = this.form.noDividedAllowed ? true : false
            this.form.noAgreementShowed = this.form.noAgreementShowed ? true : false
            this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? true : false
            this.$modal.msgSuccess("操作单已驳回")
          })
        }
      })
    },
    // 使用防抖包装实际的提交方法
    submitForm: debounce(async function (type) {
      if (this.isSubmitting) {
        return // 防止重复提交
      }
      this.isSubmitting = true

      this.$refs["form"].validate(async valid => {
        // 使用服务管理器批量处理服务实例
        this.serviceManager.processServiceInstances(this.serviceManager.getAllServiceTypeIds(), this)

        this.form.relationClientIdList = this.RelationClientIdList.toString()
        this.form.serviceTypeIdList = this.serviceList.toString()
        // TODO
        if ((this.rsOpBulkTruck.rsOpTruckList && this.rsOpBulkTruck.rsOpTruckList) || (this.rsOpCtnrTruck.rsOpTruckList && this.rsOpCtnrTruck.rsOpTruckList.length > 0)) {
          if (this.serviceList.has(50)) {
            this.form.rsOpCtnrTruckList.map(item => {
              item.sqdContainersSealsSum = item.rsOpTruckList.map(v => v.containerNo).join(",")
            })
          } else if (this.serviceList.has(51)) {
            this.form.rsOpBulkTruckList.map(item => {
              item.sqdContainersSealsSum = item.rsOpTruckList.map(v => v.containerNo).join(",")
            })
          }
        }

        // 提单信息列表
        this.form.bookingMessagesList = this.bookingMessageList

        // 优化函数：分割字符串并添加到Set集合
        const addToSet = (text, set, separator = "/") => {
          if (!text) return
          text.split(separator)
            .map(item => item.trim())
            .filter(Boolean)
            .forEach(item => set.add(item))
        }

        // 优化函数：从数组对象中提取字段并添加到Set集合
        const addFromList = (list, fieldName, set, separator = /\r\n|\r|\n/) => {
          if (!list?.length) return
          list.forEach(item => {
            if (item[fieldName]) {
              addToSet(item[fieldName], set, separator)
            }
          })
        }

        // 获取FCL或LCL列表的第一项
        const getFclOrLcl = (form) => {
          return form.rsOpSeaFclList?.[0] || form.rsOpSeaLclList?.[0] || null
        }

        // 更新表单值
        const updateFormValue = (form, fieldName, value) => {
          if (form.rsOpSeaFclList?.[0]) {
            form.rsOpSeaFclList[0][fieldName] = value
          }
          if (form.rsOpSeaLclList?.[0]) {
            form.rsOpSeaLclList[0][fieldName] = value
          }
          form[fieldName] = value
        }

        // 处理集装箱号
        const processContainers = () => {
          const allContainers = new Set()

          // 从表单中添加集装箱号
          const firstItem = getFclOrLcl(this.form)
          if (firstItem?.sqdContainersSealsSum) {
            addToSet(firstItem.sqdContainersSealsSum, allContainers)
          }

          // 从bookingMessageList添加集装箱号
          addFromList(this.bookingMessageList, "containerNo", allContainers)

          // 从rsOpCtnrTruckList添加集装箱号
          if (this.form.rsOpCtnrTruckList?.[0]?.rsOpTruckList) {
            this.form.rsOpCtnrTruckList[0].rsOpTruckList
              .map(item => item.containerNo?.trim())
              .filter(Boolean)
              .forEach(item => allContainers.add(item))
          }

          // 从rsOpBulkTruckList添加集装箱号
          if (this.form.rsOpBulkTruckList?.[0]?.rsOpTruckList) {
            this.form.rsOpBulkTruckList[0].rsOpTruckList
              .map(item => item.containerNo?.trim())
              .filter(Boolean)
              .forEach(item => allContainers.add(item))
          }

          // 更新表单字段
          const containersStr = Array.from(allContainers).join("/")
          updateFormValue(this.form, "sqdContainersSealsSum", containersStr)
        }

        // 处理提单号
        const processBlNos = () => {
          const allBlNos = new Set()

          // 从表单中添加提单号
          const firstItem = getFclOrLcl(this.form)
          if (firstItem?.blNo) {
            addToSet(firstItem.blNo, allBlNos)
          }

          // 从bookingMessageList添加提单号
          addFromList(this.bookingMessageList, "mBlNo", allBlNos)

          // 更新表单字段
          const blNoStr = Array.from(allBlNos).join("/")
          updateFormValue(this.form, "blNo", blNoStr)
        }

        // 执行处理
        processContainers()
        processBlNos()

        // 获取汇率
        let exchangeRate
        for (const a of this.$store.state.data.exchangeRateList) {
          if (this.form.podEta) {
            if (a.localCurrency === "RMB"
              && "USD" == a.overseaCurrency
              && parseTime(a.validFrom) <= parseTime(this.form.podEta)
              && parseTime(this.form.podEta) <= parseTime(a.validTo)
            ) {
              exchangeRate = currency(a.settleRate).divide(a.base).value
            }
          } else {
            if (a.localCurrency === "RMB"
              && "USD" == a.overseaCurrency
              && parseTime(a.validFrom) <= parseTime(new Date())
              && parseTime(new Date()) <= parseTime(a.validTo)) {
              exchangeRate = currency(a.settleRate).divide(a.base).value
            }
          }
        }

        this.rsClientMessageCharge(this.rsClientMessage.rsChargeList)

        // 费用
        this.form.dnRmb = this.rsClientMessageReceivableTaxRMB // rmb含税应收
        this.form.dnUsd = this.rsClientMessageReceivableTaxUSD // usd含税应收
        this.form.cnRmb = this.rsClientMessagePayableTaxRMB // rmb含税应付
        this.form.cnUsd = this.rsClientMessagePayableTaxUSD // usd含税应付
        // 折合费用
        this.form.dnInRmb = currency(this.rsClientMessageReceivableTaxRMB).add(currency(this.rsClientMessageReceivableTaxUSD).multiply(exchangeRate)).value // 折合rmb含税应收
        this.form.cnInRmb = currency(this.rsClientMessagePayableTaxRMB).add(currency(this.rsClientMessagePayableTaxUSD).multiply(exchangeRate)).value // 折合rmb含税应付

        // 统计收款和付款进度
        this.form.dnRmbBalance = this.sqdUnreceivedRmbSum // rmb含税未收
        this.form.dnUsdBalance = this.sqdUnreceivedUsdSum // usd含税未收
        this.form.cnRmbBalance = this.sqdUnpaidRmbSum // rmb含税未付
        this.form.cnUsdBalance = this.sqdUnpaidUsdSum // usd含税未付
        // 折合进度
        this.form.dnInRmbBalance = currency(this.sqdUnreceivedRmbSum).add(currency(this.sqdUnreceivedUsdSum).multiply(exchangeRate)).value // 折合rmb未收
        this.form.cnInRmbBalance = currency(this.sqdUnpaidRmbSum).add(currency(this.sqdUnpaidUsdSum).multiply(exchangeRate)).value // 折合rmb未付

        // 利润
        this.form.profitUsd = currency(this.form.dnUsd).subtract(this.form.cnUsd).value // 美元部分含税利润
        this.form.profitRmb = currency(this.form.dnRmb).subtract(this.form.cnRmb).value // 人民币部分含税利润
        this.form.profitInRmb = currency(this.form.profitUsd).multiply(exchangeRate).add(this.form.profitRmb).value // 折合人民币利润

        this.form.noTransferAllowed = this.form.noTransferAllowed ? 1 : 0
        this.form.noDividedAllowed = this.form.noDividedAllowed ? 1 : 0
        this.form.noAgreementShowed = this.form.noAgreementShowed ? 1 : 0
        this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? 1 : 0

        this.form.messageDisplay = [Number(this.serviceInfo), Number(this.orderInfo), Number(this.branchInfo), Number(this.logisticsInfo), Number(this.docInfo), Number(this.chargeInfo), Number(this.auditInfo)].toString()
        this.form.serviceMessageFold = [Number(this.clientMessage), Number(this.rsOpSealFclFold), Number(this.rsOpSealLclFold), Number(this.rsOpAirFold), Number(this.rsOpRailFclFold), Number(this.rsOpRailLclFold), Number(this.rsOpExpressFold), Number(this.rsOpCtnrTruckFold), Number(this.rsOpBulkTruckFold), Number(this.rsOpDocDeclareFold), Number(this.rsOpFreeDeclareFold), Number(this.rsOpDOAgentFold), Number(this.rsOpClearAgentFold), Number(this.rsOpWHSFold), Number(this.rsOp3rdCertFold), Number(this.rsOpINSFold), Number(this.rsOpTradingFold), Number(this.rsOpFumigationFold), Number(this.rsOpCOFold), Number(this.rsOpOtherFold)].toString()

        this.form.rsOpSeaFclList ? this.form.rsOpSeaFclList.map(item => {
          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
          return item
        }) : null
        this.form.rsOpSeaLclList ? this.form.rsOpSeaLclList.map(item => {
          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
          return item
        }) : null
        this.form.rsOpAirList ? this.form.rsOpAirList.map(item => {
          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
          return item
        }) : null
        this.form.rsOpCtnrTruckList ? this.form.rsOpCtnrTruckList.map(item => {
          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
          return item
        }) : null
        this.form.rsOpBulkTruckList ? this.form.rsOpBulkTruckList.map(item => {
          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
          return item
        }) : null
        this.form.rsOpDocDeclareList ? this.form.rsOpDocDeclareList.map(item => {
          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
          return item
        }) : null
        this.form.rsOpFreeDeclareList ? this.form.rsOpFreeDeclareList.map(item => {
          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
          return item
        }) : null

        // 海运子服务合并主表信息
        this.mergeSeaServiceWithMainService()

        // 当目前海运订舱只有一个的时候
        this.chooseWhenOnlyOne()

        // 确保在提交前清除千分位分隔符
        if (this.form.grossWeight) {
          this.form.grossWeight = this.form.grossWeight.toString().replace(/,/g, "")
        }

        if (valid) {
          let message = "修改成功"
          // 商务审核
          if (this.psaVerify && type === "psa") {
            if (!this.form.psaVerifyStatusId) {
              this.$message.warning("请选择审核状态")
            }
            // TODO 驳回不需要选择操作
            if (this.opId === null && this.form.psaVerifyStatusId == 1) {
              this.$message.warning("请指派操作员")
              return
            }

            // 审核驳回
            if (this.psaVerify && this.form.psaVerifyStatusId == 9) {
              // if 商务驳回 then 商务日期=null，业务订舱日期=null，商务审核状态=未审核，业务订舱状态=未订舱
              this.form.psaVerifyTime = null
              this.form.newBookingTime = null
              message = "已驳回"
              this.form.sqdShippingBookingStatus = 0
              this.form.psaVerify = 0
            }
            // 审核通过
            if (this.psaVerify && this.form.psaVerifyStatusId == 1) {
              message = "审核通过"
              this.form.psaVerify = 1
              this.form.psaVerifyTime = moment().format("yyyy-MM-DD HH:mm:ss")
            }
          }
          // 确认订舱
          if (type === "booking") {
            if (this.form.verifyPsaId == null) {
              this.$message.warning("请指定商务")
              return
            }
            this.form.sqdShippingBookingStatus = 1
            // 折合含税报价
            this.form.quotationInRmb = this.rsClientMessageReceivable
            // 折合含税询价
            this.form.inquiryInRmb = this.rsClientMessagePayable
            // 预期不含税利润
            // this.form.estimatedProfitInRmb = this.rsClientMessageProfitNoTax
            // 预期含税利润
            this.form.estimatedProfitInRmb = this.rsClientMessageProfit
            // 报价备注,自动填写
            if (this.form.qoutationSketch === "") {
              this.form.rsOpSeaFclList && this.form.rsOpSeaFclList[0].rsChargeList ?
                this.form.rsOpSeaFclList[0].rsChargeList.map(rsCharge => {
                  this.form.qoutationSketch += rsCharge.chargeName + ":\t" + rsCharge.dnCurrencyCode + " " + rsCharge.dnUnitRate + "/" + rsCharge.dnUnitCode + "\n"
                })
                : null
              this.form.rsOpSeaLclList && this.form.rsOpSeaLclList[0].rsChargeList ?
                this.form.rsOpSeaFclList[0].rsChargeList.map(rsCharge => {
                  this.form.qoutationSketch += rsCharge.chargeName + ":\t" + rsCharge.dnCurrencyCode + " " + rsCharge.dnUnitRate + "/" + rsCharge.dnUnitCode + "\n"
                })
                : null
            }

            this.form.newBookingTime = moment().format("yyyy-MM-DD HH:mm:ss")
            this.form.psaVerify = 0
            message = "确认订舱"
          }
          // 操作确认
          if (this.$route.query.type === "op" && type === "opConfirm") {
            if (this.form.rctNo === null) {
              // this.generateRct(true)
              // 生成rctNo
              if (this.form.orderBelongsTo && this.form.orderBelongsTo === "GZCF") {
                await getRctCFMon().then(v => {
                  let num = v.data + 1
                  if (num.toString().length < 3) {
                    const j = 3 - (num.toString().length)
                    for (let i = 0; i < j; i++) {
                      num = "0" + num
                    }
                  }
                  let date = new Date()
                  let month = (date.getMonth() + Number(this.rct.month)).toString()
                  let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)
                  this.form.rctNo = this.rct.GZCFLeadingCharacter + year + (month.length == 1 ? "0" + month : month) + num.toString()
                })
              } else if (this.form.orderBelongsTo && this.form.orderBelongsTo === "FSWH") {
                await getRctRSWHMon().then(v => {
                  let num = v.data + 1
                  if (num.toString().length < 3) {
                    const j = 3 - (num.toString().length)
                    for (let i = 0; i < j; i++) {
                      num = "0" + num
                    }
                  }
                  let date = new Date()
                  let month = (date.getMonth() + Number(this.rct.month)).toString()
                  let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)
                  this.form.rctNo = this.rct.RSWHLeadingCharacter + year + (month.length == 1 ? "0" + month : month) + num.toString()
                })
              } else {
                await getRctMon().then(v => {
                  let num = v.data + 1
                  if (num.toString().length < 3) {
                    const j = 3 - (num.toString().length)
                    for (let i = 0; i < j; i++) {
                      num = "0" + num
                    }
                  }
                  let date = new Date()
                  let month = (date.getMonth() + Number(this.rct.month)).toString()
                  let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)
                  this.form.rctNo = this.rct.leadingCharacter + year + (month.length == 1 ? "0" + month : month) + num.toString()
                })
              }
            }

            // 接单日期TODO
            this.form.rctCreateTime = moment().format("yyyy-MM-DD HH:mm:ss")
            this.form.opAccept = 1
            this.form.processStatusId = 6
            message = "操作接单成功"
          }

          // 箱型特征
          this.form.ctnrTypeCode = this.ctnrTypeCodeIds.toString()
          // 货物特征
          this.form.cargoTypeCodeSum = this.cargoTypeCodes.toString()

          //  TODO 海运子服务剩一个时更新到主表

          this.form.salesId = this.form.salesId ? this.form.salesId : this.$store.state.user.sid

          // 每次保存更新状态时间
          this.form.statusUpdateTime = moment().format("yyyy-MM-DD HH:mm:ss")
          if (this.form.rctId != null) {
            updateRct(this.form).then(response => {
              this.saveAll(this.form.rctId)
              this.$modal.msgSuccess(message)
              /* this.open = false
              this.getRctList() */
              this.form.noTransferAllowed = this.form.noTransferAllowed ? true : false
              this.form.noDividedAllowed = this.form.noDividedAllowed ? true : false
              this.form.noAgreementShowed = this.form.noAgreementShowed ? true : false
              this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? true : false

              if (this.form.opAccept == 1 && (this.form.serviceTypeIds.indexOf(1) !== -1 || this.form.serviceTypeIds.indexOf(2) !== -1)) {
                // TODO
                // 更新商务订舱信息
                updatePsarct({
                  ...this.form, noTransferAllowed: null,
                  noDividedAllowed: null,
                  noAgreementShowed: null,
                  isCustomsIntransitShowed: null
                })
              }
              this.isSubmitting = false
            }).catch(() => {
              this.isSubmitting = false
            })
          } else {
            // 操作订舱的时候在商务订舱信息中也添加一条记录
            // 操作订舱就不需要操作接单,直接出现在操作单列表
            if (this.$route.path === "/opprocess/opdetail" && this.formType === "rct") {
              if (this.form.rctNo === null) {
                // this.generateRct(true)
                // 生成rctNo
                if (this.form.orderBelongsTo && this.form.orderBelongsTo === "GZCF") {
                  await getRctCFMon().then(v => {
                    let num = v.data + 1
                    if (num.toString().length < 3) {
                      const j = 3 - (num.toString().length)
                      for (let i = 0; i < j; i++) {
                        num = "0" + num
                      }
                    }
                    let date = new Date()
                    let month = (date.getMonth() + Number(this.rct.month)).toString()
                    let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)
                    this.form.rctNo = this.rct.GZCFLeadingCharacter + year + (month.length == 1 ? "0" + month : month) + num.toString()
                  })
                } else if (this.form.orderBelongsTo && this.form.orderBelongsTo === "RSWH") {
                  await getRctRSWHMon().then(v => {
                    let num = v.data + 1
                    if (num.toString().length < 3) {
                      const j = 3 - (num.toString().length)
                      for (let i = 0; i < j; i++) {
                        num = "0" + num
                      }
                    }
                    let date = new Date()
                    let month = (date.getMonth() + Number(this.rct.month)).toString()
                    let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)
                    this.form.rctNo = this.rct.RSWHLeadingCharacter + year + (month.length == 1 ? "0" + month : month) + num.toString()
                  })
                } else {
                  await getRctMon().then(v => {
                    let num = v.data + 1
                    if (num.toString().length < 3) {
                      const j = 3 - (num.toString().length)
                      for (let i = 0; i < j; i++) {
                        num = "0" + num
                      }
                    }
                    let date = new Date()
                    let month = (date.getMonth() + Number(this.rct.month)).toString()
                    let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)
                    this.form.rctNo = this.rct.leadingCharacter + year + (month.length == 1 ? "0" + month : month) + num.toString()
                  })
                }
              }
              this.form.rctCreateTime = moment().format("yyyy-MM-DD HH:mm:ss")
              this.form.opAccept = 1
              this.form.processStatusId = 6
              // 操作自定舱
              this.form.sqdShippingBookingStatus = 1
              this.form.psaVerifyStatusId = 1
              this.form.psaVerify = 1

              let data = this.form
              data.bookingId = this.$store.state.user.sid
              data.bookingStatus = "1" // 已订舱
              data.bookingTime = moment().format("yyyy-MM-DD HH:mm:ss")
              data.distributionStatus = "2" // 操作自订舱
              // 主费用币种、单价、单位 = 例：usd8600/40HQ
              data.rctNo = this.form.rctNo
              data.clientShortName = this.form.clientName.split("/")[1]
              data.salesId = this.form.salesId
              data.salesAssistantId = this.form.salesAssistantId
              data.opId = this.$store.state.user.sid

              addPsarct(data).then(response => {
                // Psarct添加成功
              }).catch(() => {
                this.isSubmitting = false
              })
            }
            addRct(this.form).then(response => {
              this.form.rctId = response.data
              this.saveAll(response.data)
              this.$modal.msgSuccess("新增成功")
              /*  this.open = false
              this.getRctList() */
              this.form.noTransferAllowed = this.form.noTransferAllowed ? true : false
              this.form.noDividedAllowed = this.form.noDividedAllowed ? true : false
              this.form.noAgreementShowed = this.form.noAgreementShowed ? true : false
              this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? true : false

              // 新增的表是rct不是booking,所以地址栏变化
              // this.$tab.openPage("订舱单明细", "/salesquotation/bookingDetail", {rId: response.data, booking: true})
              this.isSubmitting = false
            }).catch(() => {
              this.isSubmitting = false
            })
          }
        } else {
          this.$message.warning("请完整填写操作单")
          this.isSubmitting = false
        }
      })
    }, 500),
    saveAs() {
      if (this.serviceList.has(20)) {
        this.rsOpRailFCL.rsServiceInstances = this.rsOpRailFclServiceInstance
        this.form.rsOpRailFCL = this.rsOpRailFCL
      }
      if (this.serviceList.has(21)) {
        this.rsOpRailLCL.rsServiceInstances = this.rsOpRailLclServiceInstance
        this.form.rsOpRailLCL = this.rsOpRailLCL
      }
      if (this.serviceList.has(40)) {
        this.rsOpExpress.rsServiceInstances = this.rsOpExpressServiceInstance
        this.form.rsOpExpress = this.rsOpExpress
      }
      if (this.serviceList.has(70)) {
        this.rsOpDOAgent.rsServiceInstances = this.rsOpDOAgentServiceInstance
        this.form.rsOpDOAgent = this.rsOpDOAgent
      }
      if (this.serviceList.has(71)) {
        this.rsOpClearAgent.rsServiceInstances = this.rsOpClearAgentServiceInstance
        this.form.rsOpClearAgent = this.rsOpClearAgent
      }
      if (this.serviceList.has(80)) {
        this.rsOpWHS.rsServiceInstances = this.rsOpWHSServiceInstance
        this.form.rsOpWHS = this.rsOpWHS
      }
      if (this.serviceList.has(90)) {
        this.rsOp3rdCert.rsServiceInstances = this.rsOp3rdCertServiceInstance
        this.form.rsOp3rdCert = this.rsOp3rdCert
      }
      if (this.serviceList.has(100)) {
        this.rsOpINS.rsServiceInstances = this.rsOpINSServiceInstance
        this.form.rsOpINS = this.rsOpINS
      }
      if (this.serviceList.has(101)) {
        this.rsOpTrading.rsServiceInstances = this.rsOpTradingServiceInstance
        this.form.rsOpTrading = this.rsOpTrading
      }
      if (this.serviceList.has(102)) {
        this.rsOpFumigation.rsServiceInstances = this.rsOpFumigationServiceInstance
        this.form.rsOpFumigation = this.rsOpFumigation
      }
      if (this.serviceList.has(103)) {
        this.rsOpCO.rsServiceInstances = this.rsOpCOServiceInstance
        this.form.rsOpCO = this.rsOpCO
      }
      if (this.serviceList.has(104)) {
        this.rsOpOther.rsServiceInstances = this.rsOpOtherServiceInstance
        this.form.rsOpOther = this.rsOpOther
      }

      this.form.relationClientIdList = this.RelationClientIdList.toString()
      this.form.serviceTypeIdList = this.serviceList.toString()
      // TODO
      if ((this.rsOpBulkTruck.rsOpTruckList && this.rsOpBulkTruck.rsOpTruckList) || (this.rsOpCtnrTruck.rsOpTruckList && this.rsOpCtnrTruck.rsOpTruckList.length > 0)) {
        if (this.serviceList.has(50)) {
          this.form.rsOpCtnrTruckList.map(item => {
            item.sqdContainersSealsSum = item.rsOpTruckList.map(v => v.containerNo).join(",")
          })
        } else if (this.serviceList.has(51)) {
          this.form.rsOpBulkTruckList.map(item => {
            item.sqdContainersSealsSum = item.rsOpTruckList.map(v => v.containerNo).join(",")
          })
        }
      }

      // 提单信息列表
      this.form.bookingMessagesList = this.bookingMessageList

      // 创建一个Set来存储所有不重复的集装箱号
      let allContainers = new Set()

      // 首先添加表单中已有的集装箱号
      if (this.form.sqdContainersSealsSum) {
        this.form.sqdContainersSealsSum.split("/")
          .map(container => container.trim())
          .filter(container => container)
          .forEach(container => allContainers.add(container))
      }

      // 然后添加bookingMessageList中的集装箱号
      if (this.bookingMessageList && this.bookingMessageList.length > 0) {
        this.bookingMessageList.forEach(item => {
          if (item.containerNo) {
            item.containerNo.split("/")
              .map(container => container.trim())
              .filter(container => container)
              .forEach(container => allContainers.add(container))
          }
        })
      }

      // 最后，将Set转换为逗号分隔的字符串
      this.form.sqdContainersSealsSum = Array.from(allContainers).join("/")

      // 同样处理blNo
      let allBlNos = new Set()

      // 添加表单中已有的提单号
      if (this.form.blNo) {
        allBlNos.add(this.form.blNo.trim())
      }

      // 添加bookingMessageList中的提单号
      if (this.bookingMessageList && this.bookingMessageList.length > 0) {
        this.bookingMessageList.forEach(item => {
          if (item.mBlNo) {
            allBlNos.add(item.mBlNo.trim())
          }
        })
      }

      // 将Set转换为逗号分隔的字符串
      this.form.blNo = Array.from(allBlNos).filter(blNo => blNo).join("/")

      let exchangeRate
      for (const a of this.$store.state.data.exchangeRateList) {
        if (this.form.podEta) {
          if (a.localCurrency === "RMB"
            && "USD" == a.overseaCurrency
            && parseTime(a.validFrom) <= parseTime(this.form.podEta)
            && parseTime(this.form.podEta) <= parseTime(a.validTo)
          ) {
            exchangeRate = currency(a.settleRate).divide(a.base).value
          }
        } else {
          if (a.localCurrency === "RMB"
            && "USD" == a.overseaCurrency
            && parseTime(a.validFrom) <= parseTime(new Date())
            && parseTime(new Date()) <= parseTime(a.validTo)) {
            exchangeRate = currency(a.settleRate).divide(a.base).value
          }
        }
      }

      // 费用
      this.form.dnRmb = this.rsClientMessageReceivableTaxRMB // rmb含税应收
      this.form.dnUsd = this.rsClientMessageReceivableTaxUSD // usd含税应收
      this.form.cnRmb = this.rsClientMessagePayableTaxRMB // rmb含税应付
      this.form.cnUsd = this.rsClientMessagePayableTaxUSD // usd含税应付
      // 折合费用
      this.form.dnInRmb = currency(this.rsClientMessageReceivableTaxRMB).add(currency(this.rsClientMessageReceivableTaxUSD).multiply(exchangeRate)).value // 折合rmb含税应收
      this.form.cnInRmb = currency(this.rsClientMessagePayableTaxRMB).add(currency(this.rsClientMessagePayableTaxUSD).multiply(exchangeRate)).value // 折合rmb含税应付

      // 统计收款和付款进度
      this.form.dnRmbBalance = this.sqdUnreceivedRmbSum // rmb含税未收
      this.form.dnUsdBalance = this.sqdUnreceivedUsdSum // usd含税未收
      this.form.cnRmbBalance = this.sqdUnpaidRmbSum // rmb含税未付
      this.form.cnUsdBalance = this.sqdUnpaidUsdSum // usd含税未付
      // 折合进度
      this.form.dnInRmbBalance = currency(this.sqdUnreceivedRmbSum).add(currency(this.sqdUnreceivedUsdSum).multiply(exchangeRate)).value // 折合rmb未收
      this.form.cnInRmbBalance = currency(this.sqdUnpaidRmbSum).add(currency(this.sqdUnpaidUsdSum).multiply(exchangeRate)).value // 折合rmb未付

      // 利润
      this.form.profitUsd = currency(this.form.dnUsd).subtract(this.form.cnUsd).value // 美元部分含税利润
      this.form.profitRmb = currency(this.form.dnRmb).subtract(this.form.cnRmb).value // 人民币部分含税利润
      this.form.profitInRmb = currency(this.form.profitUsd).multiply(exchangeRate).add(this.form.profitRmb).value // 折合人民币利润

      this.form.noTransferAllowed = this.form.noTransferAllowed ? 1 : 0
      this.form.noDividedAllowed = this.form.noDividedAllowed ? 1 : 0
      this.form.noAgreementShowed = this.form.noAgreementShowed ? 1 : 0
      this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? 1 : 0

      this.form.messageDisplay = [Number(this.serviceInfo), Number(this.orderInfo), Number(this.branchInfo), Number(this.logisticsInfo), Number(this.docInfo), Number(this.chargeInfo), Number(this.auditInfo)].toString()
      this.form.serviceMessageFold = [Number(this.clientMessage), Number(this.rsOpSealFclFold), Number(this.rsOpSealLclFold), Number(this.rsOpAirFold), Number(this.rsOpRailFclFold), Number(this.rsOpRailLclFold), Number(this.rsOpExpressFold), Number(this.rsOpCtnrTruckFold), Number(this.rsOpBulkTruckFold), Number(this.rsOpDocDeclareFold), Number(this.rsOpFreeDeclareFold), Number(this.rsOpDOAgentFold), Number(this.rsOpClearAgentFold), Number(this.rsOpWHSFold), Number(this.rsOp3rdCertFold), Number(this.rsOpINSFold), Number(this.rsOpTradingFold), Number(this.rsOpFumigationFold), Number(this.rsOpCOFold), Number(this.rsOpOtherFold)].toString()

      this.form.rsOpSeaFclList ? this.form.rsOpSeaFclList.map(item => {
        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
        return item
      }) : null
      this.form.rsOpSeaLclList ? this.form.rsOpSeaLclList.map(item => {
        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
        return item
      }) : null
      this.form.rsOpAirList ? this.form.rsOpAirList.map(item => {
        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
        return item
      }) : null
      this.form.rsOpCtnrTruckList ? this.form.rsOpCtnrTruckList.map(item => {
        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
        return item
      }) : null
      this.form.rsOpBulkTruckList ? this.form.rsOpBulkTruckList.map(item => {
        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
        return item
      }) : null
      this.form.rsOpDocDeclareList ? this.form.rsOpDocDeclareList.map(item => {
        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
        return item
      }) : null
      this.form.rsOpFreeDeclareList ? this.form.rsOpFreeDeclareList.map(item => {
        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)
        return item
      }) : null

      // copy一份操作单
      saveAsRct(this.form).then(response => {
        this.form.rctId = response.data
        this.saveAsAll(response.data)
        this.$modal.msgSuccess("另存成功")
        /*  this.open = false
        this.getRctList() */
        this.form.noTransferAllowed = this.form.noTransferAllowed ? true : false
        this.form.noDividedAllowed = this.form.noDividedAllowed ? true : false
        this.form.noAgreementShowed = this.form.noAgreementShowed ? true : false
        this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? true : false

        // 新增的表是rct不是booking,所以地址栏变化
        // this.$tab.openPage("订舱单明细", "/salesquotation/bookingDetail", {rId: response.data, booking: true})
      })

    },
    handleSettledRate(serviceObject) {
      if (serviceObject.settledRate) {
        if (this.form.revenueTon && this.form.revenueTon.split("+").length > 0) {
          this.form.revenueTon.split("+").map((revenueTon, i) => {
            if (revenueTon.split("x").length > 0 && i === 0) {
              this.form.countA = Number(revenueTon.split("x")[0])
              this.form.unitCodeA = revenueTon.split("x")[1]
            }
            if (revenueTon.split("x").length > 0 && i === 1) {
              this.form.countB = Number(revenueTon.split("x")[0])
              this.form.unitCodeB = revenueTon.split("x")[1]
            }
            if (revenueTon.split("x").length > 0 && i === 2) {
              this.form.countC = Number(revenueTon.split("x")[0])
              this.form.unitCodeC = revenueTon.split("x")[1]
            }
          })
        }

        let priceArr = serviceObject.settledRate.split("/")
        if (priceArr[0]) {
          // 根据结算价更改应付明细
          serviceObject.rsChargeList.map(item => {
            if (item.dnAmount === this.form.countA && item.dnUnitCode === this.form.unitCodeA) {
              item.dnUnitRate = priceArr[0]
            }
          })
        }
        if (priceArr[1]) {
          // 根据结算价更改应付明细
          serviceObject.rsChargeList.map(item => {
            if (item.dnAmount === this.form.countB && item.dnUnitCode === this.form.unitCodeB) {
              item.dnUnitRate = priceArr[1]
            }
          })
        }
        if (priceArr[2]) {
          // 根据结算价更改应付明细
          serviceObject.rsChargeList.map(item => {
            if (item.dnAmount === this.form.countC && item.dnUnitCode === this.form.unitCodeC) {
              item.dnUnitRate = priceArr[2]
            }
          })
        }
      }
    },
    mergeSeaServiceWithMainService() {
      this.form.rsOpSeaFclList ? this.form.rsOpSeaFclList = this.form.rsOpSeaFclList.map(item => {
        let data = this.customMerge(this.form, item)
        // 合并后填充其他属性
        data.sqdPsaNo = item.sqdPsaNo
        data.soNo = item.soNo
        data.blNo = item.blNo
        data.sqdContainersSealsSum = item.sqdContainersSealsSum
        data.carrierId = item.carrierId
        data.firstVessel = item.firstVessel
        data.secondVessel = item.secondVessel
        data.inquiryScheduleSummary = item.inquiryScheduleSummary
        data.firstCyOpenTime = item.firstCyOpenTime
        data.firstCyClosingTime = item.firstCyClosingTime
        data.cvClosingTime = item.cvClosingTime
        data.etd = item.etd
        data.eta = item.eta
        data.siClosingTime = item.siClosingTime
        data.sqdVgmStatus = item.sqdVgmStatus
        data.sqdAmsEnsPostStatus = item.sqdAmsEnsPostStatus
        // data.podEta = item.podEta
        // data.destinationPortEta = item.destinationPortEta
        data.bookingChargeRemark = item.bookingChargeRemark
        data.bookingAgentRemark = item.bookingAgentRemark

        data.bookingId = this.$store.state.user.sid
        data.bookingStatus = "1" // 已订舱
        data.bookingTime = moment().format("yyyy-MM-DD HH:mm:ss")
        data.distributionStatus = data.sqdPsaNo ? data.distributionStatus : "2" // 操作自订舱
        // 主费用币种、单价、单位 = 例：usd8600/40HQ
        data.rctNo = this.form.rctNo
        data.clientShortName = this.form.clientName.split("/")[1]
        data.salesId = this.form.salesId
        data.salesAssistantId = this.form.salesAssistantId
        // data.opId = this.$store.state.user.sid

        return data
      }) : null
      this.form.rsOpSeaLclList ? this.form.rsOpSeaLclList = this.form.rsOpSeaLclList.map(item => {
        let data = this.customMerge(this.form, item)
        // 合并后填充其他属性
        data.sqdPsaNo = item.sqdPsaNo
        data.soNo = item.soNo
        data.blNo = item.blNo
        data.sqdContainersSealsSum = item.sqdContainersSealsSum
        data.carrierId = item.carrierId
        data.firstVessel = item.firstVessel
        data.secondVessel = item.secondVessel
        data.inquiryScheduleSummary = item.inquiryScheduleSummary
        data.firstCyOpenTime = item.firstCyOpenTime
        data.firstCyClosingTime = item.firstCyClosingTime
        data.cvClosingTime = item.cvClosingTime
        data.etd = item.etd
        data.eta = item.eta
        data.siClosingTime = item.siClosingTime
        data.sqdVgmStatus = item.sqdVgmStatus
        data.sqdAmsEnsPostStatus = item.sqdAmsEnsPostStatus
        // data.podEta = item.podEta
        // data.destinationPortEta = item.destinationPortEta
        data.bookingChargeRemark = item.bookingChargeRemark
        data.bookingAgentRemark = item.bookingAgentRemark

        data.bookingId = this.$store.state.user.sid
        data.bookingStatus = "1" // 已订舱
        data.bookingTime = moment().format("yyyy-MM-DD HH:mm:ss")
        data.distributionStatus = data.sqdPsaNo ? data.distributionStatus : "2" // 操作自订舱
        // 主费用币种、单价、单位 = 例：usd8600/40HQ
        data.rctNo = this.form.rctNo
        data.clientShortName = this.form.clientName.split("/")[1]
        data.salesId = this.form.salesId
        data.salesAssistantId = this.form.salesAssistantId
        // data.opId = this.$store.state.user.sid

        return data
      }) : null
    },
    chooseWhenOnlyOne() {
      if (this.form.rsOpSeaFclList && this.form.rsOpSeaFclList.length === 1) {
        this.form.rsOpSeaFclList.map(item => {
          this.form.sqdPsaNo = item.sqdPsaNo
          this.form.soNo = item.soNo
          this.form.blNo = item.blNo
          this.form.sqdContainersSealsSum = item.sqdContainersSealsSum
          this.form.carrierId = item.carrierId
          this.form.firstVessel = item.firstVessel
          this.form.secondVessel = item.secondVessel
          this.form.inquiryScheduleSummary = item.inquiryScheduleSummary
          this.form.firstCyOpenTime = item.firstCyOpenTime
          this.form.firstCyClosingTime = item.firstCyClosingTime
          this.form.cvClosingTime = item.cvClosingTime
          this.form.etd = item.etd
          this.form.eta = item.eta
          this.form.siClosingTime = item.siClosingTime
          this.form.sqdVgmStatus = item.sqdVgmStatus
          this.form.sqdAmsEnsPostStatus = item.sqdAmsEnsPostStatus
          // this.form.podEta = item.podEta
          // this.form.destinationPortEta = item.destinationPortEta
          this.form.bookingChargeRemark = item.bookingChargeRemark
          this.form.bookingAgentRemark = item.bookingAgentRemark

          this.form.bookingId = item.bookingId
          this.form.bookingStatus = item.bookingStatus
          this.form.bookingTime = item.bookingTime
          this.form.distributionStatus = item.distributionStatus
          // 主费用币种、单价、单位 = 例：usd8600/40HQ
          this.form.rctNo = item.rctNo
          this.form.clientShortName = item.clientShortName
          this.form.salesId = item.salesId
          this.form.salesAssistantId = item.salesAssistantId
          // this.form.opId = item.opId
        })
      } else if (this.form.rsOpSeaLclList && this.form.rsOpSeaLclList.length === 1) {
        this.form.rsOpSeaLclList.map(item => {
          this.form.sqdPsaNo = item.sqdPsaNo
          this.form.soNo = item.soNo
          this.form.blNo = item.blNo
          this.form.sqdContainersSealsSum = item.sqdContainersSealsSum
          this.form.carrierId = item.carrierId
          this.form.firstVessel = item.firstVessel
          this.form.secondVessel = item.secondVessel
          this.form.inquiryScheduleSummary = item.inquiryScheduleSummary
          this.form.firstCyOpenTime = item.firstCyOpenTime
          this.form.firstCyClosingTime = item.firstCyClosingTime
          this.form.cvClosingTime = item.cvClosingTime
          this.form.etd = item.etd
          this.form.eta = item.eta
          this.form.siClosingTime = item.siClosingTime
          this.form.sqdVgmStatus = item.sqdVgmStatus
          this.form.sqdAmsEnsPostStatus = item.sqdAmsEnsPostStatus
          // this.form.podEta = item.podEta
          // this.form.destinationPortEta = item.destinationPortEta
          this.form.bookingChargeRemark = item.bookingChargeRemark
          this.form.bookingAgentRemark = item.bookingAgentRemark

          this.form.bookingId = item.bookingId
          this.form.bookingStatus = item.bookingStatus
          this.form.bookingTime = item.bookingTime
          this.form.distributionStatus = item.distributionStatus
          // 主费用币种、单价、单位 = 例：usd8600/40HQ
          this.form.rctNo = item.rctNo
          this.form.clientShortName = item.clientShortName
          this.form.salesId = item.salesId
          this.form.salesAssistantId = item.salesAssistantId
          // this.form.opId = item.opId
        })
      }
    },
    saveAll(id) {
      this.rsClientServiceInstance.rctId = id
      this.rsClientServiceInstance.serviceBelongTo = "client"
      this.rsClientMessage.rsServiceInstances = this.rsClientServiceInstance
      this.form.rsClientMessage = this.rsClientMessage

      this.saveAllService(id)
    },
    saveAsAll(id) {
      this.rsClientServiceInstance.rctId = id
      this.rsClientServiceInstance.serviceBelongTo = "client"
      this.rsClientMessage.rsServiceInstances = this.rsClientServiceInstance
      this.form.rsClientMessage = this.rsClientMessage

      this.saveAsAllService(id)
    },
    saveAllService(id) {
      if (this.form.rctId == null) {
        this.$message.error("请先确定单据")
        return
      }
      saveAllService(this.form).then(response => {
        if (typeof id != "number") {
          this.$message.success("保存成功")
        }

        this.updateAllService(response.data)
      })
    },
    saveAsAllService(id) {
      if (this.form.rctId == null) {
        this.$message.error("请先确定单据")
        return
      }
      saveAsAllService(this.form).then(response => {
        if (typeof id != "number") {
          this.$message.success("另存成功")
        }

      })
    },
    updateAllService(rsRct) {
      if (rsRct.serviceTypeIds.indexOf(1) !== -1 && rsRct.rsOpSeaFclList !== null) {
        this.form.rsOpSeaFclList = rsRct.rsOpSeaFclList
      }
      if (rsRct.serviceTypeIds.indexOf(2) !== -1 && rsRct.rsOpSeaLclList !== null) {
        this.form.rsOpSeaLclList = rsRct.rsOpSeaLclList
      }
      if (rsRct.serviceTypeIds.indexOf(10) !== -1 && rsRct.rsOpAirList !== null) {
        this.form.rsOpAirList = rsRct.rsOpAirList
      }
      if (rsRct.serviceTypeIds.indexOf(50) !== -1 && rsRct.rsOpCtnrTruckList !== null) {
        this.form.rsOpCtnrTruckList = rsRct.rsOpCtnrTruckList
      }
      if (rsRct.serviceTypeIds.indexOf(51) !== -1 && rsRct.rsOpBulkTruckList !== null) {
        this.form.rsOpBulkTruckList = rsRct.rsOpBulkTruckList
      }
      if (rsRct.serviceTypeIds.indexOf(60) !== -1 && rsRct.rsOpDocDeclareList !== null) {
        this.form.rsOpDocDeclareList = rsRct.rsOpDocDeclareList
      }
      if (rsRct.serviceTypeIds.indexOf(61) !== -1 && rsRct.rsOpFreeDeclareList !== null) {
        this.form.rsOpFreeDeclareList = rsRct.rsOpFreeDeclareList
      }

      if (rsRct.rsClientMessage != null) {
        this.rsClientMessage = rsRct.rsClientMessage
        this.rsClientServiceInstance = rsRct.rsClientMessage.rsServiceInstances
      }
      if (rsRct.serviceTypeIds.indexOf(20) !== -1 && rsRct.rsOpRailFCL !== null) {
        this.rsOpRailFCL = rsRct.rsOpRailFCL
        this.rsOpRailFclServiceInstance = rsRct.rsOpRailFCL.rsServiceInstances
      }
      if (rsRct.serviceTypeIds.indexOf(21) !== -1 && rsRct.rsOpRailLCL !== null) {
        this.rsOpRailLCL = rsRct.rsOpRailLCL
        this.rsOpRailLclServiceInstance = rsRct.rsOpRailLCL.rsServiceInstances
      }
      if (rsRct.serviceTypeIds.indexOf(40) !== -1 && rsRct.rsOpExpress !== null) {
        this.rsOpExpress = rsRct.rsOpExpress
        this.rsOpExpressServiceInstance = rsRct.rsOpExpress.rsServiceInstances
      }
      if (rsRct.serviceTypeIds.indexOf(70) !== -1 && rsRct.rsOpDOAgent !== null) {
        this.rsOpDOAgent = rsRct.rsOpDOAgent
        this.rsOpDOAgentServiceInstance = rsRct.rsOpDOAgent.rsServiceInstances
      }
      if (rsRct.serviceTypeIds.indexOf(71) !== -1 && rsRct.rsOpClearAgent !== null) {
        this.rsOpClearAgent = rsRct.rsOpClearAgent
        this.rsOpClearAgentServiceInstance = rsRct.rsOpClearAgent.rsServiceInstances
      }
      if (rsRct.serviceTypeIds.indexOf(80) !== -1 && rsRct.rsOpWHS !== null) {
        this.rsOpWHS = rsRct.rsOpWHS
        this.rsOpWHSServiceInstance = rsRct.rsOpWHS.rsServiceInstances
      }
      if (rsRct.serviceTypeIds.indexOf(90) !== -1 && rsRct.rsOp3rdCert !== null) {
        this.rsOp3rdCert = rsRct.rsOp3rdCert
        this.rsOp3rdCertServiceInstance = rsRct.rsOp3rdCert.rsServiceInstances
      }
      if (rsRct.serviceTypeIds.indexOf(100) !== -1 && rsRct.rsOpINS !== null) {
        this.rsOpINS = rsRct.rsOpINS
        this.rsOpINSServiceInstance = rsRct.rsOpINS.rsServiceInstances
      }
      if (rsRct.serviceTypeIds.indexOf(101) !== -1 && rsRct.rsOpTrading !== null) {
        this.rsOpTrading = rsRct.rsOpTrading
        this.rsOpTradingServiceInstance = rsRct.rsOpTrading.rsServiceInstances
      }
      if (rsRct.serviceTypeIds.indexOf(102) !== -1 && rsRct.rsOpFumigation !== null) {
        this.rsOpFumigation = rsRct.rsOpFumigation
        this.rsOpFumigationServiceInstance = rsRct.rsOpFumigation.rsServiceInstances
      }
      if (rsRct.serviceTypeIds.indexOf(103) !== -1 && rsRct.rsOpCO !== null) {
        this.rsOpCO = rsRct.rsOpCO
        this.rsOpCOServiceInstance = rsRct.rsOpCO.rsServiceInstances
      }
      if (rsRct.serviceTypeIds.indexOf(104) !== -1 && rsRct.rsOpOther !== null) {
        this.rsOpOther = rsRct.rsOpOther
        this.rsOpOtherServiceInstance = rsRct.rsOpOther.rsServiceInstances
      }
    },
    generateRct(v) {
      if (!checkRole(["Op"])) {
        this.$message.warning("无权限修改操作单号")
        return
      }
      if (v) {
        getRctMon().then(v => {
          let num = v.data
          if (num.toString().length < 3) {
            const j = 3 - (num.toString().length)
            for (let i = 0; i < j; i++) {
              num = "0" + num
            }
          }
          let date = new Date()
          let month = (date.getMonth() + Number(this.rct.month)).toString()
          let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)
          this.rct.rctNo = this.rct.leadingCharacter + year + (month.length == 1 ? "0" + month : month) + num.toString()
        })
      } else {
        this.openGenerateRct = true
      }
    },
    confirmRct() {
      this.form.rctNo = this.rct.rctNo
      this.openGenerateRct = false
    },
    cancel() {
      // this.reset()
      this.loading = false
      this.open = false
      this.openGenerateRct = false
    },
    // autoCompletion方法已移动到opDataHandlerMixin中
    getType(n) {
      if (!n) {
        return
      }
      for (const s of this.$store.state.data.serviceTypeList) {
        if (s.children) {
          for (const c of s.children) {
            if (c.serviceTypeId == n) {
              this.logisticsType = c.typeId
            }
          }
        }
        if (s.serviceTypeId == n) {
          this.logisticsType = s.typeId
        }
      }
    },
    addEmptyService(serviceTypeId) {
      if (1 === serviceTypeId && (!this.form.rsOpSeaFclList || (this.form.rsOpSeaFclList && this.form.rsOpSeaFclList.length === 0))) {
        if (!this.form.rsOpSeaFclList) {
          this.form.rsOpSeaFclList = []
        }
        let rsOpSeaFcl = this.rsOpSeaFcl
        rsOpSeaFcl.rsServiceInstances = this.rsServiceInstances
        this.form.rsOpSeaFclList.push(rsOpSeaFcl)
      }
      if (2 === serviceTypeId && (!this.form.rsOpSeaLclList || (this.form.rsOpSeaLclList && this.form.rsOpSeaLclList.length === 0))) {
        if (!this.form.rsOpSeaLclList) {
          this.form.rsOpSeaLclList = []
        }
        let rsOpSeaLcl = this.rsOpSeaLcl
        rsOpSeaLcl.rsServiceInstances = this.rsServiceInstances
        this.form.rsOpSeaLclList.push(rsOpSeaLcl)
      }
      if (10 === serviceTypeId && (!this.form.rsOpAirList || (this.form.rsOpAirList && this.form.rsOpAirList.length === 0))) {
        if (!this.form.rsOpAirList) {
          this.form.rsOpAirList = []
        }
        let rsOpAir = this.rsOpAir
        rsOpAir.rsServiceInstances = this.rsServiceInstances
        this.form.rsOpAirList.push(rsOpAir)
      }
      if (50 === serviceTypeId && (!this.form.rsOpCtnrTruckList || (this.form.rsOpCtnrTruckList && this.form.rsOpCtnrTruckList.length === 0))) {
        if (!this.form.rsOpCtnrTruckList) {
          this.form.rsOpCtnrTruckList = []
        }
        let rsOpCtnrTruck = this.rsOpCtnrTruck
        rsOpCtnrTruck.rsServiceInstances = this.rsServiceInstances
        this.form.rsOpCtnrTruckList.push(rsOpCtnrTruck)
      }
      if (51 === serviceTypeId && (!this.form.rsOpBulkTruckList || (this.form.rsOpBulkTruckList && this.form.rsOpBulkTruckList.length === 0))) {
        if (!this.form.rsOpBulkTruckList) {
          this.form.rsOpBulkTruckList = []
        }
        let rsOpBulkTruck = this.rsOpBulkTruck
        rsOpBulkTruck.rsServiceInstances = this.rsServiceInstances
        this.form.rsOpBulkTruckList.push(rsOpBulkTruck)
      }
      if (60 === serviceTypeId && (!this.form.rsOpDocDeclareList || (this.form.rsOpDocDeclareList && this.form.rsOpDocDeclareList.length === 0))) {
        if (!this.form.rsOpDocDeclareList) {
          this.form.rsOpDocDeclareList = []
        }
        let rsOpDocDeclare = this.rsOpDocDeclare
        rsOpDocDeclare.rsServiceInstances = this.rsServiceInstances
        this.form.rsOpDocDeclareList.push(rsOpDocDeclare)
      }
      if (61 === serviceTypeId && (!this.form.rsOpFreeDeclareList || (this.form.rsOpFreeDeclareList && this.form.rsOpFreeDeclareList.length === 0))) {
        if (!this.form.rsOpFreeDeclareList) {
          this.form.rsOpFreeDeclareList = []
        }
        let rsOpFreeDeclare = this.rsOpFreeDeclare
        rsOpFreeDeclare.rsServiceInstances = this.rsServiceInstances
        this.form.rsOpFreeDeclareList.push(rsOpFreeDeclare)
      }
    },
    async getServiceTypeList(val) {
      if (this.$store.state.data.serviceTypeList.length == 0) {
        await this.$store.dispatch("getServiceTypeList")
      }
      this.list.clear()
      this.serviceList.clear()
      this.RAIL.clear()
      this.EXPRESS.clear()
      this.CLEAR.clear()
      this.WHS.clear()
      this.EXTEND.clear()
      this.form.serviceTypeIds = val
      for (const s of this.$store.state.data.serviceTypeList) {
        if (s.children) {
          for (const c of s.children) {
            if (val.includes(c.serviceTypeId)) {
              if (c.typeId != null) {
                this.list.add(c.typeId)
              }
              this.serviceList.add(c.serviceTypeId)

              this.addEmptyService(c.serviceTypeId)
              c.typeId === "3" ? this.RAIL.add(c) : null
              c.typeId === "4" ? this.EXPRESS.add(c) : null
              c.typeId === "7" ? this.CLEAR.add(c) : null
              c.typeId === "8" ? this.WHS.add(c) : null
              c.typeId === "9" ? this.EXTEND.add(c) : null
            }
          }
        }
        if (val.includes(s.serviceTypeId)) {
          if (s.typeId != null) {
            this.list.add(s.typeId)
          }
          this.serviceList.add(s.serviceTypeId)

          this.addEmptyService(s.serviceTypeId)
          s.typeId === "1" ? this.SEA.add(s) : null
          s.typeId === "2" ? this.AIR.add(s) : null
          s.typeId === "3" ? this.RAIL.add(s) : null
          s.typeId === "4" ? this.EXPRESS.add(s) : null
          s.typeId === "5" ? this.TRUCK.add(s) : null
          s.typeId === "6" ? this.CUSTOM.add(s) : null
          s.typeId === "7" ? this.CLEAR.add(s) : null
          s.typeId === "8" ? this.WHS.add(s) : null
          s.typeId === "9" ? this.EXTEND.add(s) : null
        }
      }
      this.$forceUpdate()
    },
    carrierNormalizer(node) {
      return {
        id: node.carrierId,
        label: (node.carrierShortName != null ? node.carrierShortName : "") + " " + (node.carrierLocalName != null ? node.carrierLocalName : "") + " " + (node.carrierEnName != null ? node.carrierEnName : "") + "," + pinyin.getFullChars((node.carrierShortName != null ? node.carrierShortName : "") + " " + (node.carrierLocalName != null ? node.carrierLocalName : ""))
      }
    },
    loadCarrier() {
      if (this.$store.state.data.carrierList.length == 0 || this.$store.state.data.redisList.carrier) {
        store.dispatch("getCarrierList").then(() => {
            this.carrierList = this.$store.state.data.carrierList
          }
        )
      } else {
        this.carrierList = this.$store.state.data.carrierList
      }
    },
    loadSelection() {
      if (this.$store.state.data.unitList.length == 0 || this.$store.state.data.redisList.unit) {
        this.$store.dispatch("getUnitList")
      }
      if (this.$store.state.data.currencyList.length == 0 || this.$store.state.data.redisList.currency) {
        this.$store.dispatch("getCurrencyList")
      }
      if (this.$store.state.data.chargeList.length == 0 || this.$store.state.data.redisList.charge) {
        this.$store.dispatch("getChargeList")
      }
      // 加载货运类型
      if (this.$store.state.data.serviceTypeList.length == 0 || this.$store.state.data.redisList.serviceType) {
        this.$store.dispatch("getServiceTypeList")
      }
      this.loadOp()
      this.loadCarrier()
      this.loadSales()
      this.loadBusinesses()
      this.loadStaffList()
    },
    // 查询操作部用户
    loadOp() {
      if (this.$store.state.data.opList.length == 0 || this.$store.state.data.redisList.opList) {
        store.dispatch("getOpList").then(() => {
          this.opList = this.$store.state.data.opList
        })
      } else {
        this.opList = this.$store.state.data.opList
      }
    },
    // 查询业务部用户
    loadSales() {
      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {
        store.dispatch("getSalesList").then(() => {
          this.belongList = this.$store.state.data.salesList
        })
      } else {
        this.belongList = this.$store.state.data.salesList
      }
    },
    // 查询商务部用户
    loadBusinesses() {
      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {
        store.dispatch("getBusinessesList").then(() => {
          this.businessList = this.$store.state.data.businessesList
        })
      } else {
        this.businessList = this.$store.state.data.businessesList
      }
    },
    confirmed(v, rsChargeList) {
      if (v == "op") {
        if (this.rsClientServiceInstance.isDnOpConfirmed) {
          this.rsClientServiceInstance.isDnOpConfirmed = null
          this.rsClientServiceInstance.opConfirmedTime = null
          this.opConfirmedName = null
          this.opConfirmedDate = null
        } else {
          this.rsClientServiceInstance.isDnOpConfirmed = this.$store.state.user.sid
          this.rsClientServiceInstance.opConfirmedTime = parseTime(new Date(), "{y}-{m}-{d}")
          this.opConfirmedName = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isDnOpConfirmed)[0].staffFamilyLocalName + "" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isDnOpConfirmed)[0].staffGivingLocalName
          this.opConfirmedDate = this.rsClientServiceInstance.opConfirmedTime
        }
        this.updateServiceInstance(this.rsClientServiceInstance)
      }
      if (v == "account") {
        if (this.rsClientServiceInstance.isAccountConfirmed) {
          this.rsClientServiceInstance.isAccountConfirmed = null
          this.rsClientServiceInstance.accountConfirmTime = null
          this.accountConfirmedName = null
          this.accountConfirmedDate = null
        } else {
          this.rsClientServiceInstance.isAccountConfirmed = this.$store.state.user.sid
          this.rsClientServiceInstance.accountConfirmTime = parseTime(new Date(), "{y}-{m}-{d}")
          this.accountConfirmedName = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isAccountConfirmed)[0].staffFamilyLocalName + "" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isAccountConfirmed)[0].staffGivingLocalName
          this.accountConfirmedDate = this.rsClientServiceInstance.accountConfirmTime

          rsChargeList = rsChargeList.map(item => {
            if (item.isAccountConfirmed != 1) {
              item.isAccountConfirmed = 1
              updateCharge(item)
              return item
            }
          })
        }

        this.updateServiceInstance(this.rsClientServiceInstance)
      }
      if (v == "sales") {
        if (this.rsClientServiceInstance.isDnSalesConfirmed) {
          this.rsClientServiceInstance.isDnSalesConfirmed = null
          this.rsClientServiceInstance.salesConfirmedTime = null
          this.salesConfirmedName = null
          this.salesConfirmedDate = null
        } else {
          this.rsClientServiceInstance.isDnSalesConfirmed = this.$store.state.user.sid
          this.rsClientServiceInstance.salesConfirmedTime = parseTime(new Date(), "{y}-{m}-{d}")
          this.salesConfirmedName = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isDnSalesConfirmed)[0].staffFamilyLocalName + "" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isDnSalesConfirmed)[0].staffGivingLocalName
          this.salesConfirmedDate = this.rsClientServiceInstance.salesConfirmedTime
        }
        this.updateServiceInstance(this.rsClientServiceInstance)
      }
      if (v == "client") {
        if (this.rsClientServiceInstance.isDnClientConfirmed) {
          this.rsClientServiceInstance.isDnClientConfirmed = null
          this.rsClientServiceInstance.clientConfirmedTime = null
          this.clientConfirmedName = null
          this.clientConfirmedDate = null
        } else {
          this.rsClientServiceInstance.isDnClientConfirmed = this.$store.state.user.sid
          this.rsClientServiceInstance.clientConfirmedTime = parseTime(new Date(), "{y}-{m}-{d}")
          this.clientConfirmedName = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isDnClientConfirmed)[0].staffFamilyLocalName + "" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isDnClientConfirmed)[0].staffGivingLocalName
          this.clientConfirmedDate = this.rsClientServiceInstance.clientConfirmedTime
        }
        this.updateServiceInstance(this.rsClientServiceInstance)
      }
    },
    staffNormalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      let l
      if (node.staff) {
        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {
          if (node.role.roleLocalName != null) {
            l = node.role.roleLocalName + "," + pinyin.getFullChars(node.role.roleLocalName)
          } else {
            l = node.dept.deptLocalName + "," + pinyin.getFullChars(node.dept.deptLocalName)
          }
        } else {
          l = node.staff.staffCode + " " + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + " " + node.staff.staffGivingEnName + "," + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)
        }
      }
      if (node.roleId) {
        return {
          id: node.roleId,
          label: l,
          children: node.children,
          isDisabled: node.staffId == null && node.children == undefined
        }
      } else {
        return {
          id: node.deptId,
          label: l,
          children: node.children,
          isDisabled: node.staffId == null && node.children == undefined
        }
      }
    },
    businessesNormalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      let l
      if (node.staff) {
        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {
          if (node.role.roleLocalName != null) {
            l = node.role.roleLocalName + "," + pinyin.getFullChars(node.role.roleLocalName)
          } else {
            l = node.dept.deptLocalName + "," + pinyin.getFullChars(node.dept.deptLocalName)
          }
        } else {
          l = node.staff.staffCode + " " + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + " " + node.staff.staffGivingEnName + "," + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)
        }
      }
      return {
        id: node.staffId,
        label: l,
        isDisabled: node.staffId == null && node.children == undefined
      }
    },
    reset() {
      this.RelationClientList = []
      this.bookingMessageList = []
      this.form = {
        noTransferAllowed: false,
        noDividedAllowed: false,
        noAgreementShowed: false,
        isCustomsIntransitShowed: false,
        rctId: null,
        rctNo: null,
        clientId: null,
        clientSummary: null,
        clientRoleId: null,
        clientContact: null,
        clientContactTel: null,
        clientContactEmail: null,
        relationClientIdList: null,
        emergencyLevel: null,
        difficultyLevel: null,
        releaseType: null,
        paymentTitleCode: null,
        impExpType: null,
        tradingTerms: null,
        logisticsTerms: null,
        tradingPaymentChannel: null,
        clientContractNo: null,
        clientInvoiceNo: null,
        cargoTypeIdSum: null,
        goodsNameSummary: null,
        packageQuantity: null,
        goodsVolume: null,
        grossWeight: null,
        weightUnitCode: null,
        goodsCurrencyCode: null,
        goodsValue: null,
        logisticsTypeId: null,
        revenueTon: null,
        polId: null,
        localBasicPortId: null,
        transitPortId: null,
        podId: null,
        destinationPortId: null,
        cvClosingTime: null,
        siClosingTime: null,
        firstVessel: null,
        firstVoyage: null,
        firstCyOpenTime: null,
        firstCyClosingTime: null,
        firstEtd: null,
        basicVessel: null,
        basicVoyage: null,
        basicFinalGateinTime: null,
        basicEtd: null,
        podEta: null,
        destinationPortEta: null,
        carrierId: null,
        inquiryScheduleSummary: null,
        polBookingAgent: null,
        podHandleAgent: null,
        serviceTypeIdList: null,
        sqdSoNoSum: null,
        sqdMblNoSum: null,
        sqdContainersSealsSum: null,
        blFormCode: null,
        sqdExportCustomsType: null,
        sqdTrailerType: null,
        rctProcessStatusSummary: null,
        transportStatusSummary: null,
        docStatusSummary: null,
        paymentReceivingStatusSummary: null,
        paymentPayingStatusSummary: null,
        transportStatus: "0",
        docStatus: "0",
        paymentPayingStatus: "0",
        rctProcessId: null,
        porcessId: null,
        porcessStatusId: null,
        processStatusTime: moment().format("yyyy-MM-DD HH:mm:ss"),
        statusUpdateTime: moment().format("yyyy-MM-DD HH:mm:ss"),
        processRemark: null,
        qoutationNo: null,
        qoutationSketch: null,
        salesId: null,
        qoutationTime: null,
        newBookingNo: null,
        newBookingRemark: null,
        salesAssistantId: null,
        salesObserverId: null,
        newBookingTime: moment().format("yyyy-MM-DD HH:mm:ss"),
        inquiryNoticeSum: null,
        inquiryInnerRemarkSum: null,
        verifyPsaId: null,
        psaVerifyTime: null,
        opLeaderNotice: null,
        opInnerRemark: null,
        opId: null,
        bookingOpId: null,
        docOpId: null,
        opObserverId: null,
        rctCreateTime: null,
        customTitle: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deleteBy: null,
        deleteTime: null,
        deleteStatus: "0",
        precarriageRegionId: null,
        precarriageAddress: null,
        precarriageTime: null,
        precarriageContact: null,
        precarriageTel: null,
        precarriageRemark: null,
        dispatchRegionId: null,
        dispatchAddress: null,
        dispatchTime: null,
        dispatchContact: null,
        dispatchTel: null,
        dispatchRemark: null,
        paymentReceivingStatus: null,
        sqdWarehousingStatus: null,
        sqdShippingBookingStatus: null,
        sqdTrailerBookingStatus: null,
        sqdContainerBookingStatus: null,
        sqdContainerLoadingStatus: null,
        sqdVesselArrangeStatus: null,
        sqdVgmStatus: null,
        sqdCustomDocsStatus: null,
        sqdCustomAuthorizedStatus: null,
        sqdCustomExamineStatus: null,
        sqdCustomReleaseStatus: null,
        sqdSiVerifyStatus: null,
        sqdSiPostStatus: null,
        sqdAmsEnsPostStatus: null,
        sqdIsfEmnfPostStatus: null,
        sqdMainServicePayingStatus: null,
        sqdBlGettingStatus: null,
        sqdBlReleasingStatus: null,
        sqdContainerNoSum: null,
        sqdPolBookingAgent: null,
        sqdPodHandleAgent: null,
        sqdCarrierId: null,
        logisticsPaymentTermsCode: null,
        sqdDocDeliveryWay: null,
        sqdDnRmbSumVat: null,
        sqdCnRmbSumVat: null,
        sqdProfitRmbSum: null,
        sqdProfitRmbSumVat: null,
        warehousingNo: null,
        clientJobNo: null,
        bookingShipper: null,
        bookingConsignee: null,
        bookingNotifyParty: null,
        sqdInsuranceType: null,
        isOpConfirmed: null,
        opConfirmedId: null,
        opConfirmedName: null,
        opConfirmedDate: null,
        isSalesConfirmed: null,
        salesConfirmedId: null,
        salesConfirmedName: null,
        salesConfirmedDate: null,
        isClientConfirmed: null,
        clientConfirmedId: null,
        clientConfirmedName: null,
        clientConfirmedDate: null,
        isAccountConfirmed: null,
        accountConfirmedId: null,
        accountConfirmedName: null,
        accountConfirmedDate: null,
        _PaymentTitleCode: [],
        _RelationClientIdList: [],
        rsOpSeaFclList: [],
        rsOpSeaLclList: [],
        rsOpAirList: [],
        rsOpCtnrTruckList: [],
        rsOpBulkTruckList: [],
        rsOpDocDeclareList: [],
        rsOpFreeDeclareList: [],
        orderBelongsTo: null
      }

      this.rsClientMessage = {
        rsChargeList: [],
        rsOpLogList: [],
        rsDocList: []
      },
        // new 子服务数据
        this.rsOpSeaFcl = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpSeaLcl = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpAir = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpRailFCL = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpRailLCL = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpRail = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpExpress = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpTruck = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpCtnrTruck = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: [],
          rsOpTruckList: []
        },
        this.rsOpBulkTruck = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: [],
          rsOpTruckList: []
        },
        //正单报关
        this.rsOpDocDeclare = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        // 全包报关
        this.rsOpFreeDeclare = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpExportCustomsClearance = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpImportCustomsClearance = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpImportDispatchTruck = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        // 代理放单
        this.rsOpDOAgent = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpClearAgent = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpWHS = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpWarehouse = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpInspectionAndCertificate = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpInsurance = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpExpandService = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOp3rdCert = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpINS = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpTrading = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpFumigation = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpCO = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        },
        this.rsOpOther = {
          rsChargeList: [],
          rsOpLogList: [],
          rsDocList: []
        }

      this.rsClientServiceInstance = {
        serviceId: null,
        serviceTypeId: null,
        rctId: null,
        rctNo: null,
        supplierId: null,
        supplierSummary: null,
        supplierContact: null,
        supplierTel: null,
        paymentTitleCode: null,
        logisticsPaymentTermsCode: null,
        inquiryNo: null,
        agreementTypeCode: null,
        agreementNo: null,
        maxWeight: null,
        inquiryNotice: null,
        inquiryInnerRemark: null,
        inquiryPsaId: null,
        inquiryLeatestUpdatedTime: null,
        serviceBelongTo: null,
        isDnSalesConfirmed: null,
        isDnClientConfirmed: null,
        isDnOpConfirmed: null,
        isDnPsaConfirmed: null,
        isDnSupplierConfirmed: null,
        isAccountConfirmed: null,
        confirmAccountId: null,
        accountConfirmTime: null,
        salesConfirmedTime: null,
        clientConfirmedTime: null,
        opConfirmedTime: null,
        psaConfirmedTime: null,
        supplierConfirmedTime: null
      },
        this.rsOpSeaFclServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpSeaLclServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpAirServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpRailServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpRailFclServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpRailLclServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpExpressServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpTruckServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpCtnrTruckServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpBulkTruckServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpExportCustomsClearanceServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpImportCustomsClearanceServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpDocDeclareServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpFreeDeclareServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpDOAgentServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpClearAgentServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpImportDispatchTruckServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpInspectionAndCertificateServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpLandServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpInsuranceServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpExpandServiceServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpWHSServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOp3rdCertServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpINSServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpTradingServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpFumigationServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.rsOpCOServiceInstance = {
          serviceId: null,
          serviceTypeId: null,
          rctId: null,
          rctNo: null,
          supplierId: null,
          supplierSummary: null,
          supplierContact: null,
          supplierTel: null,
          paymentTitleCode: null,
          logisticsPaymentTermsCode: null,
          inquiryNo: null,
          agreementTypeCode: null,
          agreementNo: null,
          maxWeight: null,
          inquiryNotice: null,
          inquiryInnerRemark: null,
          inquiryPsaId: null,
          inquiryLeatestUpdatedTime: null,
          serviceBelongTo: null,
          isDnSalesConfirmed: null,
          isDnClientConfirmed: null,
          isDnOpConfirmed: null,
          isDnPsaConfirmed: null,
          isDnSupplierConfirmed: null,
          isAccountConfirmed: null,
          confirmAccountId: null,
          accountConfirmTime: null,
          salesConfirmedTime: null,
          clientConfirmedTime: null,
          opConfirmedTime: null,
          psaConfirmedTime: null,
          supplierConfirmedTime: null
        },
        this.resetForm("form")
    },
    loadStaffList() {
      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {
        store.dispatch("getAllRsStaffList").then(() => {
          this.staffList = this.$store.state.data.allRsStaffList
        })
      } else {
        this.staffList = this.$store.state.data.allRsStaffList
      }
    },
    copyFreight(row) {
      row.showQuotationUnit = false
      let item = this._.cloneDeep(row)
      item.clearingCompanyId = row.payClearingCompanyId
      item.companyName = row.payCompanyName
      // 如果是订舱
      if (this.$route.query.id) {
        item.clearingCompanyId = this.form.clientId
        item.companyName = this.form.company
      }
      this.rsClientMessage.rsChargeList.push(item)

      this.rsClientMessageCharge(this.rsClientMessage.rsChargeList)
    },
    rsClientMessageCharge(n) {
      // 初始化变量
      const receivable = {RMB: 0, USD: 0, taxRMB: 0, taxUSD: 0}
      const payable = {RMB: 0, USD: 0, taxRMB: 0, taxUSD: 0}
      const unreceived = {RMB: 0, USD: 0}
      const unpaid = {RMB: 0, USD: 0}

      // 计算客户信息中的应收
      n.forEach(rsCharge => {
        if (rsCharge.subtotal) {
          const isUSD = rsCharge.dnCurrencyCode === "USD"
          const currencyKey = isUSD ? "USD" : "RMB"
          const taxCurrencyKey = isUSD ? "taxUSD" : "taxRMB"
          const unreceivedKey = isUSD ? "USD" : "RMB"

          const subtotalWithoutTax = currency(rsCharge.subtotal).divide(currency(1).add(currency(rsCharge.dutyRate).divide(100))).value

          receivable[currencyKey] = currency(receivable[currencyKey]).add(subtotalWithoutTax).value
          receivable[taxCurrencyKey] = currency(receivable[taxCurrencyKey]).add(rsCharge.subtotal).value
          unreceived[unreceivedKey] = currency(unreceived[unreceivedKey]).add(rsCharge.sqdDnCurrencyBalance).value
        }
      })

      // 更新应收相关数据
      this.rsClientMessageReceivableRMB = receivable.RMB
      this.rsClientMessageReceivableUSD = receivable.USD
      this.rsClientMessageReceivableTaxRMB = receivable.taxRMB
      this.rsClientMessageReceivableTaxUSD = receivable.taxUSD
      this.sqdUnreceivedRmbSum = unreceived.RMB
      this.sqdUnreceivedUsdSum = unreceived.USD

      // 处理各种费用列表的通用函数
      const processChargeList = (chargeList) => {
        if (!chargeList) return

        chargeList.forEach(rsCharge => {
          if (rsCharge.subtotal) {
            const isUSD = rsCharge.dnCurrencyCode === "USD"
            const currencyKey = isUSD ? "USD" : "RMB"
            const taxCurrencyKey = isUSD ? "taxUSD" : "taxRMB"
            const unpaidKey = isUSD ? "USD" : "RMB"

            const subtotalWithoutTax = currency(rsCharge.subtotal).divide(currency(1).add(currency(rsCharge.dutyRate).divide(100))).value

            payable[currencyKey] = currency(payable[currencyKey]).add(subtotalWithoutTax).value
            payable[taxCurrencyKey] = currency(payable[taxCurrencyKey]).add(rsCharge.subtotal).value
            unpaid[unpaidKey] = currency(unpaid[unpaidKey]).add(rsCharge.sqdDnCurrencyBalance).value
          }
        })
      }

      // 处理各种服务类型的费用
      const serviceTypes = [
        {list: this.form.rsOpSeaFclList, property: "rsChargeList"},
        {list: this.form.rsOpSeaLclList, property: "rsChargeList"},
        {list: this.form.rsOpAirList, property: "rsChargeList"},
        {list: this.form.rsOpCtnrTruckList, property: "rsChargeList"},
        {list: this.form.rsOpBulkTruckList, property: "rsChargeList"},
        {list: this.form.rsOpDocDeclareList, property: "rsChargeList"},
        {list: this.form.rsOpFreeDeclareList, property: "rsChargeList"}
      ]

      // 处理列表类型的服务
      serviceTypes.forEach(service => {
        if (service.list) {
          service.list.forEach(item => {
            processChargeList(item[service.property])
          })
        }
      })

      // 处理单一对象类型的服务
      const singleServices = [
        "rsOpRailFCL", "rsOpRailLCL", "rsOpExpress", "rsOpDOAgent",
        "rsOpClearAgent", "rsOpWHS", "rsOp3rdCert", "rsOpINS",
        "rsOpTrading", "rsOpFumigation", "rsOpCO", "rsOpOther"
      ]

      singleServices.forEach(serviceName => {
        if (this[serviceName]) {
          processChargeList(this[serviceName].rsChargeList)
        }
      })

      // 更新应付相关数据
      this.rsClientMessagePayableRMB = payable.RMB
      this.rsClientMessagePayableTaxRMB = payable.taxRMB
      this.rsClientMessagePayableUSD = payable.USD
      this.rsClientMessagePayableTaxUSD = payable.taxUSD
      this.sqdUnpaidRmbSum = unpaid.RMB
      this.sqdUnpaidUsdSum = unpaid.USD

      // 计算利润
      this.rsClientMessageProfitRMB = currency(receivable.RMB).subtract(payable.RMB).value
      this.rsClientMessageProfitUSD = currency(receivable.USD).subtract(payable.USD).value

      // 计算含税利润
      if (n.length > 0) {
        this.rsClientMessageProfitTaxRMB = currency(receivable.taxRMB).subtract(payable.taxRMB).value
        this.rsClientMessageProfitTaxUSD = currency(receivable.taxUSD).subtract(payable.taxUSD).value
      } else {
        this.rsClientMessageProfitTaxRMB = currency(receivable.RMB).subtract(payable.taxRMB).value
        this.rsClientMessageProfitTaxUSD = currency(receivable.USD).subtract(payable.taxUSD).value
      }
    },
    calculateCharge(serviceTypeId, n, rsOpService) {
      // 如果没有费用项，直接返回
      if (!n || n.length === 0) {
        return
      }

      // 初始化变量
      let payableRMB = 0
      let payableRMBTax = 0
      let payableUSD = 0
      let payableUSDTax = 0

      // 计算费用
      n.forEach(rsCharge => {
        const subtotal = rsCharge.subtotal
        const taxDivisor = currency(1).add(currency(rsCharge.dutyRate).divide(100))
        const netAmount = currency(subtotal).divide(taxDivisor).value

        if (rsCharge.dnCurrencyCode === "USD") {
          payableUSD = currency(payableUSD).add(netAmount).value
          payableUSDTax = currency(payableUSDTax).add(subtotal).value
        } else {
          payableRMB = currency(payableRMB).add(netAmount).value
          payableRMBTax = currency(payableRMBTax).add(subtotal).value
        }
      })

      // 设置服务费用属性
      rsOpService.payableRMB = payableRMB
      rsOpService.payableUSD = payableUSD
      rsOpService.payableRMBTax = payableRMBTax
      rsOpService.payableUSDTax = payableUSDTax

      // 更新客户消息费用
      this.rsClientMessageCharge(this.rsClientMessage.rsChargeList,
        serviceTypeId === 1 || serviceTypeId === 104 ? rsOpService : undefined)

      // 特殊处理订舱服务类型的费用备注
      if ((serviceTypeId === 1 || serviceTypeId === 2) &&
        (!rsOpService.bookingChargeRemark || rsOpService.bookingChargeRemark === "")) {
        let bookingChargeRemark = ""
        n.forEach(rsCharge => {
          bookingChargeRemark += `${rsCharge.chargeName}:\t${rsCharge.dnCurrencyCode} ${rsCharge.dnUnitRate}/${rsCharge.dnUnitCode}\n`
        })
        rsOpService.bookingChargeRemark = bookingChargeRemark
      }
    },
    selectCarrier(item, v) {
      item.carrierId = v.carrierId
      // this.form.carrierId = v.carrierId
      this.form.sqdCarrier = v.carrierIntlCode
      this.form.carrierEnName = v.carrierIntlCode
    },
    handleDeselectCompanyIds(id) {
      this.RelationClientIdList = this.RelationClientIdList.filter(item => item !== id)
    },
    handleSelectCompanyIds(node) {
      if (node.filter(item => this.RelationClientIdList.indexOf(item.companyId) === -1).length > 0) {
        node.filter(item => this.RelationClientIdList.indexOf(item.companyId) === -1).map(item => this.RelationClientIdList.push(item.companyId))
      }
      node.map(item => {
        this.companyList.indexOf(item) === -1 ? this.companyList.push(item) : null
      })
    },
    selectRelationClient(id) {
    },
    // 查询附加费列表
    getLocalList(row) {
      let index = row.locals
      if (index == null || index.length == 0) {
        return new Promise((resolve, reject) => {
          queryLocal(row).then(response => {
            row.locals = response.data
            resolve(row)
          })
        })
      }
    },
    handleChargeSelect(charges) {
      charges.map(item => {
        this.chargeSelectItem.rsChargeList.push({
          ...this._.cloneDeep(item),
          isAccountConfirmed: 0,
          sqdDnCurrencyPaid: 0,
          sqdDnCurrencyBalance: currency(item.subtotal).value,
          currencyRateCalculateDate: new Date(),
          chargeId: null,
          sqdRctId: this.form.rctId,
          serviceId: this.chargeSelectItem.serviceId,
          sqdServiceTypeId: this.chargeSelectItem.sqdServiceTypeId,
          sqdRctNo: this.form.rctNo
        })
      })
    },
    /**
     *
     * @param serviceTypeId
     * @param unit 单位
     * @param freight 选择的整条费用
     * @returns {Promise<void>}
     */
    async handleFreightSelect(serviceTypeId, unit, freight) {
      // 费用录入时显示加载
      const loading = this.$loading({
        lock: true,
        text: freight.inquiryNo + " 的 " + unit + "费用自动录入中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      })

      try {
        this.setBasicInfo(serviceTypeId, freight, this.curFreightSelectRow)
        let unitCode = unit.split("x")[1]
        // 将请求的local设置到freight中
        await this.getLocalList(freight)
        if (this.freightSelectData.revenueTonList && this.freightSelectData.revenueTonList.length > 0) {
          // GP FR HQ（计费单位）
          // good => 2x20GP
          for (const good of this.freightSelectData.revenueTonList) {
            // 计费单位的id等于用户在确认选择时选择的计费单位id
            if (good.split("x")[1] == unitCode) {
              // 先将所选的运费加入报价费用中(服务项目)
              // 添加费用时某些费用没有被添加进去--> FR单位时priceB、C、D都不适用，priceA为空那么这条费用就不会进入到服务项目列表中
              await this.addCharge(good, freight, freight.freightId, serviceTypeId)
              // 再将所选运费的基础附加费（lcoal）也加入报价费用中（服务项目）
              for (const local of freight.locals) {
                await this.addCharge(good, local, freight.freightId, serviceTypeId)
              }
              // 只匹配一个单位
              break
            }
          }
        }
        this.$message.success("费用录入成功")
      } catch (error) {
        console.error("费用录入失败:", error)
        this.$message.error("费用录入失败：" + (error.message || "未知错误"))
      } finally {
        this.curFreightSelectRow = null
        loading.close()
      }
      // this.openFreightSelect = false
    },
    // 基础信息设置
    setBasicInfo(serviceTypeId, freight, curRow) {
      if (curRow) {
        curRow.rsServiceInstances.inquiryNo = freight.inquiryNo
        curRow.rsServiceInstances.supplierId = freight.supplierId
        curRow.rsServiceInstances.supplier_tel = freight.supplierTel
        curRow.rsServiceInstances.supplierName = freight.company
        curRow.rsServiceInstances.agreementNo = freight.agreementNo
        curRow.rsServiceInstances.agreementTypeCode = freight.contractType
        curRow.rsServiceInstances.inquiryInnerRemark = freight.psaRemark
        curRow.rsServiceInstances.inquiryLeatestUpdatedTime = freight.createTime
        curRow.rsServiceInstances.inquiryNotice = freight.noticeForSales
        curRow.rsServiceInstances.maxWeight = freight.maxWeight
        let curCarrier = this.carrierList.filter(item => item.carrierIntlCode === freight.carrierCode)[0]
        if (curCarrier) {
          curRow.carrierId = curCarrier.carrierId
          // this.form.carrierId = v.carrierId
          this.form.sqdCarrier = curCarrier.carrierIntlCode
          this.form.carrierEnName = curCarrier.carrierIntlCode
        }
        curRow.inquiryScheduleSummary = freight.logisticsSchedule
      }
    },
    async addCharge(revenueTons, row, freightId, serviceTypeId) {
      let unitCode = revenueTons.split("x")[1]
      let amount = revenueTons.split("x")[0]
      if (revenueTons.split("x").length === 1) {
        unitCode = revenueTons.split("x")[0]
        amount = 1
      }

      // 初始化一个费用，清空quotationFreight的属性
      this.resetCharge()
      // 费用排序
      this.rsCharge.chargeOrderNum = row.chargeOrderNum
      // 费用类型排序
      this.rsCharge.chargeTypeOrderNum = row.chargeTypeOrderNum
      this.rsCharge.sqdServiceTypeId = row.serviceTypeId
      this.rsCharge.typeId = this.typeId
      // 计费单位为'票'
      if (row.unit === "BL") {
        this.rsCharge.inquiryAmount = 1
        this.rsCharge.quotationAmount = 1
        this.rsCharge.dnAmount = 1
      } else {
        this.rsCharge.inquiryAmount = amount != null ? amount : 1
        this.rsCharge.quotationAmount = amount != null ? amount : 1
        this.rsCharge.dnAmount = amount != null ? amount : 1
      }
      this.rsCharge.dnUnitCode = row.unitCode !== "Ctnr" ? row.unitCode : (revenueTons !== null ? unitCode : null)
      // 单位是柜，成本赋值（涉及到后面是否插入服务项目）
      if (row.unitCode === "Ctnr" && revenueTons != null) {
        if (unitCode === "20GP") {
          this.rsCharge.dnUnitRate = row.priceB
        } else if (unitCode === "40GP") {
          this.rsCharge.dnUnitRate = row.priceC
        } else if (unitCode === "40HQ") {
          this.rsCharge.dnUnitRate = row.priceD
        }
      }
      if (this.rsCharge.dnUnitRate == null) {
        this.rsCharge.dnUnitRate = row.priceA
      }
      this.rsCharge.costCurrencyCode = row.currencyCode
      this.rsCharge.costCurrency = row.currency
      //
      this.rsCharge.quotationRate = this.rsCharge.inquiryRate
      // row.charge --> 标准/THC
      // 取费用英文简写
      if (row.charge != null && row.charge.includes("/")) {
        this.rsCharge.charge = row.charge.split("/")[1]
        this.rsCharge.chargeName = row.charge.split("/")[1]
      } else {
        this.rsCharge.charge = row.charge
        this.rsCharge.chargeName = row.charge
      }
      this.rsCharge.chargeEn = row.chargeEn
      this.rsCharge.freightId = freightId
      this.rsCharge.localChargeId = row.localChargeId
      this.rsCharge.profit = 0
      this.rsCharge.taxRate = 0
      this.rsCharge.quotationCurrencyCode = row.currencyCode
      this.rsCharge.quotationCurrency = row.currency
      this.rsCharge.createTime = parseTime(new Date())
      this.rsCharge.createBy = this.$store.state.user.sid
      this.rsCharge.company = row.company
      this.rsCharge.richNo = row.richNo
      this.rsCharge.dnCurrencyCode = row.currency ? row.currency : row.currencyCode
      this.rsCharge.clearingCompanyId = row.supplierId
      this.rsCharge.companyName = row.company
      this.rsCharge.dnChargeNameId = row.chargeId
      this.rsCharge.dutyRate = 0

      this.rsCharge.showAmount = false
      this.rsCharge.showClient = false
      this.rsCharge.showCostCharge = false
      this.rsCharge.showCostCurrency = false
      this.rsCharge.showCostUnit = false
      this.rsCharge.showCurrencyRate = false
      this.rsCharge.showDutyRate = false
      this.rsCharge.showQuotationCharge = false
      this.rsCharge.showQuotationCurrency = false
      this.rsCharge.showQuotationUnit = false
      this.rsCharge.showStrategy = true
      this.rsCharge.showSupplier = false
      this.rsCharge.showUnitRate = false

      // 获取汇率
      let exchangeRate
      /*       this.getExchangeRate(this.rsCharge, (result) => {
              exchangeRate = result
            })

            exchangeRate = await this.getExchangeRateDirect(this.rsCharge) */

      if (this.rsCharge) {
        for (const a of this.$store.state.data.exchangeRateList) {
          if (a.localCurrency == "RMB"
            && row.dnCurrencyCode == a.overseaCurrency
          ) {
            exchangeRate = currency(a.settleRate).divide(a.base).value
          }
        }
      }

      if (exchangeRate == null) {
        this.rsCharge.basicCurrencyRate = 1
      } else {
        this.rsCharge.basicCurrencyRate = exchangeRate
      }

      let dutyRate = currency(this.rsCharge.dnUnitRate).multiply(this.rsCharge.dnAmount).multiply(currency(row.dutyRate).divide(100)).value
      this.rsCharge.subtotal = currency(currency(this.rsCharge.dnUnitRate).multiply(this.rsCharge.dnAmount)).add(dutyRate).value
      this.rsCharge.sqdDnCurrencyBalance = currency(this.rsCharge.dnUnitRate).multiply(this.rsCharge.dnAmount).value

      // 是否要插入新的服务项目
      if (this.rsCharge.dnUnitRate != null) {
        // 插入到对应服务的chargeList中
        this.insertCharge(this.rsCharge, serviceTypeId)
      }
    },
    insertCharge(row, serviceTypeId) {
      if (serviceTypeId === 1) {
        // this.rsOpSeaFcl.rsChargeList.push(row)
        this.form.rsOpSeaFclList = this.form.rsOpSeaFclList.map(item => {
          if (item.seaId === this.curFreightSelectRow.seaId) {
            row.clearingCompanyId = item.rsServiceInstances.supplierId
            row.companyName = item.rsServiceInstances.supplierName
            item.rsChargeList.push(row)
          }
          return item
        })
      }
      if (serviceTypeId === 2) {
        this.form.rsOpSeaLclList = this.form.rsOpSeaLclList.map(item => {
          if (item.seaId === this.curFreightSelectRow.seaId) {
            row.clearingCompanyId = item.rsServiceInstances.supplierId
            row.companyName = item.rsServiceInstances.supplierName
            item.rsChargeList.push(row)
          }
          return item
        })
      }
      if (serviceTypeId === 10) {
        row.clearingCompanyId = this.rsOpAirServiceInstance.supplierId
        row.companyName = this.rsOpAirServiceInstance.supplierName
        this.rsOpAir.rsChargeList.push(row)
      }
      if (serviceTypeId === 20) {
        row.clearingCompanyId = this.rsOpRailFclServiceInstance.supplierId
        row.companyName = this.rsOpRailFclServiceInstance.supplierName
        this.rsOpRailFCL.rsChargeList.push(row)
      }
      if (serviceTypeId === 21) {
        row.clearingCompanyId = this.rsOpRailLclServiceInstance.supplierId
        row.companyName = this.rsOpRailLclServiceInstance.supplierName
        this.rsOpRailLCL.rsChargeList.push(row)
      }
      if (serviceTypeId === 40) {
        row.clearingCompanyId = this.rsOpExpressServiceInstance.supplierId
        row.companyName = this.rsOpExpressServiceInstance.supplierName
        this.rsOpExpress.rsChargeList.push(row)
      }
      if (serviceTypeId === 50) {
        this.form.rsOpCtnrTruckList = this.form.rsOpCtnrTruckList.map(item => {
          if (item.seaId === this.curFreightSelectRow.seaId) {
            row.clearingCompanyId = item.rsServiceInstances.supplierId
            row.companyName = item.rsServiceInstances.supplierName
            item.rsChargeList.push(row)
          }
          return item
        })
      }
      if (serviceTypeId === 51) {
        this.form.rsOpBulkTruckList = this.form.rsOpBulkTruckList.map(item => {
          if (item.seaId === this.curFreightSelectRow.seaId) {
            row.clearingCompanyId = item.rsServiceInstances.supplierId
            row.companyName = item.rsServiceInstances.supplierName
            item.rsChargeList.push(row)
          }
          return item
        })
      }
      if (serviceTypeId === 60) {
        this.form.rsOpDocDeclareList = this.form.rsOpDocDeclareList.map(item => {
          if (item.seaId === this.curFreightSelectRow.seaId) {
            row.clearingCompanyId = item.rsServiceInstances.supplierId
            row.companyName = item.rsServiceInstances.supplierName
            item.rsChargeList.push(row)
          }
          return item
        })
      }
      if (serviceTypeId === 61) {
        this.form.rsOpFreeDeclareList = this.form.rsOpFreeDeclareList.map(item => {
          if (item.seaId === this.curFreightSelectRow.seaId) {
            row.clearingCompanyId = item.rsServiceInstances.supplierId
            row.companyName = item.rsServiceInstances.supplierName
            item.rsChargeList.push(row)
          }
          return item
        })
      }
      if (serviceTypeId === 70) {
        row.clearingCompanyId = this.rsOpDOAgentServiceInstance.supplierId
        row.companyName = this.rsOpDOAgentServiceInstance.supplierName
        this.rsOpDOAgent.rsChargeList.push(row)
      }
      if (serviceTypeId === 71) {
        row.clearingCompanyId = this.rsOpClearAgentServiceInstance.supplierId
        row.companyName = this.rsOpClearAgentServiceInstance.supplierName
        this.rsOpClearAgent.rsChargeList.push(row)
      }
      if (serviceTypeId === 80) {
        row.clearingCompanyId = this.rsOpWHSServiceInstance.supplierId
        row.companyName = this.rsOpWHSServiceInstance.supplierName
        this.rsOpWHS.rsChargeList.push(row)
      }
      if (serviceTypeId === 90) {
        row.clearingCompanyId = this.rsOp3rdCertServiceInstance.supplierId
        row.companyName = this.rsOp3rdCertServiceInstance.supplierName
        this.rsOp3rdCert.rsChargeList.push(row)
      }
      if (serviceTypeId === 100) {
        row.clearingCompanyId = this.rsOpINSServiceInstance.supplierId
        row.companyName = this.rsOpINSServiceInstance.supplierName
        this.rsOpINS.rsChargeList.push(row)
      }
      if (serviceTypeId === 101) {
        row.clearingCompanyId = this.rsOpTradingServiceInstance.supplierId
        row.companyName = this.rsOpTradingServiceInstance.supplierName
        this.rsOpTrading.rsChargeList.push(row)
      }
      if (serviceTypeId === 102) {
        row.clearingCompanyId = this.rsOpFumigationServiceInstance.supplierId
        row.companyName = this.rsOpFumigationServiceInstance.supplierName
        this.rsOpFumigation.rsChargeList.push(row)
      }
      if (serviceTypeId === 103) {
        row.clearingCompanyId = this.rsOpCOServiceInstance.supplierId
        row.companyName = this.rsOpCOServiceInstance.supplierName
        this.rsOpCO.rsChargeList.push(row)
      }
      if (serviceTypeId === 104) {
        row.clearingCompanyId = this.rsOpCOServiceInstance.supplierId
        row.companyName = this.rsOpCOServiceInstance.supplierName
        this.rsOpOther.rsChargeList.push(row)
      }
    },
    getExchangeRate(row, callback) {
      let re
      if (row) {
        for (const a of this.$store.state.data.exchangeRateList) {
          if (a.basicCurrency == "RMB"
            && row.dnCurrencyCode == a.currencyCode
            && parseTime(a.validFrom) <= parseTime(row.createTime)
            && parseTime(row.createTime) <= parseTime(a.validTo)) {
            re = a.exchangeRate / a.base
          }
        }
        callback(re)
      }
    },
    resetCharge() {
      this.rsCharge = {
        chargeId: null,
        sqdRctId: null,
        serviceId: null,
        sqdServiceTypeId: null,
        sqdRctNo: null,
        relatedFreightId: null,
        isRecievingOrPaying: null,
        clearingCompanyId: null,
        clearingCompanySummary: null,
        quotationStrategyId: null,
        dnChargeNameId: null,
        dnCurrencyCode: null,
        dnUnitRate: null,
        dnUnitCode: null,
        dnAmount: null,
        basicCurrencyRate: null,
        dutyRate: null,
        subtotal: null,
        chargeRemark: null,
        clearingCurrencyCode: null,
        dnCurrencyReceived: null,
        dnCurrencyPaid: null,
        dnCurrencyBalance: null,
        accountReceivedIdList: null,
        accountPaidIdList: null,
        logisticsInvoiceIdList: null
      }
    },
    selectCompany() {
      this.openCompanySelect = true
    },
    selectCompanyData(row) {
      this.form.clientName = row.companyTaxCode + "/" + (row.companyShortName ? row.companyShortName : row.companyEnShortName) + "/" + row.companyLocalName
      this.form.clientId = row.companyId
      this.form.clientContact = row.mainStaffOfficialName
      this.form.clientContactTel = row.staffMobile
      this.form.clientContactEmail = row.staffEmail
      this.form.roleIds = row.roleIds
      this.form.salesId = row.belongTo
      // this.salesId = row.belongTo
      this.form.salesAssistantId = row.followUp
      // this.salesAssistantId = row.followUp
      this.form.orderBelongsTo = row.companyBelongTo
      this.form.agreementNumber = row.agreementNumber
      this.form.paymentNode = (row.receiveStandard ? row.receiveStandard : "") + (row.receiveTerm ? row.receiveTerm : "")
      this.companyList.indexOf(row.companyId) === -1 ? this.companyList.push(row) : null

      this.openCompanySelect = false

      if (this.belongList != undefined) {
        for (const a of this.belongList) {
          if (a.children != undefined) {
            for (const b of a.children) {
              if (b.children != undefined) {
                for (const c of b.children) {
                  if (c.staffId == this.form.salesId) {
                    this.salesId = c.deptId
                  }
                  if (c.staffId == this.form.salesAssistantId) {
                    this.salesAssistantId = c.deptId
                  }
                }
              }
            }
          }
        }
      }
    },
    getCompanyRoleIds(val) {
      this.form.roleIds = val
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/op-document.scss';
</style>
