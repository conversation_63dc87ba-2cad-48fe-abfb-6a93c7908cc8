{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outbound\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outbound\\index.vue", "mtime": 1754646305908}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_outboundrecord", "require", "_rich", "_inventory", "_moment", "_interopRequireDefault", "_currency", "_warehouseReceipt", "_", "_preview", "_outboundPlant", "hiprintTemplate", "_default", "name", "components", "printPreview", "data", "showLeft", "showRight", "loading", "selectOutboundList", "ids", "single", "multiple", "showSearch", "total", "outboundrecordList", "title", "open", "queryParams", "pageNum", "pageSize", "outboundNo", "clientCode", "clientName", "operator", "containerType", "containerNo", "sealNo", "outboundDate", "warehouseQuote", "workerLoadingFee", "warehouseCollection", "collectionNotes", "totalBoxes", "totalGrossWeight", "totalVolume", "totalRows", "receivedStorageFee", "unpaidUnloadingFee", "unpaidPackagingFee", "logisticsAdvanceFee", "rentalBalanceFee", "overdueRent", "freeStackDays", "overdueUnitPrice", "form", "outboundType", "preOutboundInventoryListLoading", "search", "rules", "required", "message", "trigger", "outboundForm", "moment", "format", "clientRow", "openOutbound", "preOutboundInventoryList", "selectedCargoDetail", "watch", "n", "created", "getList", "mounted", "initPrint", "methods", "<PERSON><PERSON><PERSON>", "init", "providers", "defaultElementTypeProvider", "printOutboundPlant", "_this", "printData", "customerOrderNo", "plannedOutboundDate", "cargoType", "plateNumber", "driverPhone", "warehousePay", "operationRequirement", "outboundNote", "orderDate", "outbound<PERSON><PERSON><PERSON>", "warehouseConfirm", "parseTime", "Date", "totalSummary", "concat", "totalQuantity", "inventoryList", "map", "item", "inboundSerialNo", "subOrderNo", "consignee<PERSON><PERSON>", "sqdShippingMark", "itemName", "driverInfo", "outboundQuantity", "PrintTemplate", "template", "outboundPlant", "$refs", "preView", "print", "$message", "warning", "$store", "state", "user", "split", "success", "loadChildInventory", "tree", "treeNode", "resolve", "_this2", "$set", "listInventory", "packageTo", "inventoryId", "then", "response", "rows", "children", "includes", "setTimeout", "for<PERSON>ach", "child", "push", "table", "toggleRowSelection", "finally", "warehouseRentSettlement", "outbound<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unreceivedFromCustomer", "currency", "add", "additionalStorageFee", "overdueRentalFee", "difficultyWorkFee", "value", "receivedFromCustomer", "customerReceivableBalance", "subtract", "payableToWorker", "receivedUnloadingFee", "receivedPackingFee", "receivedFromSupplier", "receivedSupplier", "promissoryNoteSales", "promissoryNoteCost", "warehouseAdvanceOtherFee", "promissoryNoteGrossProfit", "outboundConfirm", "type", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "cargoDeduction", "error", "inboundSerialNoSub", "partialOutboundFlag", "Number", "rsCargoDetailsList", "boxCount", "unitGrossWeight", "unitVolume", "addOutboundrecord", "preOutboundFlag", "preOutboundInventory", "updateOutboundrecord", "outboundRecordId", "outboundInventory", "isRentSettlement", "rentalSettlementDate", "settlement", "loadPreOutboundInventoryList", "_this4", "sqdPlannedOutboundDate", "inventoryStatus", "preOutboundRecordId", "listInventorys", "filter", "includesInboundFee", "receivedFee", "inboundFee", "difference", "packageRecord", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$nextTick", "catch", "console", "handleOutbound", "selectedRows", "handlePreOutbound", "handleDirectOutbound", "handleRentSettlement", "handleOutboundCargoDetailSelectionChange", "selection", "row", "outboundCargoDetailsList", "isRowSelected", "getSummaries", "param", "_this5", "columns", "sums", "statisticalField", "summaryResults", "column", "index", "prop", "property", "reduce", "sum", "fieldsToUpdate", "field", "undefined", "handleOutboundSelectionChange", "_this6", "treeData", "store", "states", "previousIds", "_toConsumableArray2", "default", "newlySelected", "id", "newlyDeselected", "doLayout", "date1", "date2", "rentalDays", "diff", "volumn", "isNaN", "multiply", "overdueRentalUnitPrice", "days", "freeStackPeriod", "parentNode", "find", "node", "length", "childrenLoaded", "toggleRowExpansion", "parentId", "childIndex", "indexOf", "splice", "itemIndex", "findIndex", "selectContainerType", "rate20gp", "rate40hq", "outboundClient", "rateLcl", "$forceUpdate", "_this7", "listOutboundrecord", "cancel", "reset", "defaultValues", "_objectSpread2", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "_this8", "text", "status", "$modal", "confirm", "changeStatus", "msgSuccess", "handleSelectionChange", "handleAdd", "handleUpdate", "_this9", "getOutboundrecord", "submitForm", "_this10", "validate", "valid", "handleDelete", "_this11", "outboundRecordIds", "delOutboundrecord", "handleExport", "download", "getTime", "handleSearchEnter", "_this12", "serialNo", "String", "searchValue", "scrollWrapper", "$el", "querySelector", "querySelectorAll", "targetIndex", "idx", "rowText", "textContent", "targetRow", "rowTop", "offsetTop", "scrollTo", "top", "clientHeight", "behavior", "classList", "remove", "exports"], "sources": ["src/views/system/outbound/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"单号\" prop=\"outboundNo\">\r\n            <el-input\r\n              v-model=\"queryParams.outboundNo\"\r\n              clearable\r\n              placeholder=\"出仓单号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"客户\" prop=\"clientCode\">\r\n            <el-input\r\n              v-model=\"queryParams.clientCode\"\r\n              clearable\r\n              placeholder=\"客户代码\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"名称\" prop=\"clientName\">\r\n            <el-input\r\n              v-model=\"queryParams.clientName\"\r\n              clearable\r\n              placeholder=\"客户名称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n            <el-input\r\n              v-model=\"queryParams.containerNo\"\r\n              clearable\r\n              placeholder=\"柜号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"封号\" prop=\"sealNo\">\r\n            <el-input\r\n              v-model=\"queryParams.sealNo\"\r\n              clearable\r\n              placeholder=\"封号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"日期\" prop=\"outboundDate\">\r\n            <el-date-picker v-model=\"queryParams.outboundDate\"\r\n                            clearable style=\"width: 100%\"\r\n                            placeholder=\"出仓日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:edit']\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handlePreOutbound()\"\r\n            >操作预出仓\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:edit']\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleDirectOutbound()\"\r\n            >直接出仓\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:edit']\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleRentSettlement()\"\r\n            >结算仓租\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <!--出仓记录列表(预出仓记录列表)-->\r\n        <el-table v-loading=\"loading\" :data=\"outboundrecordList\" @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"(selectedRows) =>handleOutbound(selectedRows)\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"出仓单号\" prop=\"outboundNo\"/>\r\n          <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n          <el-table-column align=\"center\" label=\"客户名称\" prop=\"clientName\"/>\r\n          <el-table-column align=\"center\" label=\"操作员\" prop=\"operator\"/>\r\n          <el-table-column align=\"center\" label=\"柜型\" prop=\"containerType\"/>\r\n          <el-table-column align=\"center\" label=\"柜号\" prop=\"containerNo\"/>\r\n          <el-table-column align=\"center\" label=\"封号\" prop=\"sealNo\"/>\r\n          <el-table-column align=\"center\" label=\"出仓日期\" prop=\"outboundDate\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.outboundDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"仓库报价\" prop=\"warehouseQuote\"/>\r\n          <el-table-column align=\"center\" label=\"工人装柜费\" prop=\"workerLoadingFee\"/>\r\n          <el-table-column align=\"center\" label=\"仓管代收\" prop=\"warehouseCollection\"/>\r\n          <el-table-column align=\"center\" label=\"代收备注\" prop=\"collectionNotes\"/>\r\n          <el-table-column align=\"center\" label=\"总箱数\" prop=\"totalBoxes\"/>\r\n          <el-table-column align=\"center\" label=\"总毛重\" prop=\"totalGrossWeight\"/>\r\n          <el-table-column align=\"center\" label=\"总体积\" prop=\"totalVolume\"/>\r\n          <el-table-column align=\"center\" label=\"总行数\" prop=\"totalRows\"/>\r\n          <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\r\n          <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\"/>\r\n          <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackagingFee\"/>\r\n          <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\r\n          <el-table-column align=\"center\" label=\"租金平衡费\" prop=\"rentalBalanceFee\"/>\r\n          <el-table-column align=\"center\" label=\"超期仓租\" prop=\"overdueRent\"/>\r\n          <el-table-column align=\"center\" label=\"免堆天数\" prop=\"freeStackDays\"/>\r\n          <el-table-column align=\"center\" label=\"超期单价\" prop=\"overdueUnitPrice\"/>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:outboundrecord:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:outboundrecord:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 出仓对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"'出库单'\"\r\n      :visible.sync=\"openOutbound\"\r\n      append-to-body\r\n      width=\"70%\"\r\n    >\r\n      <el-form ref=\"outboundForm\" :model=\"outboundForm\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.outboundNo\" class=\"disable-form\" disabled placeholder=\"出仓单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.customerOrderNo\" placeholder=\"客户单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户代码\" prop=\"outboundNo\">\r\n                  <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"outboundForm.clientCode\"\r\n                               :placeholder=\"'客户代码'\"\r\n                               :type=\"'warehouseClient'\" @return=\"outboundForm.clientCode=$event\"\r\n                               @returnData=\"outboundClient($event)\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户名称\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.clientName\" class=\"disable-form\" disabled\r\n                            placeholder=\"客户名称\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓日期\" prop=\"inboundDate\">\r\n                  <el-date-picker v-model=\"outboundForm.outboundDate\"\r\n                                  clearable\r\n                                  placeholder=\"计划出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓方式\" prop=\"outboundType\">\r\n                  <el-select v-model=\"outboundForm.outboundType\" placeholder=\"出仓方式\" style=\"width: 100%\">\r\n                    <el-option label=\"整柜\" value=\"整柜\"></el-option>\r\n                    <el-option label=\"散货\" value=\"散货\"></el-option>\r\n                    <el-option label=\"快递\" value=\"快递\"></el-option>\r\n                    <el-option label=\"其他\" value=\"其他\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜型\" prop=\"containerType\">\r\n                  <el-select v-model=\"outboundForm.containerType\" style=\"width: 100%\" @change=\"selectContainerType\">\r\n                    <el-option label=\"20GP\" value=\"20GP\"/>\r\n                    <el-option label=\"20OT\" value=\"20OT\"/>\r\n                    <el-option label=\"20FR\" value=\"20FR\"/>\r\n                    <el-option label=\"TANK\" value=\"TANK\"/>\r\n                    <el-option label=\"40GP\" value=\"40GP\"/>\r\n                    <el-option label=\"40HQ\" value=\"40HQ\"/>\r\n                    <el-option label=\"40NOR\" value=\"40NOR\"/>\r\n                    <el-option label=\"40OT\" value=\"40OT\"/>\r\n                    <el-option label=\"40FR\" value=\"40FR\"/>\r\n                    <el-option label=\"40RH\" value=\"40RH\"/>\r\n                    <el-option label=\"45HQ\" value=\"45HQ\"/>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"货物类型\" prop=\"cargoType\">\r\n                  <el-select v-model=\"form.cargoType\" placeholder=\"请选择货物类型\" style=\"width: 100%\">\r\n                    <el-option label=\"普货\" value=\"普货\"></el-option>\r\n                    <el-option label=\"大件\" value=\"大件\"></el-option>\r\n                    <el-option label=\"鲜活\" value=\"鲜活\"></el-option>\r\n                    <el-option label=\"危品\" value=\"危品\"></el-option>\r\n                    <el-option label=\"冷冻\" value=\"冷冻\"></el-option>\r\n                    <el-option label=\"标记\" value=\"标记\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n                  <el-input v-model=\"outboundForm.containerNo\" placeholder=\"柜号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"封号\" prop=\"sealNo\">\r\n                  <el-input v-model=\"outboundForm.sealNo\" placeholder=\"封号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"车牌\" prop=\"plateNumber\">\r\n                  <el-input v-model=\"outboundForm.plateNumber\" placeholder=\"车牌\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"司机电话\" prop=\"driverPhone\">\r\n                  <el-input v-model=\"outboundForm.driverPhone\" placeholder=\"司机电话\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓库报价\" prop=\"warehouseQuote\">\r\n                  <el-input v-model=\"outboundForm.warehouseQuote\" class=\"number\" placeholder=\"仓库报价\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.difficultyWorkFee\" class=\"number\" placeholder=\"困难作业费\"/>\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代收\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehouseCollection\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"工人装柜费\" prop=\"workerLoadingFee\">\r\n                  <el-input v-model=\"outboundForm.workerLoadingFee\" class=\"number\" placeholder=\"工人装柜费\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.warehouseAdvanceOtherFee\" class=\"number\"\r\n                              placeholder=\"仓库代付其他费用\"\r\n                    />\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代付\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehousePay\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"操作要求\" prop=\"operationRequirement\">\r\n                  <el-input v-model=\"outboundForm.operationRequirement\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"仓管指示\" prop=\"outboundNote\">\r\n                  <el-input v-model=\"outboundForm.outboundNote\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"操作员\" prop=\"operator\">\r\n                  <el-input v-model=\"outboundForm.operator\" class=\"disable-form\" disabled placeholder=\"操作员\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"下单日期\" prop=\"orderDate\">\r\n                  <el-date-picker v-model=\"outboundForm.orderDate\"\r\n                                  class=\"disable-form\" clearable disabled\r\n                                  placeholder=\"下单日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓经手人\" prop=\"outboundHandler\">\r\n                  <el-input v-model=\"outboundForm.outboundHandler\" class=\"disable-form\" disabled\r\n                            placeholder=\"出仓经手人\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\" @click=\"warehouseConfirm\">\r\n                      {{ \"仓管确认\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\"\r\n                               @click=\"loadPreOutboundInventoryList\"\r\n                    >\r\n                      {{ \"加载待出库\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n        <!--库存记录列表(未出库)-->\r\n        <el-row :gutter=\"10\">\r\n          <el-col>\r\n            <el-col>\r\n              <el-table v-loading=\"preOutboundInventoryListLoading\" :data=\"preOutboundInventoryList\"\r\n                        ref=\"table\" :summary-method=\"getSummaries\" max-height=\"300\"\r\n                        show-summary @selection-change=\"handleOutboundSelectionChange\"\r\n                        :load=\"loadChildInventory\" :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\" lazy\r\n                        element-loading-text=\"加载中...\" row-key=\"inventoryId\"\r\n                        style=\"width: 100%;\"\r\n              >\r\n                <el-table-column align=\"center\" fixed type=\"selection\" width=\"28\"/>\r\n                <el-table-column align=\"center\" fixed label=\"序号\" type=\"index\" width=\"28\">\r\n                  <template slot-scope=\"scope\">\r\n                    {{ scope.$index + 1 }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\" width=\"120\">\r\n                  <template slot=\"header\" slot-scope=\"scope\">\r\n                    <el-input\r\n                      v-model=\"search\"\r\n                      clearable\r\n                      placeholder=\"输入流水号搜索\"\r\n                      size=\"mini\"\r\n                      @keyup.enter.native=\"handleSearchEnter\"\r\n                    />\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"部分出库\" prop=\"inboundDate\" width=\"50\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-switch v-model=\"scope.row.partialOutboundFlag\"/>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货物明细\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      :disabled=\"scope.row.partialOutboundFlag==0\"\r\n                      trigger=\"click\"\r\n                      width=\"800\"\r\n                    >\r\n                      <el-table :data=\"scope.row.rsCargoDetailsList\"\r\n                                @selection-change=\"(selectedRows) => handleOutboundCargoDetailSelectionChange(selectedRows,scope.row)\"\r\n                      >\r\n                        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n                        <el-table-column\r\n                          label=\"唛头\"\r\n                          prop=\"shippingMark\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"货名\"\r\n                          prop=\"itemName\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"箱数\"\r\n                          prop=\"boxCount\"\r\n                        >\r\n                          <template slot-scope=\"scope\">\r\n                            <el-input v-model=\"scope.row.boxCount\" :disabled=\"!isRowSelected(scope.row)\"/>\r\n                          </template>\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"包装类型\"\r\n                          prop=\"packageType\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件长\"\r\n                          prop=\"unitLength\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件宽\"\r\n                          prop=\"unitWidth\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件高\"\r\n                          prop=\"unitHeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"体积小计\"\r\n                          prop=\"unitVolume\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"毛重小计\"\r\n                          prop=\"unitGrossWeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"破损标志\"\r\n                          prop=\"damageStatus\"\r\n                        >\r\n                        </el-table-column>\r\n                      </el-table>\r\n                      <el-button slot=\"reference\" style=\"margin: 0;padding: 5px\">查看</el-button>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"最新计租日\" prop=\"inboundDate\" width=\"80\">\r\n                  <template slot-scope=\"scope\">\r\n                    <span>{{ parseTime(scope.row.rentalSettlementDate, \"{y}-{m}-{d}\") }}</span>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\" show-overflow-tooltip/>\r\n                <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n                <el-table-column align=\"center\" label=\"箱数\" prop=\"totalBoxes\"/>\r\n                <el-table-column align=\"center\" label=\"毛重\" prop=\"totalGrossWeight\"/>\r\n                <el-table-column align=\"center\" label=\"体积\" prop=\"totalVolume\"/>\r\n                <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\"/>\r\n                <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收入仓费\" prop=\"additionalStorageFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收卸货费\" prop=\"unpaidUnloadingFee\"/>\r\n                <el-table-column align=\"center\" label=\"应付卸货费\" prop=\"receivedUnloadingFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收打包费\" prop=\"unpaidPackingFee\"/>\r\n                <el-table-column align=\"center\" label=\"应付打包费\" prop=\"receivedPackingFee\"/>\r\n                <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\r\n                <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\"/>\r\n                <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\r\n                <el-table-column align=\"center\" label=\"超租天数\" prop=\"rentalDays\"/>\r\n                <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\r\n              </el-table>\r\n            </el-col>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!--汇总费用-->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>未收客户：{{ outboundForm.unreceivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收客户：{{ outboundForm.receivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应收客户余额：{{ outboundForm.customerReceivableBalance }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应付工人：{{ outboundForm.payableToWorker }}</span>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>本票销售额：{{ outboundForm.promissoryNoteSales }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票成本：{{ outboundForm.promissoryNoteCost }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票结余：{{ outboundForm.promissoryNoteGrossProfit }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收供应商总额：{{ outboundForm.receivedFromSupplier }}</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"printOutboundPlant\">打印出仓计划</el-button>\r\n        <el-button v-if=\"outboundType===0\" type=\"primary\" @click=\"outboundConfirm(0)\">确定预出仓</el-button>\r\n        <el-button v-else type=\"primary\" @click=\"outboundConfirm(outboundType)\">{{\r\n            outboundType === 3 ? \"结 算\" : \"出 仓\"\r\n          }}</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n        <el-button @click=\"openOutbound = false\">关 闭</el-button>\r\n  </span>\r\n    </el-dialog>\r\n\r\n    <!-- 预览 -->\r\n    <print-preview ref=\"preView\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addOutboundrecord,\r\n  changeStatus,\r\n  delOutboundrecord,\r\n  getOutboundrecord,\r\n  listOutboundrecord,\r\n  updateOutboundrecord\r\n} from \"@/api/system/outboundrecord\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport {\r\n  listInventory,\r\n  listInventorys,\r\n  outboundInventory,\r\n  preOutboundInventory,\r\n  settlement\r\n} from \"@/api/system/inventory\"\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\nimport warehouseReceipt from \"@/print-template/warehouseReceipt\"\r\nimport {defaultElementTypeProvider, hiprint} from \"@\"\r\nimport printPreview from \"@/views/print/demo/design/preview.vue\"\r\nimport outboundPlant from \"@/print-template/outboundPlant\"\r\n\r\nlet hiprintTemplate\r\nexport default {\r\n  name: \"Outboundrecord\",\r\n  components: {printPreview},\r\n  data() {\r\n    return {\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      selectOutboundList: [],\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 出仓记录表格数据\r\n      outboundrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      outboundType: null,\r\n      preOutboundInventoryListLoading: false,\r\n      search: null,\r\n      // 表单校验\r\n      rules: {\r\n        clientCode: [\r\n          {required: true, message: \"客户代码不能为空\", trigger: \"blur\"}\r\n        ]\r\n      },\r\n      outboundForm: {\r\n        outboundDate: moment().format(\"yyyy-MM-DD\")\r\n      },\r\n      clientRow: {},\r\n      openOutbound: false,\r\n      preOutboundInventoryList: [],\r\n      selectedCargoDetail: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  mounted() {\r\n    this.initPrint()\r\n  },\r\n  methods: {\r\n    initPrint() {\r\n      hiprint.init({\r\n        providers: [new defaultElementTypeProvider()]\r\n      })\r\n    },\r\n    printOutboundPlant() {\r\n      // 准备打印数据\r\n      const printData = {\r\n        title: \"瑞旗仓库出仓计划\",\r\n        // 表单数据\r\n        outboundNo: this.outboundForm.outboundNo || \"\",\r\n        customerOrderNo: this.outboundForm.customerOrderNo || \"\",\r\n        clientCode: this.outboundForm.clientCode || \"\",\r\n        clientName: this.outboundForm.clientName || \"\",\r\n        plannedOutboundDate: moment(this.outboundForm.plannedOutboundDate).format(\"yyyy-MM-DD HH:mm\") || \"\",\r\n        outboundType: this.outboundForm.outboundType || \"\",\r\n        containerType: this.outboundForm.containerType || \"\",\r\n        cargoType: this.form.cargoType || \"\",\r\n        containerNo: this.outboundForm.containerNo || \"\",\r\n        sealNo: this.outboundForm.sealNo || \"\",\r\n        plateNumber: this.outboundForm.plateNumber || \"\",\r\n        driverPhone: this.outboundForm.driverPhone || \"\",\r\n        warehouseQuote: this.outboundForm.warehouseQuote || \"\",\r\n        warehouseCollection: this.outboundForm.warehouseCollection || \"\",\r\n        workerLoadingFee: this.outboundForm.workerLoadingFee || \"\",\r\n        warehousePay: this.outboundForm.warehousePay || \"\",\r\n        operationRequirement: this.outboundForm.operationRequirement || \"\",\r\n        outboundNote: this.outboundForm.outboundNote || \"\",\r\n        operator: this.outboundForm.operator || \"\",\r\n        orderDate: this.outboundForm.orderDate || \"\",\r\n        outboundHandler: this.outboundForm.outboundHandler || \"\",\r\n        warehouseConfirm: \"√ 已确认 \" + this.parseTime(new Date(), \"{y}-{m}-{d}\"),\r\n\r\n        // 汇总数据\r\n        totalBoxes: this.outboundForm.totalBoxes || 0,\r\n        totalGrossWeight: this.outboundForm.totalGrossWeight || 0,\r\n        totalVolume: this.outboundForm.totalVolume || 0,\r\n        totalSummary: `件数: ${this.outboundForm.totalBoxes || 0} / 毛重: ${this.outboundForm.totalGrossWeight || 0} / 体积: ${this.outboundForm.totalVolume || 0}`,\r\n        totalQuantity: this.outboundForm.totalBoxes || 0,\r\n\r\n        // 勾选的库存列表\r\n        inventoryList: this.selectOutboundList.map(item => {\r\n          return {\r\n            inboundSerialNo: item.inboundSerialNo || \"\",\r\n            clientCode: `${item.subOrderNo || \"\"} ${item.consigneeName || \"\"}`,\r\n            totalBoxes: (this.outboundForm.sqdShippingMark || \"\") + \" / \" + (item.itemName || \"\") + \" / \" + (item.totalBoxes || 0) + \" / \" + (item.totalGrossWeight || 0) + \"KGS / \" + (item.totalVolume || 0) + \"CBM\",\r\n            totalGrossWeight: item.totalGrossWeight || 0,\r\n            totalVolume: item.totalVolume || 0,\r\n            driverInfo: item.driverInfo || \"\",\r\n            outboundQuantity: item.totalBoxes || 0\r\n          }\r\n        })\r\n      }\r\n\r\n      // 创建打印模板并预览打印\r\n      hiprintTemplate = new hiprint.PrintTemplate({template: outboundPlant})\r\n      this.$refs.preView.print(hiprintTemplate, printData)\r\n    },\r\n    warehouseConfirm() {\r\n      // 检查客户代码是否已选择\r\n      if (!this.outboundForm.clientCode) {\r\n        this.$message.warning(\"请先选择客户\")\r\n        return\r\n      }\r\n\r\n      // 设置操作员为当前用户\r\n      this.outboundForm.operator = this.$store.state.user.name.split(\" \")[1]\r\n\r\n      // 设置下单日期为当前日期\r\n      this.outboundForm.orderDate = moment().format(\"yyyy-MM-DD\")\r\n\r\n      // 提示确认成功\r\n      this.$message.success(\"仓管确认成功\")\r\n    },\r\n    // 加载子节点数据\r\n    loadChildInventory(tree, treeNode, resolve) {\r\n      // 设置当前行的加载状态\r\n      this.$set(tree, \"loading\", true)\r\n\r\n      // 使用packageTo字段查询子节点\r\n      listInventory({packageTo: tree.inventoryId}).then(response => {\r\n        const rows = response.rows\r\n\r\n        // 先将数据传递给表格，确保子节点渲染\r\n        resolve(rows)\r\n        tree.children = rows\r\n\r\n        // 如果父项被选中，在子节点渲染完成后选中它们\r\n        if (this.ids.includes(tree.inventoryId)) {\r\n          setTimeout(() => {\r\n            rows.forEach(child => {\r\n              if (!this.ids.includes(child.inventoryId)) {\r\n                this.ids.push(child.inventoryId)\r\n                this.selectOutboundList.push(child)\r\n              }\r\n              // 在UI上选中子项\r\n              this.$refs.table.toggleRowSelection(child, true)\r\n            })\r\n          }, 50) // 等待DOM更新\r\n        }\r\n      }).finally(() => {\r\n        // 无论请求成功还是失败，都需要关闭加载状态\r\n        this.$set(tree, \"loading\", false)\r\n      })\r\n    },\r\n    warehouseRentSettlement() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 4\r\n      this.openOutbound = true\r\n    },\r\n    countSummary() {\r\n      this.outboundForm.unreceivedFromCustomer = currency(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackagingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value\r\n      this.outboundForm.receivedFromCustomer = currency(this.outboundForm.warehouseCollection).value\r\n      this.outboundForm.customerReceivableBalance = currency(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value\r\n      this.outboundForm.payableToWorker = currency(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value\r\n      this.outboundForm.receivedFromSupplier = currency(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value\r\n      this.outboundForm.promissoryNoteSales = currency(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value\r\n      this.outboundForm.promissoryNoteCost = currency(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value\r\n      this.outboundForm.promissoryNoteGrossProfit = currency(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value\r\n    },\r\n    currency,\r\n    /**\r\n     *\r\n     * @param type 0:预出仓/1:出仓/2:直接出仓\r\n     */\r\n    outboundConfirm(type) {\r\n      // 执行前再次提醒\r\n      this.$confirm(\"确定要\" + (type === 0 ? \"预出仓\" : type === 2 ? \"出仓\" : type === 3 ? \"结算仓租\" : \"\") + \"吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        // 扣货不可以出仓\r\n        this.selectOutboundList.map(item => {\r\n          if (item.cargoDeduction == 1) {\r\n            this.$message.error(\"有扣货库存请重新勾选，流水号：\" + item.inboundSerialNoSub)\r\n            return\r\n          }\r\n        })\r\n\r\n        this.selectOutboundList.map(item => {\r\n          item.partialOutboundFlag = Number(item.partialOutboundFlag)\r\n        })\r\n\r\n        // 更新箱数、毛重、体积\r\n        this.outboundForm.totalBoxes = 0\r\n        this.outboundForm.totalGrossWeight = 0\r\n        this.outboundForm.totalVolume = 0\r\n        this.selectOutboundList.map(item => {\r\n          item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n            this.outboundForm.totalBoxes = currency(item.boxCount).add(this.outboundForm.totalBoxes).value\r\n            this.outboundForm.totalGrossWeight = currency(item.unitGrossWeight).add(this.outboundForm.totalGrossWeight).value\r\n            this.outboundForm.totalVolume = currency(item.unitVolume).add(this.outboundForm.totalVolume).value\r\n            return item\r\n          }) : null\r\n          return item\r\n        })\r\n        if (type === 0) {\r\n          addOutboundrecord(this.outboundForm).then(response => {\r\n            // 列表克隆一份,打上预出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.preOutboundFlag = \"1\"\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.preOutboundFlag = \"1\"\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            preOutboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"预出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        } else if (type === 1) {\r\n          // 直接出仓\r\n          this.outboundForm.preOutboundFlag = \"1\"\r\n          this.outboundForm.outboundDate = moment().format(\"yyyy-MM-DD\")\r\n\r\n          updateOutboundrecord(this.outboundForm).then(response => {\r\n            const outboundRecordId = response.data\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        } else if (type === 2) {\r\n          // 直接出仓\r\n          this.outboundForm.preOutboundFlag = \"0\"\r\n          this.outboundForm.outboundDate = moment().format(\"yyyy-MM-DD\")\r\n\r\n          addOutboundrecord(this.outboundForm).then(response => {\r\n            const outboundRecordId = response.data\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        } else if (type === 3) {\r\n          // 结算仓租\r\n          this.outboundForm.isRentSettlement = 1 // 仓租结算记录\r\n          addOutboundrecord(this.outboundForm).then(response => {\r\n            const outboundRecordId = response.data\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rentalSettlementDate = this.outboundForm.outboundDate\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            settlement(data).then(response => {\r\n              this.$message.success(\"结算成功\")\r\n              this.loadPreOutboundInventoryList()\r\n            })\r\n          })\r\n        } else {\r\n          const outboundRecordId = this.outboundForm.outboundRecordId\r\n          this.outboundForm.preOutboundFlag = \"0\"\r\n          this.outboundForm.outboundDate = moment().format(\"yyyy-MM-DD\")\r\n          updateOutboundrecord(this.outboundForm).then(response => {\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 根据出库信息加载待出库的记录\r\n    loadPreOutboundInventoryList() {\r\n      this.preOutboundInventoryListLoading = true\r\n      this.loading = true\r\n\r\n      // 构建查询参数\r\n      const queryParams = {\r\n        sqdPlannedOutboundDate: this.outboundForm.plannedOutboundDate,\r\n        clientCode: this.outboundForm.clientCode,\r\n        inventoryStatus: \"0\"\r\n      }\r\n\r\n      // 根据出库类型添加预出库标志\r\n      if (this.queryParams.preOutboundFlag) {\r\n        queryParams.preOutboundFlag = this.queryParams.preOutboundFlag\r\n      }\r\n\r\n      if (this.queryParams.preOutboundRecordId) {\r\n        queryParams.preOutboundRecordId = this.queryParams.preOutboundRecordId\r\n      }\r\n\r\n      // 发起请求\r\n      listInventorys(queryParams)\r\n        .then(response => {\r\n          // 处理响应数据\r\n          this.preOutboundInventoryList = response.rows.filter(item => !item.packageTo)\r\n          this.preOutboundInventoryList ? response.rows.map(item => {\r\n            // 计算补收入仓费\r\n            if (item.includesInboundFee === 0) {\r\n              const receivedFee = Number(item.receivedStorageFee || 0)\r\n              const inboundFee = Number(item.inboundFee || 0)\r\n              const difference = currency(inboundFee).subtract(receivedFee).value\r\n\r\n              // 只有当差值大于0时才设置补收费用\r\n              item.additionalStorageFee = difference > 0 ? difference : 0\r\n            } else {\r\n              item.additionalStorageFee = 0\r\n            }\r\n\r\n            // 如果是打包箱，标记为有子节点\r\n            if (item.packageRecord === \"1\") {\r\n              item.hasChildren = true\r\n            }\r\n\r\n            if (this.outboundForm.outboundRecordId === item.preOutboundRecordId) {\r\n              this.selectOutboundList.push(item)\r\n              this.$nextTick(() => {\r\n                this.$refs.table.toggleRowSelection(item, true)\r\n              })\r\n            }\r\n\r\n            return item\r\n          }) : []\r\n\r\n          // 更新总数\r\n          this.total = response.total || 0\r\n\r\n          // 如果是普通出库类型，自动选中预出库标记的行\r\n          if (this.outboundType === 0 && this.$refs.table) {\r\n            this.$nextTick(() => {\r\n              this.preOutboundInventoryList.forEach(item => {\r\n                if (item.preOutboundFlag === 1) {\r\n                  this.$refs.table.toggleRowSelection(item, true)\r\n                }\r\n              })\r\n            })\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error(\"加载预出库库存列表失败:\", error)\r\n          this.$message.error(\"加载预出库库存列表失败\")\r\n        })\r\n        .finally(() => {\r\n          this.queryParams.preOutboundRecordId = null\r\n          this.queryParams.preOutboundFlag = null\r\n          this.loading = false\r\n          this.preOutboundInventoryListLoading = false\r\n        })\r\n    },\r\n    // 选择预出仓记录\r\n    handleOutbound(selectedRows) {\r\n      this.outboundReset()\r\n      this.outboundForm = selectedRows\r\n      this.outboundType = 1\r\n      this.queryParams.preOutboundRecordId = this.outboundForm.outboundRecordId\r\n      this.queryParams.preOutboundFlag = \"1\"\r\n      this.loadPreOutboundInventoryList()\r\n      this.openOutbound = true\r\n    },\r\n    // 添加预出仓记录\r\n    handlePreOutbound() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 0\r\n      this.openOutbound = true\r\n    },\r\n    // 直接出仓\r\n    handleDirectOutbound() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 2\r\n      this.openOutbound = true\r\n    },\r\n    // 结算仓租\r\n    handleRentSettlement() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 3\r\n      this.openOutbound = true\r\n    },\r\n    parseTime,\r\n    handleOutboundCargoDetailSelectionChange(selection, row) {\r\n      row.outboundCargoDetailsList = selection\r\n      this.selectedCargoDetail = selection\r\n    },\r\n    // 判断当前行是否被选中\r\n    isRowSelected(row) {\r\n      return this.selectedCargoDetail.includes(row)\r\n    },\r\n    getSummaries(param) {\r\n      const {columns, data} = param\r\n      const sums = []\r\n      const statisticalField = [\r\n        \"receivedSupplier\", \"totalBoxes\", \"unpaidInboundFee\", \"totalGrossWeight\",\r\n        \"totalVolume\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"logisticsAdvanceFee\",\r\n        \"rentalBalanceFee\", \"overdueRentalFee\", \"additionalStorageFee\", \"unpaidUnloadingFee\",\r\n        \"unpaidPackingFee\", \"receivedUnloadingFee\", \"receivedPackingFee\"\r\n      ]\r\n      // 汇总结果存储对象\r\n      const summaryResults = {}\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = \"汇总\" // 第一列显示文本\r\n        } else {\r\n          const prop = column.property\r\n          let total = 0 // 在条件块之前定义total变量\r\n\r\n          if (prop === \"totalBoxes\" || prop === \"totalVolume\" || prop === \"totalGrossWeight\") {\r\n            total = this.selectOutboundList.reduce((sum, row) => {\r\n              if (row.packageTo) {\r\n                return currency(sum).add(Number(row[prop]) || 0).value\r\n              }\r\n              return sum\r\n            }, 0)\r\n          } else {\r\n            total = this.selectOutboundList.reduce((sum, row) =>\r\n              currency(sum).add(Number(row[prop]) || 0).value, 0)\r\n          }\r\n\r\n          sums[index] = total\r\n          // 现在可以安全地使用total\r\n          summaryResults[column.property] = total\r\n        }\r\n      })\r\n\r\n      // 定义需要更新到表单的字段列表\r\n      const fieldsToUpdate = [\r\n        \"totalBoxes\", \"totalGrossWeight\", \"totalVolume\",\r\n        \"receivedStorageFee\", \"unpaidUnloadingFee\", \"unpaidPackingFee\",\r\n        \"logisticsAdvanceFee\", \"rentalBalanceFee\", \"overdueRentalFee\",\r\n        \"additionalStorageFee\", \"receivedUnloadingFee\", \"receivedPackingFee\",\r\n        \"receivedSupplier\"\r\n      ]\r\n\r\n      // 只更新指定的字段\r\n      fieldsToUpdate.forEach(field => {\r\n        if (this.outboundForm && summaryResults[field] !== undefined) {\r\n          this.outboundForm[field] = summaryResults[field]\r\n        }\r\n      })\r\n\r\n      this.countSummary()\r\n\r\n      return sums\r\n    },\r\n    handleOutboundSelectionChange(selection) {\r\n      // 正确获取表格数据 - 通过data属性\r\n      const treeData = this.$refs.table.store.states.data\r\n      // 获取之前的选择状态，用于比较变化\r\n      const previousIds = [...this.ids]\r\n\r\n      // 清空当前选择\r\n      this.ids = []\r\n      this.ids = selection.map(item => item.inventoryId)\r\n      // 找出新选中和取消选中的项\r\n      const newlySelected = this.ids.filter(id => !previousIds.includes(id))\r\n      const newlyDeselected = previousIds.filter(id => !this.ids.includes(id))\r\n\r\n      this.selectOutboundList = selection\r\n      this.$refs.table.doLayout() // 刷新表格布局\r\n\r\n      // 根据仓租结算至（rental_settlement_date），计算该条库存的租金\r\n      // （ 出库当天-仓租结算至-免租期 ） * 租金单价\r\n      selection.map(item => {\r\n        const date1 = moment(this.outboundForm.outboundDate)\r\n        const date2 = moment(item.rentalSettlementDate)\r\n        item.rentalDays = date1.diff(date2, \"days\") + 1 // 差距的天数\r\n        let volumn = item.totalVolume\r\n\r\n        if (!Number.isNaN(item.rentalDays) && item.rentalDays > 0) {\r\n          // 出仓方式不是整柜没有免租天数\r\n          if (this.outboundForm.outboundType !== \"整柜\") {\r\n            item.overdueRentalFee = currency(item.rentalDays).multiply(item.overdueRentalUnitPrice).multiply(volumn).value\r\n          } else {\r\n            let days = currency(item.rentalDays).subtract(item.freeStackPeriod).value\r\n            days = days > 0 ? days : 0\r\n            item.rentalDays = days\r\n            item.overdueRentalFee = currency(days).multiply(item.overdueRentalUnitPrice).multiply(volumn).value\r\n          }\r\n        }\r\n\r\n        // 处理新选中的打包箱：自动选中其子项\r\n        if (item.packageRecord === \"1\" && newlySelected.includes(item.inventoryId)) {\r\n          // 如果是新选中的打包箱节点\r\n\r\n          // 在树形表格数据中找到对应的节点\r\n          const parentNode = treeData.find(node => node.inventoryId === item.inventoryId)\r\n\r\n          // 检查节点是否已展开(已有children属性且有内容)\r\n          if (parentNode && parentNode.children && parentNode.children.length > 0) {\r\n            // 如果节点已展开，直接选中其所有子项\r\n            setTimeout(() => {\r\n              parentNode.children.forEach(child => {\r\n                if (!this.ids.includes(child.inventoryId)) {\r\n                  this.ids.push(child.inventoryId)\r\n                  this.selectOutboundList.push(child)\r\n                  this.$refs.table.toggleRowSelection(child, true)\r\n                }\r\n              })\r\n            }, 50) // 给一点时间让UI更新\r\n          } else if (parentNode && !parentNode.childrenLoaded && parentNode.hasChildren) {\r\n            // 如果节点未展开且未加载过但有子节点标记\r\n            parentNode.childrenLoaded = true\r\n\r\n            // 手动展开行，触发懒加载\r\n            this.$refs.table.toggleRowExpansion(parentNode, true)\r\n\r\n            // 监听子节点加载完成后再选中它们\r\n            // 这里利用了loadChildInventory方法中的逻辑，它会在子节点加载后处理选中状态\r\n          }\r\n        }\r\n      })\r\n\r\n      // 处理取消选中的打包箱：取消选中其子项\r\n      newlyDeselected.forEach(parentId => {\r\n        // 找出对应的父节点\r\n        const parentNode = treeData.find(node =>\r\n          node.inventoryId === parentId && node.packageRecord === \"1\"\r\n        )\r\n\r\n        if (parentNode && parentNode.children && parentNode.children.length > 0) {\r\n          // 取消选中所有子项\r\n          parentNode.children.forEach(child => {\r\n            const childIndex = this.ids.indexOf(child.inventoryId)\r\n            if (childIndex > -1) {\r\n              // 从选中列表中移除\r\n              this.ids.splice(childIndex, 1)\r\n              const itemIndex = this.selectOutboundList.findIndex(\r\n                item => item.inventoryId === child.inventoryId\r\n              )\r\n              if (itemIndex > -1) {\r\n                this.selectOutboundList.splice(itemIndex, 1)\r\n              }\r\n              // 在UI上取消选中\r\n              this.$refs.table.toggleRowSelection(child, false)\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      this.countSummary()\r\n    },\r\n    selectContainerType(type) {\r\n      switch (type) {\r\n        case \"20GP\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate20gp\r\n          break\r\n        case \"40HQ\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate40hq\r\n          break\r\n\r\n      }\r\n    },\r\n    outboundClient(row) {\r\n      this.outboundForm.warehouseQuote = row.rateLcl\r\n      this.outboundForm.freeStackDays = row.freeStackPeriod\r\n      this.clientRow = row\r\n      this.outboundForm.overdueRentalUnitPrice = row.overdueRent\r\n      this.outboundForm.clientName = row.clientName\r\n      // this.outboundForm.workerLoadingFee=row.workerLoadingFee\r\n      this.$forceUpdate()\r\n    },\r\n    /** 查询出仓记录列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listOutboundrecord(this.queryParams).then(response => {\r\n        this.outboundrecordList = response.rows\r\n        this.total = response.total\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    outboundReset() {\r\n      // 定义默认值对象，便于维护\r\n      const defaultValues = {\r\n        // 基本信息\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        outboundDate: moment().format(\"yyyy-MM-DD\"),\r\n\r\n        // 集装箱信息\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n\r\n        // 费用信息 - 使用数值0作为默认值\r\n        warehouseQuote: 0,\r\n        workerLoadingFee: 0,\r\n        warehouseCollection: 0,\r\n        warehousePay: 0,\r\n\r\n        // 其他费用信息 - 使用null作为默认值\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        operationRequirement: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null,\r\n\r\n        // 汇总数据\r\n        receivedSupplier: null,\r\n        receivedFromSupplier: null,\r\n        unreceivedFromCustomer: null,\r\n        receivedFromCustomer: null,\r\n        customerReceivableBalance: null,\r\n        payableToWorker: null,\r\n        promissoryNoteSales: null,\r\n        promissoryNoteCost: null,\r\n        promissoryNoteGrossProfit: null\r\n      }\r\n\r\n      // 重置表单为默认值\r\n      this.outboundForm = {...defaultValues}\r\n\r\n      // 清空预出仓库存列表\r\n      this.preOutboundInventoryList = []\r\n\r\n      // 重置表单验证状态\r\n      this.resetForm(\"outboundForm\")\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        outboundDate: moment().format(\"yyyy-MM-DD\"),\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.outboundRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.outboundRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加出仓记录\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const outboundRecordId = row.outboundRecordId || this.ids\r\n      getOutboundrecord(outboundRecordId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改出仓记录\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"outboundForm\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.outboundForm.outboundRecordId != null) {\r\n            updateOutboundrecord(this.outboundForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addOutboundrecord(this.outboundForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const outboundRecordIds = row.outboundRecordId || this.ids\r\n      this.$modal.confirm(\"是否确认删除出仓记录编号为\\\"\" + outboundRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delOutboundrecord(outboundRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/outboundrecord/export\", {\r\n        ...this.queryParams\r\n      }, `outboundrecord_${new Date().getTime()}.xlsx`)\r\n    },\r\n    // 添加搜索并滚动到匹配行的方法\r\n    handleSearchEnter() {\r\n      if (!this.search) return\r\n\r\n      // 查找匹配的行索引\r\n      const index = this.preOutboundInventoryList.findIndex(\r\n        item => {\r\n          // 确保 inboundSerialNo 存在且为字符串\r\n          const serialNo = String(item.inboundSerialNo || \"\")\r\n          const searchValue = String(this.search)\r\n          // 打印每次比较的值，帮助调试\r\n          return serialNo.includes(searchValue)\r\n        }\r\n      )\r\n\r\n      if (index > -1) {\r\n        // 获取表格DOM\r\n        const table = this.$refs.table\r\n\r\n        this.$nextTick(() => {\r\n          // 获取表格的滚动容器\r\n          const scrollWrapper = table.$el.querySelector(\".el-table__body-wrapper\")\r\n          // 获取所有行\r\n          const rows = scrollWrapper.querySelectorAll(\".el-table__row\")\r\n\r\n          // 遍历所有行，找到匹配的流水号\r\n          let targetIndex = -1\r\n          rows.forEach((row, idx) => {\r\n            const rowText = row.textContent\r\n            if (rowText.includes(this.search)) {\r\n              targetIndex = idx\r\n            }\r\n          })\r\n\r\n          if (targetIndex > -1) {\r\n            const targetRow = rows[targetIndex]\r\n            // 计算需要滚动的位置\r\n            const rowTop = targetRow.offsetTop\r\n\r\n            // 使用平滑滚动\r\n            scrollWrapper.scrollTo({\r\n              top: rowTop - scrollWrapper.clientHeight / 2,\r\n              behavior: \"smooth\"\r\n            })\r\n\r\n            // 高亮显示该行\r\n            targetRow.classList.add(\"highlight-row\")\r\n            // 1秒后移除高亮\r\n            setTimeout(() => {\r\n              targetRow.classList.remove(\"highlight-row\")\r\n            }, 2000)\r\n          }\r\n        })\r\n      } else {\r\n        this.$message.warning(\"未找到匹配的记录\")\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .edit .number .el-input__inner {\r\n  text-align: right;\r\n}\r\n\r\n// 添加高亮样式\r\n::v-deep .highlight-row {\r\n  background-color: #fdf5e6 !important;\r\n  transition: background-color 0.5s;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAgjBA,IAAAA,eAAA,GAAAC,OAAA;AAQA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAOA,IAAAG,OAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,iBAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,CAAA,GAAAP,OAAA;AACA,IAAAQ,QAAA,GAAAJ,sBAAA,CAAAJ,OAAA;AACA,IAAAS,cAAA,GAAAL,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAU,eAAA;AAAA,IAAAC,QAAA,GACA;EACAC,IAAA;EACAC,UAAA;IAAAC,YAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACAC,kBAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,kBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;QACAC,YAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,gBAAA;MACA;MACA;MACAC,IAAA;MACAC,YAAA;MACAC,+BAAA;MACAC,MAAA;MACA;MACAC,KAAA;QACA3B,UAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,YAAA;QACAzB,YAAA,MAAA0B,eAAA,IAAAC,MAAA;MACA;MACAC,SAAA;MACAC,YAAA;MACAC,wBAAA;MACAC,mBAAA;IACA;EACA;EACAC,KAAA;IACA/C,UAAA,WAAAA,WAAAgD,CAAA;MACA,IAAAA,CAAA;QACA,KAAAtD,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAwD,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAD,SAAA,WAAAA,UAAA;MACAE,SAAA,CAAAC,IAAA;QACAC,SAAA,OAAAC,4BAAA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,KAAA;MACA;MACA,IAAAC,SAAA;QACAzD,KAAA;QACA;QACAK,UAAA,OAAAgC,YAAA,CAAAhC,UAAA;QACAqD,eAAA,OAAArB,YAAA,CAAAqB,eAAA;QACApD,UAAA,OAAA+B,YAAA,CAAA/B,UAAA;QACAC,UAAA,OAAA8B,YAAA,CAAA9B,UAAA;QACAoD,mBAAA,MAAArB,eAAA,OAAAD,YAAA,CAAAsB,mBAAA,EAAApB,MAAA;QACAT,YAAA,OAAAO,YAAA,CAAAP,YAAA;QACArB,aAAA,OAAA4B,YAAA,CAAA5B,aAAA;QACAmD,SAAA,OAAA/B,IAAA,CAAA+B,SAAA;QACAlD,WAAA,OAAA2B,YAAA,CAAA3B,WAAA;QACAC,MAAA,OAAA0B,YAAA,CAAA1B,MAAA;QACAkD,WAAA,OAAAxB,YAAA,CAAAwB,WAAA;QACAC,WAAA,OAAAzB,YAAA,CAAAyB,WAAA;QACAjD,cAAA,OAAAwB,YAAA,CAAAxB,cAAA;QACAE,mBAAA,OAAAsB,YAAA,CAAAtB,mBAAA;QACAD,gBAAA,OAAAuB,YAAA,CAAAvB,gBAAA;QACAiD,YAAA,OAAA1B,YAAA,CAAA0B,YAAA;QACAC,oBAAA,OAAA3B,YAAA,CAAA2B,oBAAA;QACAC,YAAA,OAAA5B,YAAA,CAAA4B,YAAA;QACAzD,QAAA,OAAA6B,YAAA,CAAA7B,QAAA;QACA0D,SAAA,OAAA7B,YAAA,CAAA6B,SAAA;QACAC,eAAA,OAAA9B,YAAA,CAAA8B,eAAA;QACAC,gBAAA,kBAAAC,SAAA,KAAAC,IAAA;QAEA;QACArD,UAAA,OAAAoB,YAAA,CAAApB,UAAA;QACAC,gBAAA,OAAAmB,YAAA,CAAAnB,gBAAA;QACAC,WAAA,OAAAkB,YAAA,CAAAlB,WAAA;QACAoD,YAAA,mBAAAC,MAAA,MAAAnC,YAAA,CAAApB,UAAA,4BAAAuD,MAAA,MAAAnC,YAAA,CAAAnB,gBAAA,4BAAAsD,MAAA,MAAAnC,YAAA,CAAAlB,WAAA;QACAsD,aAAA,OAAApC,YAAA,CAAApB,UAAA;QAEA;QACAyD,aAAA,OAAAjF,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;UACA;YACAC,eAAA,EAAAD,IAAA,CAAAC,eAAA;YACAvE,UAAA,KAAAkE,MAAA,CAAAI,IAAA,CAAAE,UAAA,aAAAN,MAAA,CAAAI,IAAA,CAAAG,aAAA;YACA9D,UAAA,GAAAuC,KAAA,CAAAnB,YAAA,CAAA2C,eAAA,mBAAAJ,IAAA,CAAAK,QAAA,mBAAAL,IAAA,CAAA3D,UAAA,kBAAA2D,IAAA,CAAA1D,gBAAA,qBAAA0D,IAAA,CAAAzD,WAAA;YACAD,gBAAA,EAAA0D,IAAA,CAAA1D,gBAAA;YACAC,WAAA,EAAAyD,IAAA,CAAAzD,WAAA;YACA+D,UAAA,EAAAN,IAAA,CAAAM,UAAA;YACAC,gBAAA,EAAAP,IAAA,CAAA3D,UAAA;UACA;QACA;MACA;;MAEA;MACAjC,eAAA,OAAAmE,SAAA,CAAAiC,aAAA;QAAAC,QAAA,EAAAC;MAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,KAAA,CAAAzG,eAAA,EAAAyE,SAAA;IACA;IACAW,gBAAA,WAAAA,iBAAA;MACA;MACA,UAAA/B,YAAA,CAAA/B,UAAA;QACA,KAAAoF,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAtD,YAAA,CAAA7B,QAAA,QAAAoF,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA5G,IAAA,CAAA6G,KAAA;;MAEA;MACA,KAAA1D,YAAA,CAAA6B,SAAA,OAAA5B,eAAA,IAAAC,MAAA;;MAEA;MACA,KAAAmD,QAAA,CAAAM,OAAA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA,EAAAC,QAAA,EAAAC,OAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAC,IAAA,CAAAJ,IAAA;;MAEA;MACA,IAAAK,wBAAA;QAAAC,SAAA,EAAAN,IAAA,CAAAO;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACA,IAAAC,IAAA,GAAAD,QAAA,CAAAC,IAAA;;QAEA;QACAR,OAAA,CAAAQ,IAAA;QACAV,IAAA,CAAAW,QAAA,GAAAD,IAAA;;QAEA;QACA,IAAAP,MAAA,CAAA3G,GAAA,CAAAoH,QAAA,CAAAZ,IAAA,CAAAO,WAAA;UACAM,UAAA;YACAH,IAAA,CAAAI,OAAA,WAAAC,KAAA;cACA,KAAAZ,MAAA,CAAA3G,GAAA,CAAAoH,QAAA,CAAAG,KAAA,CAAAR,WAAA;gBACAJ,MAAA,CAAA3G,GAAA,CAAAwH,IAAA,CAAAD,KAAA,CAAAR,WAAA;gBACAJ,MAAA,CAAA5G,kBAAA,CAAAyH,IAAA,CAAAD,KAAA;cACA;cACA;cACAZ,MAAA,CAAAd,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAH,KAAA;YACA;UACA;QACA;MACA,GAAAI,OAAA;QACA;QACAhB,MAAA,CAAAC,IAAA,CAAAJ,IAAA;MACA;IACA;IACAoB,uBAAA,WAAAA,wBAAA;MACA,KAAAC,aAAA;MACA,KAAAlF,YAAA,CAAA8B,eAAA,QAAAyB,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA5G,IAAA,CAAA6G,KAAA;MACA,KAAAjE,YAAA;MACA,KAAAW,YAAA;IACA;IACA+E,YAAA,WAAAA,aAAA;MACA,KAAAnF,YAAA,CAAAoF,sBAAA,OAAAC,iBAAA,OAAArF,YAAA,CAAAxB,cAAA,EAAA8G,GAAA,MAAAtF,YAAA,CAAAuF,oBAAA,EAAAD,GAAA,MAAAtF,YAAA,CAAAf,kBAAA,EAAAqG,GAAA,MAAAtF,YAAA,CAAAd,kBAAA,EAAAoG,GAAA,MAAAtF,YAAA,CAAAb,mBAAA,EAAAmG,GAAA,MAAAtF,YAAA,CAAAwF,gBAAA,EAAAF,GAAA,MAAAtF,YAAA,CAAAyF,iBAAA,EAAAC,KAAA;MACA,KAAA1F,YAAA,CAAA2F,oBAAA,OAAAN,iBAAA,OAAArF,YAAA,CAAAtB,mBAAA,EAAAgH,KAAA;MACA,KAAA1F,YAAA,CAAA4F,yBAAA,OAAAP,iBAAA,OAAArF,YAAA,CAAAoF,sBAAA,EAAAS,QAAA,MAAA7F,YAAA,CAAA2F,oBAAA,EAAAD,KAAA;MACA,KAAA1F,YAAA,CAAA8F,eAAA,OAAAT,iBAAA,OAAArF,YAAA,CAAA+F,oBAAA,EAAAT,GAAA,MAAAtF,YAAA,CAAAgG,kBAAA,EAAAV,GAAA,MAAAtF,YAAA,CAAAvB,gBAAA,EAAAiH,KAAA;MACA,KAAA1F,YAAA,CAAAiG,oBAAA,OAAAZ,iBAAA,OAAArF,YAAA,CAAAkG,gBAAA,EAAAZ,GAAA,MAAAtF,YAAA,CAAAhB,kBAAA,EAAA0G,KAAA;MACA,KAAA1F,YAAA,CAAAmG,mBAAA,OAAAd,iBAAA,OAAArF,YAAA,CAAAoF,sBAAA,EAAAE,GAAA,MAAAtF,YAAA,CAAAiG,oBAAA,EAAAP,KAAA;MACA,KAAA1F,YAAA,CAAAoG,kBAAA,OAAAf,iBAAA,OAAArF,YAAA,CAAA8F,eAAA,EAAAR,GAAA,MAAAtF,YAAA,CAAAb,mBAAA,EAAAmG,GAAA,MAAAtF,YAAA,CAAAqG,wBAAA,EAAAX,KAAA;MACA,KAAA1F,YAAA,CAAAsG,yBAAA,OAAAjB,iBAAA,OAAArF,YAAA,CAAAmG,mBAAA,EAAAN,QAAA,MAAA7F,YAAA,CAAAoG,kBAAA,EAAAV,KAAA;IACA;IACAL,QAAA,EAAAA,iBAAA;IACA;AACA;AACA;AACA;IACAkB,eAAA,WAAAA,gBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAC,QAAA,UAAAF,IAAA,iBAAAA,IAAA,gBAAAA,IAAA;QACAG,iBAAA;QACAC,gBAAA;QACAJ,IAAA;MACA,GAAAnC,IAAA;QACA;QACAoC,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;UACA,IAAAA,IAAA,CAAAsE,cAAA;YACAJ,MAAA,CAAApD,QAAA,CAAAyD,KAAA,qBAAAvE,IAAA,CAAAwE,kBAAA;YACA;UACA;QACA;QAEAN,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;UACAA,IAAA,CAAAyE,mBAAA,GAAAC,MAAA,CAAA1E,IAAA,CAAAyE,mBAAA;QACA;;QAEA;QACAP,MAAA,CAAAzG,YAAA,CAAApB,UAAA;QACA6H,MAAA,CAAAzG,YAAA,CAAAnB,gBAAA;QACA4H,MAAA,CAAAzG,YAAA,CAAAlB,WAAA;QACA2H,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;UACAA,IAAA,CAAA2E,kBAAA,GAAA3E,IAAA,CAAA2E,kBAAA,CAAA5E,GAAA,WAAAC,IAAA;YACAkE,MAAA,CAAAzG,YAAA,CAAApB,UAAA,OAAAyG,iBAAA,EAAA9C,IAAA,CAAA4E,QAAA,EAAA7B,GAAA,CAAAmB,MAAA,CAAAzG,YAAA,CAAApB,UAAA,EAAA8G,KAAA;YACAe,MAAA,CAAAzG,YAAA,CAAAnB,gBAAA,OAAAwG,iBAAA,EAAA9C,IAAA,CAAA6E,eAAA,EAAA9B,GAAA,CAAAmB,MAAA,CAAAzG,YAAA,CAAAnB,gBAAA,EAAA6G,KAAA;YACAe,MAAA,CAAAzG,YAAA,CAAAlB,WAAA,OAAAuG,iBAAA,EAAA9C,IAAA,CAAA8E,UAAA,EAAA/B,GAAA,CAAAmB,MAAA,CAAAzG,YAAA,CAAAlB,WAAA,EAAA4G,KAAA;YACA,OAAAnD,IAAA;UACA;UACA,OAAAA,IAAA;QACA;QACA,IAAAiE,IAAA;UACA,IAAAc,iCAAA,EAAAb,MAAA,CAAAzG,YAAA,EAAAqE,IAAA,WAAAC,QAAA;YACA;YACA,IAAAtH,IAAA,GAAAyJ,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;cACAA,IAAA,CAAAgF,eAAA;cACAhF,IAAA,CAAA2E,kBAAA,GAAA3E,IAAA,CAAA2E,kBAAA,CAAA5E,GAAA,WAAAC,IAAA;gBACAA,IAAA,CAAAgF,eAAA;gBACA,OAAAhF,IAAA;cACA;cACA,OAAAA,IAAA;YACA;YAEA,IAAAiF,+BAAA,EAAAxK,IAAA,EAAAqH,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAA/F,OAAA;cACA+F,MAAA,CAAApD,QAAA,CAAAM,OAAA;cACA8C,MAAA,CAAArG,YAAA;YACA;UACA;QACA,WAAAoG,IAAA;UACA;UACAC,MAAA,CAAAzG,YAAA,CAAAuH,eAAA;UACAd,MAAA,CAAAzG,YAAA,CAAAzB,YAAA,OAAA0B,eAAA,IAAAC,MAAA;UAEA,IAAAuH,oCAAA,EAAAhB,MAAA,CAAAzG,YAAA,EAAAqE,IAAA,WAAAC,QAAA;YACA,IAAAoD,gBAAA,GAAApD,QAAA,CAAAtH,IAAA;YACA;YACA,IAAAA,IAAA,GAAAyJ,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;cACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;cACAnF,IAAA,CAAA2E,kBAAA,GAAA3E,IAAA,CAAA2E,kBAAA,CAAA5E,GAAA,WAAAC,IAAA;gBACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;gBACA,OAAAnF,IAAA;cACA;cACA,OAAAA,IAAA;YACA;;YAEA;YACA,IAAAoF,4BAAA,EAAA3K,IAAA,EAAAqH,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAA/F,OAAA;cACA+F,MAAA,CAAApD,QAAA,CAAAM,OAAA;cACA8C,MAAA,CAAArG,YAAA;YACA;UACA;QACA,WAAAoG,IAAA;UACA;UACAC,MAAA,CAAAzG,YAAA,CAAAuH,eAAA;UACAd,MAAA,CAAAzG,YAAA,CAAAzB,YAAA,OAAA0B,eAAA,IAAAC,MAAA;UAEA,IAAAoH,iCAAA,EAAAb,MAAA,CAAAzG,YAAA,EAAAqE,IAAA,WAAAC,QAAA;YACA,IAAAoD,gBAAA,GAAApD,QAAA,CAAAtH,IAAA;YACA;YACA,IAAAA,IAAA,GAAAyJ,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;cACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;cACAnF,IAAA,CAAA2E,kBAAA,GAAA3E,IAAA,CAAA2E,kBAAA,CAAA5E,GAAA,WAAAC,IAAA;gBACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;gBACA,OAAAnF,IAAA;cACA;cACA,OAAAA,IAAA;YACA;;YAEA;YACA,IAAAoF,4BAAA,EAAA3K,IAAA,EAAAqH,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAA/F,OAAA;cACA+F,MAAA,CAAApD,QAAA,CAAAM,OAAA;cACA8C,MAAA,CAAArG,YAAA;YACA;UACA;QACA,WAAAoG,IAAA;UACA;UACAC,MAAA,CAAAzG,YAAA,CAAA4H,gBAAA;UACA,IAAAN,iCAAA,EAAAb,MAAA,CAAAzG,YAAA,EAAAqE,IAAA,WAAAC,QAAA;YACA,IAAAoD,gBAAA,GAAApD,QAAA,CAAAtH,IAAA;YACA;YACA,IAAAA,IAAA,GAAAyJ,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;cACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;cACAnF,IAAA,CAAAsF,oBAAA,GAAApB,MAAA,CAAAzG,YAAA,CAAAzB,YAAA;cACAgE,IAAA,CAAA2E,kBAAA,GAAA3E,IAAA,CAAA2E,kBAAA,CAAA5E,GAAA,WAAAC,IAAA;gBACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;gBACA,OAAAnF,IAAA;cACA;cACA,OAAAA,IAAA;YACA;YAEA,IAAAuF,qBAAA,EAAA9K,IAAA,EAAAqH,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAApD,QAAA,CAAAM,OAAA;cACA8C,MAAA,CAAAsB,4BAAA;YACA;UACA;QACA;UACA,IAAAL,gBAAA,GAAAjB,MAAA,CAAAzG,YAAA,CAAA0H,gBAAA;UACAjB,MAAA,CAAAzG,YAAA,CAAAuH,eAAA;UACAd,MAAA,CAAAzG,YAAA,CAAAzB,YAAA,OAAA0B,eAAA,IAAAC,MAAA;UACA,IAAAuH,oCAAA,EAAAhB,MAAA,CAAAzG,YAAA,EAAAqE,IAAA,WAAAC,QAAA;YACA;YACA,IAAAtH,IAAA,GAAAyJ,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;cACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;cACAnF,IAAA,CAAA2E,kBAAA,GAAA3E,IAAA,CAAA2E,kBAAA,CAAA5E,GAAA,WAAAC,IAAA;gBACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;gBACA,OAAAnF,IAAA;cACA;cACA,OAAAA,IAAA;YACA;;YAEA;YACA,IAAAoF,4BAAA,EAAA3K,IAAA,EAAAqH,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAA/F,OAAA;cACA+F,MAAA,CAAApD,QAAA,CAAAM,OAAA;cACA8C,MAAA,CAAArG,YAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACA2H,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,MAAA;MACA,KAAAtI,+BAAA;MACA,KAAAvC,OAAA;;MAEA;MACA,IAAAU,WAAA;QACAoK,sBAAA,OAAAjI,YAAA,CAAAsB,mBAAA;QACArD,UAAA,OAAA+B,YAAA,CAAA/B,UAAA;QACAiK,eAAA;MACA;;MAEA;MACA,SAAArK,WAAA,CAAA0J,eAAA;QACA1J,WAAA,CAAA0J,eAAA,QAAA1J,WAAA,CAAA0J,eAAA;MACA;MAEA,SAAA1J,WAAA,CAAAsK,mBAAA;QACAtK,WAAA,CAAAsK,mBAAA,QAAAtK,WAAA,CAAAsK,mBAAA;MACA;;MAEA;MACA,IAAAC,yBAAA,EAAAvK,WAAA,EACAwG,IAAA,WAAAC,QAAA;QACA;QACA0D,MAAA,CAAA3H,wBAAA,GAAAiE,QAAA,CAAAC,IAAA,CAAA8D,MAAA,WAAA9F,IAAA;UAAA,QAAAA,IAAA,CAAA4B,SAAA;QAAA;QACA6D,MAAA,CAAA3H,wBAAA,GAAAiE,QAAA,CAAAC,IAAA,CAAAjC,GAAA,WAAAC,IAAA;UACA;UACA,IAAAA,IAAA,CAAA+F,kBAAA;YACA,IAAAC,WAAA,GAAAtB,MAAA,CAAA1E,IAAA,CAAAvD,kBAAA;YACA,IAAAwJ,UAAA,GAAAvB,MAAA,CAAA1E,IAAA,CAAAiG,UAAA;YACA,IAAAC,UAAA,OAAApD,iBAAA,EAAAmD,UAAA,EAAA3C,QAAA,CAAA0C,WAAA,EAAA7C,KAAA;;YAEA;YACAnD,IAAA,CAAAgD,oBAAA,GAAAkD,UAAA,OAAAA,UAAA;UACA;YACAlG,IAAA,CAAAgD,oBAAA;UACA;;UAEA;UACA,IAAAhD,IAAA,CAAAmG,aAAA;YACAnG,IAAA,CAAAoG,WAAA;UACA;UAEA,IAAAX,MAAA,CAAAhI,YAAA,CAAA0H,gBAAA,KAAAnF,IAAA,CAAA4F,mBAAA;YACAH,MAAA,CAAA5K,kBAAA,CAAAyH,IAAA,CAAAtC,IAAA;YACAyF,MAAA,CAAAY,SAAA;cACAZ,MAAA,CAAA9E,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAxC,IAAA;YACA;UACA;UAEA,OAAAA,IAAA;QACA;;QAEA;QACAyF,MAAA,CAAAvK,KAAA,GAAA6G,QAAA,CAAA7G,KAAA;;QAEA;QACA,IAAAuK,MAAA,CAAAvI,YAAA,UAAAuI,MAAA,CAAA9E,KAAA,CAAA4B,KAAA;UACAkD,MAAA,CAAAY,SAAA;YACAZ,MAAA,CAAA3H,wBAAA,CAAAsE,OAAA,WAAApC,IAAA;cACA,IAAAA,IAAA,CAAAgF,eAAA;gBACAS,MAAA,CAAA9E,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAxC,IAAA;cACA;YACA;UACA;QACA;MACA,GACAsG,KAAA,WAAA/B,KAAA;QACAgC,OAAA,CAAAhC,KAAA,iBAAAA,KAAA;QACAkB,MAAA,CAAA3E,QAAA,CAAAyD,KAAA;MACA,GACA9B,OAAA;QACAgD,MAAA,CAAAnK,WAAA,CAAAsK,mBAAA;QACAH,MAAA,CAAAnK,WAAA,CAAA0J,eAAA;QACAS,MAAA,CAAA7K,OAAA;QACA6K,MAAA,CAAAtI,+BAAA;MACA;IACA;IACA;IACAqJ,cAAA,WAAAA,eAAAC,YAAA;MACA,KAAA9D,aAAA;MACA,KAAAlF,YAAA,GAAAgJ,YAAA;MACA,KAAAvJ,YAAA;MACA,KAAA5B,WAAA,CAAAsK,mBAAA,QAAAnI,YAAA,CAAA0H,gBAAA;MACA,KAAA7J,WAAA,CAAA0J,eAAA;MACA,KAAAQ,4BAAA;MACA,KAAA3H,YAAA;IACA;IACA;IACA6I,iBAAA,WAAAA,kBAAA;MACA,KAAA/D,aAAA;MACA,KAAAlF,YAAA,CAAA8B,eAAA,QAAAyB,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA5G,IAAA,CAAA6G,KAAA;MACA,KAAAjE,YAAA;MACA,KAAAW,YAAA;IACA;IACA;IACA8I,oBAAA,WAAAA,qBAAA;MACA,KAAAhE,aAAA;MACA,KAAAlF,YAAA,CAAA8B,eAAA,QAAAyB,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA5G,IAAA,CAAA6G,KAAA;MACA,KAAAjE,YAAA;MACA,KAAAW,YAAA;IACA;IACA;IACA+I,oBAAA,WAAAA,qBAAA;MACA,KAAAjE,aAAA;MACA,KAAAlF,YAAA,CAAA8B,eAAA,QAAAyB,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA5G,IAAA,CAAA6G,KAAA;MACA,KAAAjE,YAAA;MACA,KAAAW,YAAA;IACA;IACA4B,SAAA,EAAAA,eAAA;IACAoH,wCAAA,WAAAA,yCAAAC,SAAA,EAAAC,GAAA;MACAA,GAAA,CAAAC,wBAAA,GAAAF,SAAA;MACA,KAAA/I,mBAAA,GAAA+I,SAAA;IACA;IACA;IACAG,aAAA,WAAAA,cAAAF,GAAA;MACA,YAAAhJ,mBAAA,CAAAmE,QAAA,CAAA6E,GAAA;IACA;IACAG,YAAA,WAAAA,aAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,OAAA,GAAAF,KAAA,CAAAE,OAAA;QAAA5M,IAAA,GAAA0M,KAAA,CAAA1M,IAAA;MACA,IAAA6M,IAAA;MACA,IAAAC,gBAAA,IACA,0EACA,kFACA,sFACA,iEACA;MACA;MACA,IAAAC,cAAA;MACAH,OAAA,CAAAjF,OAAA,WAAAqF,MAAA,EAAAC,KAAA;QACA,IAAAA,KAAA;UACAJ,IAAA,CAAAI,KAAA;QACA;UACA,IAAAC,IAAA,GAAAF,MAAA,CAAAG,QAAA;UACA,IAAA1M,KAAA;;UAEA,IAAAyM,IAAA,qBAAAA,IAAA,sBAAAA,IAAA;YACAzM,KAAA,GAAAkM,MAAA,CAAAvM,kBAAA,CAAAgN,MAAA,WAAAC,GAAA,EAAAf,GAAA;cACA,IAAAA,GAAA,CAAAnF,SAAA;gBACA,WAAAkB,iBAAA,EAAAgF,GAAA,EAAA/E,GAAA,CAAA2B,MAAA,CAAAqC,GAAA,CAAAY,IAAA,SAAAxE,KAAA;cACA;cACA,OAAA2E,GAAA;YACA;UACA;YACA5M,KAAA,GAAAkM,MAAA,CAAAvM,kBAAA,CAAAgN,MAAA,WAAAC,GAAA,EAAAf,GAAA;cAAA,OACA,IAAAjE,iBAAA,EAAAgF,GAAA,EAAA/E,GAAA,CAAA2B,MAAA,CAAAqC,GAAA,CAAAY,IAAA,SAAAxE,KAAA;YAAA;UACA;UAEAmE,IAAA,CAAAI,KAAA,IAAAxM,KAAA;UACA;UACAsM,cAAA,CAAAC,MAAA,CAAAG,QAAA,IAAA1M,KAAA;QACA;MACA;;MAEA;MACA,IAAA6M,cAAA,IACA,iDACA,gEACA,+DACA,sEACA,mBACA;;MAEA;MACAA,cAAA,CAAA3F,OAAA,WAAA4F,KAAA;QACA,IAAAZ,MAAA,CAAA3J,YAAA,IAAA+J,cAAA,CAAAQ,KAAA,MAAAC,SAAA;UACAb,MAAA,CAAA3J,YAAA,CAAAuK,KAAA,IAAAR,cAAA,CAAAQ,KAAA;QACA;MACA;MAEA,KAAApF,YAAA;MAEA,OAAA0E,IAAA;IACA;IACAY,6BAAA,WAAAA,8BAAApB,SAAA;MAAA,IAAAqB,MAAA;MACA;MACA,IAAAC,QAAA,QAAAzH,KAAA,CAAA4B,KAAA,CAAA8F,KAAA,CAAAC,MAAA,CAAA7N,IAAA;MACA;MACA,IAAA8N,WAAA,OAAAC,mBAAA,CAAAC,OAAA,OAAA3N,GAAA;;MAEA;MACA,KAAAA,GAAA;MACA,KAAAA,GAAA,GAAAgM,SAAA,CAAA/G,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA6B,WAAA;MAAA;MACA;MACA,IAAA6G,aAAA,QAAA5N,GAAA,CAAAgL,MAAA,WAAA6C,EAAA;QAAA,QAAAJ,WAAA,CAAArG,QAAA,CAAAyG,EAAA;MAAA;MACA,IAAAC,eAAA,GAAAL,WAAA,CAAAzC,MAAA,WAAA6C,EAAA;QAAA,QAAAR,MAAA,CAAArN,GAAA,CAAAoH,QAAA,CAAAyG,EAAA;MAAA;MAEA,KAAA9N,kBAAA,GAAAiM,SAAA;MACA,KAAAnG,KAAA,CAAA4B,KAAA,CAAAsG,QAAA;;MAEA;MACA;MACA/B,SAAA,CAAA/G,GAAA,WAAAC,IAAA;QACA,IAAA8I,KAAA,OAAApL,eAAA,EAAAyK,MAAA,CAAA1K,YAAA,CAAAzB,YAAA;QACA,IAAA+M,KAAA,OAAArL,eAAA,EAAAsC,IAAA,CAAAsF,oBAAA;QACAtF,IAAA,CAAAgJ,UAAA,GAAAF,KAAA,CAAAG,IAAA,CAAAF,KAAA;QACA,IAAAG,MAAA,GAAAlJ,IAAA,CAAAzD,WAAA;QAEA,KAAAmI,MAAA,CAAAyE,KAAA,CAAAnJ,IAAA,CAAAgJ,UAAA,KAAAhJ,IAAA,CAAAgJ,UAAA;UACA;UACA,IAAAb,MAAA,CAAA1K,YAAA,CAAAP,YAAA;YACA8C,IAAA,CAAAiD,gBAAA,OAAAH,iBAAA,EAAA9C,IAAA,CAAAgJ,UAAA,EAAAI,QAAA,CAAApJ,IAAA,CAAAqJ,sBAAA,EAAAD,QAAA,CAAAF,MAAA,EAAA/F,KAAA;UACA;YACA,IAAAmG,IAAA,OAAAxG,iBAAA,EAAA9C,IAAA,CAAAgJ,UAAA,EAAA1F,QAAA,CAAAtD,IAAA,CAAAuJ,eAAA,EAAApG,KAAA;YACAmG,IAAA,GAAAA,IAAA,OAAAA,IAAA;YACAtJ,IAAA,CAAAgJ,UAAA,GAAAM,IAAA;YACAtJ,IAAA,CAAAiD,gBAAA,OAAAH,iBAAA,EAAAwG,IAAA,EAAAF,QAAA,CAAApJ,IAAA,CAAAqJ,sBAAA,EAAAD,QAAA,CAAAF,MAAA,EAAA/F,KAAA;UACA;QACA;;QAEA;QACA,IAAAnD,IAAA,CAAAmG,aAAA,YAAAuC,aAAA,CAAAxG,QAAA,CAAAlC,IAAA,CAAA6B,WAAA;UACA;;UAEA;UACA,IAAA2H,UAAA,GAAApB,QAAA,CAAAqB,IAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAA7H,WAAA,KAAA7B,IAAA,CAAA6B,WAAA;UAAA;;UAEA;UACA,IAAA2H,UAAA,IAAAA,UAAA,CAAAvH,QAAA,IAAAuH,UAAA,CAAAvH,QAAA,CAAA0H,MAAA;YACA;YACAxH,UAAA;cACAqH,UAAA,CAAAvH,QAAA,CAAAG,OAAA,WAAAC,KAAA;gBACA,KAAA8F,MAAA,CAAArN,GAAA,CAAAoH,QAAA,CAAAG,KAAA,CAAAR,WAAA;kBACAsG,MAAA,CAAArN,GAAA,CAAAwH,IAAA,CAAAD,KAAA,CAAAR,WAAA;kBACAsG,MAAA,CAAAtN,kBAAA,CAAAyH,IAAA,CAAAD,KAAA;kBACA8F,MAAA,CAAAxH,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAH,KAAA;gBACA;cACA;YACA;UACA,WAAAmH,UAAA,KAAAA,UAAA,CAAAI,cAAA,IAAAJ,UAAA,CAAApD,WAAA;YACA;YACAoD,UAAA,CAAAI,cAAA;;YAEA;YACAzB,MAAA,CAAAxH,KAAA,CAAA4B,KAAA,CAAAsH,kBAAA,CAAAL,UAAA;;YAEA;YACA;UACA;QACA;MACA;;MAEA;MACAZ,eAAA,CAAAxG,OAAA,WAAA0H,QAAA;QACA;QACA,IAAAN,UAAA,GAAApB,QAAA,CAAAqB,IAAA,WAAAC,IAAA;UAAA,OACAA,IAAA,CAAA7H,WAAA,KAAAiI,QAAA,IAAAJ,IAAA,CAAAvD,aAAA;QAAA,CACA;QAEA,IAAAqD,UAAA,IAAAA,UAAA,CAAAvH,QAAA,IAAAuH,UAAA,CAAAvH,QAAA,CAAA0H,MAAA;UACA;UACAH,UAAA,CAAAvH,QAAA,CAAAG,OAAA,WAAAC,KAAA;YACA,IAAA0H,UAAA,GAAA5B,MAAA,CAAArN,GAAA,CAAAkP,OAAA,CAAA3H,KAAA,CAAAR,WAAA;YACA,IAAAkI,UAAA;cACA;cACA5B,MAAA,CAAArN,GAAA,CAAAmP,MAAA,CAAAF,UAAA;cACA,IAAAG,SAAA,GAAA/B,MAAA,CAAAtN,kBAAA,CAAAsP,SAAA,CACA,UAAAnK,IAAA;gBAAA,OAAAA,IAAA,CAAA6B,WAAA,KAAAQ,KAAA,CAAAR,WAAA;cAAA,CACA;cACA,IAAAqI,SAAA;gBACA/B,MAAA,CAAAtN,kBAAA,CAAAoP,MAAA,CAAAC,SAAA;cACA;cACA;cACA/B,MAAA,CAAAxH,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAH,KAAA;YACA;UACA;QACA;MACA;MAEA,KAAAO,YAAA;IACA;IACAwH,mBAAA,WAAAA,oBAAAnG,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAAxG,YAAA,CAAAxB,cAAA,QAAA2B,SAAA,CAAAyM,QAAA;UACA;QACA;UACA,KAAA5M,YAAA,CAAAxB,cAAA,QAAA2B,SAAA,CAAA0M,QAAA;UACA;MAEA;IACA;IACAC,cAAA,WAAAA,eAAAxD,GAAA;MACA,KAAAtJ,YAAA,CAAAxB,cAAA,GAAA8K,GAAA,CAAAyD,OAAA;MACA,KAAA/M,YAAA,CAAAV,aAAA,GAAAgK,GAAA,CAAAwC,eAAA;MACA,KAAA3L,SAAA,GAAAmJ,GAAA;MACA,KAAAtJ,YAAA,CAAA4L,sBAAA,GAAAtC,GAAA,CAAAjK,WAAA;MACA,KAAAW,YAAA,CAAA9B,UAAA,GAAAoL,GAAA,CAAApL,UAAA;MACA;MACA,KAAA8O,YAAA;IACA;IACA,eACAtM,OAAA,WAAAA,QAAA;MAAA,IAAAuM,MAAA;MACA,KAAA9P,OAAA;MACA,IAAA+P,kCAAA,OAAArP,WAAA,EAAAwG,IAAA,WAAAC,QAAA;QACA2I,MAAA,CAAAvP,kBAAA,GAAA4G,QAAA,CAAAC,IAAA;QACA0I,MAAA,CAAAxP,KAAA,GAAA6G,QAAA,CAAA7G,KAAA;MACA,GAAAuH,OAAA;QACAiI,MAAA,CAAA9P,OAAA;MACA;IACA;IACA;IACAgQ,MAAA,WAAAA,OAAA;MACA,KAAAvP,IAAA;MACA,KAAAwP,KAAA;IACA;IACAlI,aAAA,WAAAA,cAAA;MACA;MACA,IAAAmI,aAAA;QACA;QACA3F,gBAAA;QACA1J,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAI,YAAA,MAAA0B,eAAA,IAAAC,MAAA;QAEA;QACA9B,aAAA;QACAC,WAAA;QACAC,MAAA;QAEA;QACAE,cAAA;QACAC,gBAAA;QACAC,mBAAA;QACAgD,YAAA;QAEA;QACA/C,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,WAAA;QACAsC,oBAAA;QACArC,aAAA;QACAC,gBAAA;QAEA;QACA2G,gBAAA;QACAD,oBAAA;QACAb,sBAAA;QACAO,oBAAA;QACAC,yBAAA;QACAE,eAAA;QACAK,mBAAA;QACAC,kBAAA;QACAE,yBAAA;MACA;;MAEA;MACA,KAAAtG,YAAA,OAAAsN,cAAA,CAAAtC,OAAA,MAAAqC,aAAA;;MAEA;MACA,KAAAhN,wBAAA;;MAEA;MACA,KAAAkN,SAAA;IACA;IACA;IACAH,KAAA,WAAAA,MAAA;MACA,KAAA5N,IAAA;QACAjB,YAAA,MAAA0B,eAAA,IAAAC,MAAA;QACAwH,gBAAA;QACA1J,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;QACAE,cAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,gBAAA;MACA;MACA,KAAAgO,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA3P,WAAA,CAAAC,OAAA;MACA,KAAA4C,OAAA;IACA;IACA,aACA+M,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACAE,kBAAA,WAAAA,mBAAApE,GAAA;MAAA,IAAAqE,MAAA;MACA,IAAAC,IAAA,GAAAtE,GAAA,CAAAuE,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,WAAAH,IAAA,SAAAvJ,IAAA;QACA,WAAA2J,4BAAA,EAAA1E,GAAA,CAAA5B,gBAAA,EAAA4B,GAAA,CAAAuE,MAAA;MACA,GAAAxJ,IAAA;QACAsJ,MAAA,CAAAG,MAAA,CAAAG,UAAA,CAAAL,IAAA;MACA,GAAA/E,KAAA;QACAS,GAAA,CAAAuE,MAAA,GAAAvE,GAAA,CAAAuE,MAAA;MACA;IACA;IACA;IACAK,qBAAA,WAAAA,sBAAA7E,SAAA;MACA,KAAAhM,GAAA,GAAAgM,SAAA,CAAA/G,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAmF,gBAAA;MAAA;MACA,KAAApK,MAAA,GAAA+L,SAAA,CAAA6C,MAAA;MACA,KAAA3O,QAAA,IAAA8L,SAAA,CAAA6C,MAAA;IACA;IACA,aACAiC,SAAA,WAAAA,UAAA;MACA,KAAAf,KAAA;MACA,KAAAxP,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAyQ,YAAA,WAAAA,aAAA9E,GAAA;MAAA,IAAA+E,MAAA;MACA,KAAAjB,KAAA;MACA,IAAA1F,gBAAA,GAAA4B,GAAA,CAAA5B,gBAAA,SAAArK,GAAA;MACA,IAAAiR,iCAAA,EAAA5G,gBAAA,EAAArD,IAAA,WAAAC,QAAA;QACA+J,MAAA,CAAA7O,IAAA,GAAA8E,QAAA,CAAAtH,IAAA;QACAqR,MAAA,CAAAzQ,IAAA;QACAyQ,MAAA,CAAA1Q,KAAA;MACA;IACA;IACA,WACA4Q,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAAtL,KAAA,iBAAAuL,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,OAAA,CAAAxO,YAAA,CAAA0H,gBAAA;YACA,IAAAD,oCAAA,EAAA+G,OAAA,CAAAxO,YAAA,EAAAqE,IAAA,WAAAC,QAAA;cACAkK,OAAA,CAAAV,MAAA,CAAAG,UAAA;cACAO,OAAA,CAAA5Q,IAAA;cACA4Q,OAAA,CAAA9N,OAAA;YACA;UACA;YACA,IAAA4G,iCAAA,EAAAkH,OAAA,CAAAxO,YAAA,EAAAqE,IAAA,WAAAC,QAAA;cACAkK,OAAA,CAAAV,MAAA,CAAAG,UAAA;cACAO,OAAA,CAAA5Q,IAAA;cACA4Q,OAAA,CAAA9N,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAiO,YAAA,WAAAA,aAAArF,GAAA;MAAA,IAAAsF,OAAA;MACA,IAAAC,iBAAA,GAAAvF,GAAA,CAAA5B,gBAAA,SAAArK,GAAA;MACA,KAAAyQ,MAAA,CAAAC,OAAA,qBAAAc,iBAAA,cAAAxK,IAAA;QACA,WAAAyK,iCAAA,EAAAD,iBAAA;MACA,GAAAxK,IAAA;QACAuK,OAAA,CAAAlO,OAAA;QACAkO,OAAA,CAAAd,MAAA,CAAAG,UAAA;MACA,GAAApF,KAAA,cACA;IACA;IACA,aACAkG,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,qCAAA1B,cAAA,CAAAtC,OAAA,MACA,KAAAnN,WAAA,qBAAAsE,MAAA,CACA,IAAAF,IAAA,GAAAgN,OAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACA,UAAAxP,MAAA;;MAEA;MACA,IAAAsK,KAAA,QAAA5J,wBAAA,CAAAqM,SAAA,CACA,UAAAnK,IAAA;QACA;QACA,IAAA6M,QAAA,GAAAC,MAAA,CAAA9M,IAAA,CAAAC,eAAA;QACA,IAAA8M,WAAA,GAAAD,MAAA,CAAAF,OAAA,CAAAxP,MAAA;QACA;QACA,OAAAyP,QAAA,CAAA3K,QAAA,CAAA6K,WAAA;MACA,CACA;MAEA,IAAArF,KAAA;QACA;QACA,IAAAnF,KAAA,QAAA5B,KAAA,CAAA4B,KAAA;QAEA,KAAA8D,SAAA;UACA;UACA,IAAA2G,aAAA,GAAAzK,KAAA,CAAA0K,GAAA,CAAAC,aAAA;UACA;UACA,IAAAlL,IAAA,GAAAgL,aAAA,CAAAG,gBAAA;;UAEA;UACA,IAAAC,WAAA;UACApL,IAAA,CAAAI,OAAA,WAAA2E,GAAA,EAAAsG,GAAA;YACA,IAAAC,OAAA,GAAAvG,GAAA,CAAAwG,WAAA;YACA,IAAAD,OAAA,CAAApL,QAAA,CAAA0K,OAAA,CAAAxP,MAAA;cACAgQ,WAAA,GAAAC,GAAA;YACA;UACA;UAEA,IAAAD,WAAA;YACA,IAAAI,SAAA,GAAAxL,IAAA,CAAAoL,WAAA;YACA;YACA,IAAAK,MAAA,GAAAD,SAAA,CAAAE,SAAA;;YAEA;YACAV,aAAA,CAAAW,QAAA;cACAC,GAAA,EAAAH,MAAA,GAAAT,aAAA,CAAAa,YAAA;cACAC,QAAA;YACA;;YAEA;YACAN,SAAA,CAAAO,SAAA,CAAAhL,GAAA;YACA;YACAZ,UAAA;cACAqL,SAAA,CAAAO,SAAA,CAAAC,MAAA;YACA;UACA;QACA;MACA;QACA,KAAAlN,QAAA,CAAAC,OAAA;MACA;IACA;EACA;AACA;AAAAkN,OAAA,CAAAxF,OAAA,GAAApO,QAAA"}]}