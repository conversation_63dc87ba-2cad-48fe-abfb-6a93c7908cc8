package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.RsCargoDetails;
import com.rich.common.core.domain.entity.RsInventory;
import com.rich.common.core.domain.entity.RsOutboundRecord;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.RedisIdGeneratorService;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.RsInventoryMapper;
import com.rich.system.mapper.RsOutboundRecordMapper;
import com.rich.system.service.RsOutboundRecordService;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 出仓记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@Service
public class RsOutboundRecordServiceImpl implements RsOutboundRecordService {
    @Autowired
    private RsOutboundRecordMapper rsOutboundRecordMapper;

    @Autowired
    private RsInventoryMapper rsInventoryMapper;

    @Resource
    private RedisIdGeneratorService redisIdGeneratorService;

    /**
     * 查询出仓记录
     *
     * @param outboundRecordId 出仓记录主键
     * @return 出仓记录
     */
    @Override
    public RsOutboundRecord selectRsOutboundRecordByOutboundRecordId(Long outboundRecordId) {
        return rsOutboundRecordMapper.selectRsOutboundRecordByOutboundRecordId(outboundRecordId);
    }

    /**
     * 查询出仓记录列表
     *
     * @param rsOutboundRecord 出仓记录
     * @return 出仓记录
     */
    @Override
    public List<RsOutboundRecord> selectRsOutboundRecordList(RsOutboundRecord rsOutboundRecord) {
        return rsOutboundRecordMapper.selectRsOutboundRecordList(rsOutboundRecord);
    }

    /**
     * 新增出仓记录
     *
     * @param rsOutboundRecord 出仓记录
     * @return 结果
     */
    @Override
    public Long insertRsOutboundRecord(RsOutboundRecord rsOutboundRecord) {
        String outboundId = redisIdGeneratorService.generateUniqueId("outbound");
        String date = DateUtils.dateTime();
        rsOutboundRecord.setOutboundNo("OB" + date.substring(2) + outboundId);
        rsOutboundRecord.setOperatorId(SecurityUtils.getUserId());
        int i = rsOutboundRecordMapper.insertRsOutboundRecord(rsOutboundRecord);
        return rsOutboundRecord.getOutboundRecordId();
    }

    /**
     * 修改出仓记录
     *
     * @param rsOutboundRecord 出仓记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRsOutboundRecord(RsOutboundRecord rsOutboundRecord) {
        int i = rsOutboundRecordMapper.updateRsOutboundRecord(rsOutboundRecord);
        List<RsInventory> rsInventoryList = rsOutboundRecord.getRsInventoryList();
        if (rsInventoryList != null && !rsInventoryList.isEmpty()) {
            for (RsInventory rsInventory : rsInventoryList) {
                rsInventoryMapper.updateRsInventory(rsInventory);
            }
        }
        return i;
    }

    /**
     * 修改出仓记录状态
     *
     * @param rsOutboundRecord 出仓记录
     * @return 出仓记录
     */
    @Override
    public int changeStatus(RsOutboundRecord rsOutboundRecord) {
        return rsOutboundRecordMapper.updateRsOutboundRecord(rsOutboundRecord);
    }

    @Override
    public RsOutboundRecord selectRsOutboundRecordsByOutboundRecordId(Long outboundRecordId) {
        return rsOutboundRecordMapper.selectRsOutboundRecordsByOutboundRecordId(outboundRecordId);
    }

    @Override
    public void writeData(RsOutboundRecord rsOutboundRecord, Workbook workbook) {
        // 获取第一个工作表
        Sheet sheet = workbook.getSheetAt(0);

        // 填充基本信息
        writeBasicInfo(rsOutboundRecord, sheet);

        // 创建黄色背景样式（用于打包箱）
        CellStyle yellowStyle = createYellowCellStyle(workbook);

        // 填充库存和货物明细列表
        writeInventoryAndCargoDetails(rsOutboundRecord, sheet, yellowStyle);

        // 填充费用汇总信息
        writeFeesSummary(rsOutboundRecord, sheet);
    }

    /**
     * 填充出仓单基本信息
     *
     * @param rsOutboundRecord 出仓记录对象
     * @param sheet            工作表对象
     */
    private void writeBasicInfo(RsOutboundRecord rsOutboundRecord, Sheet sheet) {
        // TO
        sheet.getRow(3).getCell(16).setCellValue(rsOutboundRecord.getClientName() + "-" + rsOutboundRecord.getClientCode());
        // LoadingNo
        sheet.getRow(4).getCell(2).setCellValue(rsOutboundRecord.getOutboundNo());
        // LoadingDate
        sheet.getRow(4).getCell(12).setCellValue(rsOutboundRecord.getOutboundDate() == null ? null : DateUtils.dateTime(rsOutboundRecord.getOutboundDate()));
        // TotalCTNS
        sheet.getRow(6).getCell(2).setCellValue(Optional.ofNullable(rsOutboundRecord.getTotalBoxes()).orElse(0L) + " CTNS");
        // Truck No.:
        sheet.getRow(6).getCell(12).setCellValue(rsOutboundRecord.getPlateNumber());
        // ShippingMark
        sheet.getRow(5).getCell(2).setCellValue("");
        // Customer Ref：
        sheet.getRow(5).getCell(12).setCellValue(rsOutboundRecord.getCustomerOrderNo());
        // GrossWeight
        sheet.getRow(7).getCell(2).setCellValue(Optional.ofNullable(rsOutboundRecord.getTotalGrossWeight()).orElse(new BigDecimal(0)).doubleValue() + " KGS");
        // containerType
        sheet.getRow(7).getCell(12).setCellValue(rsOutboundRecord.getContainerType());
        // NetWeight
        sheet.getRow(8).getCell(2).setCellValue("");
        // ContainerNo
        sheet.getRow(8).getCell(12).setCellValue(rsOutboundRecord.getContainerNo());
        // Volume
        sheet.getRow(9).getCell(2).setCellValue(Optional.ofNullable(rsOutboundRecord.getTotalVolume()).orElse(new BigDecimal(0)).doubleValue() + " CBM");
        // SealNo
        sheet.getRow(9).getCell(12).setCellValue(rsOutboundRecord.getSealNo());
    }

    /**
     * 创建黄色背景单元格样式
     *
     * @param workbook 工作簿对象
     * @return 单元格样式
     */
    private CellStyle createYellowCellStyle(Workbook workbook) {
        CellStyle yellowStyle = workbook.createCellStyle();
        yellowStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        yellowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        yellowStyle.setBorderRight(BorderStyle.THIN);
        yellowStyle.setBorderLeft(BorderStyle.THIN);
        yellowStyle.setBorderTop(BorderStyle.THIN);
        yellowStyle.setBorderBottom(BorderStyle.THIN);
        return yellowStyle;
    }

    /**
     * 填充库存和货物明细列表
     *
     * @param rsOutboundRecord 出仓记录对象
     * @param sheet            工作表对象
     * @param yellowStyle      黄色背景样式
     */
    private void writeInventoryAndCargoDetails(RsOutboundRecord rsOutboundRecord, Sheet sheet, CellStyle yellowStyle) {
        int inventoryIndex = 13;
        int cargoDetailIndex = 13;
        int noIndex = 1;
        BigDecimal totalUnpaidUnloadingFee = new BigDecimal(0);
        BigDecimal totalUnpaidPackingFee = new BigDecimal(0);
        BigDecimal totalLogisticsAdvanceFee = new BigDecimal(0);
        BigDecimal totalOverdueRentalFee = new BigDecimal(0);
        BigDecimal totalUnpaidInboundFee = new BigDecimal(0);

        // 先过滤掉打包箱中的快递
        List<RsInventory> rsInventoryList = rsOutboundRecord.getRsInventoryList();
        // 打包箱排在前面

        List<RsInventory> filteredInventories = rsInventoryList.stream()
                .filter(rsInventory -> rsInventory.getPackageTo() == null)
                .sorted(Comparator.comparing(RsInventory::getPackageTo, Comparator.nullsFirst(Comparator.naturalOrder())))
                .collect(Collectors.toList());

        for (RsInventory rsInventory : filteredInventories) {
            Row inventoryRow = getOrCreateRow(sheet, inventoryIndex);

            // 判断是否为打包箱
            boolean isPackageBox = "1".equals(rsInventory.getPackageRecord());

            // 如果是打包箱，设置背景颜色为黄色
            if (isPackageBox) {
                applyYellowStyleToRow(inventoryRow, yellowStyle);
            }

            // 填充库存基本信息
            writeInventoryBasicInfo(inventoryRow, rsInventory, isPackageBox, yellowStyle);

            // 累计费用
            totalUnpaidUnloadingFee = totalUnpaidUnloadingFee.add(Optional.ofNullable(rsInventory.getUnpaidUnloadingFee()).orElse(new BigDecimal(0)));
            totalUnpaidPackingFee = totalUnpaidPackingFee.add(Optional.ofNullable(rsInventory.getUnpaidPackingFee()).orElse(new BigDecimal(0)));
            totalLogisticsAdvanceFee = totalLogisticsAdvanceFee.add(Optional.ofNullable(rsInventory.getLogisticsAdvanceFee()).orElse(new BigDecimal(0)));
            totalOverdueRentalFee = totalOverdueRentalFee.add(Optional.ofNullable(rsInventory.getOverdueRentalFee()).orElse(new BigDecimal(0)));
            totalUnpaidInboundFee = totalUnpaidInboundFee.add(Optional.ofNullable(rsOutboundRecord.getUnpaidInboundFee()).orElse(new BigDecimal(0)));

            // 获取当前主记录的子记录数量
            cargoDetailIndex = inventoryIndex;

            // 处理货物明细
            for (RsCargoDetails rsCargoDetails : rsInventory.getRsCargoDetailsList()) {
                Row cargoDetailRow = getOrCreateRow(sheet, cargoDetailIndex);

                // 编号
                Cell noCell = cargoDetailRow.createCell(0);
                noCell.setCellValue(noIndex);
                if (isPackageBox) noCell.setCellStyle(yellowStyle);

                // 根据是否为打包箱，填充不同的货物明细信息
                if (rsInventory.getPackageRecord() != null && rsInventory.getPackageRecord().equals("1")) {
                    writePackageBoxCargoDetails(cargoDetailRow, rsInventory, yellowStyle, isPackageBox);
                } else {
                    writeNormalCargoDetails(cargoDetailRow, rsInventory, rsCargoDetails, yellowStyle, isPackageBox);
                }

                noIndex++;
                cargoDetailIndex++;
            }

            // 如果是打包箱，处理被打包的物品
            if (isPackageBox) {
                cargoDetailIndex = processPackagedItems(sheet, rsInventory, cargoDetailIndex, noIndex, yellowStyle);
                noIndex = updateNoIndexAfterPackagedItems(rsInventory);
            }

            // 更新inventoryIndex，为下一个主记录预留足够的行
            inventoryIndex = cargoDetailIndex;
        }
    }

    /**
     * 获取或创建工作表行
     *
     * @param sheet    工作表
     * @param rowIndex 行索引
     * @return 行对象
     */
    private Row getOrCreateRow(Sheet sheet, int rowIndex) {
        Row row = sheet.getRow(rowIndex);
        if (row == null) {
            row = sheet.createRow(rowIndex);
        }
        return row;
    }

    /**
     * 为行应用黄色背景样式
     *
     * @param row         行对象
     * @param yellowStyle 黄色背景样式
     */
    private void applyYellowStyleToRow(Row row, CellStyle yellowStyle) {
        // 为当前行的所有单元格设置黄色背景
        for (int i = 0; i < 26; i++) {
            Cell cell = row.createCell(i);
            cell.setCellStyle(yellowStyle);
        }
    }

    /**
     * 填充库存基本信息
     *
     * @param row          行对象
     * @param rsInventory  库存对象
     * @param isPackageBox 是否为打包箱
     * @param yellowStyle  黄色背景样式
     */
    private void writeInventoryBasicInfo(Row row, RsInventory rsInventory, boolean isPackageBox, CellStyle yellowStyle) {
        // 打包标记
        createCellWithStyle(row, 15, rsInventory.getRepackingStatus(), isPackageBox, yellowStyle);

        // 打包至
        createCellWithStyle(row, 16, rsInventory.getPackageToNo(), isPackageBox, yellowStyle);

        // 原始分单号
        createCellWithStyle(row, 17, rsInventory.getSubOrderNo(), isPackageBox, yellowStyle);

        // 客户名字
        createCellWithStyle(row, 18, rsInventory.getConsigneeName(), isPackageBox, yellowStyle);

        // 客户电话
        createCellWithStyle(row, 19, rsInventory.getConsigneeTel(), isPackageBox, yellowStyle);

        //原始件数
        createCellWithStyle(row, 21, rsInventory.getTotalBoxes(), isPackageBox, yellowStyle);

        // 总毛重
        createCellWithStyle(row, 23, rsInventory.getTotalGrossWeight().doubleValue(), isPackageBox, yellowStyle);

        // 总体积
        createCellWithStyle(row, 28, rsInventory.getTotalVolume() == null ? 0 : rsInventory.getTotalVolume().doubleValue(), isPackageBox, yellowStyle);

        // 供货商
        createCellWithStyle(row, 31, rsInventory.getSupplier(), isPackageBox, yellowStyle);

        // 联系方式
        createCellWithStyle(row, 32, rsInventory.getContractType(), isPackageBox, yellowStyle);

        // 入仓时间
        String inboundTime = rsInventory.getActualInboundTime() == null ? null : DateUtils.dateTime(rsInventory.getActualInboundTime());
        createCellWithStyle(row, 29, inboundTime, isPackageBox, yellowStyle);

        // 物流单号
        createCellWithStyle(row, 30, rsInventory.getDriverInfo(), isPackageBox, yellowStyle);

        // 未收入仓费(补收入仓费)
        BigDecimal unpaidInboundFee = rsInventory.getUnpaidInboundFee().subtract(rsInventory.getReceivedStorageFee());
        if (unpaidInboundFee.compareTo(new BigDecimal(0)) < 0) {
            unpaidInboundFee = new BigDecimal(0);
        }
        createCellWithStyle(row, 34, unpaidInboundFee.doubleValue(), isPackageBox, yellowStyle);

        // 未收卸货费
        createCellWithStyle(row, 35, Optional.ofNullable(rsInventory.getUnpaidUnloadingFee()).orElse(new BigDecimal(0)).doubleValue(), isPackageBox, yellowStyle);

        // 未收打包费
        createCellWithStyle(row, 36, Optional.ofNullable(rsInventory.getUnpaidPackingFee()).orElse(new BigDecimal(0)).doubleValue(), isPackageBox, yellowStyle);

        // 物流代垫费
        createCellWithStyle(row, 37, Optional.ofNullable(rsInventory.getLogisticsAdvanceFee()).orElse(new BigDecimal(0)).doubleValue(), isPackageBox, yellowStyle);

        // 计租天数
        createCellWithStyle(row, 38, rsInventory.getRentalDays(), isPackageBox, yellowStyle);

        // 租金单价
        createCellWithStyle(row, 39, Optional.ofNullable(rsInventory.getOverdueRentalUnitPrice()).orElse(new BigDecimal(0)).doubleValue(), isPackageBox, yellowStyle);

        // 超期仓租
        createCellWithStyle(row, 40, Optional.ofNullable(rsInventory.getOverdueRentalFee()).orElse(new BigDecimal(0)).doubleValue(), isPackageBox, yellowStyle);

        // 其他费用
        createCellWithStyle(row, 41, "", isPackageBox, yellowStyle);
    }

    /**
     * 创建单元格并设置样式
     *
     * @param row          行对象
     * @param columnIndex  列索引
     * @param value        单元格值
     * @param isPackageBox 是否为打包箱
     * @param yellowStyle  黄色背景样式
     */
    private void createCellWithStyle(Row row, int columnIndex, Object value, boolean isPackageBox, CellStyle yellowStyle) {
        Cell cell = row.createCell(columnIndex);

        if (value != null) {
            if (value instanceof String) {
                cell.setCellValue((String) value);
            } else if (value instanceof Number) {
                cell.setCellValue(((Number) value).doubleValue());
            } else if (value instanceof Boolean) {
                cell.setCellValue((Boolean) value);
            }
        }

        if (isPackageBox) {
            cell.setCellStyle(yellowStyle);
        }
    }

    /**
     * 填充打包箱货物明细信息
     *
     * @param row          行对象
     * @param rsInventory  库存对象
     * @param yellowStyle  黄色背景样式
     * @param isPackageBox 是否为打包箱
     */
    private void writePackageBoxCargoDetails(Row row, RsInventory rsInventory, CellStyle yellowStyle, boolean isPackageBox) {
        // 装载标记
        Cell shippingMarkCell = row.createCell(2);
        String value = rsInventory.getSubOrderNo().split("-").length > 0 ? rsInventory.getSubOrderNo().split("-")[1] : "";
        shippingMarkCell.setCellValue(value + "_[快递打包箱]");
        if (isPackageBox) shippingMarkCell.setCellStyle(yellowStyle);

        // 流水号
        Cell inboundSerialNoCell = row.createCell(1);
        inboundSerialNoCell.setCellValue(rsInventory.getInboundSerialNo());
        if (isPackageBox) inboundSerialNoCell.setCellStyle(yellowStyle);

        // 货名
        Cell itemNameCell = row.createCell(5);
        itemNameCell.setCellValue("");
        if (isPackageBox) itemNameCell.setCellStyle(yellowStyle);

        // 英文货名
        Cell itemEnNameCell = row.createCell(6);
        itemEnNameCell.setCellValue("");
        if (isPackageBox) itemEnNameCell.setCellStyle(yellowStyle);

        // 箱数
        Cell boxCountCell = row.createCell(8);
        boxCountCell.setCellValue(1);
        if (isPackageBox) boxCountCell.setCellStyle(yellowStyle);

        // 总毛重
        Cell totalGrossWeightCell = row.createCell(11);
        totalGrossWeightCell.setCellValue(Optional.ofNullable(rsInventory.getTotalGrossWeight()).orElse(new BigDecimal(0)).doubleValue());
        if (isPackageBox) totalGrossWeightCell.setCellStyle(yellowStyle);

        // 总体积
        Cell totalVolumeCell = row.createCell(12);
        totalVolumeCell.setCellValue(Optional.ofNullable(rsInventory.getTotalVolume()).orElse(new BigDecimal(0)).doubleValue());
        if (isPackageBox) totalVolumeCell.setCellStyle(yellowStyle);
    }

    /**
     * 填充普通货物明细信息
     *
     * @param row            行对象
     * @param rsInventory    库存对象
     * @param rsCargoDetails 货物明细对象
     * @param yellowStyle    黄色背景样式
     * @param isPackageBox   是否为打包箱
     */
    private void writeNormalCargoDetails(Row row, RsInventory rsInventory, RsCargoDetails rsCargoDetails, CellStyle yellowStyle, boolean isPackageBox) {
        //流水号
        Cell inboundSerialNoCell = row.createCell(1);
        inboundSerialNoCell.setCellValue(rsInventory.getInboundSerialNo());
        if (isPackageBox) inboundSerialNoCell.setCellStyle(yellowStyle);

        // 索引
        Cell subOrderNoCell = row.createCell(2);
        subOrderNoCell.setCellValue(rsInventory.getSubOrderNo());
        if (isPackageBox) subOrderNoCell.setCellStyle(yellowStyle);

        // 唛头
        Cell shippingMarkCell = row.createCell(3);
        shippingMarkCell.setCellValue(rsInventory.getSqdShippingMark());
        if (isPackageBox) shippingMarkCell.setCellStyle(yellowStyle);

        // 品牌
        Cell ibrandNameCell = row.createCell(4);
        ibrandNameCell.setCellValue(rsInventory.getBrandName());
        if (isPackageBox) ibrandNameCell.setCellStyle(yellowStyle);

        // 货名
        Cell itemNameCell = row.createCell(5);
        itemNameCell.setCellValue(rsCargoDetails.getItemName());
        if (isPackageBox) itemNameCell.setCellStyle(yellowStyle);

        // 英文货名
        Cell itemEnNameCell = row.createCell(6);
        itemEnNameCell.setCellValue(rsCargoDetails.getItemEnName());
        if (isPackageBox) itemEnNameCell.setCellStyle(yellowStyle);

        // 箱数
//        Cell boxCountCell = row.createCell(4);
//        boxCountCell.setCellValue("");
//        if (isPackageBox) boxCountCell.setCellStyle(yellowStyle);

        // 总件数
        Cell totalItemCountCell = row.createCell(9);
        totalItemCountCell.setCellValue(Optional.ofNullable(rsCargoDetails.getBoxCount()).orElse(0L));
        if (isPackageBox) totalItemCountCell.setCellStyle(yellowStyle);

        // 总净重
        Cell totalNetWeightCell = row.createCell(10);
        totalNetWeightCell.setCellValue(Optional.ofNullable(rsCargoDetails.getUnitGrossWeight()).orElse(new BigDecimal(0)).doubleValue());
        if (isPackageBox) totalNetWeightCell.setCellStyle(yellowStyle);

        // 总毛重
        Cell totalGrossWeightCell = row.createCell(11);
        totalGrossWeightCell.setCellValue(Optional.ofNullable(rsCargoDetails.getUnitGrossWeight()).orElse(new BigDecimal(0)).doubleValue());

        // 总体积
        Cell totalVolumeCell = row.createCell(12);
        totalVolumeCell.setCellValue(Optional.ofNullable(rsCargoDetails.getUnitVolume()).orElse(new BigDecimal(0)).doubleValue());

        // 备注
        Cell damageStatusCell = row.createCell(13);
        damageStatusCell.setCellValue(rsInventory.getNotes());
        if (isPackageBox) damageStatusCell.setCellStyle(yellowStyle);
    }

    /**
     * 处理被打包的物品
     *
     * @param sheet            工作表对象
     * @param rsInventory      库存对象
     * @param cargoDetailIndex 当前货物明细索引
     * @param noIndex          当前编号索引
     * @param yellowStyle      黄色背景样式
     * @return 更新后的货物明细索引
     */
    private int processPackagedItems(Sheet sheet, RsInventory rsInventory, int cargoDetailIndex, int noIndex, CellStyle yellowStyle) {
        RsInventory queryParam = new RsInventory();
        queryParam.setPackageTo(rsInventory.getInventoryId());
        List<RsInventory> packagedItems = rsInventoryMapper.selectRsInventoryList(queryParam);

        if (packagedItems != null && !packagedItems.isEmpty()) {
            for (RsInventory packagedItem : packagedItems) {
                // 创建新行
                Row packagedItemRow = getOrCreateRow(sheet, cargoDetailIndex);

                // 填充被打包物品的信息
                writePackagedItemInfo(packagedItemRow, packagedItem, rsInventory);

                // 处理货物详情
                List<RsCargoDetails> packagedCargoDetails = packagedItem.getRsCargoDetailsList();
                if (packagedCargoDetails != null && !packagedCargoDetails.isEmpty()) {
                    for (RsCargoDetails packagedCargoDetail : packagedCargoDetails) {
                        writePackagedItemCargoDetails(packagedItemRow, packagedItem, packagedCargoDetail, rsInventory, noIndex);
                        noIndex++;
                        cargoDetailIndex++;

                        // 如果有多个货物详情，创建新行
                        if (packagedCargoDetails.indexOf(packagedCargoDetail) < packagedCargoDetails.size() - 1) {
                            packagedItemRow = sheet.createRow(cargoDetailIndex);
                        }
                    }
                } else {
                    // 没有货物详情，也要增加行索引
                    cargoDetailIndex++;
                }
            }
        }

        return cargoDetailIndex;
    }

    /**
     * 填充被打包物品的基本信息
     *
     * @param row             行对象
     * @param packagedItem    被打包的库存对象
     * @param parentInventory 父库存对象（打包箱）
     */
    private void writePackagedItemInfo(Row row, RsInventory packagedItem, RsInventory parentInventory) {
        // 打包标识
        row.createCell(15).setCellValue(packagedItem.getRepackingStatus());
        // 打包至
        row.createCell(16).setCellValue(parentInventory.getSubOrderNo());
        // 原始分单号
        row.createCell(17).setCellValue(packagedItem.getSubOrderNo());
        // 客户名字
        row.createCell(18).setCellValue(packagedItem.getConsigneeName());
        // 客户电话
        row.createCell(19).setCellValue(packagedItem.getConsigneeTel());
        // 总件数
        row.createCell(21).setCellValue(packagedItem.getTotalBoxes());
        // 总毛重
        row.createCell(23).setCellValue(packagedItem.getTotalGrossWeight().doubleValue());
        // 总体积
        row.createCell(28).setCellValue(packagedItem.getTotalVolume().doubleValue());

        // 入仓时间
        row.createCell(29).setCellValue(packagedItem.getActualInboundTime() == null ? null : DateUtils.dateTime(packagedItem.getActualInboundTime()));
        // 供货商
        row.createCell(31).setCellValue(packagedItem.getSupplier());
        // 物流单号
        row.createCell(30).setCellValue(packagedItem.getDriverInfo());
    }

    /**
     * 填充被打包物品的货物明细信息
     *
     * @param row                 行对象
     * @param packagedItem        被打包的库存对象
     * @param packagedCargoDetail 被打包的货物明细对象
     * @param parentInventory     父库存对象（打包箱）
     * @param noIndex             当前编号索引
     */
    private void writePackagedItemCargoDetails(Row row, RsInventory packagedItem, RsCargoDetails packagedCargoDetail, RsInventory parentInventory, int noIndex) {
        // 编号
        row.createCell(0).setCellValue(noIndex);
        // 流水号
        row.createCell(1).setCellValue(packagedItem.getInboundSerialNo());
        // 索引
        String value = parentInventory.getSubOrderNo().split("-").length > 0 ? parentInventory.getSubOrderNo().split("-")[1] : "";
        row.createCell(2).setCellValue(value + "_" + packagedItem.getSubOrderNo());
        // 唛头
        row.createCell(3).setCellValue(packagedCargoDetail.getShippingMark());
        // 货名
        row.createCell(5).setCellValue(packagedCargoDetail.getItemName());
        // 英文货名
        row.createCell(6).setCellValue(packagedCargoDetail.getItemEnName());
        // 箱数
        row.createCell(8).setCellValue("");
        // 总件数
        row.createCell(9).setCellValue(packagedCargoDetail.getBoxCount());
        // 总净重
        row.createCell(10).setCellValue(0);
        // 总毛重
        row.createCell(11).setCellValue(0);
        // 总体积
        row.createCell(12).setCellValue(0);
        // 备注
        row.createCell(13).setCellValue(packagedItem.getNotes());
    }

    /**
     * 更新打包箱处理后的编号索引
     *
     * @param rsInventory 库存对象
     * @return 更新后的编号索引
     */
    private int updateNoIndexAfterPackagedItems(RsInventory rsInventory) {
        RsInventory queryParam = new RsInventory();
        queryParam.setPackageTo(rsInventory.getInventoryId());
        List<RsInventory> packagedItems = rsInventoryMapper.selectRsInventoryList(queryParam);

        int noIndex = 1;
        if (packagedItems != null) {
            for (RsInventory packagedItem : packagedItems) {
                List<RsCargoDetails> packagedCargoDetails = packagedItem.getRsCargoDetailsList();
                if (packagedCargoDetails != null) {
                    noIndex += packagedCargoDetails.size();
                } else {
                    noIndex += 1;
                }
            }
        }

        return noIndex;
    }

    /**
     * 填充费用汇总信息
     *
     * @param rsOutboundRecord 出仓记录对象
     * @param sheet            工作表对象
     */
    private void writeFeesSummary(RsOutboundRecord rsOutboundRecord, Sheet sheet) {
        // 仓库装柜费
        Row row9 = getOrCreateRow(sheet, 7);

        // 装柜报价
        row9.createCell(16).setCellValue(Optional.ofNullable(rsOutboundRecord.getWarehouseQuote()).orElse(new BigDecimal(0)).doubleValue());
        // 补收入仓费
        row9.createCell(17).setCellValue(Optional.ofNullable(rsOutboundRecord.getAdditionalStorageFee()).orElse(BigDecimal.ZERO).doubleValue());
        // 未收卸货
        row9.createCell(18).setCellValue(Optional.ofNullable(rsOutboundRecord.getUnpaidUnloadingFee()).orElse(new BigDecimal(0)).doubleValue());
        // 未收打包
        row9.createCell(19).setCellValue(Optional.ofNullable(rsOutboundRecord.getUnpaidPackingFee()).orElse(new BigDecimal(0)).doubleValue());
        // 物流代垫费
        row9.createCell(20).setCellValue(Optional.ofNullable(rsOutboundRecord.getLogisticsAdvanceFee()).orElse(new BigDecimal(0)).doubleValue());
        // 超期租金
        row9.createCell(21).setCellValue(Optional.ofNullable(rsOutboundRecord.getOverdueRentalFee()).orElse(new BigDecimal(0)).doubleValue());
        // 其他费用
        row9.createCell(22).setCellValue(Optional.ofNullable(rsOutboundRecord.getWarehouseAdvanceOtherFee()).orElse(new BigDecimal(0)).doubleValue());
    }

    @Override
    public List<RsOutboundRecord> selectRentalRecordList(RsOutboundRecord rsOutboundRecord) {
        return rsOutboundRecordMapper.selectRentalRecordList(rsOutboundRecord);
    }

    @Override
    public RsOutboundRecord selectRentalsByOutboundRecordId(Long outboundRecordId) {
        return rsOutboundRecordMapper.selectRentalsByOutboundRecordId(outboundRecordId);
    }

    @Override
    public RsOutboundRecord selectRsOutboundRecordByRctId(Long rctId) {
        return rsOutboundRecordMapper.selectRsOutboundRecordByRctId(rctId);
    }

    /**
     * 批量删除出仓记录
     *
     * @param outboundRecordIds 需要删除的出仓记录主键
     * @return 结果
     */
    @Override
    public int deleteRsOutboundRecordByOutboundRecordIds(Long[] outboundRecordIds) {
        return rsOutboundRecordMapper.deleteRsOutboundRecordByOutboundRecordIds(outboundRecordIds);
    }

    /**
     * 删除出仓记录信息
     *
     * @param outboundRecordId 出仓记录主键
     * @return 结果
     */
    @Override
    public int deleteRsOutboundRecordByOutboundRecordId(Long outboundRecordId) {
        return rsOutboundRecordMapper.deleteRsOutboundRecordByOutboundRecordId(outboundRecordId);
    }
}
