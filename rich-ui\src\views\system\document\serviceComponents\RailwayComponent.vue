<template>
  <div class="railway-component">
    <!--铁路-->
    <div class="railway-item">
      <el-row>
        <el-col :span="18">
          <!--标题栏-->
          <div class="service-bar">
            <a
              :class="[
                'service-toggle-icon',
                getFold(serviceItem.serviceTypeId) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'
              ]"
            />
            <h3 class="service-title" @click="changeFold(serviceItem.serviceTypeId)">
              铁路-{{ serviceItem.serviceShortName }}
            </h3>
            <!--审核信息-->
            <audit
              v-if="auditInfo"
              :audit="true"
              :basic-info="getServiceInstance(serviceItem.serviceTypeId)"
              :disabled="disabled"
              :payable="getPayable(serviceItem.serviceTypeId)"
              :rs-charge-list="serviceItem.rsChargeList"
              @auditFee="auditCharge(serviceItem.serviceTypeId, $event)"
              @return="changeServiceObject(serviceItem.serviceTypeId, $event)"
            />
          </div>
        </el-col>
      </el-row>
      <!--内容区域-->
      <transition name="fade">
        <el-row
          v-if="getFold(serviceItem.serviceTypeId)"
          :gutter="10"
          class="service-content-area"
        >
          <!--服务信息栏-->
          <transition name="fade">
            <el-col v-if="branchInfo" :span="3" class="service-info-col">
              <el-form-item label="询价单号">
                <el-input
                  v-model="getServiceInstance(serviceItem.serviceTypeId).inquiryNo"
                  placeholder="询价单号"
                  @focus="generateFreight(3, serviceItem.serviceTypeId, getServiceObject(serviceItem.serviceTypeId))"
                />
              </el-form-item>

              <el-form-item v-if="!booking && branchInfo" label="供应商">
                <el-popover
                  :content="getSupplierEmail(serviceItem.serviceTypeId)"
                  placement="bottom"
                  trigger="hover"
                  width="200"
                >
                  <template #reference>
                    <el-input
                      :value="getServiceInstance(serviceItem.serviceTypeId).supplierName"
                      class="disable-form"
                      disabled
                    />
                  </template>
                </el-popover>
              </el-form-item>

              <el-form-item label="合约类型">
                <el-input
                  :value="getAgreementDisplay(serviceItem.serviceTypeId)"
                  class="disable-form"
                  disabled
                  placeholder="合约类型"
                />
              </el-form-item>

              <el-form-item label="业务须知">
                <el-input
                  v-model="getServiceInstance(serviceItem.serviceTypeId).inquiryNotice"
                  class="disable-form"
                  disabled
                  placeholder="业务须知"
                />
              </el-form-item>
            </el-col>
          </transition>

          <!--主表信息-->
          <transition name="fade">
            <el-col v-if="branchInfo" :span="15">
              <el-row :gutter="5">
                <el-col :span="5">
                  <el-form-item label="商务单号">
                    <el-row>
                      <el-col :span="20">
                        <el-input :value="form.sqdPsaNo" class="disable-form" disabled/>
                      </el-col>
                      <el-col :span="4">
                        <el-button
                          size="mini"
                          style="color: red"
                          type="text"
                          @click="psaBookingCancel"
                        >
                          取消
                        </el-button>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </transition>

          <!--物流进度-->
          <transition name="fade">
            <el-col v-if="logisticsInfo" :span="4">
              <logistics-progress
                :disabled="getFormDisable(serviceItem.serviceTypeId) || disabled || psaVerify"
                :logistics-progress-data="getServiceObject(serviceItem.serviceTypeId).rsOpLogList"
                :open-logistics-progress-list="true"
                :process-type="4"
                :service-type="20"
                @deleteItem="deleteLogItem(serviceItem.serviceTypeId, $event)"
                @return="updateLogList(serviceItem.serviceTypeId, $event)"
              />
            </el-col>
          </transition>

          <!--费用列表-->
          <el-col v-if="chargeInfo" :span="10.3">
            <charge-list
              :a-t-d="form.podEta"
              :charge-data="getServiceObject(serviceItem.serviceTypeId).rsChargeList"
              :company-list="companyList"
              :disabled="getFormDisable(serviceItem.serviceTypeId) || disabled"
              :hiddenSupplier="booking"
              :is-receivable="false"
              :open-charge-list="true"
              :pay-detail-r-m-b="getServiceObject(serviceItem.serviceTypeId).payableRMB"
              :pay-detail-r-m-b-tax="getServiceObject(serviceItem.serviceTypeId).payableRMBTax"
              :pay-detail-u-s-d="getServiceObject(serviceItem.serviceTypeId).payableUSD"
              :pay-detail-u-s-d-tax="getServiceObject(serviceItem.serviceTypeId).payableUSDTax"
              :service-type-id="20"
              @copyFreight="copyFreight($event)"
              @deleteAll="deleteAllCharge(serviceItem.serviceTypeId)"
              @deleteItem="deleteChargeItem(serviceItem.serviceTypeId, $event)"
              @return="calculateCharge(serviceItem.serviceTypeId, $event, getServiceObject(serviceItem.serviceTypeId))"
            />
          </el-col>
        </el-row>
      </transition>
    </div>
  </div>
</template>

<script>
import Audit from "@/views/system/document/audit.vue"
import LogisticsProgress from "@/views/system/document/logisticsProgress.vue"
import ChargeList from "@/views/system/document/chargeList.vue"

export default {
  name: "RailwayComponent",
  components: {
    Audit,
    LogisticsProgress,
    ChargeList
  },
  props: {
    serviceItem: {
      type: Object,
      default: () => ({})
    },
    // 铁路服务数据集合
    railwayServices: {
      type: [Array, Set],
      default: () => []
    },
    // 表单数据
    form: {
      type: Object,
      default: () => ({})
    },
    // 显示控制
    branchInfo: {
      type: Boolean,
      default: true
    },
    logisticsInfo: {
      type: Boolean,
      default: true
    },
    chargeInfo: {
      type: Boolean,
      default: true
    },
    auditInfo: {
      type: Boolean,
      default: false
    },
    // 状态控制
    disabled: {
      type: Boolean,
      default: false
    },
    booking: {
      type: Boolean,
      default: false
    },
    psaVerify: {
      type: Boolean,
      default: false
    },
    // 数据列表
    supplierList: {
      type: Array,
      default: () => []
    },
    companyList: {
      type: Array,
      default: () => []
    },
    // 新增属性，不再依赖$parent
    foldState: {
      type: Boolean,
      default: false
    },
    serviceInstance: {
      type: Object,
      default: () => ({})
    },
    serviceObject: {
      type: Object,
      default: () => ({})
    },
    formDisable: {
      type: Boolean,
      default: false
    },
    payable: {
      type: Object,
      default: null
    }
  },
  computed: {
    // 判断是否禁用状态
    isDisabled() {
      return this.disabled || this.psaVerify
    }
  },
  methods: {
    // 获取供应商邮箱
    getSupplierEmail(serviceTypeId) {
      const serviceInstance = this.getServiceInstance(serviceTypeId)
      if (!serviceInstance || !serviceInstance.supplierId) return ''

      const supplier = this.supplierList.find(v => v.companyId === serviceInstance.supplierId)
      return supplier ? supplier.staffEmail : ''
    },
    // 获取合约显示文本
    getAgreementDisplay(serviceTypeId) {
      const serviceInstance = this.getServiceInstance(serviceTypeId)
      if (!serviceInstance) return ''
      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo
    },
    // 事件转发给父组件
    changeFold(serviceTypeId) {
      this.$emit("changeFold", serviceTypeId)
    },
    getFold(serviceTypeId) {
      return this.foldState
    },
    getServiceInstance(serviceTypeId) {
      return this.serviceInstance
    },
    getServiceObject(serviceTypeId) {
      return this.serviceObject
    },
    getPayable(serviceTypeId) {
      return this.payable
    },
    getFormDisable(serviceTypeId) {
      return this.formDisable
    },
    changeServiceObject(serviceTypeId, serviceObject) {
      this.$emit("changeServiceObject", serviceTypeId, serviceObject)
    },
    auditCharge(serviceTypeId, event) {
      this.$emit("auditCharge", serviceTypeId, event)
    },
    generateFreight(type1, type2, item) {
      this.$emit("generateFreight", type1, type2, item)
    },
    psaBookingCancel() {
      this.$emit("psaBookingCancel")
    },
    copyFreight(event) {
      this.$emit("copyFreight", event)
    },
    calculateCharge(serviceTypeId, event, item) {
      this.$emit("calculateCharge", serviceTypeId, event, item)
    },
    // 物流进度相关方法
    deleteLogItem(serviceTypeId, event) {
      const serviceObject = this.getServiceObject(serviceTypeId)
      if (serviceObject && serviceObject.rsOpLogList) {
        serviceObject.rsOpLogList = serviceObject.rsOpLogList.filter(item => item !== event)
      }
    },
    updateLogList(serviceTypeId, event) {
      const serviceObject = this.getServiceObject(serviceTypeId)
      if (serviceObject) {
        serviceObject.rsOpLogList = event
      }
    },
    // 费用列表相关方法
    deleteAllCharge(serviceTypeId) {
      const serviceObject = this.getServiceObject(serviceTypeId)
      if (serviceObject) {
        serviceObject.rsChargeList = []
      }
    },
    deleteChargeItem(serviceTypeId, event) {
      const serviceObject = this.getServiceObject(serviceTypeId)
      if (serviceObject && serviceObject.rsChargeList) {
        serviceObject.rsChargeList = serviceObject.rsChargeList.filter(item => item !== event)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/op-document';

// Railway组件特定样式
.railway-component {
  width: 100%;

  .railway-item {
    margin-bottom: 10px;
  }

  .service-bar {
    display: flex;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%;

    .service-toggle-icon {
      cursor: pointer;
      margin-right: 5px;
    }

    .service-title {
      margin: 0;
      width: 250px;
      text-align: left;
      cursor: pointer;
    }
  }

  .service-content-area {
    margin-bottom: 15px;
    display: -webkit-box;

    .service-info-col {
      .el-form-item {
        margin-bottom: 10px;
      }
    }
  }

  // 优化表单输入框样式
  .el-input {
    width: 100%;
  }

  // 优化日期选择器样式
  .el-date-picker {
    width: 100%;
  }
}
</style>
