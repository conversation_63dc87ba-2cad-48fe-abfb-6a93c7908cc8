<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" :show-message="false"
             v-loading="mac" status-icon
    >
      <div class="title">
        <h3 style="margin: auto;width: fit-content" @dblclick="toggleDebugMode">瑞旗系统</h3>
        <!-- 添加调试区域 -->
        <!--<div v-if="showDebugOptions" class="debug-options">-->
        <!--  <el-checkbox v-model="enableWechatVerify" border size="mini">启用微信验证</el-checkbox>-->
        <!--</div>-->
      </div>
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          auto-complete="off"
          placeholder="账号"
          type="text"
        >
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          auto-complete="off"
          placeholder="密码"
          type="password"
          @keyup.enter.native="handleLogin"
        >
        </el-input>
      </el-form-item>
      <el-form-item v-if="captchaEnabled" prop="code">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" class="login-code-img" @click="getCode"/>
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          style="width:100%;"
          type="primary"
          @click="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div v-if="register" style="float: right;">
          <router-link :to="'/register'" class="link-type">立即注册</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span @click="incrementDebugCounter">Copyright © 2009-2024 RichShipping All Rights Reserved.</span>
    </div>

    <!-- 微信扫码登录弹窗 -->
    <wechat-scan
      v-if="showWechatScan"
      :visible.sync="showWechatScan"
      :username="loginForm.username"
      @scan-success="handleWechatScanSuccess"
    />
  </div>
</template>

<script>
import {getCodeImg, getMac, checkNeedWechatScan} from '@/api/login'
import Cookies from 'js-cookie'
import {decrypt, encrypt} from '@/utils/jsencrypt'
import Fingerprint2 from 'fingerprintjs2'
import Fingerprint from '@fingerprintjs/fingerprintjs'
import WechatScan from '@/components/WechatScan'

export default {
  name: 'Login',
  components: {
    WechatScan
  },
  data() {
    return {
      mac: false,
      codeUrl: '',
      loginForm: {
        username: '',
        password: '',
        rememberMe: false,
        code: '',
        uuid: '',
        unid: ''
      },
      loginRules: {
        username: [
          {required: true, trigger: 'blur', message: '您的账号'}
        ],
        password: [
          {required: true, trigger: 'blur', message: '您的密码'}
        ],
        code: [{required: true, trigger: 'change', message: '验证码'}]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: false,
      redirect: undefined,
      // 微信扫码登录
      showWechatScan: false,
      // 调试选项
      showDebugOptions: false,
      debugClickCount: 0,
      // 微信验证开关 - 根据环境设置，生产环境开启，开发环境关闭
      enableWechatVerify: process.env.NODE_ENV === 'production',
      // 微信扫码验证Cookie名
      wechatScanCookieName: 'wechat_scan_verified'
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    },
    // 监听微信验证开关变化，保存到本地存储
    enableWechatVerify: {
      handler(val) {
        localStorage.setItem('debug_enable_wechat_verify', val.toString());
      }
    }
  },
  created() {
    this.getCode()
    this.getCookie()
    // 根据环境设置微信验证开关，且允许本地存储覆盖(仅在调试模式下)
    const storedSetting = localStorage.getItem('debug_enable_wechat_verify');
    if (this.showDebugOptions && storedSetting !== null) {
      this.enableWechatVerify = storedSetting === 'true';
    } else {
      // 默认根据环境设置：生产环境开启，开发环境关闭
      this.enableWechatVerify = process.env.NODE_ENV === 'production';
      // 更新本地存储
      localStorage.setItem('debug_enable_wechat_verify', this.enableWechatVerify.toString());
    }
  },
  methods: {
    // 切换调试模式
    toggleDebugMode() {
      // 双击标题时显示调试选项
      this.showDebugOptions = !this.showDebugOptions;
      if (this.showDebugOptions) {
        this.$message.success('已' + (this.enableWechatVerify ? '启用' : '关闭') + '微信验证');
      }
    },
    // 点击版权信息增加计数器
    incrementDebugCounter() {
      this.debugClickCount++;
      if (this.debugClickCount >= 5) {
        this.showDebugOptions = true;
        this.debugClickCount = 0;
        this.$message.success('已' + (this.enableWechatVerify ? '启用' : '关闭') + '微信验证');
      }
    },
    getMac() {
      this.mac = true
      this.getFingerPrint(v => {
        this.$alert(v, '', {
          callback: action => {
            if (action == 'confirm') {
              this.$message.success('已复制')
              this.mac = false
              navigator.clipboard.writeText(v)
            }
            if (action == 'cancel') {
              this.mac = false
            }
          }
        })
      })
    },
    async getFingerPrint(callback) {
      let options = Fingerprint2.Options = {
        excludes: {
          webdriver: true,
          userAgent: true,
          language: true,
          colorDepth: true,
          deviceMemory: true,
          pixelRatio: true,
          hardwareConcurrency: true,
          screenResolution: true,
          availableScreenResolution: true,
          timezoneOffset: true,
          timezone: true,
          sessionStorage: true,
          localStorage: true,
          indexedDb: true,
          addBehavior: true,
          openDatabase: true,
          cpuClass: true,
          platform: true,
          doNotTrack: true,
          plugins: true,
          canvas: true,
          webgl: false,
          webglVendorAndRenderer: false,
          adBlock: true,
          hasLiedLanguages: true,
          hasLiedResolution: true,
          hasLiedOs: true,
          hasLiedBrowser: true,
          touchSupport: true,
          fonts: true,
          fontsFlash: true,
          audio: false,
          enumerateDevices: true
        }
      }
      Fingerprint2.get(options, async (components) => {
        const values = components.map(function (component, index) {
            return component.value
          })
          const murmur = Fingerprint2.x64hash128(values.join(''), 31)
        const fp = await Fingerprint.load()
        const result = await fp.get()
        callback(result.visitorId)
        }
      )
    },
    async logCode() {
      const fp = await Fingerprint.load()
      const result = await fp.get()
      console.log('Browser fingerprint:', result.visitorId)
      return result.visitorId
    },
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled == undefined ? true : res.captchaEnabled
        if (this.captchaEnabled) {
          this.codeUrl = 'data:image/gif;base64,' + res.img
          this.loginForm.uuid = res.uuid
        }
      })
    }
    ,
    getCookie() {
      const username = Cookies.get('username')
      const password = Cookies.get('password')
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username == undefined ? this.loginForm.username : username,
        password: password == undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe == undefined ? false : Boolean(rememberMe)
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          if (this.loginForm.rememberMe) {
            Cookies.set('username', this.loginForm.username, {expires: 30})
            Cookies.set('password', encrypt(this.loginForm.password), {expires: 30})
            Cookies.set('rememberMe', this.loginForm.rememberMe, {expires: 30})
          } else {
            Cookies.remove('username')
            Cookies.remove('password')
            Cookies.remove('rememberMe')
          }
          this.getFingerPrint(v => {
            this.loginForm.unid = v

            // 检查是否需要微信扫码验证
            this.checkNeedWechatScan()
          })
        }
      })
    },

    // 检查是否需要微信扫码验证
    async checkNeedWechatScan() {
      // 如果微信验证开关关闭，直接登录
      if (!this.enableWechatVerify) {
        console.log('微信验证已关闭，直接登录');
        this.doLogin();
        return;
      }

      try {
        const res = await checkNeedWechatScan(this.loginForm.username)
        if (res.code === 200) {
          const needScan = res.data.needScan
          const isWechatBound = res.data.isWechatBound;

          // 检查Cookie中是否存在微信扫码验证标记
          const scanVerified = Cookies.get(this.wechatScanCookieName);
          if (needScan && !scanVerified) {
            // 需要微信扫码验证
            this.showWechatScan = true;
            this.loading = false;

            // 如果用户未绑定微信，显示提示
            if (!isWechatBound) {
              this.$message.info('首次登录需要绑定微信账号进行验证');
            }
          } else {
            // 不需要微信扫码验证，直接登录
            this.doLogin();
          }
        } else {
          // 检查失败，直接登录
          this.doLogin();
        }
      } catch (error) {
        console.error('检查微信扫码验证失败', error);
        // 发生错误，直接登录
        this.doLogin();
      }
    },

    // 执行登录
    doLogin() {
      this.$store.dispatch('Login', this.loginForm).then(() => {
        // 登录成功后，设置微信扫码验证标记Cookie，有效期24小时
        Cookies.set(this.wechatScanCookieName, 'true', {expires: 1});
        this.$router.push({path: this.redirect || '/'}).catch(() => {
        })
      }).catch(() => {
        this.loading = false
        if (this.captchaEnabled) {
          this.getCode()
        }
      })
    },

    // 微信扫码登录成功回调
    handleWechatScanSuccess() {
      this.showWechatScan = false
      this.doLogin()
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}

.title {
  margin: 0 auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial, serif;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 38px;
}

/* 添加调试选项样式 */
.debug-options {
  margin-top: 10px;
  padding: 5px;
  background-color: rgba(255, 255, 100, 0.2);
  border: 1px dashed #e6a23c;
  border-radius: 4px;
}
</style>
