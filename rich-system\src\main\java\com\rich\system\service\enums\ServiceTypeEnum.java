package com.rich.system.service.enums;

import lombok.Getter;
import lombok.AllArgsConstructor;

/**
 * 服务类型枚举
 * 定义所有支持的服务类型及其属性
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ServiceTypeEnum {
    
    // 列表类型服务 (List Services)
    SEA_FCL(1L, "SeaFCL", "海运整箱", ServiceCategory.LIST, ProcessingPriority.HIGH),
    SEA_LCL(2L, "SeaLCL", "海运拼箱", ServiceCategory.LIST, ProcessingPriority.HIGH),
    AIR(10L, "AIR", "空运", ServiceCategory.LIST, ProcessingPriority.HIGH),
    CTNR_TRUCK(50L, "CtnrTruck", "集装箱卡车", ServiceCategory.LIST, ProcessingPriority.MEDIUM),
    BULK_TRUCK(51L, "BulkTruck", "散货拖车", ServiceCategory.LIST, ProcessingPriority.MEDIUM),
    FREE_DECLARE(60L, "FreeDeclare", "自由报关", ServiceCategory.LIST, ProcessingPriority.MEDIUM),
    DOC_DECLARE(61L, "DocDeclare", "单证报关", ServiceCategory.LIST, ProcessingPriority.MEDIUM),
    
    // 单个服务 (Single Services)
    RAIL_FCL(20L, "RailFCL", "铁路整箱", ServiceCategory.SINGLE, ProcessingPriority.MEDIUM),
    RAIL_LCL(21L, "RailLCL", "铁路拼箱", ServiceCategory.SINGLE, ProcessingPriority.MEDIUM),
    EXPRESS(30L, "Express", "快递", ServiceCategory.SINGLE, ProcessingPriority.LOW),
    DO_AGENT(70L, "DOAgent", "DO代理", ServiceCategory.SINGLE, ProcessingPriority.MEDIUM),
    CLEAR_AGENT(71L, "ClearAgent", "清关代理", ServiceCategory.SINGLE, ProcessingPriority.MEDIUM),
    WHS(80L, "WHS", "仓库服务", ServiceCategory.SINGLE, ProcessingPriority.MEDIUM),
    
    // 扩展服务 (Expand Services)
    THIRD_CERT(90L, "3rdCert", "第三方证书", ServiceCategory.EXPAND, ProcessingPriority.LOW),
    INSURANCE(100L, "INS", "保险", ServiceCategory.EXPAND, ProcessingPriority.LOW),
    TRADING(101L, "Trading", "贸易", ServiceCategory.EXPAND, ProcessingPriority.LOW),
    FUMIGATION(102L, "Fumigation", "熏蒸", ServiceCategory.EXPAND, ProcessingPriority.LOW),
    CO_CERT(103L, "CO", "CO证书", ServiceCategory.EXPAND, ProcessingPriority.LOW),
    OTHER(104L, "Other", "其他服务", ServiceCategory.EXPAND, ProcessingPriority.LOW),
    
    // 特殊服务
    CLIENT_MESSAGE(0L, "client", "客户信息", ServiceCategory.SPECIAL, ProcessingPriority.CRITICAL);
    
    /**
     * 服务类型ID
     */
    private final Long serviceTypeId;
    
    /**
     * 服务归属标识
     */
    private final String serviceBelongTo;
    
    /**
     * 服务显示名称
     */
    private final String displayName;
    
    /**
     * 服务分类
     */
    private final ServiceCategory category;
    
    /**
     * 处理优先级
     */
    private final ProcessingPriority priority;
    
    /**
     * 服务分类枚举
     */
    public enum ServiceCategory {
        LIST,      // 列表类型服务
        SINGLE,    // 单个服务
        EXPAND,    // 扩展服务
        SPECIAL    // 特殊服务
    }
    
    /**
     * 处理优先级枚举
     */
    public enum ProcessingPriority {
        CRITICAL(1),   // 关键服务，必须同步处理
        HIGH(2),       // 高优先级，优先处理
        MEDIUM(3),     // 中等优先级
        LOW(4);        // 低优先级，可异步处理
        
        @Getter
        private final int level;
        
        ProcessingPriority(int level) {
            this.level = level;
        }
    }
    
    /**
     * 根据服务类型ID查找枚举
     */
    public static ServiceTypeEnum findByServiceTypeId(Long serviceTypeId) {
        for (ServiceTypeEnum serviceType : values()) {
            if (serviceType.getServiceTypeId().equals(serviceTypeId)) {
                return serviceType;
            }
        }
        return null;
    }
    
    /**
     * 根据服务归属标识查找枚举
     */
    public static ServiceTypeEnum findByServiceBelongTo(String serviceBelongTo) {
        for (ServiceTypeEnum serviceType : values()) {
            if (serviceType.getServiceBelongTo().equals(serviceBelongTo)) {
                return serviceType;
            }
        }
        return null;
    }
    
    /**
     * 获取指定分类的所有服务类型
     */
    public static ServiceTypeEnum[] getByCategory(ServiceCategory category) {
        return java.util.Arrays.stream(values())
                .filter(serviceType -> serviceType.getCategory() == category)
                .toArray(ServiceTypeEnum[]::new);
    }
    
    /**
     * 获取指定优先级的所有服务类型
     */
    public static ServiceTypeEnum[] getByPriority(ProcessingPriority priority) {
        return java.util.Arrays.stream(values())
                .filter(serviceType -> serviceType.getPriority() == priority)
                .toArray(ServiceTypeEnum[]::new);
    }
    
    /**
     * 检查是否为关键服务
     */
    public boolean isCritical() {
        return this.priority == ProcessingPriority.CRITICAL;
    }
    
    /**
     * 检查是否可以异步处理
     */
    public boolean canProcessAsync() {
        return this.priority == ProcessingPriority.LOW;
    }
    
    /**
     * 检查是否为列表类型服务
     */
    public boolean isListService() {
        return this.category == ServiceCategory.LIST;
    }
    
    /**
     * 检查是否为单个服务
     */
    public boolean isSingleService() {
        return this.category == ServiceCategory.SINGLE;
    }
    
    /**
     * 检查是否为扩展服务
     */
    public boolean isExpandService() {
        return this.category == ServiceCategory.EXPAND;
    }
}
