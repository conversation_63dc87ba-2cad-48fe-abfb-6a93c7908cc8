{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\index.vue", "mtime": 1754646305888}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_company", "require", "_role", "_message", "_communication", "_agreementrecord", "_js<PERSON><PERSON>yin", "_interopRequireDefault", "_store", "_vueTreeselect", "_BlackList", "_communication2", "_agreementRecord", "_staffInfo", "_company2", "_contactor", "_location", "_role2", "_serviceType", "_departure", "_destination", "_cargoType", "_carrier", "_account", "_agreement", "_communication3", "_grade", "_achievement", "_remark", "_belong", "_auth", "_rich", "_elementUi", "_index", "_rsPaymentTitle", "_accountInfo", "_permission", "name", "dicts", "components", "AccountInfo", "Confirmed", "Treeselect", "communication", "communications", "BlackList", "belong", "company", "contactor", "staffInfo", "location", "role", "serviceType", "departure", "destination", "cargoType", "carrier", "account", "agreement", "agreementRecord", "grade", "achievement", "remark", "rsPaymentTitle", "props", "data", "loading", "showLeft", "showRight", "single", "multiple", "ids", "showSearch", "total", "add", "selectTwo", "size", "$store", "state", "app", "mergeList", "companyList", "staffList", "accountList", "communicationList", "agreementList", "belongList", "carrierList", "businessList", "temCarrierList", "locationOptions", "carrierIds", "companyInfo", "queryCarrierIds", "title", "merge", "openCompany", "openStaff", "openAccount", "openCommunication", "openAgreement", "openBlackList", "edit", "belongTo", "followUp", "queryBFStaffId", "queryBStaffId", "refreshTable", "ctotle", "atotle", "queryParams", "pageNum", "pageSize", "roleRich", "roleClient", "roleSupplier", "roleSupport", "companyQuery", "locationId", "idleStatus", "queryStaffId", "showPriority", "serviceTypeIds", "cargoTypeIds", "locationDepartureIds", "lineDepartureIds", "locationDestinationIds", "lineDestinationIds", "roleIds", "form", "agreementStartDate", "agreementEndDate", "settlementDate", "rules", "companyRow", "isLock", "showConfirm", "localStandard", "description", "computed", "columns", "get", "roleTypeId", "listSettings", "supplierSetting", "clientSetting", "commonLock", "psaConfirmed", "salesConfirmed", "basicLock", "opConfirmed", "agreementLock", "accConfirmed", "watch", "n", "formBelongTo", "formServiceTypeIds", "loadCarrier", "list", "undefined", "includes", "_iterator", "_createForOfIteratorHelper2", "default", "_step", "s", "done", "v", "value", "children", "length", "_iterator2", "_step2", "a", "_iterator3", "_step3", "b", "carrierId", "serviceTypeId", "push", "err", "e", "f", "_iterator4", "_step4", "c", "_iterator7", "_step7", "_iterator8", "_step8", "ch", "_iterator5", "_step5", "_iterator6", "_step6", "handleTermChange", "formReceiveTerm", "formPayTerm", "formReceiveWay", "formPayWay", "created", "_this", "getList", "then", "loadBusinesses", "loadSales", "methods", "parseTime", "receiveWay", "payWay", "receiveStandard", "receiveTerm", "substring", "payStandard", "payTerm", "_this2", "salesList", "redisList", "checkRole", "store", "dispatch", "_this3", "businessesList", "_this4", "serviceTypeCarriers", "querySame", "_this5", "companyShortName", "companyLocalName", "deleteStatus", "getInfoByStaffId", "user", "sid", "response", "_iterator9", "_step9", "_iterator10", "_step10", "_iterator11", "_step11", "staffId", "$refs", "validate", "valid", "res", "newRoleType", "oldRoleType", "$confirm", "confirmButtonText", "cancelButtonText", "type", "customClass", "_iterator12", "_step12", "_iterator13", "_step13", "_iterator14", "_step14", "deptId", "organizationIds", "msg", "toString", "indexOf", "messageOwner", "messageType", "messageFrom", "messageTitle", "split", "messageContent", "addMessage", "$message", "message", "success", "_iterator15", "_step15", "_iterator16", "_step16", "_iterator17", "_step17", "_this6", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "listCompany", "_objectSpread2", "permissionLevel", "permissionLevelList", "C", "rows", "isNaN", "stop", "staffNormalizer", "node", "l", "staff", "staffFamilyLocalName", "staffGivingLocalName", "roleLocalName", "pinyin", "getFullChars", "dept", "deptLocalName", "staffCode", "staffGivingEnName", "roleId", "id", "label", "isDisabled", "validateCompanyShortName", "rule", "callback", "hyphenCount", "Error", "carrierNormalizer", "carrierLocalName", "carrierEnName", "serviceLocalName", "serviceEnName", "carrierIntlCode", "cancel", "reset", "accountCancel", "locationDetail", "companyId", "companyIntlCode", "companyEnShortName", "companyEnName", "companyTaxCode", "companyPortIds", "salesConfirmedId", "salesConfirmedName", "salesConfirmedDate", "psaConfirmedId", "psaConfirmedName", "psaConfirmedDate", "accConfirmedId", "accConfirmedName", "accConfirmedDate", "opConfirmedId", "opConfirmedName", "opConfirmedDate", "rsPaymentTitles", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "setCompanyInfo", "handleAdd", "_iterator18", "_step18", "_iterator19", "_step19", "_iterator20", "_step20", "_iterator21", "_step21", "agreementCurrencyCode", "getReturn", "row", "_this7", "key", "getConnect", "listCommunication", "sqdCompanyId", "totle", "listAgreementrecord", "getBank", "companyLocation", "mainStaffId", "handleUpdate", "_this8", "auth", "<PERSON><PERSON><PERSON><PERSON>", "getCompany", "_iterator22", "_step22", "_iterator23", "_step23", "_iterator24", "_step24", "agreementDateRange", "formatter", "Intl", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "receiveCreditLimit", "toLocaleString", "replace", "format", "payCreditLimit", "submitForm", "_this9", "sqdReceiveTermsSummary", "sqdPayTermsSummary", "String", "startDate", "Date", "endDate", "Message", "creditCycleMonth", "updateCompany", "$modal", "msgSuccess", "info", "handleDelete", "_this10", "companyIds", "delCompany", "catch", "handleBlackList", "handleExport", "download", "concat", "getTime", "queryLocationId", "val", "getLocationId", "getSourceId", "sourceId", "sourceShortName", "getOrganizationIds", "getServiceTypeIds", "queryServiceTypeIds", "getCargoTypeIds", "queryCargoTypeIds", "getCompanyRoleIds", "queryCompanyRoleIds", "queryLocationDepartureIds", "getLineDepartureIds", "getLocationDestinationIds", "queryLineDepartureIds", "getLocationDepartureIds", "queryLocationDestinationIds", "getLineDestinationIds", "queryLineDestinationIds", "handleSelectBelongTo", "handleDeselectBelongTo", "handleSelectFollowUp", "handleDeselectFollowUp", "handleSelectBFStaffId", "cleanBFStaffId", "cleanBStaffId", "handleSelectBStaffId", "_this11", "handleSelectCarrierIds", "handleSelectQueryCarrierIds", "handleDeselectCarrierIds", "filter", "handleDeselectQueryCarrierIds", "refreshColumns", "_this12", "$nextTick", "handleMergeCompany", "handleMerge", "save", "del", "_this13", "mergeCompany", "dept<PERSON>ock", "_this14", "deptConfirmed", "deptConfirmedId", "deptConfirmedName", "deptConfirmedDate", "msgError", "financeLock", "_this15", "financeConfirmed", "financeConfirmedId", "financeConfirmedName", "financeConfirmedDate", "psaLock", "_this16", "opLock", "_this17", "getCurrencyCode", "getcreditLevel", "creditLevel", "getRsPaymentTitle", "_this18", "_callee2", "_iterator25", "_step25", "_iterator26", "_step26", "_iterator27", "_step27", "_callee2$", "_context2", "sent", "formatCreditLimit", "formatDisplayCreditLimit", "notation", "changeDate", "$forceUpdate", "exports", "_default"], "sources": ["src/views/system/company/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-if=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" :size=\"size\"\r\n                 label-width=\"35px\"\r\n        >\r\n          <el-form-item v-if=\"roleClient==='1'\" label=\"所属\" prop=\"queryStaffId\">\r\n            <treeselect v-model=\"queryBFStaffId\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"belongList\" :show-count=\"true\" placeholder=\"所属人\" style=\"width: 100%\"\r\n                        @input=\"cleanBFStaffId\" @open=\"loadSales\" @select=\"handleSelectBFStaffId\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"roleTypeId==='2'\" label=\"所属\" prop=\"queryStaffId\">\r\n            <treeselect v-model=\"queryBStaffId\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :options=\"businessList\"\r\n                        :show-count=\"true\" placeholder=\"所属人\" style=\"width: 100%\"\r\n                        @input=\"cleanBStaffId\" @open=\"loadBusinesses\" @select=\"handleSelectBStaffId\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <!--          <el-form-item v-if=\"roleTypeId==='1'\" label=\"闲置\">\r\n                      <el-select v-model=\"queryParams.idleStatus\" placeholder=\"是否闲置\" style=\"width: 100%\"\r\n                                 @change=\"handleQuery\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.sys_is_idle\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>-->\r\n          <el-form-item label=\"客户\" prop=\"companyQuery\">\r\n            <el-input v-model=\"queryParams.companyQuery\" clearable placeholder=\"客户名称/代码\" style=\"width: 100%\"\r\n                      @change=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <!--行政区域-->\r\n          <el-form-item label=\"地址\" prop=\"locationId\">\r\n            <location-select :multiple=\"false\" :pass=\"queryParams.locationId\"\r\n                             :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationId\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"信用\" prop=\"creditLevel\">\r\n            <tree-select :multiple=\"false\" :pass=\"queryParams.creditLevel\"\r\n                         :type=\"'creditLevel'\" style=\"width: 100%\"\r\n                         @return=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <!--供应商提供哪些服务如：海运、铁路等-->\r\n          <el-form-item label=\"服务\" prop=\"serviceTypeIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.serviceTypeIds\"\r\n                         :placeholder=\"'服务类型'\" :type=\"'serviceType'\" style=\"width: 100%\"\r\n                         :d-load=\"true\" @return=\"queryServiceTypeIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"启运\" prop=\"locationDepartureIds\">\r\n            <location-select :multiple=\"true\" :pass=\"queryParams.locationDepartureIds\"\r\n                             :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationDepartureIds\"\r\n            />\r\n          </el-form-item>\r\n          <!--目的区域-->\r\n          <el-form-item label=\"目的\" prop=\"locationDestinationIds\">\r\n            <location-select :multiple=\"true\" :pass=\"queryParams.locationDestinationIds\"\r\n                             :en=\"true\" :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationDestinationIds\"\r\n            />\r\n          </el-form-item>\r\n          <!--公司角色-->\r\n          <el-form-item label=\"角色\" prop=\"locationDestinationIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.roleIds\" :type=\"'companyRole'\"\r\n                         @return=\"queryCompanyRoleIds\"\r\n            />\r\n          </el-form-item>\r\n          <!--目的航线-->\r\n          <el-form-item label=\"航线\" prop=\"lineDestinationIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.lineDestinationIds\"\r\n                         :placeholder=\"'目的航线'\" :type=\"'line'\" style=\"width: 100%\"\r\n                         :d-load=\"true\" @return=\"queryLineDestinationIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"货物\" prop=\"cargoTypeIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.cargoTypeIds\" :placeholder=\"'货物特征'\"\r\n                         :d-load=\"true\" :type=\"'cargoType'\" style=\"width: 100%\" @return=\"queryCargoTypeIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"承运\" prop=\"carrierIds\">\r\n            <treeselect v-model=\"queryCarrierIds\" :disable-fuzzy-matching=\"true\" :disable-branch-nodes=\"true\"\r\n                        :flat=\"true\" :flatten-search-results=\"true\" :multiple=\"true\"\r\n                        :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\" @open=\"loadCarrier\"\r\n                        placeholder=\"优选承运人\" style=\"width: 100%\" @deselect=\"handleDeselectQueryCarrierIds\"\r\n                        @select=\"handleSelectQueryCarrierIds\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{ node.raw.carrier.carrierIntlCode }}\r\n                {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : \"\" }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"评级\" prop=\"creditLevel\">\r\n            <el-input v-model=\"queryParams.creditLevel\" placeholder=\"A~E\" style=\"width: 100%\"\r\n                      @change=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button :size=\"size\" icon=\"el-icon-search\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button :size=\"size\" icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:company:add']\"\r\n              :size=\"size\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:company:export']\"\r\n              :size=\"size\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              :disabled=\"single\"\r\n              :size=\"size\"\r\n              icon=\"el-icon-user-solid\"\r\n              type=\"info\"\r\n              @click=\"handleBlackList\"\r\n            >\r\n              拉黑\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              :disabled=\"selectTwo\"\r\n              :size=\"size\"\r\n              icon=\"el-icon-connection\"\r\n              @click=\"handleMergeCompany\"\r\n            >\r\n              合并\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :columns=\"columns\" :showSearch.sync=\"showSearch\"\r\n                         :types=\"this.roleTypeId=='2'?'supplier':this.roleTypeId=='1'?'client':''\"\r\n                         @queryTable=\"getList\" @refreshColumns=\"refreshColumns\"\r\n          />\r\n        </el-row>\r\n        <!--合并公司-->\r\n        <el-dialog\r\n          :close-on-click-modal=\"false\"\r\n          :modal-append-to-body=\"false\"\r\n          v-dialogDrag v-dialogDragWidth\r\n          title=\"选择保留公司\"\r\n          :visible.sync=\"merge\"\r\n          width=\"800px\"\r\n        >\r\n          <el-row v-if=\"mergeList.length>0\" :gutter=\"5\">\r\n            <el-col :span=\"12\">\r\n              <el-descriptions title=\"公司1\" direction=\"vertical\" border>\r\n                <el-descriptions-item label=\"简称\">{{ mergeList[0].companyShortName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"中文名\">{{ mergeList[0].companyLocalName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"英文名\">{{ mergeList[0].companyEnName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"选择\">\r\n                  <el-button @click=\"handleMerge(mergeList[0].companyId,mergeList[1].companyId)\">\r\n                    留下{{ mergeList[0].companyShortName }}\r\n                  </el-button>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-descriptions title=\"公司2\" direction=\"vertical\" border>\r\n                <el-descriptions-item label=\"简称\">{{ mergeList[1].companyShortName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"中文名\">{{ mergeList[1].companyLocalName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"英文名\">{{ mergeList[1].companyEnName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"选择\">\r\n                  <el-button @click=\"handleMerge(mergeList[1].companyId,mergeList[0].companyId)\">\r\n                    留下{{ mergeList[1].companyShortName }}\r\n                  </el-button>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </el-col>\r\n          </el-row>\r\n        </el-dialog>\r\n        <!--公司列表-->\r\n        <el-table v-if=\"refreshTable\" v-loading=\"loading\" :data=\"companyList\" border\r\n                  @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column\r\n            v-for=\"data in columns\"\r\n            v-if=\"data.visible\"\r\n            :key=\"data.key\"\r\n            :align=\"data.align\"\r\n            :label=\"data.label\"\r\n            :width=\"data.width\"\r\n            :show-overflow-tooltip=\"data.tooltip\"\r\n          >\r\n            <template v-slot=\"scope\">\r\n              <!--内置组件，根据:is动态加载组件-->\r\n              <!--渲染组件根据组件内部的return方法返回指针渲染内容-->\r\n              <component :is=\"data.prop\" :scope=\"scope\" @return=\"getReturn\"/>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"结款方式\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <h6 style=\"margin: 0;\">{{ }}</h6>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"额度\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <!--币种-->\r\n              <span style=\"margin: 0;\">{{\r\n                  (roleClient == 1 || roleRich == 1) ? scope.row.receiveCurrencyCode : scope.row.payCurrencyCode\r\n                }}</span>\r\n              <!--额度-->\r\n              <span style=\"margin: 0;\">{{\r\n                  formatDisplayCreditLimit(roleClient == 1 || roleRich == 1 ? scope.row.receiveCreditLimit : scope.row.payCreditLimit)\r\n                }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"录入人\" show-overflow-tooltip width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                <h6 class=\"text-display\" style=\"margin: 0;\">{{ scope.row.updateByName }}</h6>\r\n                <h6 class=\"text-display\" style=\"margin: 0;\">{{ parseTime(scope.row.updateTime, \"{y}.{m}.{d}\") }}</h6>\r\n              </div>\r\n\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"90px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-hasPermi=\"['system:company:edit']\"\r\n                         :size=\"size\" icon=\"el-icon-edit\" type=\"success\" style=\"margin: 0 3px\"\r\n                         @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button v-hasPermi=\"['system:company:remove']\"\r\n                         :size=\"size\" icon=\"el-icon-delete\" type=\"danger\" style=\"margin: 0\"\r\n                         @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 分页 -->\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改公司对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      append-to-body\r\n      v-dialogDrag v-dialogDragWidth\r\n      :title=\"title\"\r\n      :visible.sync=\"openCompany\"\r\n      width=\"55%\"\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"68px\" class=\"edit\">\r\n        <el-row :gutter=\"5\">\r\n          <el-col :span=\"12\">\r\n            <!--从属信息-->\r\n            <el-row v-if=\"roleClient==='1'\" :gutter=\"5\">\r\n              <el-divider content-position=\"left\">从属信息</el-divider>\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"业务员\">\r\n                    <treeselect v-model=\"belongTo\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                                :class=\"isLock ?'disable-form':''\" :disabled=\" isLock\"\r\n                                :flatten-search-results=\"true\"\r\n                                :normalizer=\"staffNormalizer\" :options=\"belongList\"\r\n                                :show-count=\"true\" class=\"sss\" placeholder=\"选择所属人\" @input=\"handleDeselectBelongTo\"\r\n                                @open=\"loadSales\" @select=\"handleSelectBelongTo\"\r\n                    >\r\n                      <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                        {{\r\n                          node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                        }}\r\n                      </div>\r\n                      <label slot=\"option-label\"\r\n                             slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                             :class=\"labelClassName\"\r\n                      >\r\n                        {{\r\n                          node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                        }}\r\n                        <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                      </label>\r\n                    </treeselect>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"业务助理\">\r\n                    <treeselect v-model=\"followUp\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                                :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                                :options=\"belongList\" :show-count=\"true\" class=\"sss\"\r\n                                :class=\"isLock ?'disable-form':''\" :disabled=\"isLock\"\r\n                                placeholder=\"业务员自己跟进的情况无须填写\"\r\n                                @input=\"handleDeselectFollowUp\" @open=\"loadSales\" @select=\"handleSelectFollowUp\"\r\n                    >\r\n                      <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                        {{\r\n                          node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                        }}\r\n                      </div>\r\n                      <label slot=\"option-label\"\r\n                             slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                             :class=\"labelClassName\"\r\n                      >\r\n                        {{\r\n                          node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                        }}\r\n                        <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                      </label>\r\n                    </treeselect>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"所属公司\">\r\n                    <tree-select\r\n                      :pass=\"form.companyBelongTo\" :placeholder=\"'收付路径'\"\r\n                      :class=\"isLock ?'disable-form':''\" :disabled=\"isLock\" :type=\"'rsPaymentTitle'\"\r\n                      @return=\"form.companyBelongTo=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n            <!--基本信息-->\r\n            <el-row>\r\n              <el-divider content-position=\"left\">基础资料</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"公司代码\" prop=\"companyTaxCode\">\r\n                    <el-input v-model=\"form.companyTaxCode\" class=\" disable-form\" disabled\r\n                              :class=\"'sss'\" placeholder=\"国际通用简称\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"税号\" prop=\"companyTaxCode\">\r\n                    <el-input v-model=\"form.taxNo\" :class=\"basicLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"basicLock || commonLock\"\r\n                              placeholder=\"公司税号/统一社会信用代码\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item\r\n                    :rules=\"[{ required: true, message: '简称不能为空', trigger: 'blur'},{validator: validateCompanyShortName, trigger: 'blur'}]\"\r\n                    label=\"公司简称\"\r\n                    prop=\"companyShortName\"\r\n                  >\r\n                    <el-input v-model=\"form.companyShortName\" :class=\"basicLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"basicLock || commonLock\"\r\n                              class=\"sss\" placeholder=\"公司简称\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item :rules=\"[{ required: true, message: '抬头不能为空', trigger: 'blur'}]\" label=\"发票抬头\"\r\n                                prop=\"companyLocalName\"\r\n                  >\r\n                    <el-input v-model=\"form.companyLocalName\" :class=\"basicLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"basicLock || commonLock\" placeholder=\"公司发票抬头\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"行政区域\" prop=\"locationId\">\r\n                    <location-select :load-options=\"locationOptions\" :multiple=\"false\" :pass=\"form.locationId\"\r\n                                     class=\"sss\" @return=\"getLocationId\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"详细地址\" prop=\"locationDetail\">\r\n                    <el-input v-model=\"form.locationDetail\"\r\n                              placeholder=\"详细地址信息\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"主联系人\" prop=\"mainStaffOfficialName\">\r\n                    <el-input v-model=\"form.mainStaffOfficialName\" class=\"sss\"\r\n                              placeholder=\"联系人名称\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"微信\" prop=\"staffWechat\">\r\n                    <el-input v-model=\"form.staffWechat\" class=\"sss\"\r\n                              placeholder=\"联系人微信\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"QQ\" prop=\"staffQq\">\r\n                    <el-input v-model=\"form.staffQq\"\r\n                              placeholder=\"联系人QQ\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"手机\" prop=\"staffMobile\">\r\n                    <el-input v-model=\"form.staffMobile\" class=\"sss\"\r\n                              placeholder=\"手机\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"邮箱\" prop=\"staffEmail\">\r\n                    <el-input v-model=\"form.staffEmail\"\r\n                              placeholder=\"联系人邮箱\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"Whatsapp\" prop=\"staffWhatsapp\">\r\n                    <el-input v-model=\"form.staffWhatsapp\" class=\"sss\"\r\n                              placeholder=\"联系人Whatsapp\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"联系方式\" prop=\"staffOtherContact\">\r\n                    <el-input v-model=\"form.staffOtherContact\"\r\n                              placeholder=\"员工其他联系方式\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n            <!--协议信息-->\r\n            <el-row>\r\n              <el-divider content-position=\"left\">协议信息</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"协议号\" prop=\"agreementNumber\">\r\n                    <el-input v-model=\"form.agreementNumber\" :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"agreementLock || commonLock\"\r\n                              class=\"sss\" placeholder=\"协议号\"\r\n\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"起讫日期\" prop=\"agreementStartDate\">\r\n                    <el-date-picker\r\n                      v-model=\"form.agreementDateRange\"\r\n                      :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                      :disabled=\"agreementLock || commonLock\" class=\"sss\"\r\n                      end-placeholder=\"结束日期\"\r\n                      range-separator=\"至\"\r\n                      start-placeholder=\"开始日期\"\r\n                      style=\"width: 100%;\"\r\n                      :default-time=\"['00:00:00', '23:59:59']\"\r\n                      type=\"daterange\"\r\n                      @input=\"changeDate\"\r\n                      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    >\r\n                    </el-date-picker>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"信用等级\" prop=\"creditLevel\">\r\n                    <tree-select :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :disabled=\"agreementLock || commonLock\" :pass=\"form.creditLevel\"\r\n                                 :type=\"'creditLevel'\" class=\"sss\"\r\n                                 @return=\"getcreditLevel\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item\r\n                    :label=\"roleClient==1|| roleRich==1?'收款方式':roleSupplier==1|| roleSupport==1?'付款方式':''\"\r\n                    prop=\"creditLevel\"\r\n                  >\r\n                    <tree-select :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :disabled=\"agreementLock || commonLock\"\r\n                                 v-if=\"roleClient==1|| roleRich==1\" :multiple=\"false\"\r\n                                 :pass=\"form.receiveWay\" :placeholder=\"'收款方式'\"\r\n                                 :flat=\"false\" :type=\"'releaseTypeCode'\"\r\n                                 class=\"sss\"\r\n                                 @returnData=\"form.receiveWay=$event.releaseTypeShortName\"\r\n                    />\r\n                    <tree-select v-if=\"roleSupplier==1|| roleSupport==1\"\r\n                                 :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :flat=\"false\" :multiple=\"false\"\r\n                                 :disabled=\"agreementLock || commonLock\" :pass=\"form.payWay\"\r\n                                 :placeholder=\"'付款方式'\" :type=\"'releaseTypeCode'\" class=\"sss\"\r\n                                 @returnData=\"form.payWay=$event.releaseTypeShortName\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item\r\n                    :label=\"roleClient==1|| roleRich==1?'收款信用':roleSupplier==1|| roleSupport==1?'付款信用':''\"\r\n                    prop=\"creditLevel\"\r\n                  >\r\n                    <el-col v-if=\"roleClient==1|| roleRich==1\">\r\n                      <el-col :span=\"9\">\r\n                        <tree-select :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                     :disabled=\"agreementLock || commonLock\"\r\n                                     :pass=\"form.receiveCurrencyCode\"\r\n                                     class=\" currency\" @return=\"form.receiveCurrencyCode=$event\"\r\n                                     :type=\"'currency'\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"15\">\r\n                        <el-input v-model=\"form.receiveCreditLimit\"\r\n                                  :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                  :disabled=\"agreementLock || commonLock\"\r\n                                  placeholder=\"信用额度(填入整数)\" @change=\"formatCreditLimit\"\r\n                                  class=\" limit\"\r\n                        />\r\n                      </el-col>\r\n                    </el-col>\r\n                    <el-col v-if=\"roleSupplier==1|| roleSupport==1\">\r\n                      <el-col :span=\"9\">\r\n                        <tree-select :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                     :disabled=\"agreementLock || commonLock\"\r\n                                     :pass=\"form.payCurrencyCode\"\r\n                                     class=\" currency\" @return=\"form.payCurrencyCode=$event\"\r\n                                     :type=\"'currency'\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"15\">\r\n                        <el-input v-model=\"form.payCreditLimit\" :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                  :disabled=\"agreementLock || commonLock\"\r\n                                  placeholder=\"信用额度(填入整数)\" @change=\"formatCreditLimit\"\r\n                                  class=\" limit\"\r\n                        />\r\n                      </el-col>\r\n                    </el-col>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"账单基准\" prop=\"creditLevel\">\r\n                    <tree-select v-if=\"roleClient==1 || roleRich==1\"\r\n                                 :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :disabled=\"agreementLock || commonLock\"\r\n                                 :flat=\"false\" :multiple=\"false\"\r\n                                 :pass=\"form.receiveStandard\" :placeholder=\"'收款账单基准'\"\r\n                                 :type=\"'commonInfoOfTime'\" class=\"sss\"\r\n                                 @return=\"form.receiveStandard=$event\"\r\n                                 @returnData=\"localStandard=$event.infoLocalName\"\r\n                    />\r\n                    <tree-select v-if=\"roleSupplier==1 || roleSupport==1\"\r\n                                 :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :disabled=\"agreementLock || commonLock\" :flat=\"false\"\r\n                                 :multiple=\"false\"\r\n                                 :pass=\"form.payStandard\" :placeholder=\"'付款账单基准'\"\r\n                                 :type=\"'commonInfoOfTime'\" class=\"sss\"\r\n                                 @returnData=\"form.payStandard=$event.infoShortName\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item\r\n                    :label=\"roleClient==1|| roleRich==1?'收款期限':roleSupplier==1|| roleSupport==1?'付款期限':''\"\r\n                    :class=\"agreementLock || commonLock ?'disable-form':''\" :disabled=\"agreementLock || commonLock\"\r\n                    prop=\"agreementNumber\"\r\n                  >\r\n                    <el-input v-if=\"roleClient==1|| roleRich==1\" v-model=\"form.receiveTerm\"\r\n                              :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"agreementLock || commonLock\" class=\"sss\"\r\n                              placeholder=\"收款期限(填写±n)\"\r\n                              @change=\"handleTermChange\"\r\n                    />\r\n                    <el-input v-if=\"roleSupplier==1|| roleSupport==1\" v-model=\"form.payTerm\"\r\n                              :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"agreementLock || commonLock\" class=\"sss\"\r\n                              placeholder=\"付款期限(填写±n)\"\r\n                              @change=\"handleTermChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-input :value=\"description\" class=\"disable-form\" disabled/>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-form-item label=\"协议备注\" prop=\"agreementRemark\">\r\n                  <el-input v-model=\"form.agreementRemark\" :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                            :disabled=\"agreementLock || commonLock\"\r\n                            placeholder=\"协议备注\"\r\n                  />\r\n                </el-form-item>\r\n              </el-row>\r\n            </el-row>\r\n            <!--审核意见-->\r\n            <el-row v-if=\"showConfirm\">\r\n              <el-divider content-position=\"left\">\r\n                审核意见\r\n              </el-divider>\r\n              <el-row>\r\n                <el-col v-if=\"this.roleClient==1\" :span=\"4\">\r\n                  <confirmed :id=\"'companyId'\" :confirmed=\"this.form.salesConfirmed==0\" :row=\"form\" :type=\"'sales'\"\r\n                             @lockMethod=\"updateCompany\"\r\n                  />\r\n                </el-col>\r\n                <el-col v-if=\"this.roleSupplier==1\" :span=\"4\">\r\n                  <!--商务锁定-->\r\n                  <confirmed :id=\"'companyId'\" :confirmed=\"this.form.psaConfirmed==0\" :row=\"form\" :type=\"'psa'\"\r\n                             @lockMethod=\"updateCompany\"\r\n                  />\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <!--操作锁定-->\r\n                  <confirmed :id=\"'companyId'\" :confirmed=\"this.form.opConfirmed==0\" :row=\"form\" :type=\"'op'\"\r\n                             @lockMethod=\"updateCompany\"\r\n                  />\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <!--财务锁定-->\r\n                  <confirmed :id=\"'companyId'\" :confirmed=\"this.form.accConfirmed==0\" :row=\"form\" :type=\"'acc'\"\r\n                             @lockMethod=\"updateCompany\"\r\n                  />\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <!--分类信息-->\r\n            <el-row v-if=\"roleClient==1\">\r\n              <el-divider content-position=\"left\">\r\n                开发分类\r\n              </el-divider>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"客户来源\">\r\n                    <tree-select :pass=\"form.sourceId\" :type=\"'source'\" class=\"sss\"\r\n                                 @returnData=\"getSourceId\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"开发权重\">\r\n                    <el-input v-model=\"form.developmentWeight\" class=\"sss\"\r\n                              placeholder=\"重要程度\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"业务标记\" prop=\"salesRemark\">\r\n                    <el-input v-model=\"form.salesRemark\" class=\"sss\"\r\n                              placeholder=\"重要程度\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n            <!--物流分类-->\r\n            <el-row>\r\n              <el-divider content-position=\"left\">\r\n                物流分类\r\n              </el-divider>\r\n              <el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <!--供应商公司的角色-->\r\n                    <el-form-item label=\"公司角色\" prop=\"roleIds\">\r\n                      <!--<tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"true\"\r\n                                   :pass=\"queryParams.roleIds\" :placeholder=\"'公司角色'\" :type=\"'companyRole'\"\r\n                                   class=\"sss\" style=\"width: 100%\" @return=\"queryCompanyRoleIds\"\r\n                      />-->\r\n                      <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.roleIds\" :type=\"'companyRole'\"\r\n                                   class=\"sss\" @return=\"getCompanyRoleIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"所属组织\" prop=\"organizationIds\">\r\n                      <tree-select :multiple=\"true\" :pass=\"form.organizationIds\" :type=\"'organization'\"\r\n                                   class=\"sss\" @return=\"getOrganizationIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"服务类型\" prop=\"serviceTypeIds\">\r\n                      <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.serviceTypeIds\" :type=\"'serviceType'\"\r\n                                   class=\"sss\" @return=\"getServiceTypeIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"货物类型\" prop=\"cargoTypeIds\">\r\n                      <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.cargoTypeIds\" :type=\"'cargoType'\"\r\n                                   class=\"sss\" @return=\"getCargoTypeIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"启运区域\" prop=\"locationDepartureIds\">\r\n                      <location-select :load-options=\"locationOptions\" :multiple=\"true\"\r\n                                       :pass=\"form.locationDepartureIds\" class=\"sss\"\r\n                                       @return=\"getLocationDepartureIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"优选承运\" prop=\"carrierIds\">\r\n                      <treeselect v-model=\"carrierIds\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                                  :flat=\"true\" :flatten-search-results=\"true\" :multiple=\"true\"\r\n                                  :normalizer=\"carrierNormalizer\"\r\n                                  :options=\"temCarrierList\" :show-count=\"true\" class=\"sss\"\r\n                                  placeholder=\"选择承运人\" @deselect=\"handleDeselectCarrierIds\" @open=\"loadCarrier\"\r\n                                  @select=\"handleSelectCarrierIds\"\r\n                      >\r\n                        <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                          {{ node.raw.carrier.carrierIntlCode }}\r\n                          {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : \"\" }}\r\n                        </div>\r\n                        <label slot=\"option-label\"\r\n                               slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                               :class=\"labelClassName\"\r\n                        >\r\n                          {{\r\n                            node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                          }}\r\n                          <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                        </label>\r\n                      </treeselect>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"目的区域\" prop=\"locationDestinationIds\">\r\n                      <location-select :en=\"true\" :load-options=\"locationOptions\"\r\n                                       :multiple=\"true\" :pass=\"form.locationDestinationIds\"\r\n                                       class=\"sss\"\r\n                                       @return=\"getLocationDestinationIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"目的航线\" prop=\"lineDestinationIds\">\r\n                      <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.lineDestinationIds\" :type=\"'line'\"\r\n                                   class=\"sss\" @return=\"getLineDestinationIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"备注信息\" prop=\"remark\">\r\n                    <el-input v-model=\"form.remark\" class=\"sss\"\r\n                              placeholder=\"备注信息\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n            <!--客户习惯-->\r\n            <el-row>\r\n              <el-divider content-position=\"left\">\r\n                客户习惯\r\n              </el-divider>\r\n              <el-col :span=\"24\">\r\n                <el-input v-model=\"form.partnerHabit\" :autosize=\"{ minRows: 15, maxRows: 10}\" maxlength=\"150\"\r\n                          placeholder=\"备注\" show-word-limit type=\"textarea\"\r\n                />\r\n              </el-col>\r\n            </el-row>\r\n          </el-col>\r\n        </el-row>\r\n\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button v-if=\"!edit\" :size=\"size\" type=\"primary\" @click=\"querySame\">查重</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <div>\r\n      <staff-info :company=\"companyInfo\" :load-options=\"staffList\" :open=\"openStaff\" @openStaffs=\"cancel\"/>\r\n      <!--    <account-info :company=\"companyInfo\" :load-options=\"accountList\" :open=\"openAccount\" :type=\"'supplier'\"\r\n                        @openAccounts=\"cancel\"\r\n          />-->\r\n      <account-info :company=\"companyInfo\" :is-lock=\"isLock\" :load-options=\"accountList\" :open=\"openAccount\"\r\n                    :type=\"'company'\"\r\n                    @openAccounts=\"accountCancel\"\r\n      />\r\n      <communications :company=\"companyInfo\" :load-options=\"communicationList\" :open=\"openCommunication\" :totle=\"ctotle\"\r\n                      @openCommunications=\"cancel\"\r\n      />\r\n      <agreement-record :company=\"companyInfo\" :load-options=\"agreementList\" :open=\"openAgreement\" :totle=\"atotle\"\r\n                        @openCommunications=\"cancel\"\r\n      />\r\n      <BlackList :company=\"companyInfo\" :open=\"openBlackList\" @openBlackList=\"cancel\"/>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  delCompany,\r\n  getBank,\r\n  getCompany,\r\n  getConnect,\r\n  listCompany,\r\n  mergeCompany,\r\n  querySame,\r\n  updateCompany\r\n} from \"@/api/system/company\"\r\nimport {getInfoByStaffId} from \"@/api/system/role\"\r\nimport {addMessage} from \"@/api/system/message\"\r\nimport {listCommunication} from \"@/api/system/communication\"\r\nimport {listAgreementrecord} from \"@/api/system/agreementrecord\"\r\n\r\nimport pinyin from \"js-pinyin\"\r\nimport store from \"@/store\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.min.css\"\r\n\r\nimport BlackList from \"@/components/BlackList\"\r\nimport communications from \"@/views/system/communication\"\r\nimport agreementRecord from \"@/views/system/agreementRecord\"\r\nimport staffInfo from \"@/views/system/company/staffInfo\"\r\n\r\nimport company from \"@/views/system/company/company\"\r\nimport contactor from \"@/views/system/company/contactor\"\r\nimport location from \"@/views/system/company/location\"\r\nimport role from \"@/views/system/company/role\"\r\nimport serviceType from \"@/views/system/company/serviceType\"\r\nimport departure from \"@/views/system/company/departure\"\r\nimport destination from \"@/views/system/company/destination\"\r\nimport cargoType from \"@/views/system/company/cargoType\"\r\nimport carrier from \"@/views/system/company/carrier\"\r\nimport account from \"@/views/system/company/account\"\r\nimport agreement from \"@/views/system/company/agreement\"\r\nimport communication from \"@/views/system/company/communication\"\r\nimport grade from \"@/views/system/company/grade\"\r\nimport achievement from \"@/views/system/company/achievement\"\r\nimport remark from \"@/views/system/company/remark\"\r\nimport belong from \"@/views/system/company/belong\"\r\nimport auth from \"@/plugins/auth\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport {Message} from \"element-ui\"\r\nimport Confirmed from \"@/components/Confirmed/index.vue\"\r\nimport rsPaymentTitle from \"@/views/system/company/rsPaymentTitle.vue\"\r\nimport AccountInfo from \"@/views/system/company/accountInfo.vue\"\r\nimport {checkRole} from \"@/utils/permission\"\r\n\r\nexport default {\r\n  name: \"Company\",\r\n  dicts: [\"sys_is_idle\"],\r\n  components: {\r\n    AccountInfo,\r\n    Confirmed,\r\n    Treeselect,\r\n    communication,\r\n    communications,\r\n    BlackList,\r\n    belong,\r\n    company,\r\n    contactor,\r\n    staffInfo,\r\n    location,\r\n    role,\r\n    serviceType,\r\n    departure,\r\n    destination,\r\n    cargoType,\r\n    carrier,\r\n    account,\r\n    agreement,\r\n    agreementRecord,\r\n    grade,\r\n    achievement,\r\n    remark,\r\n    rsPaymentTitle\r\n  },\r\n  props: [\"roleTypeId\", \"roleRich\", \"roleClient\", \"roleSupplier\", \"roleSupport\"],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      add: false,\r\n      selectTwo: true,\r\n      size: this.$store.state.app.size || \"mini\",\r\n      // 公司表格数据\r\n      mergeList: [],\r\n      companyList: [],\r\n      staffList: [],\r\n      accountList: [],\r\n      communicationList: [],\r\n      agreementList: [],\r\n      belongList: [],\r\n      carrierList: [],\r\n      businessList: [],\r\n      temCarrierList: [],\r\n      locationOptions: [],\r\n      carrierIds: [],\r\n      companyInfo: {},\r\n      queryCarrierIds: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      merge: false,\r\n      openCompany: false,\r\n      openStaff: false,\r\n      openAccount: false,\r\n      openCommunication: false,\r\n      openAgreement: false,\r\n      openBlackList: false,\r\n      edit: false,\r\n      belongTo: null,\r\n      followUp: null,\r\n      queryBFStaffId: null,\r\n      queryBStaffId: null,\r\n      refreshTable: true,\r\n      ctotle: null,\r\n      atotle: null,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        // roleTypeId: this.roleTypeId,\r\n        roleRich: this.roleRich ? this.roleRich : null,\r\n        roleClient: this.roleClient ? this.roleClient : null,\r\n        roleSupplier: this.roleSupplier ? this.roleSupplier : null,\r\n        roleSupport: this.roleSupport ? this.roleSupport : null,\r\n        companyQuery: null,\r\n        locationId: null,\r\n        idleStatus: null,\r\n        queryStaffId: null,\r\n        showPriority: null,\r\n        serviceTypeIds: [],\r\n        cargoTypeIds: [],\r\n        locationDepartureIds: [],\r\n        lineDepartureIds: [],\r\n        locationDestinationIds: [],\r\n        lineDestinationIds: [],\r\n        roleIds: [],\r\n        carrierIds: []\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        agreementStartDate: null,\r\n        agreementEndDate: null,\r\n        settlementDate: null\r\n      },\r\n      // 表单校验\r\n      rules: {},\r\n      // 当前点击修改时选中的记录\r\n      companyRow: null,\r\n      isLock: true,\r\n      showConfirm: false,\r\n      localStandard: null,\r\n      description: null\r\n    }\r\n  },\r\n  computed: {\r\n    columns: {\r\n      get() {\r\n        if (this.roleTypeId == \"2\") {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n        if (this.roleTypeId == \"1\") {\r\n          return this.$store.state.listSettings.clientSetting\r\n        }\r\n        if (this.roleClient) {\r\n          return this.$store.state.listSettings.clientSetting\r\n        }\r\n        if (this.roleSupplier) {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n        if (this.roleRich) {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n        if (this.roleSupport) {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n      }\r\n    },\r\n    commonLock() {\r\n      return (this.form.psaConfirmed == 1 || this.form.salesConfirmed == 1) ? true : false\r\n    },\r\n    basicLock() {\r\n      return this.form.opConfirmed == 1 ? true : false\r\n    },\r\n    agreementLock() {\r\n      return this.form.accConfirmed == 1 ? true : false\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    queryStaffId() {\r\n      this.queryParams.queryStaffId = this.queryStaffId\r\n    },\r\n    \"form.belongTo\"() {\r\n      if (this.form.belongTo == this.form.followUp) {\r\n        this.form.followUp = 0\r\n        this.followUp = null\r\n      }\r\n    },\r\n    \"form.serviceTypeIds\"(n) {\r\n      this.loadCarrier()\r\n      let list = []\r\n      if (this.carrierList != undefined && n != null && n.includes(-1)) {\r\n        this.temCarrierList = this.carrierList\r\n        for (const v of this.carrierList) {\r\n          if (v.children != undefined && v.children.length > 0) {\r\n            for (const a of v.children) {\r\n              if (a.children != undefined && a.children.length > 0) {\r\n                for (const b of a.children) {\r\n                  if (this.form.carrierIds != null && this.form.carrierIds.includes(b.carrier.carrierId) && !this.carrierIds.includes(b.serviceTypeId)) {\r\n                    this.carrierIds.push(b.serviceTypeId)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      if (this.carrierList != undefined && n != null && !n.includes(-1)) {\r\n        for (const c of this.carrierList) {\r\n          if (n != null && n != undefined) {\r\n            for (const s of n) {\r\n              if (c.serviceTypeId == s) {\r\n                list.push(c)\r\n              }\r\n              if (c.children != undefined && c.children.length > 0) {\r\n                for (const ch of c.children) {\r\n                  if (ch.serviceTypeId == s) {\r\n                    list.push(ch)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.temCarrierList = list\r\n        if (this.temCarrierList.length > 0) {\r\n          for (const v of this.temCarrierList) {\r\n            if (v.children != undefined && v.children.length > 0) {\r\n              for (const a of v.children) {\r\n                if (this.form.carrierIds != null && this.form.carrierIds.includes(a.carrier.carrierId) && !this.carrierIds.includes(a.serviceTypeId)) {\r\n                  this.carrierIds.push(a.serviceTypeId)\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    form() {\r\n      if (this.form.salesConfirmed == 1 || this.form.accConfirmed == 1 || this.form.psaConfirmed == 1 || this.form.opConfirmed == 1) {\r\n        this.isLock = true\r\n      } else {\r\n        this.isLock = false\r\n      }\r\n    },\r\n    localStandard(v) {\r\n      this.handleTermChange()\r\n    },\r\n    \"form.receiveTerm\"(v) {\r\n      this.handleTermChange()\r\n    },\r\n    \"form.payTerm\"(v) {\r\n      this.handleTermChange()\r\n    },\r\n    \"form.receiveWay\"(v) {\r\n      this.handleTermChange()\r\n    },\r\n    \"form.payWay\"(v) {\r\n      this.handleTermChange()\r\n    }\r\n  },\r\n  created() {\r\n    this.getList().then(() => {\r\n      this.loadBusinesses()\r\n      this.loadCarrier()\r\n      this.loadSales()\r\n    })\r\n  },\r\n  methods: {\r\n    parseTime,\r\n    handleTermChange(v) {\r\n      if (this.form.receiveWay === \"月结\" || this.form.payWay === \"月结\") {\r\n        if (this.roleClient == 1 || this.roleRich == 1) {\r\n          this.description = (this.form.receiveStandard ? this.form.receiveStandard : \"\") + (this.form.receiveTerm ? (this.form.receiveTerm.substring(0, 1) === \"-\" ? (\"的下个月\" + this.form.receiveTerm.substring(1, 3) + \"号之后\") : (\"的下个月\" + this.form.receiveTerm.substring(1, 3) + \"号之前\")) : \"前\")\r\n        } else {\r\n          this.description = (this.form.payStandard ? this.form.payStandard : \"\") + (this.form.payTerm ? (this.form.payTerm.substring(0, 1) === \"-\" ? (\"的下个月\" + this.form.payTerm.substring(1, 3) + \"号之后\") : (\"的下个月\" + this.form.payTerm.substring(1, 3) + \"号之前\")) : \"前\")\r\n        }\r\n      } else {\r\n        if (this.roleClient == 1 || this.roleRich == 1) {\r\n          this.description = (this.form.receiveStandard ? this.form.receiveStandard : \"\") + (this.form.receiveTerm ? (this.form.receiveTerm.substring(0, 1) === \"-\" ? this.form.receiveTerm.substring(1, 3) + \"天前\" : \"后\" + this.form.receiveTerm.substring(1, 3) + \"天内\") : \"前\")\r\n        } else {\r\n          this.description = (this.form.payStandard ? this.form.payStandard : \"\") + (this.form.payTerm ? (this.form.payTerm.substring(0, 1) === \"-\" ? this.form.payTerm.substring(1, 3) + \"天前\" : \"后\" + this.form.payTerm.substring(1, 3) + \"天内\") : \"前\")\r\n        }\r\n      }\r\n    },\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        if (checkRole([\"Operator\"])) {\r\n          store.dispatch(\"getSalesList\").then(() => {\r\n            this.belongList = this.$store.state.data.salesList\r\n          })\r\n        } else {\r\n          store.dispatch(\"getSalesListC\").then(() => {\r\n            this.belongList = this.$store.state.data.salesList\r\n          })\r\n        }\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        store.dispatch(\"getBusinessesList\").then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    loadCarrier() {\r\n      if (this.$store.state.data.serviceTypeCarriers.length == 0 || this.$store.state.data.redisList.serviceTypeCarriers) {\r\n        store.dispatch(\"getServiceTypeCarriersList\").then(() => {\r\n          this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n        })\r\n      } else {\r\n        this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n      }\r\n    },\r\n    // 查重\r\n    querySame() {\r\n      // 初始化请求数据,特别注意deleteStatus设置未1,后端会对这个值的数据做重复校验\r\n      let data = {\r\n        cargoTypeIds: [],\r\n        locationDepartureIds: [],\r\n        locationDestinationIds: [],\r\n        lineDepartureIds: [],\r\n        lineDestinationIds: [],\r\n        companyShortName: this.form.companyShortName,\r\n        companyLocalName: this.form.companyLocalName,\r\n        serviceTypeIds: this.form.serviceTypeIds,\r\n        roleTypeId: this.roleTypeId,\r\n        roleRich: this.roleRich ? this.roleRich : null,\r\n        roleClient: this.roleClient ? this.roleClient : null,\r\n        roleSupplier: this.roleSupplier ? this.roleSupplier : null,\r\n        roleSupport: this.roleSupport ? this.roleSupport : null,\r\n        belongTo: this.form.belongTo,\r\n        followUp: this.form.followUp,\r\n        deleteStatus: 1\r\n      }\r\n      getInfoByStaffId(this.$store.state.user.sid).then(response => {\r\n        data.cargoTypeIds = response.cargoTypeIds\r\n        data.locationDepartureIds = response.locationDepartureIds\r\n        data.locationDestinationIds = response.locationDestinationIds\r\n        data.lineDepartureIds = response.lineDepartureIds\r\n        data.lineDestinationIds = response.lineDestinationIds\r\n      })\r\n      // 公司所属\r\n      if (data.belongTo == null) {\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == this.$store.state.user.sid) {\r\n                      data.belongTo = this.$store.state.user.sid\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          querySame(data).then(response => {\r\n            let res = response.data\r\n            if (res != undefined) {\r\n              let newRoleType = this.roleRich ? \"瑞旗分支\" : this.roleClient ? \"客户\" : this.roleSupplier ? \"供应商\" : this.roleSupport ? \"支持\" : \"\"\r\n              let oldRoleType = res.roleRich == 1 ? \"瑞旗分支\" : res.roleClient == 1 ? \"客户\" : res.roleSupplier == 1 ? \"供应商\" : res.roleSupport == 1 ? \"支持\" : \"\"\r\n              this.$confirm(res.deleteStatus == 0 ? \"此公司已存在，角色为\" + oldRoleType + \"，新增角色为\" + newRoleType + \",是否确认新增\" : \"存在重复数据，但已删除，是否重新读取\", \"提示\", {\r\n                confirmButtonText: \"确定\",\r\n                cancelButtonText: \"取消\",\r\n                type: \"warning\",\r\n                customClass: \"modal-confirm\"\r\n              }).then(() => {\r\n                res.deleteStatus = 0\r\n                this.form = res\r\n                this.form.roleTypeId = this.roleTypeId\r\n                if (this.belongList != undefined) {\r\n                  for (const a of this.belongList) {\r\n                    if (a.children != undefined) {\r\n                      for (const b of a.children) {\r\n                        if (b.children != undefined) {\r\n                          for (const c of b.children) {\r\n                            if (c.staffId == response.data.belongTo) {\r\n                              this.belongTo = c.deptId\r\n                            }\r\n                            if (c.staffId == response.data.followUp) {\r\n                              this.followUp = c.deptId\r\n                            }\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n                this.form.roleIds = response.roleIds\r\n                this.form.serviceTypeIds = response.serviceTypeIds\r\n                this.form.cargoTypeIds = response.cargoTypeIds\r\n                this.form.lineDepartureIds = response.lineDepartureIds\r\n                this.form.locationDepartureIds = response.locationDepartureIds\r\n                this.form.lineDestinationIds = response.lineDestinationIds\r\n                this.form.locationDestinationIds = response.locationDestinationIds\r\n                this.form.carrierIds = response.carrierIds\r\n                // this.form.organizationIds = response.companyOrganizationIds\r\n                this.form.organizationIds = response.organizationIds\r\n                this.locationOptions = response.locationOptions\r\n                this.openCompany = true\r\n                this.title = \"修改公司信息\"\r\n                this.loading = false\r\n              })\r\n            }\r\n\r\n            // 错误处理,弹出提示后点击确定发送请求更新公司信息\r\n            if (response.msg.toString().indexOf(\"Error\") > -1) {\r\n              this.$confirm(response.msg, \"提示\", {\r\n                confirmButtonText: \"确定\",\r\n                cancelButtonText: \"取消\",\r\n                type: \"warning\",\r\n                customClass: \"modal-confirm\"\r\n              }).then(() => {\r\n                let data = {\r\n                  messageOwner: 1,\r\n                  messageType: 3,\r\n                  messageFrom: null,\r\n                  messageTitle: this.$store.state.user.name.split(\" \")[1] + \"请求更新公司\",\r\n                  messageContent: response.msg\r\n                }\r\n                addMessage(data).then(response => {\r\n                  this.$message({\r\n                    type: \"success\",\r\n                    message: \"已发送请求!\"\r\n                  })\r\n                })\r\n              })\r\n            }\r\n\r\n            // 新增验证通过(通过将请求数据中的deleteStatus设置未0)\r\n            if (response.msg.toString().indexOf(\"Success\") > -1) {\r\n              this.$confirm(\"不存在重复的公司简称，是否确定新增客户？\", \"提示\", {\r\n                confirmButtonText: \"确定\",\r\n                cancelButtonText: \"取消\",\r\n                type: \"warning\",\r\n                customClass: \"modal-confirm\"\r\n              }).then(() => {\r\n                data.deleteStatus = 0\r\n                // 真正开始新增\r\n                querySame(data).then(response => {\r\n                  if (response.data) {\r\n                    this.$message.success(\"添加成功\")\r\n                    this.form = response.data\r\n                    this.form.roleTypeId = this.roleTypeId\r\n                    if (this.belongList != undefined) {\r\n                      for (const a of this.belongList) {\r\n                        if (a.children != undefined) {\r\n                          for (const b of a.children) {\r\n                            if (b.children != undefined) {\r\n                              for (const c of b.children) {\r\n                                if (c.staffId == response.data.belongTo) {\r\n                                  this.belongTo = c.deptId\r\n                                }\r\n                                if (c.staffId == response.data.followUp) {\r\n                                  this.followUp = c.deptId\r\n                                }\r\n                              }\r\n                            }\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n                    this.form.roleIds = response.roleIds\r\n                    this.form.serviceTypeIds = response.serviceTypeIds\r\n                    this.form.cargoTypeIds = response.cargoTypeIds\r\n                    this.form.lineDepartureIds = response.lineDepartureIds\r\n                    this.form.locationDepartureIds = response.locationDepartureIds\r\n                    this.form.lineDestinationIds = response.lineDestinationIds\r\n                    this.form.locationDestinationIds = response.locationDestinationIds\r\n                    this.form.carrierIds = response.carrierIds\r\n                    // this.form.organizationIds = response.companyOrganizationIds\r\n                    this.form.organizationIds = response.organizationIds\r\n                    this.locationOptions = response.locationOptions\r\n                    this.openCompany = true\r\n                    this.title = \"修改公司信息\"\r\n                    this.loading = false\r\n                  }\r\n                })\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /** 查询公司列表 */\r\n    async getList() {\r\n      this.loading = true\r\n      await listCompany({\r\n        ...this.queryParams,\r\n        permissionLevel: this.$store.state.user.permissionLevelList.C\r\n      }).then(response => {\r\n        this.companyList = response.rows\r\n        if (!isNaN(response.total)) {\r\n          this.total = response.total\r\n        }\r\n        this.loading = false\r\n      })\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    },\r\n    validateCompanyShortName(rule, value, callback) {\r\n\r\n      // 检查 value 是否为空或非字符串\r\n      if (value || typeof value === \"string\") {\r\n        // 检查是否包含多个中横线\r\n        const hyphenCount = value.split(\"-\").length - 1 // 通过 split 分割来统计中横线数量\r\n        if (hyphenCount > 1) {\r\n          return callback(new Error(\"只能包含一个中横线\"))\r\n        }\r\n      }\r\n      // 验证通过\r\n      callback()\r\n    },\r\n    carrierNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (!node.carrier || (node.carrier.carrierLocalName == null && node.carrier.carrierEnName == null)) {\r\n        l = node.serviceLocalName + \" \" + node.serviceEnName + \",\" + pinyin.getFullChars(node.serviceLocalName)\r\n      } else {\r\n        l = (node.carrier.carrierIntlCode != null ? node.carrier.carrierIntlCode : \"\") + \" \" + (node.carrier.carrierEnName != null ? node.carrier.carrierEnName : \"\") + \" \" + (node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : \"\") + \",\" + pinyin.getFullChars((node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : \"\"))\r\n      }\r\n      return {\r\n        id: node.serviceTypeId,\r\n        label: l,\r\n        children: node.children\r\n      }\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.openCompany = false\r\n      this.openAccount = false\r\n      this.openStaff = false\r\n      this.openCommunication = false\r\n      this.openAgreement = false\r\n      this.openBlackList = false\r\n      this.add = false\r\n      this.merge = false\r\n      this.edit = false\r\n      this.reset()\r\n      this.belongTo = null\r\n      this.followUp = null\r\n      // this.getList()\r\n    },\r\n    accountCancel() {\r\n      this.openAccount = false\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.belongTo = null\r\n      this.followUp = null\r\n      this.carrierIds = []\r\n      this.form = {\r\n        belongTo: null,\r\n        followUp: null,\r\n        carrierIds: null,\r\n        locationDetail: null,\r\n        companyId: null,\r\n        companyIntlCode: null,\r\n        companyShortName: null,\r\n        companyEnShortName: null,\r\n        companyLocalName: null,\r\n        companyEnName: null,\r\n        companyTaxCode: null,\r\n        roleIds: null,\r\n        serviceTypeIds: null,\r\n        cargoTypeIds: null,\r\n        locationDepartureIds: null,\r\n        lineDepartureIds: null,\r\n        locationDestinationIds: null,\r\n        lineDestinationIds: null,\r\n        organizationIds: null,\r\n        companyPortIds: null,\r\n        roleTypeId: this.roleTypeId,\r\n        roleRich: this.roleRich ? this.roleRich : null,\r\n        roleClient: this.roleClient ? this.roleClient : null,\r\n        roleSupplier: this.roleSupplier ? this.roleSupplier : null,\r\n        roleSupport: this.roleSupport ? this.roleSupport : null,\r\n        locationId: null,\r\n        salesConfirmed: 0,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        psaConfirmed: 0,\r\n        psaConfirmedId: null,\r\n        psaConfirmedName: null,\r\n        psaConfirmedDate: null,\r\n        accConfirmed: 0,\r\n        accConfirmedId: null,\r\n        accConfirmedName: null,\r\n        accConfirmedDate: null,\r\n        opConfirmed: 0,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        remark: null,\r\n        rsPaymentTitles: []\r\n      }\r\n      this.carrierIds = []\r\n      this.companyRow = null\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.queryBStaffId = null\r\n      this.queryParams.locationId = null\r\n      this.queryParams.serviceTypeIds = null\r\n      this.queryParams.cargoTypeIds = null\r\n      this.queryParams.locationDepartureIds = null\r\n      this.queryParams.lineDepartureIds = null\r\n      this.queryParams.locationDestinationIds = null\r\n      this.queryParams.lineDestinationIds = null\r\n      this.queryParams.organizationIds = null\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.companyId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n      this.selectTwo = selection.length != 2\r\n      if (selection.length == 1) {\r\n        this.setCompanyInfo(selection[0])\r\n      }\r\n      if (selection.length == 2) {\r\n        this.mergeList = selection\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.edit = false\r\n      this.form.belongTo = null\r\n      this.openCompany = true\r\n      this.title = \"新增公司信息\"\r\n      this.form.serviceTypeIds = []\r\n      this.temCarrierList = this.carrierList\r\n      if (this.temCarrierList != undefined && this.form.serviceTypeIds != null) {\r\n        for (const a of this.temCarrierList) {\r\n          // this.form.serviceTypeIds.push(a.serviceTypeId)\r\n        }\r\n      }\r\n      this.add = true\r\n      if (this.belongList != undefined) {\r\n        for (const a of this.belongList) {\r\n          if (a.children != undefined) {\r\n            for (const b of a.children) {\r\n              if (b.children != undefined) {\r\n                for (const c of b.children) {\r\n                  if (c.staffId == this.$store.state.user.sid) {\r\n                    this.belongTo = c.deptId\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      // 币种默认为人民币RMB\r\n      this.form.agreementCurrencyCode = \"RMB\"\r\n      this.showConfirm = false\r\n    },\r\n    getReturn(row) {\r\n      if (row.key == \"contactor\") {\r\n        this.setCompanyInfo(row.value)\r\n        getConnect(row.value.companyId).then(response => {\r\n          this.staffList = response.staffList\r\n          this.openStaff = true\r\n        })\r\n      }\r\n      if (row.key == \"communication\") {\r\n        this.setCompanyInfo(row.value)\r\n        listCommunication({sqdCompanyId: row.value.companyId}).then(response => {\r\n          this.communicationList = response.rows\r\n          this.ctotle = response.totle\r\n          this.openCommunication = true\r\n        })\r\n      }\r\n      if (row.key == \"agreement\") {\r\n        this.setCompanyInfo(row.value)\r\n        listAgreementrecord({sqdCompanyId: row.value.companyId}).then(response => {\r\n          this.agreementList = response.rows\r\n          this.atotle = response.totle\r\n          this.openAgreement = true\r\n        })\r\n      }\r\n      if (row.key == \"account\") {\r\n        this.setCompanyInfo(row.value)\r\n        getBank(row.value.companyId).then(response => {\r\n          this.accountList = response.accountList\r\n          this.openAccount = true\r\n        })\r\n      }\r\n    },\r\n    // 设置客户信息\r\n    setCompanyInfo(row) {\r\n      this.companyInfo = {\r\n        companyId: row.companyId != null ? row.companyId : \"\",\r\n        companyTaxCode: row.companyTaxCode != null ? row.companyTaxCode : \"\",\r\n        companyShortName: row.companyShortName != null ? row.companyShortName : \"\",\r\n        companyEnShortName: row.companyEnShortName != null ? row.companyEnShortName : \"\",\r\n        companyLocalName: row.companyLocalName != null ? row.companyLocalName : \"\",\r\n        companyEnName: row.companyEnName != null ? row.companyEnName : \"\",\r\n        companyLocation: row.locationId != null ? row.locationId : \"\",\r\n        companyIntlCode: row.companyIntlCode != null ? row.companyIntlCode : \"\",\r\n        mainStaffId: row.staff != null ? row.staff.staffId : \"\"\r\n      }\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      this.loading = true\r\n      this.edit = true\r\n      this.companyRow = row\r\n      this.add = auth.hasPermi(\"system:client:distribute\")\r\n      const companyId = row.companyId || this.ids\r\n      this.showConfirm = true\r\n      getCompany(companyId).then(response => {\r\n        this.form = response.data\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.belongTo) {\r\n                      this.belongTo = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.followUp) {\r\n                      this.followUp = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.form.roleIds = response.roleIds\r\n        this.form.serviceTypeIds = response.serviceTypeIds\r\n        this.form.cargoTypeIds = response.cargoTypeIds\r\n        this.form.lineDepartureIds = response.lineDepartureIds\r\n        this.form.locationDepartureIds = response.locationDepartureIds\r\n        this.form.lineDestinationIds = response.lineDestinationIds\r\n        this.form.locationDestinationIds = response.locationDestinationIds\r\n        this.form.carrierIds = response.carrierIds\r\n        // this.form.organizationIds = response.companyOrganizationIds\r\n        this.form.organizationIds = response.organizationIds\r\n        this.locationOptions = response.locationOptions\r\n        this.openCompany = true\r\n        this.title = \"修改公司信息\"\r\n        // this.form.rsPaymentTitles = response.data.rsPaymentTitle ? response.data.rsPaymentTitle.split(',') : []\r\n        this.loading = false\r\n\r\n        if (response.data.agreementStartDate !== null && response.data.agreementEndDate !== null) {\r\n          this.form.agreementDateRange = []\r\n          this.form.agreementDateRange.push(response.data.agreementStartDate)\r\n          this.form.agreementDateRange.push(response.data.agreementEndDate)\r\n        }\r\n\r\n        const formatter = new Intl.NumberFormat(\"en-US\", {\r\n          style: \"decimal\",\r\n          minimumFractionDigits: 2,\r\n          maximumFractionDigits: 2\r\n        })\r\n        if ((this.roleClient == 1 || this.roleRich == 1) && response.data.receiveCreditLimit !== null) {\r\n          this.form.receiveCreditLimit = response.data.receiveCreditLimit.toLocaleString(\"en-US\")\r\n          this.form.receiveCreditLimit = this.form.receiveCreditLimit ? this.form.receiveCreditLimit.replace(/,/g, \"\") : 0\r\n          this.form.receiveCreditLimit = formatter.format(this.form.receiveCreditLimit)\r\n        }\r\n        if ((this.roleSupplier == 1 || this.roleSupport == 1) && response.data.payCreditLimit !== null) {\r\n          this.form.payCreditLimit = response.data.payCreditLimit.toLocaleString(\"en-US\")\r\n          this.form.payCreditLimit = this.form.payCreditLimit ? this.form.payCreditLimit.replace(/,/g, \"\") : 0\r\n          this.form.payCreditLimit = formatter.format(this.form.payCreditLimit)\r\n        }\r\n\r\n      })\r\n\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        // 收付款描述\r\n        if (this.roleClient == 1 || this.roleRich == 1) {\r\n          this.form.sqdReceiveTermsSummary = this.description\r\n        }\r\n        if (this.roleClient == 1 || this.roleRich == 1) {\r\n          this.form.sqdPayTermsSummary = this.description\r\n        }\r\n        // 公司类型\r\n        this.form.roleRich = this.roleRich ? this.roleRich : null\r\n        this.form.roleClient = this.roleClient ? this.roleClient : null\r\n        this.form.roleSupplier = this.roleSupplier ? this.roleSupplier : null\r\n        this.form.roleSupport = this.roleSupport ? this.roleSupport : null\r\n        // 转换额度显示\r\n        this.form.receiveCreditLimit = this.form.receiveCreditLimit ? String(this.form.receiveCreditLimit).replace(/,/g, \"\") : 0\r\n        this.form.payCreditLimit = this.form.payCreditLimit ? String(this.form.payCreditLimit).replace(/,/g, \"\") : 0\r\n        if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {\r\n          this.form.agreementStartDate = this.form.agreementDateRange[0]\r\n          this.form.agreementEndDate = this.form.agreementDateRange[1]\r\n        }\r\n        // 判断日期开始时间是否大于结束时间\r\n        let startDate = new Date(this.form.agreementStartDate)\r\n        let endDate = new Date(this.form.agreementEndDate)\r\n        if (startDate > endDate) {\r\n          Message({\r\n            message: \"协议开始时间不能大于结束时间\",\r\n            type: \"error\"\r\n          })\r\n          return\r\n        }\r\n        // 信用周期要为整数\r\n        if (this.form.creditCycleMonth != null && this.form.creditCycleMonth % 1 != 0) {\r\n          Message({\r\n            message: \"信用周期必须为整数\",\r\n            type: \"error\"\r\n          })\r\n          return\r\n        }\r\n        if (valid) {\r\n          if (this.form.companyId != null) {\r\n            updateCompany(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.openCompany = false\r\n              this.getList()\r\n            })\r\n            this.reset()\r\n          } else {\r\n            this.$message.info(\"未查重，先对简称查重吧\")\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      row.roleRich = this.roleRich ? this.roleRich : null\r\n      row.roleClient = this.roleClient ? this.roleClient : null\r\n      row.roleSupplier = this.roleSupplier ? this.roleSupplier : null\r\n      row.roleSupport = this.roleSupport ? this.roleSupport : null\r\n\r\n      const companyIds = row.companyId || this.ids\r\n      this.$confirm(\"是否确认删除公司编号为\\\"\" + companyIds + \"\\\"的数据项？\", \"提示\", {customClass: \"modal-confirm\"}).then(function () {\r\n        return delCompany(row)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    handleBlackList() {\r\n      this.openBlackList = true\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/company/export\", {\r\n        ...this.queryParams\r\n      }, `company_${new Date().getTime()}.xlsx`)\r\n    },\r\n    queryLocationId(val) {\r\n      this.queryParams.locationId = val\r\n      this.handleQuery()\r\n    },\r\n    getLocationId(val) {\r\n      this.form.locationId = val\r\n    },\r\n    getSourceId(val) {\r\n      this.form.sourceId = val.sourceShortName\r\n    },\r\n    getOrganizationIds(val) {\r\n      this.form.organizationIds = val\r\n    },\r\n    getServiceTypeIds(val) {\r\n      this.form.serviceTypeIds = val\r\n      if (val == undefined) {\r\n        this.carrierIds = null\r\n        this.form.carrierIds = null\r\n      }\r\n    },\r\n    queryServiceTypeIds(val) {\r\n      this.queryParams.serviceTypeIds = val\r\n      this.handleQuery()\r\n    },\r\n    getCargoTypeIds(val) {\r\n      this.form.cargoTypeIds = val\r\n    },\r\n    queryCargoTypeIds(val) {\r\n      this.queryParams.cargoTypeIds = val\r\n      this.handleQuery()\r\n    },\r\n    getCompanyRoleIds(val) {\r\n      this.form.roleIds = val\r\n    },\r\n    queryCompanyRoleIds(val) {\r\n      this.queryParams.roleIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLocationDepartureIds(val) {\r\n      this.queryParams.locationDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    getLineDepartureIds(val) {\r\n      this.form.lineDepartureIds = val\r\n    },\r\n    getLocationDestinationIds(val) {\r\n      this.form.locationDestinationIds = val\r\n    },\r\n    queryLineDepartureIds(val) {\r\n      this.queryParams.lineDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    getLocationDepartureIds(val) {\r\n      this.form.locationDepartureIds = val\r\n    },\r\n    queryLocationDestinationIds(val) {\r\n      this.queryParams.locationDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    getLineDestinationIds(val) {\r\n      this.form.lineDestinationIds = val\r\n    },\r\n    queryLineDestinationIds(val) {\r\n      this.queryParams.lineDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    handleSelectBelongTo(node) {\r\n      this.form.belongTo = node.staffId\r\n    },\r\n    handleDeselectBelongTo(v) {\r\n      if (v == undefined) {\r\n        this.form.belongTo = 0\r\n        this.belongTo = null\r\n      }\r\n    },\r\n    handleSelectFollowUp(node) {\r\n      this.form.followUp = node.staffId\r\n    },\r\n    handleDeselectFollowUp(value) {\r\n      if (value == undefined) {\r\n        this.form.followUp = 0\r\n        this.followUp = null\r\n      }\r\n    },\r\n    handleSelectBFStaffId(node) {\r\n      this.queryParams.queryBFStaffId = node.staffId\r\n      this.handleQuery()\r\n    },\r\n    cleanBFStaffId(val) {\r\n      if (val == undefined) {\r\n        this.queryParams.queryBFStaffId = null\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    cleanBStaffId(val) {\r\n      if (val == undefined) {\r\n        this.queryParams.queryBStaffId = null\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    handleSelectBStaffId(node) {\r\n      this.queryParams.queryBStaffId = node.staffId\r\n      getInfoByStaffId(node.staffId).then(response => {\r\n        this.queryParams.cargoTypeIds = response.cargoTypeIds\r\n        this.queryParams.serviceTypeIds = response.serviceTypeIds\r\n        this.queryParams.locationDepartureIds = response.locationDepartureIds\r\n        this.queryParams.lineDepartureIds = response.lineDepartureIds\r\n        this.queryParams.locationDestinationIds = response.locationDestinationIds\r\n        this.queryParams.lineDestinationIds = response.lineDestinationIds\r\n        this.locationOptions = response.locationOptions\r\n        this.handleQuery()\r\n      })\r\n    },\r\n    handleSelectCarrierIds(node) {\r\n      this.form.carrierIds.push(node.carrier.carrierId)\r\n    },\r\n    handleSelectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds.push(node.carrier.carrierId)\r\n      this.handleQuery()\r\n    },\r\n    handleDeselectCarrierIds(node) {\r\n      this.form.carrierIds = this.form.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n    },\r\n    handleDeselectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds = this.queryParams.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n      this.handleQuery()\r\n    },\r\n    refreshColumns() {\r\n      this.refreshTable = false\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true\r\n      })\r\n    },\r\n    handleMergeCompany() {\r\n      this.merge = true\r\n    },\r\n    handleMerge(save, del) {\r\n      mergeCompany(save, del).then(response => {\r\n        this.$message.success(response.msg)\r\n        this.merge = false\r\n        this.getList()\r\n      })\r\n    },\r\n    deptLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.deptConfirmed = this.form.deptConfirmed == 0 ? 1 : 0\r\n        this.form.deptConfirmedId = this.$store.state.user.sid\r\n        this.form.deptConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.deptConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    financeLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.financeConfirmed = this.form.financeConfirmed == 0 ? 1 : 0\r\n        this.form.financeConfirmedId = this.$store.state.user.sid\r\n        this.form.financeConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.financeConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    psaLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.psaConfirmed = this.form.psaConfirmed == 0 ? 1 : 0\r\n        this.form.psaConfirmedId = this.$store.state.user.sid\r\n        this.form.psaConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.psaConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    opLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.opConfirmed = this.form.opConfirmed === 0 ? 1 : 0\r\n        this.form.opConfirmedId = this.$store.state.user.sid\r\n        this.form.opConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.opConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    getCurrencyCode(val) {\r\n      this.form.agreementCurrencyCode = val\r\n    },\r\n    getcreditLevel(val) {\r\n      this.form.creditLevel = val\r\n    },\r\n    getRsPaymentTitle(val) {\r\n      this.form.rsPaymentTitle = val\r\n    },\r\n    async updateCompany(form) {\r\n      // TODO 只更新有修改的字段\r\n      // console.log(Object.assign(this.form, form))\r\n\r\n      // this.form.creditLimit = this.form.creditLimit.replace(/,/g, '')\r\n\r\n      if (this.roleClient == 1 || this.roleRich == 1) {\r\n        this.form.receiveCreditLimit = this.form.receiveCreditLimit ? this.form.receiveCreditLimit.replace(/,/g, \"\") : 0\r\n      }\r\n      if (this.roleSupplier == 1 || this.roleSupport == 1) {\r\n        this.form.payCreditLimit = this.form.payCreditLimit ? this.form.payCreditLimit.replace(/,/g, \"\") : 0\r\n      }\r\n\r\n      if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {\r\n        this.form.agreementStartDate = this.form.agreementDateRange[0]\r\n        this.form.agreementEndDate = this.form.agreementDateRange[1]\r\n      }\r\n\r\n      await updateCompany(form)\r\n      this.$modal.msgSuccess(\"修改成功\")\r\n      let response = await getCompany(form.companyId)\r\n      // 更新信息\r\n      // this.$nextTick(() => {\r\n      this.form = response.data\r\n      if (this.belongList != undefined) {\r\n        for (const a of this.belongList) {\r\n          if (a.children != undefined) {\r\n            for (const b of a.children) {\r\n              if (b.children != undefined) {\r\n                for (const c of b.children) {\r\n                  if (c.staffId == response.data.belongTo) {\r\n                    this.belongTo = c.deptId\r\n                  }\r\n                  if (c.staffId == response.data.followUp) {\r\n                    this.followUp = c.deptId\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      this.form.roleIds = response.roleIds\r\n      this.form.serviceTypeIds = response.serviceTypeIds\r\n      this.form.cargoTypeIds = response.cargoTypeIds\r\n      this.form.lineDepartureIds = response.lineDepartureIds\r\n      this.form.locationDepartureIds = response.locationDepartureIds\r\n      this.form.lineDestinationIds = response.lineDestinationIds\r\n      this.form.locationDestinationIds = response.locationDestinationIds\r\n      this.form.carrierIds = response.carrierIds\r\n      // this.form.organizationIds = response.companyOrganizationIds\r\n      this.form.organizationIds = response.organizationIds\r\n      this.locationOptions = response.locationOptions\r\n      this.openCompany = true\r\n      this.title = \"修改公司信息\"\r\n      this.loading = false\r\n\r\n      const formatter = new Intl.NumberFormat(\"en-US\", {\r\n        style: \"decimal\",\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      })\r\n      /* this.form.creditLimit = response.data.creditLimit.toLocaleString('en-US')\r\n      this.form.creditLimit = this.form.creditLimit.replace(/,/g, '')\r\n      this.form.creditLimit = formatter.format(this.form.creditLimit) */\r\n\r\n      if (this.roleClient == 1 || this.roleRich == 1) {\r\n        this.form.receiveCreditLimit = response.data.receiveCreditLimit.toLocaleString(\"en-US\")\r\n        this.form.receiveCreditLimit = this.form.receiveCreditLimit ? String(this.form.receiveCreditLimit).replace(/,/g, \"\") : 0\r\n        this.form.receiveCreditLimit = formatter.format(this.form.receiveCreditLimit)\r\n      }\r\n      if (this.roleSupplier == 1 || this.roleSupport == 1) {\r\n        this.form.payCreditLimit = response.data.payCreditLimit.toLocaleString(\"en-US\")\r\n        this.form.payCreditLimit = this.form.payCreditLimit ? String(this.form.payCreditLimit).replace(/,/g, \"\") : 0\r\n        this.form.payCreditLimit = formatter.format(this.form.payCreditLimit)\r\n      }\r\n\r\n      // 更新日期\r\n      this.form.agreementDateRange = (response.data.agreementStartDate != null && response.data.agreementEndDate != null) ? [response.data.agreementStartDate, response.data.agreementEndDate] : []\r\n      /* if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {\r\n        this.form.agreementStartDate = this.form.agreementDateRange[0]\r\n        this.form.agreementEndDate = this.form.agreementDateRange[1]\r\n      } */\r\n\r\n      // })\r\n      this.form.salesConfirmed = response.data.salesConfirmed\r\n      this.form.psaConfirmed = response.data.psaConfirmed\r\n      this.form.opConfirmed = response.data.opConfirmed\r\n      this.form.accConfirmed = response.data.accConfirmed\r\n    },\r\n    formatCreditLimit() {\r\n      if (this.form.receiveCreditLimit != null || this.form.payCreditLimit != null) {\r\n        const formatter = new Intl.NumberFormat(\"en-US\", {\r\n          style: \"decimal\",\r\n          minimumFractionDigits: 2,\r\n          maximumFractionDigits: 2\r\n        })\r\n        if (this.roleClient == 1) {\r\n          this.form.receiveCreditLimit = String(this.form.receiveCreditLimit).replace(/,/g, \"\")\r\n          this.form.receiveCreditLimit = formatter.format(this.form.receiveCreditLimit)\r\n        }\r\n        if (this.roleSupplier == 1) {\r\n          this.form.payCreditLimit = String(this.form.payCreditLimit).replace(/,/g, \"\")\r\n          this.form.payCreditLimit = formatter.format(this.form.payCreditLimit)\r\n        }\r\n\r\n      }\r\n    },\r\n    formatDisplayCreditLimit(value) {\r\n      const formatter = new Intl.NumberFormat(\"en-US\", {\r\n        notation: \"compact\"\r\n      })\r\n      return formatter.format(value)\r\n    },\r\n    changeDate() {\r\n      this.$forceUpdate()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n\r\n<style scoped lang=\"scss\">\r\n.sss {\r\n  //padding-left: 5px !important;\r\n  padding-right: 15px !important;\r\n  width: 100%;\r\n}\r\n\r\n.creditLimit {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n.limit {\r\n  flex: 3\r\n}\r\n\r\n.currency {\r\n  flex: 1\r\n}\r\n\r\n.confirm {\r\n  display: flex;\r\n  flex-direction: row;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.box-card {\r\n  width: 20%;\r\n  flex-wrap: wrap;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAs1BA,IAAAA,QAAA,GAAAC,OAAA;AAUA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,gBAAA,GAAAJ,OAAA;AAEA,IAAAK,SAAA,GAAAC,sBAAA,CAAAN,OAAA;AACA,IAAAO,MAAA,GAAAD,sBAAA,CAAAN,OAAA;AACA,IAAAQ,cAAA,GAAAF,sBAAA,CAAAN,OAAA;AACAA,OAAA;AAEA,IAAAS,UAAA,GAAAH,sBAAA,CAAAN,OAAA;AACA,IAAAU,eAAA,GAAAJ,sBAAA,CAAAN,OAAA;AACA,IAAAW,gBAAA,GAAAL,sBAAA,CAAAN,OAAA;AACA,IAAAY,UAAA,GAAAN,sBAAA,CAAAN,OAAA;AAEA,IAAAa,SAAA,GAAAP,sBAAA,CAAAN,OAAA;AACA,IAAAc,UAAA,GAAAR,sBAAA,CAAAN,OAAA;AACA,IAAAe,SAAA,GAAAT,sBAAA,CAAAN,OAAA;AACA,IAAAgB,MAAA,GAAAV,sBAAA,CAAAN,OAAA;AACA,IAAAiB,YAAA,GAAAX,sBAAA,CAAAN,OAAA;AACA,IAAAkB,UAAA,GAAAZ,sBAAA,CAAAN,OAAA;AACA,IAAAmB,YAAA,GAAAb,sBAAA,CAAAN,OAAA;AACA,IAAAoB,UAAA,GAAAd,sBAAA,CAAAN,OAAA;AACA,IAAAqB,QAAA,GAAAf,sBAAA,CAAAN,OAAA;AACA,IAAAsB,QAAA,GAAAhB,sBAAA,CAAAN,OAAA;AACA,IAAAuB,UAAA,GAAAjB,sBAAA,CAAAN,OAAA;AACA,IAAAwB,eAAA,GAAAlB,sBAAA,CAAAN,OAAA;AACA,IAAAyB,MAAA,GAAAnB,sBAAA,CAAAN,OAAA;AACA,IAAA0B,YAAA,GAAApB,sBAAA,CAAAN,OAAA;AACA,IAAA2B,OAAA,GAAArB,sBAAA,CAAAN,OAAA;AACA,IAAA4B,OAAA,GAAAtB,sBAAA,CAAAN,OAAA;AACA,IAAA6B,KAAA,GAAAvB,sBAAA,CAAAN,OAAA;AACA,IAAA8B,KAAA,GAAA9B,OAAA;AACA,IAAA+B,UAAA,GAAA/B,OAAA;AACA,IAAAgC,MAAA,GAAA1B,sBAAA,CAAAN,OAAA;AACA,IAAAiC,eAAA,GAAA3B,sBAAA,CAAAN,OAAA;AACA,IAAAkC,YAAA,GAAA5B,sBAAA,CAAAN,OAAA;AACA,IAAAmC,WAAA,GAAAnC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAoC,IAAA;EACAC,KAAA;EACAC,UAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,SAAA,EAAAA,cAAA;IACAC,UAAA,EAAAA,sBAAA;IACAC,aAAA,EAAAA,uBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,OAAA,EAAAA,iBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,IAAA,EAAAA,cAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,OAAA,EAAAA,gBAAA;IACAC,OAAA,EAAAA,gBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,cAAA,EAAAA;EACA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,GAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACAC,GAAA;MACAC,SAAA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;MACA;MACAI,SAAA;MACAC,WAAA;MACAC,SAAA;MACAC,WAAA;MACAC,iBAAA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,YAAA;MACAC,cAAA;MACAC,eAAA;MACAC,UAAA;MACAC,WAAA;MACAC,eAAA;MACA;MACAC,KAAA;MACA;MACAC,KAAA;MACAC,WAAA;MACAC,SAAA;MACAC,WAAA;MACAC,iBAAA;MACAC,aAAA;MACAC,aAAA;MACAC,IAAA;MACAC,QAAA;MACAC,QAAA;MACAC,cAAA;MACAC,aAAA;MACAC,YAAA;MACAC,MAAA;MACAC,MAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACA;QACAC,QAAA,OAAAA,QAAA,QAAAA,QAAA;QACAC,UAAA,OAAAA,UAAA,QAAAA,UAAA;QACAC,YAAA,OAAAA,YAAA,QAAAA,YAAA;QACAC,WAAA,OAAAA,WAAA,QAAAA,WAAA;QACAC,YAAA;QACAC,UAAA;QACAC,UAAA;QACAC,YAAA;QACAC,YAAA;QACAC,cAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,gBAAA;QACAC,sBAAA;QACAC,kBAAA;QACAC,OAAA;QACArC,UAAA;MACA;MACA;MACAsC,IAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,cAAA;MACA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACAC,MAAA;MACAC,WAAA;MACAC,aAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA;MACAC,GAAA,WAAAA,IAAA;QACA,SAAAC,UAAA;UACA,YAAAjE,MAAA,CAAAC,KAAA,CAAAiE,YAAA,CAAAC,eAAA;QACA;QACA,SAAAF,UAAA;UACA,YAAAjE,MAAA,CAAAC,KAAA,CAAAiE,YAAA,CAAAE,aAAA;QACA;QACA,SAAA/B,UAAA;UACA,YAAArC,MAAA,CAAAC,KAAA,CAAAiE,YAAA,CAAAE,aAAA;QACA;QACA,SAAA9B,YAAA;UACA,YAAAtC,MAAA,CAAAC,KAAA,CAAAiE,YAAA,CAAAC,eAAA;QACA;QACA,SAAA/B,QAAA;UACA,YAAApC,MAAA,CAAAC,KAAA,CAAAiE,YAAA,CAAAC,eAAA;QACA;QACA,SAAA5B,WAAA;UACA,YAAAvC,MAAA,CAAAC,KAAA,CAAAiE,YAAA,CAAAC,eAAA;QACA;MACA;IACA;IACAE,UAAA,WAAAA,WAAA;MACA,YAAAjB,IAAA,CAAAkB,YAAA,cAAAlB,IAAA,CAAAmB,cAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,YAAApB,IAAA,CAAAqB,WAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAAtB,IAAA,CAAAuB,YAAA;IACA;EACA;EACAC,KAAA;IACAjF,UAAA,WAAAA,WAAAkF,CAAA;MACA,IAAAA,CAAA;QACA,KAAAtF,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;IACAqD,YAAA,WAAAA,aAAA;MACA,KAAAV,WAAA,CAAAU,YAAA,QAAAA,YAAA;IACA;IACA,0BAAAmC,aAAA;MACA,SAAA1B,IAAA,CAAA1B,QAAA,SAAA0B,IAAA,CAAAzB,QAAA;QACA,KAAAyB,IAAA,CAAAzB,QAAA;QACA,KAAAA,QAAA;MACA;IACA;IACA,gCAAAoD,mBAAAF,CAAA;MACA,KAAAG,WAAA;MACA,IAAAC,IAAA;MACA,SAAAvE,WAAA,IAAAwE,SAAA,IAAAL,CAAA,YAAAA,CAAA,CAAAM,QAAA;QACA,KAAAvE,cAAA,QAAAF,WAAA;QAAA,IAAA0E,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACA,KAAA5E,WAAA;UAAA6E,KAAA;QAAA;UAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAP,CAAA,IAAAY,IAAA;YAAA,IAAAC,CAAA,GAAAH,KAAA,CAAAI,KAAA;YACA,IAAAD,CAAA,CAAAE,QAAA,IAAAV,SAAA,IAAAQ,CAAA,CAAAE,QAAA,CAAAC,MAAA;cAAA,IAAAC,UAAA,OAAAT,2BAAA,CAAAC,OAAA,EACAI,CAAA,CAAAE,QAAA;gBAAAG,MAAA;cAAA;gBAAA,KAAAD,UAAA,CAAAN,CAAA,MAAAO,MAAA,GAAAD,UAAA,CAAAjB,CAAA,IAAAY,IAAA;kBAAA,IAAAO,CAAA,GAAAD,MAAA,CAAAJ,KAAA;kBACA,IAAAK,CAAA,CAAAJ,QAAA,IAAAV,SAAA,IAAAc,CAAA,CAAAJ,QAAA,CAAAC,MAAA;oBAAA,IAAAI,UAAA,OAAAZ,2BAAA,CAAAC,OAAA,EACAU,CAAA,CAAAJ,QAAA;sBAAAM,MAAA;oBAAA;sBAAA,KAAAD,UAAA,CAAAT,CAAA,MAAAU,MAAA,GAAAD,UAAA,CAAApB,CAAA,IAAAY,IAAA;wBAAA,IAAAU,CAAA,GAAAD,MAAA,CAAAP,KAAA;wBACA,SAAAvC,IAAA,CAAAtC,UAAA,iBAAAsC,IAAA,CAAAtC,UAAA,CAAAqE,QAAA,CAAAgB,CAAA,CAAAxH,OAAA,CAAAyH,SAAA,WAAAtF,UAAA,CAAAqE,QAAA,CAAAgB,CAAA,CAAAE,aAAA;0BACA,KAAAvF,UAAA,CAAAwF,IAAA,CAAAH,CAAA,CAAAE,aAAA;wBACA;sBACA;oBAAA,SAAAE,GAAA;sBAAAN,UAAA,CAAAO,CAAA,CAAAD,GAAA;oBAAA;sBAAAN,UAAA,CAAAQ,CAAA;oBAAA;kBACA;gBACA;cAAA,SAAAF,GAAA;gBAAAT,UAAA,CAAAU,CAAA,CAAAD,GAAA;cAAA;gBAAAT,UAAA,CAAAW,CAAA;cAAA;YACA;UACA;QAAA,SAAAF,GAAA;UAAAnB,SAAA,CAAAoB,CAAA,CAAAD,GAAA;QAAA;UAAAnB,SAAA,CAAAqB,CAAA;QAAA;MACA;MACA,SAAA/F,WAAA,IAAAwE,SAAA,IAAAL,CAAA,aAAAA,CAAA,CAAAM,QAAA;QAAA,IAAAuB,UAAA,OAAArB,2BAAA,CAAAC,OAAA,EACA,KAAA5E,WAAA;UAAAiG,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAlB,CAAA,MAAAmB,MAAA,GAAAD,UAAA,CAAA7B,CAAA,IAAAY,IAAA;YAAA,IAAAmB,CAAA,GAAAD,MAAA,CAAAhB,KAAA;YACA,IAAAd,CAAA,YAAAA,CAAA,IAAAK,SAAA;cAAA,IAAA2B,UAAA,OAAAxB,2BAAA,CAAAC,OAAA,EACAT,CAAA;gBAAAiC,MAAA;cAAA;gBAAA,KAAAD,UAAA,CAAArB,CAAA,MAAAsB,MAAA,GAAAD,UAAA,CAAAhC,CAAA,IAAAY,IAAA;kBAAA,IAAAD,CAAA,GAAAsB,MAAA,CAAAnB,KAAA;kBACA,IAAAiB,CAAA,CAAAP,aAAA,IAAAb,CAAA;oBACAP,IAAA,CAAAqB,IAAA,CAAAM,CAAA;kBACA;kBACA,IAAAA,CAAA,CAAAhB,QAAA,IAAAV,SAAA,IAAA0B,CAAA,CAAAhB,QAAA,CAAAC,MAAA;oBAAA,IAAAkB,UAAA,OAAA1B,2BAAA,CAAAC,OAAA,EACAsB,CAAA,CAAAhB,QAAA;sBAAAoB,MAAA;oBAAA;sBAAA,KAAAD,UAAA,CAAAvB,CAAA,MAAAwB,MAAA,GAAAD,UAAA,CAAAlC,CAAA,IAAAY,IAAA;wBAAA,IAAAwB,EAAA,GAAAD,MAAA,CAAArB,KAAA;wBACA,IAAAsB,EAAA,CAAAZ,aAAA,IAAAb,CAAA;0BACAP,IAAA,CAAAqB,IAAA,CAAAW,EAAA;wBACA;sBACA;oBAAA,SAAAV,GAAA;sBAAAQ,UAAA,CAAAP,CAAA,CAAAD,GAAA;oBAAA;sBAAAQ,UAAA,CAAAN,CAAA;oBAAA;kBACA;gBACA;cAAA,SAAAF,GAAA;gBAAAM,UAAA,CAAAL,CAAA,CAAAD,GAAA;cAAA;gBAAAM,UAAA,CAAAJ,CAAA;cAAA;YACA;UACA;QAAA,SAAAF,GAAA;UAAAG,UAAA,CAAAF,CAAA,CAAAD,GAAA;QAAA;UAAAG,UAAA,CAAAD,CAAA;QAAA;QACA,KAAA7F,cAAA,GAAAqE,IAAA;QACA,SAAArE,cAAA,CAAAiF,MAAA;UAAA,IAAAqB,UAAA,OAAA7B,2BAAA,CAAAC,OAAA,EACA,KAAA1E,cAAA;YAAAuG,MAAA;UAAA;YAAA,KAAAD,UAAA,CAAA1B,CAAA,MAAA2B,MAAA,GAAAD,UAAA,CAAArC,CAAA,IAAAY,IAAA;cAAA,IAAAC,EAAA,GAAAyB,MAAA,CAAAxB,KAAA;cACA,IAAAD,EAAA,CAAAE,QAAA,IAAAV,SAAA,IAAAQ,EAAA,CAAAE,QAAA,CAAAC,MAAA;gBAAA,IAAAuB,UAAA,OAAA/B,2BAAA,CAAAC,OAAA,EACAI,EAAA,CAAAE,QAAA;kBAAAyB,MAAA;gBAAA;kBAAA,KAAAD,UAAA,CAAA5B,CAAA,MAAA6B,MAAA,GAAAD,UAAA,CAAAvC,CAAA,IAAAY,IAAA;oBAAA,IAAAO,EAAA,GAAAqB,MAAA,CAAA1B,KAAA;oBACA,SAAAvC,IAAA,CAAAtC,UAAA,iBAAAsC,IAAA,CAAAtC,UAAA,CAAAqE,QAAA,CAAAa,EAAA,CAAArH,OAAA,CAAAyH,SAAA,WAAAtF,UAAA,CAAAqE,QAAA,CAAAa,EAAA,CAAAK,aAAA;sBACA,KAAAvF,UAAA,CAAAwF,IAAA,CAAAN,EAAA,CAAAK,aAAA;oBACA;kBACA;gBAAA,SAAAE,GAAA;kBAAAa,UAAA,CAAAZ,CAAA,CAAAD,GAAA;gBAAA;kBAAAa,UAAA,CAAAX,CAAA;gBAAA;cACA;YACA;UAAA,SAAAF,GAAA;YAAAW,UAAA,CAAAV,CAAA,CAAAD,GAAA;UAAA;YAAAW,UAAA,CAAAT,CAAA;UAAA;QACA;MACA;IACA;IACArD,IAAA,WAAAA,KAAA;MACA,SAAAA,IAAA,CAAAmB,cAAA,cAAAnB,IAAA,CAAAuB,YAAA,cAAAvB,IAAA,CAAAkB,YAAA,cAAAlB,IAAA,CAAAqB,WAAA;QACA,KAAAf,MAAA;MACA;QACA,KAAAA,MAAA;MACA;IACA;IACAE,aAAA,WAAAA,cAAA8B,CAAA;MACA,KAAA4B,gBAAA;IACA;IACA,6BAAAC,gBAAA7B,CAAA;MACA,KAAA4B,gBAAA;IACA;IACA,yBAAAE,YAAA9B,CAAA;MACA,KAAA4B,gBAAA;IACA;IACA,4BAAAG,eAAA/B,CAAA;MACA,KAAA4B,gBAAA;IACA;IACA,wBAAAI,WAAAhC,CAAA;MACA,KAAA4B,gBAAA;IACA;EACA;EACAK,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA,GAAAC,IAAA;MACAF,KAAA,CAAAG,cAAA;MACAH,KAAA,CAAA5C,WAAA;MACA4C,KAAA,CAAAI,SAAA;IACA;EACA;EACAC,OAAA;IACAC,SAAA,EAAAA,eAAA;IACAZ,gBAAA,WAAAA,iBAAA5B,CAAA;MACA,SAAAtC,IAAA,CAAA+E,UAAA,kBAAA/E,IAAA,CAAAgF,MAAA;QACA,SAAA/F,UAAA,cAAAD,QAAA;UACA,KAAAyB,WAAA,SAAAT,IAAA,CAAAiF,eAAA,QAAAjF,IAAA,CAAAiF,eAAA,eAAAjF,IAAA,CAAAkF,WAAA,QAAAlF,IAAA,CAAAkF,WAAA,CAAAC,SAAA,+BAAAnF,IAAA,CAAAkF,WAAA,CAAAC,SAAA,+BAAAnF,IAAA,CAAAkF,WAAA,CAAAC,SAAA;QACA;UACA,KAAA1E,WAAA,SAAAT,IAAA,CAAAoF,WAAA,QAAApF,IAAA,CAAAoF,WAAA,eAAApF,IAAA,CAAAqF,OAAA,QAAArF,IAAA,CAAAqF,OAAA,CAAAF,SAAA,+BAAAnF,IAAA,CAAAqF,OAAA,CAAAF,SAAA,+BAAAnF,IAAA,CAAAqF,OAAA,CAAAF,SAAA;QACA;MACA;QACA,SAAAlG,UAAA,cAAAD,QAAA;UACA,KAAAyB,WAAA,SAAAT,IAAA,CAAAiF,eAAA,QAAAjF,IAAA,CAAAiF,eAAA,eAAAjF,IAAA,CAAAkF,WAAA,QAAAlF,IAAA,CAAAkF,WAAA,CAAAC,SAAA,sBAAAnF,IAAA,CAAAkF,WAAA,CAAAC,SAAA,2BAAAnF,IAAA,CAAAkF,WAAA,CAAAC,SAAA;QACA;UACA,KAAA1E,WAAA,SAAAT,IAAA,CAAAoF,WAAA,QAAApF,IAAA,CAAAoF,WAAA,eAAApF,IAAA,CAAAqF,OAAA,QAAArF,IAAA,CAAAqF,OAAA,CAAAF,SAAA,sBAAAnF,IAAA,CAAAqF,OAAA,CAAAF,SAAA,2BAAAnF,IAAA,CAAAqF,OAAA,CAAAF,SAAA;QACA;MACA;IACA;IACAP,SAAA,WAAAA,UAAA;MAAA,IAAAU,MAAA;MACA,SAAA1I,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAuJ,SAAA,CAAA9C,MAAA,cAAA7F,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAwJ,SAAA,CAAAD,SAAA;QACA,QAAAE,qBAAA;UACAC,cAAA,CAAAC,QAAA,iBAAAjB,IAAA;YACAY,MAAA,CAAAjI,UAAA,GAAAiI,MAAA,CAAA1I,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAuJ,SAAA;UACA;QACA;UACAG,cAAA,CAAAC,QAAA,kBAAAjB,IAAA;YACAY,MAAA,CAAAjI,UAAA,GAAAiI,MAAA,CAAA1I,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAuJ,SAAA;UACA;QACA;MACA;QACA,KAAAlI,UAAA,QAAAT,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAuJ,SAAA;MACA;IACA;IACAZ,cAAA,WAAAA,eAAA;MAAA,IAAAiB,MAAA;MACA,SAAAhJ,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA6J,cAAA,CAAApD,MAAA,cAAA7F,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAwJ,SAAA,CAAAK,cAAA;QACAH,cAAA,CAAAC,QAAA,sBAAAjB,IAAA;UACAkB,MAAA,CAAArI,YAAA,GAAAqI,MAAA,CAAAhJ,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA6J,cAAA;QACA;MACA;QACA,KAAAtI,YAAA,QAAAX,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA6J,cAAA;MACA;IACA;IACAjE,WAAA,WAAAA,YAAA;MAAA,IAAAkE,MAAA;MACA,SAAAlJ,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA+J,mBAAA,CAAAtD,MAAA,cAAA7F,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAwJ,SAAA,CAAAO,mBAAA;QACAL,cAAA,CAAAC,QAAA,+BAAAjB,IAAA;UACAoB,MAAA,CAAAxI,WAAA,GAAAwI,MAAA,CAAAlJ,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA+J,mBAAA;QACA;MACA;QACA,KAAAzI,WAAA,QAAAV,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA+J,mBAAA;MACA;IACA;IACA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAjK,IAAA;QACA0D,YAAA;QACAC,oBAAA;QACAE,sBAAA;QACAD,gBAAA;QACAE,kBAAA;QACAoG,gBAAA,OAAAlG,IAAA,CAAAkG,gBAAA;QACAC,gBAAA,OAAAnG,IAAA,CAAAmG,gBAAA;QACA1G,cAAA,OAAAO,IAAA,CAAAP,cAAA;QACAoB,UAAA,OAAAA,UAAA;QACA7B,QAAA,OAAAA,QAAA,QAAAA,QAAA;QACAC,UAAA,OAAAA,UAAA,QAAAA,UAAA;QACAC,YAAA,OAAAA,YAAA,QAAAA,YAAA;QACAC,WAAA,OAAAA,WAAA,QAAAA,WAAA;QACAb,QAAA,OAAA0B,IAAA,CAAA1B,QAAA;QACAC,QAAA,OAAAyB,IAAA,CAAAzB,QAAA;QACA6H,YAAA;MACA;MACA,IAAAC,sBAAA,OAAAzJ,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAC,GAAA,EAAA7B,IAAA,WAAA8B,QAAA;QACAxK,IAAA,CAAA0D,YAAA,GAAA8G,QAAA,CAAA9G,YAAA;QACA1D,IAAA,CAAA2D,oBAAA,GAAA6G,QAAA,CAAA7G,oBAAA;QACA3D,IAAA,CAAA6D,sBAAA,GAAA2G,QAAA,CAAA3G,sBAAA;QACA7D,IAAA,CAAA4D,gBAAA,GAAA4G,QAAA,CAAA5G,gBAAA;QACA5D,IAAA,CAAA8D,kBAAA,GAAA0G,QAAA,CAAA1G,kBAAA;MACA;MACA;MACA,IAAA9D,IAAA,CAAAsC,QAAA;QACA,SAAAjB,UAAA,IAAAyE,SAAA;UAAA,IAAA2E,UAAA,OAAAxE,2BAAA,CAAAC,OAAA,EACA,KAAA7E,UAAA;YAAAqJ,MAAA;UAAA;YAAA,KAAAD,UAAA,CAAArE,CAAA,MAAAsE,MAAA,GAAAD,UAAA,CAAAhF,CAAA,IAAAY,IAAA;cAAA,IAAAO,CAAA,GAAA8D,MAAA,CAAAnE,KAAA;cACA,IAAAK,CAAA,CAAAJ,QAAA,IAAAV,SAAA;gBAAA,IAAA6E,WAAA,OAAA1E,2BAAA,CAAAC,OAAA,EACAU,CAAA,CAAAJ,QAAA;kBAAAoE,OAAA;gBAAA;kBAAA,KAAAD,WAAA,CAAAvE,CAAA,MAAAwE,OAAA,GAAAD,WAAA,CAAAlF,CAAA,IAAAY,IAAA;oBAAA,IAAAU,CAAA,GAAA6D,OAAA,CAAArE,KAAA;oBACA,IAAAQ,CAAA,CAAAP,QAAA,IAAAV,SAAA;sBAAA,IAAA+E,WAAA,OAAA5E,2BAAA,CAAAC,OAAA,EACAa,CAAA,CAAAP,QAAA;wBAAAsE,OAAA;sBAAA;wBAAA,KAAAD,WAAA,CAAAzE,CAAA,MAAA0E,OAAA,GAAAD,WAAA,CAAApF,CAAA,IAAAY,IAAA;0BAAA,IAAAmB,CAAA,GAAAsD,OAAA,CAAAvE,KAAA;0BACA,IAAAiB,CAAA,CAAAuD,OAAA,SAAAnK,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAC,GAAA;4BACAvK,IAAA,CAAAsC,QAAA,QAAA1B,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAC,GAAA;0BACA;wBACA;sBAAA,SAAApD,GAAA;wBAAA0D,WAAA,CAAAzD,CAAA,CAAAD,GAAA;sBAAA;wBAAA0D,WAAA,CAAAxD,CAAA;sBAAA;oBACA;kBACA;gBAAA,SAAAF,GAAA;kBAAAwD,WAAA,CAAAvD,CAAA,CAAAD,GAAA;gBAAA;kBAAAwD,WAAA,CAAAtD,CAAA;gBAAA;cACA;YACA;UAAA,SAAAF,GAAA;YAAAsD,UAAA,CAAArD,CAAA,CAAAD,GAAA;UAAA;YAAAsD,UAAA,CAAApD,CAAA;UAAA;QACA;MACA;MACA,KAAA2D,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAlB,kBAAA,EAAAhK,IAAA,EAAA0I,IAAA,WAAA8B,QAAA;YACA,IAAAW,GAAA,GAAAX,QAAA,CAAAxK,IAAA;YACA,IAAAmL,GAAA,IAAArF,SAAA;cACA,IAAAsF,WAAA,GAAAnB,MAAA,CAAAjH,QAAA,YAAAiH,MAAA,CAAAhH,UAAA,UAAAgH,MAAA,CAAA/G,YAAA,WAAA+G,MAAA,CAAA9G,WAAA;cACA,IAAAkI,WAAA,GAAAF,GAAA,CAAAnI,QAAA,iBAAAmI,GAAA,CAAAlI,UAAA,eAAAkI,GAAA,CAAAjI,YAAA,gBAAAiI,GAAA,CAAAhI,WAAA;cACA8G,MAAA,CAAAqB,QAAA,CAAAH,GAAA,CAAAf,YAAA,uBAAAiB,WAAA,cAAAD,WAAA;gBACAG,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;gBACAC,WAAA;cACA,GAAAhD,IAAA;gBACAyC,GAAA,CAAAf,YAAA;gBACAH,MAAA,CAAAjG,IAAA,GAAAmH,GAAA;gBACAlB,MAAA,CAAAjG,IAAA,CAAAa,UAAA,GAAAoF,MAAA,CAAApF,UAAA;gBACA,IAAAoF,MAAA,CAAA5I,UAAA,IAAAyE,SAAA;kBAAA,IAAA6F,WAAA,OAAA1F,2BAAA,CAAAC,OAAA,EACA+D,MAAA,CAAA5I,UAAA;oBAAAuK,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAAvF,CAAA,MAAAwF,OAAA,GAAAD,WAAA,CAAAlG,CAAA,IAAAY,IAAA;sBAAA,IAAAO,GAAA,GAAAgF,OAAA,CAAArF,KAAA;sBACA,IAAAK,GAAA,CAAAJ,QAAA,IAAAV,SAAA;wBAAA,IAAA+F,WAAA,OAAA5F,2BAAA,CAAAC,OAAA,EACAU,GAAA,CAAAJ,QAAA;0BAAAsF,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAAzF,CAAA,MAAA0F,OAAA,GAAAD,WAAA,CAAApG,CAAA,IAAAY,IAAA;4BAAA,IAAAU,EAAA,GAAA+E,OAAA,CAAAvF,KAAA;4BACA,IAAAQ,EAAA,CAAAP,QAAA,IAAAV,SAAA;8BAAA,IAAAiG,WAAA,OAAA9F,2BAAA,CAAAC,OAAA,EACAa,EAAA,CAAAP,QAAA;gCAAAwF,OAAA;8BAAA;gCAAA,KAAAD,WAAA,CAAA3F,CAAA,MAAA4F,OAAA,GAAAD,WAAA,CAAAtG,CAAA,IAAAY,IAAA;kCAAA,IAAAmB,EAAA,GAAAwE,OAAA,CAAAzF,KAAA;kCACA,IAAAiB,EAAA,CAAAuD,OAAA,IAAAP,QAAA,CAAAxK,IAAA,CAAAsC,QAAA;oCACA2H,MAAA,CAAA3H,QAAA,GAAAkF,EAAA,CAAAyE,MAAA;kCACA;kCACA,IAAAzE,EAAA,CAAAuD,OAAA,IAAAP,QAAA,CAAAxK,IAAA,CAAAuC,QAAA;oCACA0H,MAAA,CAAA1H,QAAA,GAAAiF,EAAA,CAAAyE,MAAA;kCACA;gCACA;8BAAA,SAAA9E,GAAA;gCAAA4E,WAAA,CAAA3E,CAAA,CAAAD,GAAA;8BAAA;gCAAA4E,WAAA,CAAA1E,CAAA;8BAAA;4BACA;0BACA;wBAAA,SAAAF,GAAA;0BAAA0E,WAAA,CAAAzE,CAAA,CAAAD,GAAA;wBAAA;0BAAA0E,WAAA,CAAAxE,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAwE,WAAA,CAAAvE,CAAA,CAAAD,GAAA;kBAAA;oBAAAwE,WAAA,CAAAtE,CAAA;kBAAA;gBACA;gBACA4C,MAAA,CAAAjG,IAAA,CAAAD,OAAA,GAAAyG,QAAA,CAAAzG,OAAA;gBACAkG,MAAA,CAAAjG,IAAA,CAAAP,cAAA,GAAA+G,QAAA,CAAA/G,cAAA;gBACAwG,MAAA,CAAAjG,IAAA,CAAAN,YAAA,GAAA8G,QAAA,CAAA9G,YAAA;gBACAuG,MAAA,CAAAjG,IAAA,CAAAJ,gBAAA,GAAA4G,QAAA,CAAA5G,gBAAA;gBACAqG,MAAA,CAAAjG,IAAA,CAAAL,oBAAA,GAAA6G,QAAA,CAAA7G,oBAAA;gBACAsG,MAAA,CAAAjG,IAAA,CAAAF,kBAAA,GAAA0G,QAAA,CAAA1G,kBAAA;gBACAmG,MAAA,CAAAjG,IAAA,CAAAH,sBAAA,GAAA2G,QAAA,CAAA3G,sBAAA;gBACAoG,MAAA,CAAAjG,IAAA,CAAAtC,UAAA,GAAA8I,QAAA,CAAA9I,UAAA;gBACA;gBACAuI,MAAA,CAAAjG,IAAA,CAAAkI,eAAA,GAAA1B,QAAA,CAAA0B,eAAA;gBACAjC,MAAA,CAAAxI,eAAA,GAAA+I,QAAA,CAAA/I,eAAA;gBACAwI,MAAA,CAAAlI,WAAA;gBACAkI,MAAA,CAAApI,KAAA;gBACAoI,MAAA,CAAAhK,OAAA;cACA;YACA;;YAEA;YACA,IAAAuK,QAAA,CAAA2B,GAAA,CAAAC,QAAA,GAAAC,OAAA;cACApC,MAAA,CAAAqB,QAAA,CAAAd,QAAA,CAAA2B,GAAA;gBACAZ,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;gBACAC,WAAA;cACA,GAAAhD,IAAA;gBACA,IAAA1I,IAAA;kBACAsM,YAAA;kBACAC,WAAA;kBACAC,WAAA;kBACAC,YAAA,EAAAxC,MAAA,CAAArJ,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAlM,IAAA,CAAAsO,KAAA;kBACAC,cAAA,EAAAnC,QAAA,CAAA2B;gBACA;gBACA,IAAAS,mBAAA,EAAA5M,IAAA,EAAA0I,IAAA,WAAA8B,QAAA;kBACAP,MAAA,CAAA4C,QAAA;oBACApB,IAAA;oBACAqB,OAAA;kBACA;gBACA;cACA;YACA;;YAEA;YACA,IAAAtC,QAAA,CAAA2B,GAAA,CAAAC,QAAA,GAAAC,OAAA;cACApC,MAAA,CAAAqB,QAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;gBACAC,WAAA;cACA,GAAAhD,IAAA;gBACA1I,IAAA,CAAAoK,YAAA;gBACA;gBACA,IAAAJ,kBAAA,EAAAhK,IAAA,EAAA0I,IAAA,WAAA8B,QAAA;kBACA,IAAAA,QAAA,CAAAxK,IAAA;oBACAiK,MAAA,CAAA4C,QAAA,CAAAE,OAAA;oBACA9C,MAAA,CAAAjG,IAAA,GAAAwG,QAAA,CAAAxK,IAAA;oBACAiK,MAAA,CAAAjG,IAAA,CAAAa,UAAA,GAAAoF,MAAA,CAAApF,UAAA;oBACA,IAAAoF,MAAA,CAAA5I,UAAA,IAAAyE,SAAA;sBAAA,IAAAkH,WAAA,OAAA/G,2BAAA,CAAAC,OAAA,EACA+D,MAAA,CAAA5I,UAAA;wBAAA4L,OAAA;sBAAA;wBAAA,KAAAD,WAAA,CAAA5G,CAAA,MAAA6G,OAAA,GAAAD,WAAA,CAAAvH,CAAA,IAAAY,IAAA;0BAAA,IAAAO,GAAA,GAAAqG,OAAA,CAAA1G,KAAA;0BACA,IAAAK,GAAA,CAAAJ,QAAA,IAAAV,SAAA;4BAAA,IAAAoH,WAAA,OAAAjH,2BAAA,CAAAC,OAAA,EACAU,GAAA,CAAAJ,QAAA;8BAAA2G,OAAA;4BAAA;8BAAA,KAAAD,WAAA,CAAA9G,CAAA,MAAA+G,OAAA,GAAAD,WAAA,CAAAzH,CAAA,IAAAY,IAAA;gCAAA,IAAAU,GAAA,GAAAoG,OAAA,CAAA5G,KAAA;gCACA,IAAAQ,GAAA,CAAAP,QAAA,IAAAV,SAAA;kCAAA,IAAAsH,WAAA,OAAAnH,2BAAA,CAAAC,OAAA,EACAa,GAAA,CAAAP,QAAA;oCAAA6G,OAAA;kCAAA;oCAAA,KAAAD,WAAA,CAAAhH,CAAA,MAAAiH,OAAA,GAAAD,WAAA,CAAA3H,CAAA,IAAAY,IAAA;sCAAA,IAAAmB,GAAA,GAAA6F,OAAA,CAAA9G,KAAA;sCACA,IAAAiB,GAAA,CAAAuD,OAAA,IAAAP,QAAA,CAAAxK,IAAA,CAAAsC,QAAA;wCACA2H,MAAA,CAAA3H,QAAA,GAAAkF,GAAA,CAAAyE,MAAA;sCACA;sCACA,IAAAzE,GAAA,CAAAuD,OAAA,IAAAP,QAAA,CAAAxK,IAAA,CAAAuC,QAAA;wCACA0H,MAAA,CAAA1H,QAAA,GAAAiF,GAAA,CAAAyE,MAAA;sCACA;oCACA;kCAAA,SAAA9E,GAAA;oCAAAiG,WAAA,CAAAhG,CAAA,CAAAD,GAAA;kCAAA;oCAAAiG,WAAA,CAAA/F,CAAA;kCAAA;gCACA;8BACA;4BAAA,SAAAF,GAAA;8BAAA+F,WAAA,CAAA9F,CAAA,CAAAD,GAAA;4BAAA;8BAAA+F,WAAA,CAAA7F,CAAA;4BAAA;0BACA;wBACA;sBAAA,SAAAF,GAAA;wBAAA6F,WAAA,CAAA5F,CAAA,CAAAD,GAAA;sBAAA;wBAAA6F,WAAA,CAAA3F,CAAA;sBAAA;oBACA;oBACA4C,MAAA,CAAAjG,IAAA,CAAAD,OAAA,GAAAyG,QAAA,CAAAzG,OAAA;oBACAkG,MAAA,CAAAjG,IAAA,CAAAP,cAAA,GAAA+G,QAAA,CAAA/G,cAAA;oBACAwG,MAAA,CAAAjG,IAAA,CAAAN,YAAA,GAAA8G,QAAA,CAAA9G,YAAA;oBACAuG,MAAA,CAAAjG,IAAA,CAAAJ,gBAAA,GAAA4G,QAAA,CAAA5G,gBAAA;oBACAqG,MAAA,CAAAjG,IAAA,CAAAL,oBAAA,GAAA6G,QAAA,CAAA7G,oBAAA;oBACAsG,MAAA,CAAAjG,IAAA,CAAAF,kBAAA,GAAA0G,QAAA,CAAA1G,kBAAA;oBACAmG,MAAA,CAAAjG,IAAA,CAAAH,sBAAA,GAAA2G,QAAA,CAAA3G,sBAAA;oBACAoG,MAAA,CAAAjG,IAAA,CAAAtC,UAAA,GAAA8I,QAAA,CAAA9I,UAAA;oBACA;oBACAuI,MAAA,CAAAjG,IAAA,CAAAkI,eAAA,GAAA1B,QAAA,CAAA0B,eAAA;oBACAjC,MAAA,CAAAxI,eAAA,GAAA+I,QAAA,CAAA/I,eAAA;oBACAwI,MAAA,CAAAlI,WAAA;oBACAkI,MAAA,CAAApI,KAAA;oBACAoI,MAAA,CAAAhK,OAAA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA,aACAwI,OAAA,WAAAA,QAAA;MAAA,IAAA6E,MAAA;MAAA,WAAAC,kBAAA,CAAArH,OAAA,oBAAAsH,oBAAA,CAAAtH,OAAA,IAAAuH,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAtH,OAAA,IAAAyH,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAT,MAAA,CAAArN,OAAA;cAAA4N,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAC,oBAAA,MAAAC,cAAA,CAAA/H,OAAA,MAAA+H,cAAA,CAAA/H,OAAA,MACAoH,MAAA,CAAAzK,WAAA;gBACAqL,eAAA,EAAAZ,MAAA,CAAA1M,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAA6D,mBAAA,CAAAC;cAAA,EACA,EAAA1F,IAAA,WAAA8B,QAAA;gBACA8C,MAAA,CAAAtM,WAAA,GAAAwJ,QAAA,CAAA6D,IAAA;gBACA,KAAAC,KAAA,CAAA9D,QAAA,CAAAhK,KAAA;kBACA8M,MAAA,CAAA9M,KAAA,GAAAgK,QAAA,CAAAhK,KAAA;gBACA;gBACA8M,MAAA,CAAArN,OAAA;cACA;YAAA;YAAA;cAAA,OAAA4N,QAAA,CAAAU,IAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IACA;IACAc,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAA,IAAA,CAAAjI,QAAA,KAAAiI,IAAA,CAAAjI,QAAA,CAAAC,MAAA;QACA,OAAAgI,IAAA,CAAAjI,QAAA;MACA;MACA,IAAAkI,CAAA;MACA,IAAAD,IAAA,CAAAE,KAAA;QACA,IAAAF,IAAA,CAAAE,KAAA,CAAAC,oBAAA,YAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA;UACA,IAAAJ,IAAA,CAAAvP,IAAA,CAAA4P,aAAA;YACAJ,CAAA,GAAAD,IAAA,CAAAvP,IAAA,CAAA4P,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAAP,IAAA,CAAAvP,IAAA,CAAA4P,aAAA;UACA;YACAJ,CAAA,GAAAD,IAAA,CAAAQ,IAAA,CAAAC,aAAA,SAAAH,iBAAA,CAAAC,YAAA,CAAAP,IAAA,CAAAQ,IAAA,CAAAC,aAAA;UACA;QACA;UACAR,CAAA,GAAAD,IAAA,CAAAE,KAAA,CAAAQ,SAAA,SAAAV,IAAA,CAAAE,KAAA,CAAAC,oBAAA,GAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA,SAAAJ,IAAA,CAAAE,KAAA,CAAAS,iBAAA,SAAAL,iBAAA,CAAAC,YAAA,CAAAP,IAAA,CAAAE,KAAA,CAAAC,oBAAA,GAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA;QACA;MACA;MACA,IAAAJ,IAAA,CAAAY,MAAA;QACA;UACAC,EAAA,EAAAb,IAAA,CAAAY,MAAA;UACAE,KAAA,EAAAb,CAAA;UACAlI,QAAA,EAAAiI,IAAA,CAAAjI,QAAA;UACAgJ,UAAA,EAAAf,IAAA,CAAA1D,OAAA,YAAA0D,IAAA,CAAAjI,QAAA,IAAAV;QACA;MACA;QACA;UACAwJ,EAAA,EAAAb,IAAA,CAAAxC,MAAA;UACAsD,KAAA,EAAAb,CAAA;UACAlI,QAAA,EAAAiI,IAAA,CAAAjI,QAAA;UACAgJ,UAAA,EAAAf,IAAA,CAAA1D,OAAA,YAAA0D,IAAA,CAAAjI,QAAA,IAAAV;QACA;MACA;IACA;IACA2J,wBAAA,WAAAA,yBAAAC,IAAA,EAAAnJ,KAAA,EAAAoJ,QAAA;MAEA;MACA,IAAApJ,KAAA,WAAAA,KAAA;QACA;QACA,IAAAqJ,WAAA,GAAArJ,KAAA,CAAAmG,KAAA,MAAAjG,MAAA;QACA,IAAAmJ,WAAA;UACA,OAAAD,QAAA,KAAAE,KAAA;QACA;MACA;MACA;MACAF,QAAA;IACA;IACAG,iBAAA,WAAAA,kBAAArB,IAAA;MACA,IAAAA,IAAA,CAAAjI,QAAA,KAAAiI,IAAA,CAAAjI,QAAA,CAAAC,MAAA;QACA,OAAAgI,IAAA,CAAAjI,QAAA;MACA;MACA,IAAAkI,CAAA;MACA,KAAAD,IAAA,CAAAlP,OAAA,IAAAkP,IAAA,CAAAlP,OAAA,CAAAwQ,gBAAA,YAAAtB,IAAA,CAAAlP,OAAA,CAAAyQ,aAAA;QACAtB,CAAA,GAAAD,IAAA,CAAAwB,gBAAA,SAAAxB,IAAA,CAAAyB,aAAA,SAAAnB,iBAAA,CAAAC,YAAA,CAAAP,IAAA,CAAAwB,gBAAA;MACA;QACAvB,CAAA,IAAAD,IAAA,CAAAlP,OAAA,CAAA4Q,eAAA,WAAA1B,IAAA,CAAAlP,OAAA,CAAA4Q,eAAA,gBAAA1B,IAAA,CAAAlP,OAAA,CAAAyQ,aAAA,WAAAvB,IAAA,CAAAlP,OAAA,CAAAyQ,aAAA,gBAAAvB,IAAA,CAAAlP,OAAA,CAAAwQ,gBAAA,WAAAtB,IAAA,CAAAlP,OAAA,CAAAwQ,gBAAA,eAAAhB,iBAAA,CAAAC,YAAA,CAAAP,IAAA,CAAAlP,OAAA,CAAAwQ,gBAAA,WAAAtB,IAAA,CAAAlP,OAAA,CAAAwQ,gBAAA;MACA;MACA;QACAT,EAAA,EAAAb,IAAA,CAAAxH,aAAA;QACAsI,KAAA,EAAAb,CAAA;QACAlI,QAAA,EAAAiI,IAAA,CAAAjI;MACA;IACA;IACA;IACA4J,MAAA,WAAAA,OAAA;MACA,KAAArO,WAAA;MACA,KAAAE,WAAA;MACA,KAAAD,SAAA;MACA,KAAAE,iBAAA;MACA,KAAAC,aAAA;MACA,KAAAC,aAAA;MACA,KAAA3B,GAAA;MACA,KAAAqB,KAAA;MACA,KAAAO,IAAA;MACA,KAAAgO,KAAA;MACA,KAAA/N,QAAA;MACA,KAAAC,QAAA;MACA;IACA;IACA+N,aAAA,WAAAA,cAAA;MACA,KAAArO,WAAA;IACA;IACA;IACAoO,KAAA,WAAAA,MAAA;MACA,KAAA/N,QAAA;MACA,KAAAC,QAAA;MACA,KAAAb,UAAA;MACA,KAAAsC,IAAA;QACA1B,QAAA;QACAC,QAAA;QACAb,UAAA;QACA6O,cAAA;QACAC,SAAA;QACAC,eAAA;QACAvG,gBAAA;QACAwG,kBAAA;QACAvG,gBAAA;QACAwG,aAAA;QACAC,cAAA;QACA7M,OAAA;QACAN,cAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,gBAAA;QACAC,sBAAA;QACAC,kBAAA;QACAoI,eAAA;QACA2E,cAAA;QACAhM,UAAA,OAAAA,UAAA;QACA7B,QAAA,OAAAA,QAAA,QAAAA,QAAA;QACAC,UAAA,OAAAA,UAAA,QAAAA,UAAA;QACAC,YAAA,OAAAA,YAAA,QAAAA,YAAA;QACAC,WAAA,OAAAA,WAAA,QAAAA,WAAA;QACAE,UAAA;QACA8B,cAAA;QACA2L,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACA9L,YAAA;QACA+L,cAAA;QACAC,gBAAA;QACAC,gBAAA;QACA5L,YAAA;QACA6L,cAAA;QACAC,gBAAA;QACAC,gBAAA;QACAjM,WAAA;QACAkM,aAAA;QACAC,eAAA;QACAC,eAAA;QACA5R,MAAA;QACA6R,eAAA;MACA;MACA,KAAAhQ,UAAA;MACA,KAAA2C,UAAA;MACA,KAAAsN,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA/O,WAAA,CAAAC,OAAA;MACA,KAAA2F,OAAA;IACA;IACA,aACAoJ,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAlP,aAAA;MACA,KAAAI,WAAA,CAAAQ,UAAA;MACA,KAAAR,WAAA,CAAAY,cAAA;MACA,KAAAZ,WAAA,CAAAa,YAAA;MACA,KAAAb,WAAA,CAAAc,oBAAA;MACA,KAAAd,WAAA,CAAAe,gBAAA;MACA,KAAAf,WAAA,CAAAgB,sBAAA;MACA,KAAAhB,WAAA,CAAAiB,kBAAA;MACA,KAAAjB,WAAA,CAAAqJ,eAAA;MACA,KAAA0F,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzR,GAAA,GAAAyR,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAzB,SAAA;MAAA;MACA,KAAApQ,MAAA,GAAA2R,SAAA,CAAAtL,MAAA;MACA,KAAApG,QAAA,IAAA0R,SAAA,CAAAtL,MAAA;MACA,KAAA/F,SAAA,GAAAqR,SAAA,CAAAtL,MAAA;MACA,IAAAsL,SAAA,CAAAtL,MAAA;QACA,KAAAyL,cAAA,CAAAH,SAAA;MACA;MACA,IAAAA,SAAA,CAAAtL,MAAA;QACA,KAAA1F,SAAA,GAAAgR,SAAA;MACA;IACA;IACA,aACAI,SAAA,WAAAA,UAAA;MACA,KAAA9B,KAAA;MACA,KAAAhO,IAAA;MACA,KAAA2B,IAAA,CAAA1B,QAAA;MACA,KAAAP,WAAA;MACA,KAAAF,KAAA;MACA,KAAAmC,IAAA,CAAAP,cAAA;MACA,KAAAjC,cAAA,QAAAF,WAAA;MACA,SAAAE,cAAA,IAAAsE,SAAA,SAAA9B,IAAA,CAAAP,cAAA;QAAA,IAAA2O,WAAA,OAAAnM,2BAAA,CAAAC,OAAA,EACA,KAAA1E,cAAA;UAAA6Q,OAAA;QAAA;UAAA,KAAAD,WAAA,CAAAhM,CAAA,MAAAiM,OAAA,GAAAD,WAAA,CAAA3M,CAAA,IAAAY,IAAA;YAAA,IAAAO,CAAA,GAAAyL,OAAA,CAAA9L,KAAA;UAEA,EADA;QACA,SAAAY,GAAA;UAAAiL,WAAA,CAAAhL,CAAA,CAAAD,GAAA;QAAA;UAAAiL,WAAA,CAAA/K,CAAA;QAAA;MACA;MACA,KAAA5G,GAAA;MACA,SAAAY,UAAA,IAAAyE,SAAA;QAAA,IAAAwM,WAAA,OAAArM,2BAAA,CAAAC,OAAA,EACA,KAAA7E,UAAA;UAAAkR,OAAA;QAAA;UAAA,KAAAD,WAAA,CAAAlM,CAAA,MAAAmM,OAAA,GAAAD,WAAA,CAAA7M,CAAA,IAAAY,IAAA;YAAA,IAAAO,GAAA,GAAA2L,OAAA,CAAAhM,KAAA;YACA,IAAAK,GAAA,CAAAJ,QAAA,IAAAV,SAAA;cAAA,IAAA0M,WAAA,OAAAvM,2BAAA,CAAAC,OAAA,EACAU,GAAA,CAAAJ,QAAA;gBAAAiM,OAAA;cAAA;gBAAA,KAAAD,WAAA,CAAApM,CAAA,MAAAqM,OAAA,GAAAD,WAAA,CAAA/M,CAAA,IAAAY,IAAA;kBAAA,IAAAU,CAAA,GAAA0L,OAAA,CAAAlM,KAAA;kBACA,IAAAQ,CAAA,CAAAP,QAAA,IAAAV,SAAA;oBAAA,IAAA4M,WAAA,OAAAzM,2BAAA,CAAAC,OAAA,EACAa,CAAA,CAAAP,QAAA;sBAAAmM,OAAA;oBAAA;sBAAA,KAAAD,WAAA,CAAAtM,CAAA,MAAAuM,OAAA,GAAAD,WAAA,CAAAjN,CAAA,IAAAY,IAAA;wBAAA,IAAAmB,CAAA,GAAAmL,OAAA,CAAApM,KAAA;wBACA,IAAAiB,CAAA,CAAAuD,OAAA,SAAAnK,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAC,GAAA;0BACA,KAAAjI,QAAA,GAAAkF,CAAA,CAAAyE,MAAA;wBACA;sBACA;oBAAA,SAAA9E,GAAA;sBAAAuL,WAAA,CAAAtL,CAAA,CAAAD,GAAA;oBAAA;sBAAAuL,WAAA,CAAArL,CAAA;oBAAA;kBACA;gBACA;cAAA,SAAAF,GAAA;gBAAAqL,WAAA,CAAApL,CAAA,CAAAD,GAAA;cAAA;gBAAAqL,WAAA,CAAAnL,CAAA;cAAA;YACA;UACA;QAAA,SAAAF,GAAA;UAAAmL,WAAA,CAAAlL,CAAA,CAAAD,GAAA;QAAA;UAAAmL,WAAA,CAAAjL,CAAA;QAAA;MACA;MACA;MACA,KAAArD,IAAA,CAAA4O,qBAAA;MACA,KAAArO,WAAA;IACA;IACAsO,SAAA,WAAAA,UAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,GAAA,CAAAE,GAAA;QACA,KAAAd,cAAA,CAAAY,GAAA,CAAAvM,KAAA;QACA,IAAA0M,mBAAA,EAAAH,GAAA,CAAAvM,KAAA,CAAAiK,SAAA,EAAA9H,IAAA,WAAA8B,QAAA;UACAuI,MAAA,CAAA9R,SAAA,GAAAuJ,QAAA,CAAAvJ,SAAA;UACA8R,MAAA,CAAA/Q,SAAA;QACA;MACA;MACA,IAAA8Q,GAAA,CAAAE,GAAA;QACA,KAAAd,cAAA,CAAAY,GAAA,CAAAvM,KAAA;QACA,IAAA2M,gCAAA;UAAAC,YAAA,EAAAL,GAAA,CAAAvM,KAAA,CAAAiK;QAAA,GAAA9H,IAAA,WAAA8B,QAAA;UACAuI,MAAA,CAAA5R,iBAAA,GAAAqJ,QAAA,CAAA6D,IAAA;UACA0E,MAAA,CAAApQ,MAAA,GAAA6H,QAAA,CAAA4I,KAAA;UACAL,MAAA,CAAA7Q,iBAAA;QACA;MACA;MACA,IAAA4Q,GAAA,CAAAE,GAAA;QACA,KAAAd,cAAA,CAAAY,GAAA,CAAAvM,KAAA;QACA,IAAA8M,oCAAA;UAAAF,YAAA,EAAAL,GAAA,CAAAvM,KAAA,CAAAiK;QAAA,GAAA9H,IAAA,WAAA8B,QAAA;UACAuI,MAAA,CAAA3R,aAAA,GAAAoJ,QAAA,CAAA6D,IAAA;UACA0E,MAAA,CAAAnQ,MAAA,GAAA4H,QAAA,CAAA4I,KAAA;UACAL,MAAA,CAAA5Q,aAAA;QACA;MACA;MACA,IAAA2Q,GAAA,CAAAE,GAAA;QACA,KAAAd,cAAA,CAAAY,GAAA,CAAAvM,KAAA;QACA,IAAA+M,gBAAA,EAAAR,GAAA,CAAAvM,KAAA,CAAAiK,SAAA,EAAA9H,IAAA,WAAA8B,QAAA;UACAuI,MAAA,CAAA7R,WAAA,GAAAsJ,QAAA,CAAAtJ,WAAA;UACA6R,MAAA,CAAA9Q,WAAA;QACA;MACA;IACA;IACA;IACAiQ,cAAA,WAAAA,eAAAY,GAAA;MACA,KAAAnR,WAAA;QACA6O,SAAA,EAAAsC,GAAA,CAAAtC,SAAA,WAAAsC,GAAA,CAAAtC,SAAA;QACAI,cAAA,EAAAkC,GAAA,CAAAlC,cAAA,WAAAkC,GAAA,CAAAlC,cAAA;QACA1G,gBAAA,EAAA4I,GAAA,CAAA5I,gBAAA,WAAA4I,GAAA,CAAA5I,gBAAA;QACAwG,kBAAA,EAAAoC,GAAA,CAAApC,kBAAA,WAAAoC,GAAA,CAAApC,kBAAA;QACAvG,gBAAA,EAAA2I,GAAA,CAAA3I,gBAAA,WAAA2I,GAAA,CAAA3I,gBAAA;QACAwG,aAAA,EAAAmC,GAAA,CAAAnC,aAAA,WAAAmC,GAAA,CAAAnC,aAAA;QACA4C,eAAA,EAAAT,GAAA,CAAAzP,UAAA,WAAAyP,GAAA,CAAAzP,UAAA;QACAoN,eAAA,EAAAqC,GAAA,CAAArC,eAAA,WAAAqC,GAAA,CAAArC,eAAA;QACA+C,WAAA,EAAAV,GAAA,CAAAnE,KAAA,WAAAmE,GAAA,CAAAnE,KAAA,CAAA5D,OAAA;MACA;IACA;IACA,aACA0I,YAAA,WAAAA,aAAAX,GAAA;MAAA,IAAAY,MAAA;MACA,KAAArD,KAAA;MACA,KAAApQ,OAAA;MACA,KAAAoC,IAAA;MACA,KAAAgC,UAAA,GAAAyO,GAAA;MACA,KAAArS,GAAA,GAAAkT,aAAA,CAAAC,QAAA;MACA,IAAApD,SAAA,GAAAsC,GAAA,CAAAtC,SAAA,SAAAlQ,GAAA;MACA,KAAAiE,WAAA;MACA,IAAAsP,mBAAA,EAAArD,SAAA,EAAA9H,IAAA,WAAA8B,QAAA;QACAkJ,MAAA,CAAA1P,IAAA,GAAAwG,QAAA,CAAAxK,IAAA;QACA,IAAA0T,MAAA,CAAArS,UAAA,IAAAyE,SAAA;UAAA,IAAAgO,WAAA,OAAA7N,2BAAA,CAAAC,OAAA,EACAwN,MAAA,CAAArS,UAAA;YAAA0S,OAAA;UAAA;YAAA,KAAAD,WAAA,CAAA1N,CAAA,MAAA2N,OAAA,GAAAD,WAAA,CAAArO,CAAA,IAAAY,IAAA;cAAA,IAAAO,CAAA,GAAAmN,OAAA,CAAAxN,KAAA;cACA,IAAAK,CAAA,CAAAJ,QAAA,IAAAV,SAAA;gBAAA,IAAAkO,WAAA,OAAA/N,2BAAA,CAAAC,OAAA,EACAU,CAAA,CAAAJ,QAAA;kBAAAyN,OAAA;gBAAA;kBAAA,KAAAD,WAAA,CAAA5N,CAAA,MAAA6N,OAAA,GAAAD,WAAA,CAAAvO,CAAA,IAAAY,IAAA;oBAAA,IAAAU,CAAA,GAAAkN,OAAA,CAAA1N,KAAA;oBACA,IAAAQ,CAAA,CAAAP,QAAA,IAAAV,SAAA;sBAAA,IAAAoO,WAAA,OAAAjO,2BAAA,CAAAC,OAAA,EACAa,CAAA,CAAAP,QAAA;wBAAA2N,OAAA;sBAAA;wBAAA,KAAAD,WAAA,CAAA9N,CAAA,MAAA+N,OAAA,GAAAD,WAAA,CAAAzO,CAAA,IAAAY,IAAA;0BAAA,IAAAmB,CAAA,GAAA2M,OAAA,CAAA5N,KAAA;0BACA,IAAAiB,CAAA,CAAAuD,OAAA,IAAAP,QAAA,CAAAxK,IAAA,CAAAsC,QAAA;4BACAoR,MAAA,CAAApR,QAAA,GAAAkF,CAAA,CAAAyE,MAAA;0BACA;0BACA,IAAAzE,CAAA,CAAAuD,OAAA,IAAAP,QAAA,CAAAxK,IAAA,CAAAuC,QAAA;4BACAmR,MAAA,CAAAnR,QAAA,GAAAiF,CAAA,CAAAyE,MAAA;0BACA;wBACA;sBAAA,SAAA9E,GAAA;wBAAA+M,WAAA,CAAA9M,CAAA,CAAAD,GAAA;sBAAA;wBAAA+M,WAAA,CAAA7M,CAAA;sBAAA;oBACA;kBACA;gBAAA,SAAAF,GAAA;kBAAA6M,WAAA,CAAA5M,CAAA,CAAAD,GAAA;gBAAA;kBAAA6M,WAAA,CAAA3M,CAAA;gBAAA;cACA;YACA;UAAA,SAAAF,GAAA;YAAA2M,WAAA,CAAA1M,CAAA,CAAAD,GAAA;UAAA;YAAA2M,WAAA,CAAAzM,CAAA;UAAA;QACA;QACAqM,MAAA,CAAA1P,IAAA,CAAAD,OAAA,GAAAyG,QAAA,CAAAzG,OAAA;QACA2P,MAAA,CAAA1P,IAAA,CAAAP,cAAA,GAAA+G,QAAA,CAAA/G,cAAA;QACAiQ,MAAA,CAAA1P,IAAA,CAAAN,YAAA,GAAA8G,QAAA,CAAA9G,YAAA;QACAgQ,MAAA,CAAA1P,IAAA,CAAAJ,gBAAA,GAAA4G,QAAA,CAAA5G,gBAAA;QACA8P,MAAA,CAAA1P,IAAA,CAAAL,oBAAA,GAAA6G,QAAA,CAAA7G,oBAAA;QACA+P,MAAA,CAAA1P,IAAA,CAAAF,kBAAA,GAAA0G,QAAA,CAAA1G,kBAAA;QACA4P,MAAA,CAAA1P,IAAA,CAAAH,sBAAA,GAAA2G,QAAA,CAAA3G,sBAAA;QACA6P,MAAA,CAAA1P,IAAA,CAAAtC,UAAA,GAAA8I,QAAA,CAAA9I,UAAA;QACA;QACAgS,MAAA,CAAA1P,IAAA,CAAAkI,eAAA,GAAA1B,QAAA,CAAA0B,eAAA;QACAwH,MAAA,CAAAjS,eAAA,GAAA+I,QAAA,CAAA/I,eAAA;QACAiS,MAAA,CAAA3R,WAAA;QACA2R,MAAA,CAAA7R,KAAA;QACA;QACA6R,MAAA,CAAAzT,OAAA;QAEA,IAAAuK,QAAA,CAAAxK,IAAA,CAAAiE,kBAAA,aAAAuG,QAAA,CAAAxK,IAAA,CAAAkE,gBAAA;UACAwP,MAAA,CAAA1P,IAAA,CAAAoQ,kBAAA;UACAV,MAAA,CAAA1P,IAAA,CAAAoQ,kBAAA,CAAAlN,IAAA,CAAAsD,QAAA,CAAAxK,IAAA,CAAAiE,kBAAA;UACAyP,MAAA,CAAA1P,IAAA,CAAAoQ,kBAAA,CAAAlN,IAAA,CAAAsD,QAAA,CAAAxK,IAAA,CAAAkE,gBAAA;QACA;QAEA,IAAAmQ,SAAA,OAAAC,IAAA,CAAAC,YAAA;UACAC,KAAA;UACAC,qBAAA;UACAC,qBAAA;QACA;QACA,KAAAhB,MAAA,CAAAzQ,UAAA,SAAAyQ,MAAA,CAAA1Q,QAAA,UAAAwH,QAAA,CAAAxK,IAAA,CAAA2U,kBAAA;UACAjB,MAAA,CAAA1P,IAAA,CAAA2Q,kBAAA,GAAAnK,QAAA,CAAAxK,IAAA,CAAA2U,kBAAA,CAAAC,cAAA;UACAlB,MAAA,CAAA1P,IAAA,CAAA2Q,kBAAA,GAAAjB,MAAA,CAAA1P,IAAA,CAAA2Q,kBAAA,GAAAjB,MAAA,CAAA1P,IAAA,CAAA2Q,kBAAA,CAAAE,OAAA;UACAnB,MAAA,CAAA1P,IAAA,CAAA2Q,kBAAA,GAAAN,SAAA,CAAAS,MAAA,CAAApB,MAAA,CAAA1P,IAAA,CAAA2Q,kBAAA;QACA;QACA,KAAAjB,MAAA,CAAAxQ,YAAA,SAAAwQ,MAAA,CAAAvQ,WAAA,UAAAqH,QAAA,CAAAxK,IAAA,CAAA+U,cAAA;UACArB,MAAA,CAAA1P,IAAA,CAAA+Q,cAAA,GAAAvK,QAAA,CAAAxK,IAAA,CAAA+U,cAAA,CAAAH,cAAA;UACAlB,MAAA,CAAA1P,IAAA,CAAA+Q,cAAA,GAAArB,MAAA,CAAA1P,IAAA,CAAA+Q,cAAA,GAAArB,MAAA,CAAA1P,IAAA,CAAA+Q,cAAA,CAAAF,OAAA;UACAnB,MAAA,CAAA1P,IAAA,CAAA+Q,cAAA,GAAAV,SAAA,CAAAS,MAAA,CAAApB,MAAA,CAAA1P,IAAA,CAAA+Q,cAAA;QACA;MAEA;IAEA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAjK,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA;QACA,IAAA+J,MAAA,CAAAhS,UAAA,SAAAgS,MAAA,CAAAjS,QAAA;UACAiS,MAAA,CAAAjR,IAAA,CAAAkR,sBAAA,GAAAD,MAAA,CAAAxQ,WAAA;QACA;QACA,IAAAwQ,MAAA,CAAAhS,UAAA,SAAAgS,MAAA,CAAAjS,QAAA;UACAiS,MAAA,CAAAjR,IAAA,CAAAmR,kBAAA,GAAAF,MAAA,CAAAxQ,WAAA;QACA;QACA;QACAwQ,MAAA,CAAAjR,IAAA,CAAAhB,QAAA,GAAAiS,MAAA,CAAAjS,QAAA,GAAAiS,MAAA,CAAAjS,QAAA;QACAiS,MAAA,CAAAjR,IAAA,CAAAf,UAAA,GAAAgS,MAAA,CAAAhS,UAAA,GAAAgS,MAAA,CAAAhS,UAAA;QACAgS,MAAA,CAAAjR,IAAA,CAAAd,YAAA,GAAA+R,MAAA,CAAA/R,YAAA,GAAA+R,MAAA,CAAA/R,YAAA;QACA+R,MAAA,CAAAjR,IAAA,CAAAb,WAAA,GAAA8R,MAAA,CAAA9R,WAAA,GAAA8R,MAAA,CAAA9R,WAAA;QACA;QACA8R,MAAA,CAAAjR,IAAA,CAAA2Q,kBAAA,GAAAM,MAAA,CAAAjR,IAAA,CAAA2Q,kBAAA,GAAAS,MAAA,CAAAH,MAAA,CAAAjR,IAAA,CAAA2Q,kBAAA,EAAAE,OAAA;QACAI,MAAA,CAAAjR,IAAA,CAAA+Q,cAAA,GAAAE,MAAA,CAAAjR,IAAA,CAAA+Q,cAAA,GAAAK,MAAA,CAAAH,MAAA,CAAAjR,IAAA,CAAA+Q,cAAA,EAAAF,OAAA;QACA,IAAAI,MAAA,CAAAjR,IAAA,CAAAoQ,kBAAA,IAAAa,MAAA,CAAAjR,IAAA,CAAAoQ,kBAAA,CAAA3N,MAAA;UACAwO,MAAA,CAAAjR,IAAA,CAAAC,kBAAA,GAAAgR,MAAA,CAAAjR,IAAA,CAAAoQ,kBAAA;UACAa,MAAA,CAAAjR,IAAA,CAAAE,gBAAA,GAAA+Q,MAAA,CAAAjR,IAAA,CAAAoQ,kBAAA;QACA;QACA;QACA,IAAAiB,SAAA,OAAAC,IAAA,CAAAL,MAAA,CAAAjR,IAAA,CAAAC,kBAAA;QACA,IAAAsR,OAAA,OAAAD,IAAA,CAAAL,MAAA,CAAAjR,IAAA,CAAAE,gBAAA;QACA,IAAAmR,SAAA,GAAAE,OAAA;UACA,IAAAC,kBAAA;YACA1I,OAAA;YACArB,IAAA;UACA;UACA;QACA;QACA;QACA,IAAAwJ,MAAA,CAAAjR,IAAA,CAAAyR,gBAAA,YAAAR,MAAA,CAAAjR,IAAA,CAAAyR,gBAAA;UACA,IAAAD,kBAAA;YACA1I,OAAA;YACArB,IAAA;UACA;UACA;QACA;QACA,IAAAP,KAAA;UACA,IAAA+J,MAAA,CAAAjR,IAAA,CAAAwM,SAAA;YACA,IAAAkF,sBAAA,EAAAT,MAAA,CAAAjR,IAAA,EAAA0E,IAAA,WAAA8B,QAAA;cACAyK,MAAA,CAAAU,MAAA,CAAAC,UAAA;cACAX,MAAA,CAAAlT,WAAA;cACAkT,MAAA,CAAAxM,OAAA;YACA;YACAwM,MAAA,CAAA5E,KAAA;UACA;YACA4E,MAAA,CAAApI,QAAA,CAAAgJ,IAAA;UACA;QACA;MACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAhD,GAAA;MAAA,IAAAiD,OAAA;MACAjD,GAAA,CAAA9P,QAAA,QAAAA,QAAA,QAAAA,QAAA;MACA8P,GAAA,CAAA7P,UAAA,QAAAA,UAAA,QAAAA,UAAA;MACA6P,GAAA,CAAA5P,YAAA,QAAAA,YAAA,QAAAA,YAAA;MACA4P,GAAA,CAAA3P,WAAA,QAAAA,WAAA,QAAAA,WAAA;MAEA,IAAA6S,UAAA,GAAAlD,GAAA,CAAAtC,SAAA,SAAAlQ,GAAA;MACA,KAAAgL,QAAA,mBAAA0K,UAAA;QAAAtK,WAAA;MAAA,GAAAhD,IAAA;QACA,WAAAuN,mBAAA,EAAAnD,GAAA;MACA,GAAApK,IAAA;QACAqN,OAAA,CAAAtN,OAAA;QACAsN,OAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA,cACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAA/T,aAAA;IACA;IACA,aACAgU,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,8BAAApI,cAAA,CAAA/H,OAAA,MACA,KAAArD,WAAA,cAAAyT,MAAA,CACA,IAAAhB,IAAA,GAAAiB,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,GAAA;MACA,KAAA5T,WAAA,CAAAQ,UAAA,GAAAoT,GAAA;MACA,KAAA7E,WAAA;IACA;IACA8E,aAAA,WAAAA,cAAAD,GAAA;MACA,KAAAzS,IAAA,CAAAX,UAAA,GAAAoT,GAAA;IACA;IACAE,WAAA,WAAAA,YAAAF,GAAA;MACA,KAAAzS,IAAA,CAAA4S,QAAA,GAAAH,GAAA,CAAAI,eAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAL,GAAA;MACA,KAAAzS,IAAA,CAAAkI,eAAA,GAAAuK,GAAA;IACA;IACAM,iBAAA,WAAAA,kBAAAN,GAAA;MACA,KAAAzS,IAAA,CAAAP,cAAA,GAAAgT,GAAA;MACA,IAAAA,GAAA,IAAA3Q,SAAA;QACA,KAAApE,UAAA;QACA,KAAAsC,IAAA,CAAAtC,UAAA;MACA;IACA;IACAsV,mBAAA,WAAAA,oBAAAP,GAAA;MACA,KAAA5T,WAAA,CAAAY,cAAA,GAAAgT,GAAA;MACA,KAAA7E,WAAA;IACA;IACAqF,eAAA,WAAAA,gBAAAR,GAAA;MACA,KAAAzS,IAAA,CAAAN,YAAA,GAAA+S,GAAA;IACA;IACAS,iBAAA,WAAAA,kBAAAT,GAAA;MACA,KAAA5T,WAAA,CAAAa,YAAA,GAAA+S,GAAA;MACA,KAAA7E,WAAA;IACA;IACAuF,iBAAA,WAAAA,kBAAAV,GAAA;MACA,KAAAzS,IAAA,CAAAD,OAAA,GAAA0S,GAAA;IACA;IACAW,mBAAA,WAAAA,oBAAAX,GAAA;MACA,KAAA5T,WAAA,CAAAkB,OAAA,GAAA0S,GAAA;MACA,KAAA7E,WAAA;IACA;IACAyF,yBAAA,WAAAA,0BAAAZ,GAAA;MACA,KAAA5T,WAAA,CAAAc,oBAAA,GAAA8S,GAAA;MACA,KAAA7E,WAAA;IACA;IACA0F,mBAAA,WAAAA,oBAAAb,GAAA;MACA,KAAAzS,IAAA,CAAAJ,gBAAA,GAAA6S,GAAA;IACA;IACAc,yBAAA,WAAAA,0BAAAd,GAAA;MACA,KAAAzS,IAAA,CAAAH,sBAAA,GAAA4S,GAAA;IACA;IACAe,qBAAA,WAAAA,sBAAAf,GAAA;MACA,KAAA5T,WAAA,CAAAe,gBAAA,GAAA6S,GAAA;MACA,KAAA7E,WAAA;IACA;IACA6F,uBAAA,WAAAA,wBAAAhB,GAAA;MACA,KAAAzS,IAAA,CAAAL,oBAAA,GAAA8S,GAAA;IACA;IACAiB,2BAAA,WAAAA,4BAAAjB,GAAA;MACA,KAAA5T,WAAA,CAAAgB,sBAAA,GAAA4S,GAAA;MACA,KAAA7E,WAAA;IACA;IACA+F,qBAAA,WAAAA,sBAAAlB,GAAA;MACA,KAAAzS,IAAA,CAAAF,kBAAA,GAAA2S,GAAA;IACA;IACAmB,uBAAA,WAAAA,wBAAAnB,GAAA;MACA,KAAA5T,WAAA,CAAAiB,kBAAA,GAAA2S,GAAA;MACA,KAAA7E,WAAA;IACA;IACAiG,oBAAA,WAAAA,qBAAApJ,IAAA;MACA,KAAAzK,IAAA,CAAA1B,QAAA,GAAAmM,IAAA,CAAA1D,OAAA;IACA;IACA+M,sBAAA,WAAAA,uBAAAxR,CAAA;MACA,IAAAA,CAAA,IAAAR,SAAA;QACA,KAAA9B,IAAA,CAAA1B,QAAA;QACA,KAAAA,QAAA;MACA;IACA;IACAyV,oBAAA,WAAAA,qBAAAtJ,IAAA;MACA,KAAAzK,IAAA,CAAAzB,QAAA,GAAAkM,IAAA,CAAA1D,OAAA;IACA;IACAiN,sBAAA,WAAAA,uBAAAzR,KAAA;MACA,IAAAA,KAAA,IAAAT,SAAA;QACA,KAAA9B,IAAA,CAAAzB,QAAA;QACA,KAAAA,QAAA;MACA;IACA;IACA0V,qBAAA,WAAAA,sBAAAxJ,IAAA;MACA,KAAA5L,WAAA,CAAAL,cAAA,GAAAiM,IAAA,CAAA1D,OAAA;MACA,KAAA6G,WAAA;IACA;IACAsG,cAAA,WAAAA,eAAAzB,GAAA;MACA,IAAAA,GAAA,IAAA3Q,SAAA;QACA,KAAAjD,WAAA,CAAAL,cAAA;QACA,KAAAoP,WAAA;MACA;IACA;IACAuG,aAAA,WAAAA,cAAA1B,GAAA;MACA,IAAAA,GAAA,IAAA3Q,SAAA;QACA,KAAAjD,WAAA,CAAAJ,aAAA;QACA,KAAAmP,WAAA;MACA;IACA;IACAwG,oBAAA,WAAAA,qBAAA3J,IAAA;MAAA,IAAA4J,OAAA;MACA,KAAAxV,WAAA,CAAAJ,aAAA,GAAAgM,IAAA,CAAA1D,OAAA;MACA,IAAAV,sBAAA,EAAAoE,IAAA,CAAA1D,OAAA,EAAArC,IAAA,WAAA8B,QAAA;QACA6N,OAAA,CAAAxV,WAAA,CAAAa,YAAA,GAAA8G,QAAA,CAAA9G,YAAA;QACA2U,OAAA,CAAAxV,WAAA,CAAAY,cAAA,GAAA+G,QAAA,CAAA/G,cAAA;QACA4U,OAAA,CAAAxV,WAAA,CAAAc,oBAAA,GAAA6G,QAAA,CAAA7G,oBAAA;QACA0U,OAAA,CAAAxV,WAAA,CAAAe,gBAAA,GAAA4G,QAAA,CAAA5G,gBAAA;QACAyU,OAAA,CAAAxV,WAAA,CAAAgB,sBAAA,GAAA2G,QAAA,CAAA3G,sBAAA;QACAwU,OAAA,CAAAxV,WAAA,CAAAiB,kBAAA,GAAA0G,QAAA,CAAA1G,kBAAA;QACAuU,OAAA,CAAA5W,eAAA,GAAA+I,QAAA,CAAA/I,eAAA;QACA4W,OAAA,CAAAzG,WAAA;MACA;IACA;IACA0G,sBAAA,WAAAA,uBAAA7J,IAAA;MACA,KAAAzK,IAAA,CAAAtC,UAAA,CAAAwF,IAAA,CAAAuH,IAAA,CAAAlP,OAAA,CAAAyH,SAAA;IACA;IACAuR,2BAAA,WAAAA,4BAAA9J,IAAA;MACA,KAAA5L,WAAA,CAAAnB,UAAA,CAAAwF,IAAA,CAAAuH,IAAA,CAAAlP,OAAA,CAAAyH,SAAA;MACA,KAAA4K,WAAA;IACA;IACA4G,wBAAA,WAAAA,yBAAA/J,IAAA;MACA,KAAAzK,IAAA,CAAAtC,UAAA,QAAAsC,IAAA,CAAAtC,UAAA,CAAA+W,MAAA,WAAAxG,IAAA;QACA,OAAAA,IAAA,IAAAxD,IAAA,CAAAlP,OAAA,CAAAyH,SAAA;MACA;IACA;IACA0R,6BAAA,WAAAA,8BAAAjK,IAAA;MACA,KAAA5L,WAAA,CAAAnB,UAAA,QAAAmB,WAAA,CAAAnB,UAAA,CAAA+W,MAAA,WAAAxG,IAAA;QACA,OAAAA,IAAA,IAAAxD,IAAA,CAAAlP,OAAA,CAAAyH,SAAA;MACA;MACA,KAAA4K,WAAA;IACA;IACA+G,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA,KAAAlW,YAAA;MACA,KAAAmW,SAAA;QACAD,OAAA,CAAAlW,YAAA;MACA;IACA;IACAoW,kBAAA,WAAAA,mBAAA;MACA,KAAAhX,KAAA;IACA;IACAiX,WAAA,WAAAA,YAAAC,IAAA,EAAAC,GAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,qBAAA,EAAAH,IAAA,EAAAC,GAAA,EAAAvQ,IAAA,WAAA8B,QAAA;QACA0O,OAAA,CAAArM,QAAA,CAAAE,OAAA,CAAAvC,QAAA,CAAA2B,GAAA;QACA+M,OAAA,CAAApX,KAAA;QACAoX,OAAA,CAAAzQ,OAAA;MACA;IACA;IACA2Q,QAAA,WAAAA,SAAA;MAAA,IAAAC,OAAA;MACA,SAAArV,IAAA,CAAAwM,SAAA;QACA,KAAAxM,IAAA,CAAAsV,aAAA,QAAAtV,IAAA,CAAAsV,aAAA;QACA,KAAAtV,IAAA,CAAAuV,eAAA,QAAA3Y,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAC,GAAA;QACA,KAAAvG,IAAA,CAAAwV,iBAAA,QAAA5Y,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAlM,IAAA,CAAAsO,KAAA;QACA,KAAA1I,IAAA,CAAAyV,iBAAA,OAAA3Q,eAAA,MAAAwM,IAAA;QACA,IAAAI,sBAAA,OAAA1R,IAAA,EAAA0E,IAAA,WAAA8B,QAAA;UACA6O,OAAA,CAAA1D,MAAA,CAAAC,UAAA;QACA;MACA;QACA,KAAAD,MAAA,CAAA+D,QAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,SAAA5V,IAAA,CAAAwM,SAAA;QACA,KAAAxM,IAAA,CAAA6V,gBAAA,QAAA7V,IAAA,CAAA6V,gBAAA;QACA,KAAA7V,IAAA,CAAA8V,kBAAA,QAAAlZ,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAC,GAAA;QACA,KAAAvG,IAAA,CAAA+V,oBAAA,QAAAnZ,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAlM,IAAA,CAAAsO,KAAA;QACA,KAAA1I,IAAA,CAAAgW,oBAAA,OAAAlR,eAAA,MAAAwM,IAAA;QACA,IAAAI,sBAAA,OAAA1R,IAAA,EAAA0E,IAAA,WAAA8B,QAAA;UACAoP,OAAA,CAAAjE,MAAA,CAAAC,UAAA;QACA;MACA;QACA,KAAAD,MAAA,CAAA+D,QAAA;MACA;IACA;IACAO,OAAA,WAAAA,QAAA;MAAA,IAAAC,OAAA;MACA,SAAAlW,IAAA,CAAAwM,SAAA;QACA,KAAAxM,IAAA,CAAAkB,YAAA,QAAAlB,IAAA,CAAAkB,YAAA;QACA,KAAAlB,IAAA,CAAAiN,cAAA,QAAArQ,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAC,GAAA;QACA,KAAAvG,IAAA,CAAAkN,gBAAA,QAAAtQ,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAlM,IAAA,CAAAsO,KAAA;QACA,KAAA1I,IAAA,CAAAmN,gBAAA,OAAArI,eAAA,MAAAwM,IAAA;QACA,IAAAI,sBAAA,OAAA1R,IAAA,EAAA0E,IAAA,WAAA8B,QAAA;UACA0P,OAAA,CAAAvE,MAAA,CAAAC,UAAA;QACA;MACA;QACA,KAAAD,MAAA,CAAA+D,QAAA;MACA;IACA;IACAS,MAAA,WAAAA,OAAA;MAAA,IAAAC,OAAA;MACA,SAAApW,IAAA,CAAAwM,SAAA;QACA,KAAAxM,IAAA,CAAAqB,WAAA,QAAArB,IAAA,CAAAqB,WAAA;QACA,KAAArB,IAAA,CAAAuN,aAAA,QAAA3Q,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAC,GAAA;QACA,KAAAvG,IAAA,CAAAwN,eAAA,QAAA5Q,MAAA,CAAAC,KAAA,CAAAyJ,IAAA,CAAAlM,IAAA,CAAAsO,KAAA;QACA,KAAA1I,IAAA,CAAAyN,eAAA,OAAA3I,eAAA,MAAAwM,IAAA;QACA,IAAAI,sBAAA,OAAA1R,IAAA,EAAA0E,IAAA,WAAA8B,QAAA;UACA4P,OAAA,CAAAzE,MAAA,CAAAC,UAAA;QACA;MACA;QACA,KAAAD,MAAA,CAAA+D,QAAA;MACA;IACA;IACAW,eAAA,WAAAA,gBAAA5D,GAAA;MACA,KAAAzS,IAAA,CAAA4O,qBAAA,GAAA6D,GAAA;IACA;IACA6D,cAAA,WAAAA,eAAA7D,GAAA;MACA,KAAAzS,IAAA,CAAAuW,WAAA,GAAA9D,GAAA;IACA;IACA+D,iBAAA,WAAAA,kBAAA/D,GAAA;MACA,KAAAzS,IAAA,CAAAlE,cAAA,GAAA2W,GAAA;IACA;IACAf,aAAA,WAAAA,cAAA1R,IAAA;MAAA,IAAAyW,OAAA;MAAA,WAAAlN,kBAAA,CAAArH,OAAA,oBAAAsH,oBAAA,CAAAtH,OAAA,IAAAuH,IAAA,UAAAiN,SAAA;QAAA,IAAAlQ,QAAA,EAAAmQ,WAAA,EAAAC,OAAA,EAAAhU,CAAA,EAAAiU,WAAA,EAAAC,OAAA,EAAA/T,CAAA,EAAAgU,WAAA,EAAAC,OAAA,EAAAxT,CAAA,EAAA6M,SAAA;QAAA,WAAA7G,oBAAA,CAAAtH,OAAA,IAAAyH,IAAA,UAAAsN,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApN,IAAA,GAAAoN,SAAA,CAAAnN,IAAA;YAAA;cACA;cACA;;cAEA;;cAEA,IAAA0M,OAAA,CAAAxX,UAAA,SAAAwX,OAAA,CAAAzX,QAAA;gBACAyX,OAAA,CAAAzW,IAAA,CAAA2Q,kBAAA,GAAA8F,OAAA,CAAAzW,IAAA,CAAA2Q,kBAAA,GAAA8F,OAAA,CAAAzW,IAAA,CAAA2Q,kBAAA,CAAAE,OAAA;cACA;cACA,IAAA4F,OAAA,CAAAvX,YAAA,SAAAuX,OAAA,CAAAtX,WAAA;gBACAsX,OAAA,CAAAzW,IAAA,CAAA+Q,cAAA,GAAA0F,OAAA,CAAAzW,IAAA,CAAA+Q,cAAA,GAAA0F,OAAA,CAAAzW,IAAA,CAAA+Q,cAAA,CAAAF,OAAA;cACA;cAEA,IAAA4F,OAAA,CAAAzW,IAAA,CAAAoQ,kBAAA,IAAAqG,OAAA,CAAAzW,IAAA,CAAAoQ,kBAAA,CAAA3N,MAAA;gBACAgU,OAAA,CAAAzW,IAAA,CAAAC,kBAAA,GAAAwW,OAAA,CAAAzW,IAAA,CAAAoQ,kBAAA;gBACAqG,OAAA,CAAAzW,IAAA,CAAAE,gBAAA,GAAAuW,OAAA,CAAAzW,IAAA,CAAAoQ,kBAAA;cACA;cAAA8G,SAAA,CAAAnN,IAAA;cAAA,OAEA,IAAA2H,sBAAA,EAAA1R,IAAA;YAAA;cACAyW,OAAA,CAAA9E,MAAA,CAAAC,UAAA;cAAAsF,SAAA,CAAAnN,IAAA;cAAA,OACA,IAAA8F,mBAAA,EAAA7P,IAAA,CAAAwM,SAAA;YAAA;cAAAhG,QAAA,GAAA0Q,SAAA,CAAAC,IAAA;cACA;cACA;cACAV,OAAA,CAAAzW,IAAA,GAAAwG,QAAA,CAAAxK,IAAA;cACA,IAAAya,OAAA,CAAApZ,UAAA,IAAAyE,SAAA;gBAAA6U,WAAA,OAAA1U,2BAAA,CAAAC,OAAA,EACAuU,OAAA,CAAApZ,UAAA;gBAAA;kBAAA,KAAAsZ,WAAA,CAAAvU,CAAA,MAAAwU,OAAA,GAAAD,WAAA,CAAAlV,CAAA,IAAAY,IAAA;oBAAAO,CAAA,GAAAgU,OAAA,CAAArU,KAAA;oBACA,IAAAK,CAAA,CAAAJ,QAAA,IAAAV,SAAA;sBAAA+U,WAAA,OAAA5U,2BAAA,CAAAC,OAAA,EACAU,CAAA,CAAAJ,QAAA;sBAAA;wBAAA,KAAAqU,WAAA,CAAAzU,CAAA,MAAA0U,OAAA,GAAAD,WAAA,CAAApV,CAAA,IAAAY,IAAA;0BAAAU,CAAA,GAAA+T,OAAA,CAAAvU,KAAA;0BACA,IAAAQ,CAAA,CAAAP,QAAA,IAAAV,SAAA;4BAAAiV,WAAA,OAAA9U,2BAAA,CAAAC,OAAA,EACAa,CAAA,CAAAP,QAAA;4BAAA;8BAAA,KAAAuU,WAAA,CAAA3U,CAAA,MAAA4U,OAAA,GAAAD,WAAA,CAAAtV,CAAA,IAAAY,IAAA;gCAAAmB,CAAA,GAAAwT,OAAA,CAAAzU,KAAA;gCACA,IAAAiB,CAAA,CAAAuD,OAAA,IAAAP,QAAA,CAAAxK,IAAA,CAAAsC,QAAA;kCACAmY,OAAA,CAAAnY,QAAA,GAAAkF,CAAA,CAAAyE,MAAA;gCACA;gCACA,IAAAzE,CAAA,CAAAuD,OAAA,IAAAP,QAAA,CAAAxK,IAAA,CAAAuC,QAAA;kCACAkY,OAAA,CAAAlY,QAAA,GAAAiF,CAAA,CAAAyE,MAAA;gCACA;8BACA;4BAAA,SAAA9E,GAAA;8BAAA4T,WAAA,CAAA3T,CAAA,CAAAD,GAAA;4BAAA;8BAAA4T,WAAA,CAAA1T,CAAA;4BAAA;0BACA;wBACA;sBAAA,SAAAF,GAAA;wBAAA0T,WAAA,CAAAzT,CAAA,CAAAD,GAAA;sBAAA;wBAAA0T,WAAA,CAAAxT,CAAA;sBAAA;oBACA;kBACA;gBAAA,SAAAF,GAAA;kBAAAwT,WAAA,CAAAvT,CAAA,CAAAD,GAAA;gBAAA;kBAAAwT,WAAA,CAAAtT,CAAA;gBAAA;cACA;cACAoT,OAAA,CAAAzW,IAAA,CAAAD,OAAA,GAAAyG,QAAA,CAAAzG,OAAA;cACA0W,OAAA,CAAAzW,IAAA,CAAAP,cAAA,GAAA+G,QAAA,CAAA/G,cAAA;cACAgX,OAAA,CAAAzW,IAAA,CAAAN,YAAA,GAAA8G,QAAA,CAAA9G,YAAA;cACA+W,OAAA,CAAAzW,IAAA,CAAAJ,gBAAA,GAAA4G,QAAA,CAAA5G,gBAAA;cACA6W,OAAA,CAAAzW,IAAA,CAAAL,oBAAA,GAAA6G,QAAA,CAAA7G,oBAAA;cACA8W,OAAA,CAAAzW,IAAA,CAAAF,kBAAA,GAAA0G,QAAA,CAAA1G,kBAAA;cACA2W,OAAA,CAAAzW,IAAA,CAAAH,sBAAA,GAAA2G,QAAA,CAAA3G,sBAAA;cACA4W,OAAA,CAAAzW,IAAA,CAAAtC,UAAA,GAAA8I,QAAA,CAAA9I,UAAA;cACA;cACA+Y,OAAA,CAAAzW,IAAA,CAAAkI,eAAA,GAAA1B,QAAA,CAAA0B,eAAA;cACAuO,OAAA,CAAAhZ,eAAA,GAAA+I,QAAA,CAAA/I,eAAA;cACAgZ,OAAA,CAAA1Y,WAAA;cACA0Y,OAAA,CAAA5Y,KAAA;cACA4Y,OAAA,CAAAxa,OAAA;cAEAoU,SAAA,OAAAC,IAAA,CAAAC,YAAA;gBACAC,KAAA;gBACAC,qBAAA;gBACAC,qBAAA;cACA;cACA;AACA;AACA;cAEA,IAAA+F,OAAA,CAAAxX,UAAA,SAAAwX,OAAA,CAAAzX,QAAA;gBACAyX,OAAA,CAAAzW,IAAA,CAAA2Q,kBAAA,GAAAnK,QAAA,CAAAxK,IAAA,CAAA2U,kBAAA,CAAAC,cAAA;gBACA6F,OAAA,CAAAzW,IAAA,CAAA2Q,kBAAA,GAAA8F,OAAA,CAAAzW,IAAA,CAAA2Q,kBAAA,GAAAS,MAAA,CAAAqF,OAAA,CAAAzW,IAAA,CAAA2Q,kBAAA,EAAAE,OAAA;gBACA4F,OAAA,CAAAzW,IAAA,CAAA2Q,kBAAA,GAAAN,SAAA,CAAAS,MAAA,CAAA2F,OAAA,CAAAzW,IAAA,CAAA2Q,kBAAA;cACA;cACA,IAAA8F,OAAA,CAAAvX,YAAA,SAAAuX,OAAA,CAAAtX,WAAA;gBACAsX,OAAA,CAAAzW,IAAA,CAAA+Q,cAAA,GAAAvK,QAAA,CAAAxK,IAAA,CAAA+U,cAAA,CAAAH,cAAA;gBACA6F,OAAA,CAAAzW,IAAA,CAAA+Q,cAAA,GAAA0F,OAAA,CAAAzW,IAAA,CAAA+Q,cAAA,GAAAK,MAAA,CAAAqF,OAAA,CAAAzW,IAAA,CAAA+Q,cAAA,EAAAF,OAAA;gBACA4F,OAAA,CAAAzW,IAAA,CAAA+Q,cAAA,GAAAV,SAAA,CAAAS,MAAA,CAAA2F,OAAA,CAAAzW,IAAA,CAAA+Q,cAAA;cACA;;cAEA;cACA0F,OAAA,CAAAzW,IAAA,CAAAoQ,kBAAA,GAAA5J,QAAA,CAAAxK,IAAA,CAAAiE,kBAAA,YAAAuG,QAAA,CAAAxK,IAAA,CAAAkE,gBAAA,YAAAsG,QAAA,CAAAxK,IAAA,CAAAiE,kBAAA,EAAAuG,QAAA,CAAAxK,IAAA,CAAAkE,gBAAA;cACA;AACA;AACA;AACA;;cAEA;cACAuW,OAAA,CAAAzW,IAAA,CAAAmB,cAAA,GAAAqF,QAAA,CAAAxK,IAAA,CAAAmF,cAAA;cACAsV,OAAA,CAAAzW,IAAA,CAAAkB,YAAA,GAAAsF,QAAA,CAAAxK,IAAA,CAAAkF,YAAA;cACAuV,OAAA,CAAAzW,IAAA,CAAAqB,WAAA,GAAAmF,QAAA,CAAAxK,IAAA,CAAAqF,WAAA;cACAoV,OAAA,CAAAzW,IAAA,CAAAuB,YAAA,GAAAiF,QAAA,CAAAxK,IAAA,CAAAuF,YAAA;YAAA;YAAA;cAAA,OAAA2V,SAAA,CAAA3M,IAAA;UAAA;QAAA,GAAAmM,QAAA;MAAA;IACA;IACAU,iBAAA,WAAAA,kBAAA;MACA,SAAApX,IAAA,CAAA2Q,kBAAA,iBAAA3Q,IAAA,CAAA+Q,cAAA;QACA,IAAAV,SAAA,OAAAC,IAAA,CAAAC,YAAA;UACAC,KAAA;UACAC,qBAAA;UACAC,qBAAA;QACA;QACA,SAAAzR,UAAA;UACA,KAAAe,IAAA,CAAA2Q,kBAAA,GAAAS,MAAA,MAAApR,IAAA,CAAA2Q,kBAAA,EAAAE,OAAA;UACA,KAAA7Q,IAAA,CAAA2Q,kBAAA,GAAAN,SAAA,CAAAS,MAAA,MAAA9Q,IAAA,CAAA2Q,kBAAA;QACA;QACA,SAAAzR,YAAA;UACA,KAAAc,IAAA,CAAA+Q,cAAA,GAAAK,MAAA,MAAApR,IAAA,CAAA+Q,cAAA,EAAAF,OAAA;UACA,KAAA7Q,IAAA,CAAA+Q,cAAA,GAAAV,SAAA,CAAAS,MAAA,MAAA9Q,IAAA,CAAA+Q,cAAA;QACA;MAEA;IACA;IACAsG,wBAAA,WAAAA,yBAAA9U,KAAA;MACA,IAAA8N,SAAA,OAAAC,IAAA,CAAAC,YAAA;QACA+G,QAAA;MACA;MACA,OAAAjH,SAAA,CAAAS,MAAA,CAAAvO,KAAA;IACA;IACAgV,UAAA,WAAAA,WAAA;MACA,KAAAC,YAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAvV,OAAA,GAAAwV,QAAA"}]}