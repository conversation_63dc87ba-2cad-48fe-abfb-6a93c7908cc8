{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\login.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\login.vue", "mtime": 1754646305884}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\" :show-message=\"false\"\r\n             v-loading=\"mac\" status-icon\r\n    >\r\n      <div class=\"title\">\r\n        <h3 style=\"margin: auto;width: fit-content\" @dblclick=\"toggleDebugMode\">瑞旗系统</h3>\r\n        <!-- 添加调试区域 -->\r\n        <!--<div v-if=\"showDebugOptions\" class=\"debug-options\">-->\r\n        <!--  <el-checkbox v-model=\"enableWechatVerify\" border size=\"mini\">启用微信验证</el-checkbox>-->\r\n        <!--</div>-->\r\n      </div>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"loginForm.username\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n          type=\"text\"\r\n        >\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"loginForm.password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          type=\"password\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item v-if=\"captchaEnabled\" prop=\"code\">\r\n        <el-input\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n        </el-input>\r\n        <div class=\"login-code\">\r\n          <img :src=\"codeUrl\" class=\"login-code-img\" @click=\"getCode\"/>\r\n        </div>\r\n      </el-form-item>\r\n      <el-checkbox v-model=\"loginForm.rememberMe\" style=\"margin:0px 0px 25px 0px;\">记住密码</el-checkbox>\r\n      <el-form-item style=\"width:100%;\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          style=\"width:100%;\"\r\n          type=\"primary\"\r\n          @click=\"handleLogin\"\r\n        >\r\n          <span v-if=\"!loading\">登 录</span>\r\n          <span v-else>登 录 中...</span>\r\n        </el-button>\r\n        <div v-if=\"register\" style=\"float: right;\">\r\n          <router-link :to=\"'/register'\" class=\"link-type\">立即注册</router-link>\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-login-footer\">\r\n      <span @click=\"incrementDebugCounter\">Copyright © 2009-2024 RichShipping All Rights Reserved.</span>\r\n    </div>\r\n\r\n    <!-- 微信扫码登录弹窗 -->\r\n    <wechat-scan\r\n      v-if=\"showWechatScan\"\r\n      :visible.sync=\"showWechatScan\"\r\n      :username=\"loginForm.username\"\r\n      @scan-success=\"handleWechatScanSuccess\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getCodeImg, getMac, checkNeedWechatScan} from '@/api/login'\r\nimport Cookies from 'js-cookie'\r\nimport {decrypt, encrypt} from '@/utils/jsencrypt'\r\nimport Fingerprint2 from 'fingerprintjs2'\r\nimport Fingerprint from '@fingerprintjs/fingerprintjs'\r\nimport WechatScan from '@/components/WechatScan'\r\n\r\nexport default {\r\n  name: 'Login',\r\n  components: {\r\n    WechatScan\r\n  },\r\n  data() {\r\n    return {\r\n      mac: false,\r\n      codeUrl: '',\r\n      loginForm: {\r\n        username: '',\r\n        password: '',\r\n        rememberMe: false,\r\n        code: '',\r\n        uuid: '',\r\n        unid: ''\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          {required: true, trigger: 'blur', message: '您的账号'}\r\n        ],\r\n        password: [\r\n          {required: true, trigger: 'blur', message: '您的密码'}\r\n        ],\r\n        code: [{required: true, trigger: 'change', message: '验证码'}]\r\n      },\r\n      loading: false,\r\n      // 验证码开关\r\n      captchaEnabled: true,\r\n      // 注册开关\r\n      register: false,\r\n      redirect: undefined,\r\n      // 微信扫码登录\r\n      showWechatScan: false,\r\n      // 调试选项\r\n      showDebugOptions: false,\r\n      debugClickCount: 0,\r\n      // 微信验证开关 - 根据环境设置，生产环境开启，开发环境关闭\r\n      enableWechatVerify: process.env.NODE_ENV === 'production',\r\n      // 微信扫码验证Cookie名\r\n      wechatScanCookieName: 'wechat_scan_verified'\r\n    }\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function (route) {\r\n        this.redirect = route.query && route.query.redirect\r\n      },\r\n      immediate: true\r\n    },\r\n    // 监听微信验证开关变化，保存到本地存储\r\n    enableWechatVerify: {\r\n      handler(val) {\r\n        localStorage.setItem('debug_enable_wechat_verify', val.toString());\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getCode()\r\n    this.getCookie()\r\n    // 根据环境设置微信验证开关，且允许本地存储覆盖(仅在调试模式下)\r\n    const storedSetting = localStorage.getItem('debug_enable_wechat_verify');\r\n    if (this.showDebugOptions && storedSetting !== null) {\r\n      this.enableWechatVerify = storedSetting === 'true';\r\n    } else {\r\n      // 默认根据环境设置：生产环境开启，开发环境关闭\r\n      this.enableWechatVerify = process.env.NODE_ENV === 'production';\r\n      // 更新本地存储\r\n      localStorage.setItem('debug_enable_wechat_verify', this.enableWechatVerify.toString());\r\n    }\r\n  },\r\n  methods: {\r\n    // 切换调试模式\r\n    toggleDebugMode() {\r\n      // 双击标题时显示调试选项\r\n      this.showDebugOptions = !this.showDebugOptions;\r\n      if (this.showDebugOptions) {\r\n        this.$message.success('已' + (this.enableWechatVerify ? '启用' : '关闭') + '微信验证');\r\n      }\r\n    },\r\n    // 点击版权信息增加计数器\r\n    incrementDebugCounter() {\r\n      this.debugClickCount++;\r\n      if (this.debugClickCount >= 5) {\r\n        this.showDebugOptions = true;\r\n        this.debugClickCount = 0;\r\n        this.$message.success('已' + (this.enableWechatVerify ? '启用' : '关闭') + '微信验证');\r\n      }\r\n    },\r\n    getMac() {\r\n      this.mac = true\r\n      this.getFingerPrint(v => {\r\n        this.$alert(v, '', {\r\n          callback: action => {\r\n            if (action == 'confirm') {\r\n              this.$message.success('已复制')\r\n              this.mac = false\r\n              navigator.clipboard.writeText(v)\r\n            }\r\n            if (action == 'cancel') {\r\n              this.mac = false\r\n            }\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async getFingerPrint(callback) {\r\n      let options = Fingerprint2.Options = {\r\n        excludes: {\r\n          webdriver: true,\r\n          userAgent: true,\r\n          language: true,\r\n          colorDepth: true,\r\n          deviceMemory: true,\r\n          pixelRatio: true,\r\n          hardwareConcurrency: true,\r\n          screenResolution: true,\r\n          availableScreenResolution: true,\r\n          timezoneOffset: true,\r\n          timezone: true,\r\n          sessionStorage: true,\r\n          localStorage: true,\r\n          indexedDb: true,\r\n          addBehavior: true,\r\n          openDatabase: true,\r\n          cpuClass: true,\r\n          platform: true,\r\n          doNotTrack: true,\r\n          plugins: true,\r\n          canvas: true,\r\n          webgl: false,\r\n          webglVendorAndRenderer: false,\r\n          adBlock: true,\r\n          hasLiedLanguages: true,\r\n          hasLiedResolution: true,\r\n          hasLiedOs: true,\r\n          hasLiedBrowser: true,\r\n          touchSupport: true,\r\n          fonts: true,\r\n          fontsFlash: true,\r\n          audio: false,\r\n          enumerateDevices: true\r\n        }\r\n      }\r\n      Fingerprint2.get(options, async (components) => {\r\n        const values = components.map(function (component, index) {\r\n            return component.value\r\n          })\r\n          const murmur = Fingerprint2.x64hash128(values.join(''), 31)\r\n        const fp = await Fingerprint.load()\r\n        const result = await fp.get()\r\n        callback(result.visitorId)\r\n        }\r\n      )\r\n    },\r\n    async logCode() {\r\n      const fp = await Fingerprint.load()\r\n      const result = await fp.get()\r\n      console.log('Browser fingerprint:', result.visitorId)\r\n      return result.visitorId\r\n    },\r\n    getCode() {\r\n      getCodeImg().then(res => {\r\n        this.captchaEnabled = res.captchaEnabled == undefined ? true : res.captchaEnabled\r\n        if (this.captchaEnabled) {\r\n          this.codeUrl = 'data:image/gif;base64,' + res.img\r\n          this.loginForm.uuid = res.uuid\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    getCookie() {\r\n      const username = Cookies.get('username')\r\n      const password = Cookies.get('password')\r\n      const rememberMe = Cookies.get('rememberMe')\r\n      this.loginForm = {\r\n        username: username == undefined ? this.loginForm.username : username,\r\n        password: password == undefined ? this.loginForm.password : decrypt(password),\r\n        rememberMe: rememberMe == undefined ? false : Boolean(rememberMe)\r\n      }\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true\r\n          if (this.loginForm.rememberMe) {\r\n            Cookies.set('username', this.loginForm.username, {expires: 30})\r\n            Cookies.set('password', encrypt(this.loginForm.password), {expires: 30})\r\n            Cookies.set('rememberMe', this.loginForm.rememberMe, {expires: 30})\r\n          } else {\r\n            Cookies.remove('username')\r\n            Cookies.remove('password')\r\n            Cookies.remove('rememberMe')\r\n          }\r\n          this.getFingerPrint(v => {\r\n            this.loginForm.unid = v\r\n\r\n            // 检查是否需要微信扫码验证\r\n            this.checkNeedWechatScan()\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 检查是否需要微信扫码验证\r\n    async checkNeedWechatScan() {\r\n      // 如果微信验证开关关闭，直接登录\r\n      if (!this.enableWechatVerify) {\r\n        console.log('微信验证已关闭，直接登录');\r\n        this.doLogin();\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const res = await checkNeedWechatScan(this.loginForm.username)\r\n        if (res.code === 200) {\r\n          const needScan = res.data.needScan\r\n          const isWechatBound = res.data.isWechatBound;\r\n\r\n          // 检查Cookie中是否存在微信扫码验证标记\r\n          const scanVerified = Cookies.get(this.wechatScanCookieName);\r\n          if (needScan && !scanVerified) {\r\n            // 需要微信扫码验证\r\n            this.showWechatScan = true;\r\n            this.loading = false;\r\n\r\n            // 如果用户未绑定微信，显示提示\r\n            if (!isWechatBound) {\r\n              this.$message.info('首次登录需要绑定微信账号进行验证');\r\n            }\r\n          } else {\r\n            // 不需要微信扫码验证，直接登录\r\n            this.doLogin();\r\n          }\r\n        } else {\r\n          // 检查失败，直接登录\r\n          this.doLogin();\r\n        }\r\n      } catch (error) {\r\n        console.error('检查微信扫码验证失败', error);\r\n        // 发生错误，直接登录\r\n        this.doLogin();\r\n      }\r\n    },\r\n\r\n    // 执行登录\r\n    doLogin() {\r\n      this.$store.dispatch('Login', this.loginForm).then(() => {\r\n        // 登录成功后，设置微信扫码验证标记Cookie，有效期24小时\r\n        Cookies.set(this.wechatScanCookieName, 'true', {expires: 1});\r\n        this.$router.push({path: this.redirect || '/'}).catch(() => {\r\n        })\r\n      }).catch(() => {\r\n        this.loading = false\r\n        if (this.captchaEnabled) {\r\n          this.getCode()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 微信扫码登录成功回调\r\n    handleWechatScanSuccess() {\r\n      this.showWechatScan = false\r\n      this.doLogin()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" rel=\"stylesheet/scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n\r\n.title {\r\n  margin: 0 auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n\r\n  .el-input {\r\n    height: 38px;\r\n\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n\r\n.login-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n\r\n.login-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n\r\n.el-login-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial, serif;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.login-code-img {\r\n  height: 38px;\r\n}\r\n\r\n/* 添加调试选项样式 */\r\n.debug-options {\r\n  margin-top: 10px;\r\n  padding: 5px;\r\n  background-color: rgba(255, 255, 100, 0.2);\r\n  border: 1px dashed #e6a23c;\r\n  border-radius: 4px;\r\n}\r\n</style>\r\n"]}]}