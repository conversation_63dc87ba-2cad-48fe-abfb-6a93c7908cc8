package com.rich.system.service;

import com.rich.common.core.domain.entity.RsRct;
import com.rich.common.core.domain.entity.RsServiceInstances;
import com.rich.common.core.domain.entity.RsOpSeaFcl;
import com.rich.common.core.domain.entity.RsCharge;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 性能对比测试
 * 比较原始方法和优化方法的性能差异
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class PerformanceComparisonTest {
    
    @MockBean
    private RsRctService originalRsRctService;
    
    @MockBean
    private OptimizedRsRctService optimizedRsRctService;
    
    private List<RsRct> testDataSets;
    
    @BeforeEach
    void setUp() {
        testDataSets = createTestDataSets();
        
        // 模拟原始服务的行为
        when(originalRsRctService.saveAllServices(any(RsRct.class)))
                .thenAnswer(invocation -> {
                    // 模拟原始方法的处理时间（较慢）
                    Thread.sleep(2000); // 2秒
                    return invocation.getArgument(0);
                });
        
        // 模拟优化服务的行为
        when(optimizedRsRctService.saveAllServicesOptimized(any(RsRct.class)))
                .thenAnswer(invocation -> {
                    // 模拟优化方法的处理时间（较快）
                    Thread.sleep(500); // 0.5秒
                    return invocation.getArgument(0);
                });
    }
    
    @Test
    @DisplayName("单个操作单处理性能对比")
    void testSingleRctProcessingPerformance() {
        RsRct testRct = testDataSets.get(0);
        
        // 测试原始方法
        long originalStartTime = System.currentTimeMillis();
        RsRct originalResult = originalRsRctService.saveAllServices(testRct);
        long originalDuration = System.currentTimeMillis() - originalStartTime;
        
        // 测试优化方法
        long optimizedStartTime = System.currentTimeMillis();
        RsRct optimizedResult = optimizedRsRctService.saveAllServicesOptimized(testRct);
        long optimizedDuration = System.currentTimeMillis() - optimizedStartTime;
        
        // 验证结果
        assertNotNull(originalResult);
        assertNotNull(optimizedResult);
        assertEquals(originalResult.getRctId(), optimizedResult.getRctId());
        
        // 验证性能提升
        assertTrue(optimizedDuration < originalDuration, 
                String.format("优化方法应该更快。原始: %dms, 优化: %dms", originalDuration, optimizedDuration));
        
        double improvementRatio = (double) originalDuration / optimizedDuration;
        assertTrue(improvementRatio > 1.5, 
                String.format("性能提升应该超过50%%。实际提升: %.2fx", improvementRatio));
        
        System.out.printf("单个操作单处理性能对比:\n");
        System.out.printf("原始方法: %dms\n", originalDuration);
        System.out.printf("优化方法: %dms\n", optimizedDuration);
        System.out.printf("性能提升: %.2fx\n", improvementRatio);
    }
    
    @Test
    @DisplayName("批量操作单处理性能对比")
    void testBatchRctProcessingPerformance() {
        int batchSize = Math.min(5, testDataSets.size());
        List<RsRct> batchData = testDataSets.subList(0, batchSize);
        
        // 测试原始方法批量处理
        long originalStartTime = System.currentTimeMillis();
        List<RsRct> originalResults = new ArrayList<>();
        for (RsRct rct : batchData) {
            originalResults.add(originalRsRctService.saveAllServices(rct));
        }
        long originalDuration = System.currentTimeMillis() - originalStartTime;
        
        // 测试优化方法批量处理
        long optimizedStartTime = System.currentTimeMillis();
        List<RsRct> optimizedResults = new ArrayList<>();
        for (RsRct rct : batchData) {
            optimizedResults.add(optimizedRsRctService.saveAllServicesOptimized(rct));
        }
        long optimizedDuration = System.currentTimeMillis() - optimizedStartTime;
        
        // 验证结果
        assertEquals(originalResults.size(), optimizedResults.size());
        assertEquals(batchSize, originalResults.size());
        
        // 验证性能提升
        assertTrue(optimizedDuration < originalDuration, 
                String.format("批量处理优化方法应该更快。原始: %dms, 优化: %dms", originalDuration, optimizedDuration));
        
        double improvementRatio = (double) originalDuration / optimizedDuration;
        double avgOriginalTime = (double) originalDuration / batchSize;
        double avgOptimizedTime = (double) optimizedDuration / batchSize;
        
        System.out.printf("批量操作单处理性能对比 (数量: %d):\n", batchSize);
        System.out.printf("原始方法总时间: %dms (平均: %.2fms)\n", originalDuration, avgOriginalTime);
        System.out.printf("优化方法总时间: %dms (平均: %.2fms)\n", optimizedDuration, avgOptimizedTime);
        System.out.printf("性能提升: %.2fx\n", improvementRatio);
    }
    
    @Test
    @DisplayName("并发处理性能对比")
    void testConcurrentProcessingPerformance() throws InterruptedException {
        int concurrentCount = 3;
        List<RsRct> concurrentData = testDataSets.subList(0, Math.min(concurrentCount, testDataSets.size()));
        
        // 测试原始方法并发处理
        long originalStartTime = System.currentTimeMillis();
        List<Thread> originalThreads = new ArrayList<>();
        
        for (RsRct rct : concurrentData) {
            Thread thread = new Thread(() -> {
                originalRsRctService.saveAllServices(rct);
            });
            originalThreads.add(thread);
            thread.start();
        }
        
        for (Thread thread : originalThreads) {
            thread.join();
        }
        long originalDuration = System.currentTimeMillis() - originalStartTime;
        
        // 测试优化方法并发处理
        long optimizedStartTime = System.currentTimeMillis();
        List<Thread> optimizedThreads = new ArrayList<>();
        
        for (RsRct rct : concurrentData) {
            Thread thread = new Thread(() -> {
                optimizedRsRctService.saveAllServicesOptimized(rct);
            });
            optimizedThreads.add(thread);
            thread.start();
        }
        
        for (Thread thread : optimizedThreads) {
            thread.join();
        }
        long optimizedDuration = System.currentTimeMillis() - optimizedStartTime;
        
        // 验证性能提升
        assertTrue(optimizedDuration < originalDuration, 
                String.format("并发处理优化方法应该更快。原始: %dms, 优化: %dms", originalDuration, optimizedDuration));
        
        double improvementRatio = (double) originalDuration / optimizedDuration;
        
        System.out.printf("并发处理性能对比 (并发数: %d):\n", concurrentCount);
        System.out.printf("原始方法: %dms\n", originalDuration);
        System.out.printf("优化方法: %dms\n", optimizedDuration);
        System.out.printf("性能提升: %.2fx\n", improvementRatio);
    }
    
    @Test
    @DisplayName("内存使用对比")
    void testMemoryUsageComparison() {
        RsRct testRct = testDataSets.get(0);
        
        // 测试原始方法内存使用
        Runtime runtime = Runtime.getRuntime();
        runtime.gc(); // 强制垃圾回收
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
        
        originalRsRctService.saveAllServices(testRct);
        
        runtime.gc(); // 强制垃圾回收
        long memoryAfterOriginal = runtime.totalMemory() - runtime.freeMemory();
        long originalMemoryUsage = memoryAfterOriginal - memoryBefore;
        
        // 测试优化方法内存使用
        runtime.gc(); // 强制垃圾回收
        memoryBefore = runtime.totalMemory() - runtime.freeMemory();
        
        optimizedRsRctService.saveAllServicesOptimized(testRct);
        
        runtime.gc(); // 强制垃圾回收
        long memoryAfterOptimized = runtime.totalMemory() - runtime.freeMemory();
        long optimizedMemoryUsage = memoryAfterOptimized - memoryBefore;
        
        System.out.printf("内存使用对比:\n");
        System.out.printf("原始方法: %d bytes\n", originalMemoryUsage);
        System.out.printf("优化方法: %d bytes\n", optimizedMemoryUsage);
        
        if (optimizedMemoryUsage < originalMemoryUsage) {
            double memoryReduction = (double) (originalMemoryUsage - optimizedMemoryUsage) / originalMemoryUsage * 100;
            System.out.printf("内存使用减少: %.2f%%\n", memoryReduction);
        }
    }
    
    @Test
    @DisplayName("事务持续时间对比")
    void testTransactionDurationComparison() {
        RsRct testRct = testDataSets.get(0);
        
        // 模拟事务持续时间监控
        Map<String, Long> transactionDurations = new HashMap<>();
        
        // 原始方法事务时间（模拟长事务）
        long originalTxStart = System.currentTimeMillis();
        originalRsRctService.saveAllServices(testRct);
        long originalTxDuration = System.currentTimeMillis() - originalTxStart;
        transactionDurations.put("original", originalTxDuration);
        
        // 优化方法事务时间（模拟短事务）
        long optimizedTxStart = System.currentTimeMillis();
        optimizedRsRctService.saveAllServicesOptimized(testRct);
        long optimizedTxDuration = System.currentTimeMillis() - optimizedTxStart;
        transactionDurations.put("optimized", optimizedTxDuration);
        
        // 验证事务时间缩短
        assertTrue(optimizedTxDuration < originalTxDuration, 
                "优化方法的事务时间应该更短");
        
        double txImprovementRatio = (double) originalTxDuration / optimizedTxDuration;
        
        System.out.printf("事务持续时间对比:\n");
        System.out.printf("原始方法事务时间: %dms\n", originalTxDuration);
        System.out.printf("优化方法事务时间: %dms\n", optimizedTxDuration);
        System.out.printf("事务时间缩短: %.2fx\n", txImprovementRatio);
        
        // 验证事务时间在合理范围内
        assertTrue(originalTxDuration < 10000, "原始方法事务时间不应超过10秒");
        assertTrue(optimizedTxDuration < 5000, "优化方法事务时间不应超过5秒");
    }
    
    /**
     * 创建测试数据集
     */
    private List<RsRct> createTestDataSets() {
        List<RsRct> dataSets = new ArrayList<>();
        
        for (int i = 1; i <= 10; i++) {
            RsRct rsRct = new RsRct();
            rsRct.setRctId((long) i);
            rsRct.setRctNo("PERF-TEST-" + String.format("%03d", i));
            
            // 添加海运整箱服务
            List<RsOpSeaFcl> seaFclList = new ArrayList<>();
            for (int j = 1; j <= 3; j++) {
                RsOpSeaFcl seaFcl = new RsOpSeaFcl();
                seaFcl.setSeaId((long) (i * 10 + j));
                seaFcl.setServiceId((long) (i * 100 + j));
                
                // 添加费用记录
                List<RsCharge> charges = new ArrayList<>();
                for (int k = 1; k <= 5; k++) {
                    RsCharge charge = new RsCharge();
                    charge.setChargeId((long) (i * 1000 + j * 100 + k));
                    charge.setServiceId(seaFcl.getServiceId());
                    charge.setDnAmount(1000.0 * k);
                    charge.setSubtotal(1000.0 * k);
                    charges.add(charge);
                }
                seaFcl.setRsChargeList(charges);
                seaFclList.add(seaFcl);
            }
            rsRct.setRsOpSeaFclList(seaFclList);
            
            dataSets.add(rsRct);
        }
        
        return dataSets;
    }
}
