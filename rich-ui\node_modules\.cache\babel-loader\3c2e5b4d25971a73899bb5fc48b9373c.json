{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\ExpressComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\ExpressComponent.vue", "mtime": 1754646305902}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_audit", "_interopRequireDefault", "require", "_logisticsProgress", "_chargeList", "name", "components", "Audit", "LogisticsProgress", "ChargeList", "props", "serviceItem", "type", "Object", "default", "_default", "expressServices", "Array", "Set", "form", "rsOpExpress", "rsOpExpressServiceInstance", "rsOpExpressFold", "Boolean", "rsOpExpressFormDisable", "branchInfo", "serviceInfo", "logisticsInfo", "chargeInfo", "auditInfo", "disabled", "booking", "psaVerify", "supplierList", "companyList", "foldState", "serviceInstance", "serviceObject", "formDisable", "payable", "computed", "isDisabled", "methods", "changeFold", "serviceTypeId", "$emit", "getFold", "updateExpressData", "data", "getSupplierEmail", "getServiceInstance", "supplierId", "supplier", "find", "v", "companyId", "staffEmail", "getAgreementDisplay", "agreementTypeCode", "agreementNo", "getServiceObject", "getPayable", "getFormDisable", "auditCharge", "event", "generateFreight", "type1", "type2", "item", "psaBookingCancel", "copyFreight", "calculateCharge", "deleteLogItem", "rsOpLogList", "filter", "updateLogList", "deleteAllCharge", "rsChargeList", "deleteChargeItem", "exports", "_default2"], "sources": ["src/views/system/document/serviceComponents/ExpressComponent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"express-component\">\r\n    <!--快递-->\r\n    <div class=\"express-item\">\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <!--标题栏-->\r\n          <div class=\"service-bar\">\r\n            <a\r\n              :class=\"[\r\n                'service-toggle-icon',\r\n                foldState ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <h3 class=\"service-title\" @click=\"changeFold(serviceItem.serviceTypeId)\">\r\n              快递-EXPRESS\r\n            </h3>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"rsOpExpressServiceInstance\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(serviceItem.serviceTypeId)\"\r\n              :rs-charge-list=\"serviceItem.rsChargeList\"\r\n              @auditFee=\"auditCharge($event)\"\r\n              @return=\"updateExpressData($event)\"\r\n            />\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"getFold(serviceItem.serviceTypeId)\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(5, serviceItem.serviceTypeId, getServiceObject(serviceItem.serviceTypeId))\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(serviceItem.serviceTypeId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"getServiceInstance(serviceItem.serviceTypeId).supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(serviceItem.serviceTypeId)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--主表信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"商务单号\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input :value=\"form.psaNo\" class=\"disable-form\" disabled/>\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button\r\n                          size=\"mini\"\r\n                          style=\"color: red\"\r\n                          type=\"text\"\r\n                          @click=\"psaBookingCancel\"\r\n                        >\r\n                          取消\r\n                        </el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"rsOpExpressFormDisable || disabled || psaVerify\"\r\n                :logistics-progress-data=\"getServiceObject(serviceItem.serviceTypeId).rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"40\"\r\n                @deleteItem=\"deleteLogItem($event)\"\r\n                @return=\"updateLogList($event)\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"getServiceObject(serviceItem.serviceTypeId).rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"rsOpExpressFormDisable || disabled\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"getServiceObject(serviceItem.serviceTypeId).payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"getServiceObject(serviceItem.serviceTypeId).payableRMBTax\"\r\n              :pay-detail-u-s-d=\"getServiceObject(serviceItem.serviceTypeId).payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"getServiceObject(serviceItem.serviceTypeId).payableUSDTax\"\r\n              :service-type-id=\"40\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"deleteAllCharge(serviceItem.serviceTypeId)\"\r\n              @deleteItem=\"deleteChargeItem(serviceItem.serviceTypeId, $event)\"\r\n              @return=\"calculateCharge(serviceItem.serviceTypeId, $event, getServiceObject(serviceItem.serviceTypeId))\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\n\r\nexport default {\r\n  name: \"ExpressComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    serviceItem: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 快递服务数据集合\r\n    expressServices: {\r\n      type: [Array, Set],\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 快递数据对象\r\n    rsOpExpress: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 快递服务实例\r\n    rsOpExpressServiceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 快递折叠状态\r\n    rsOpExpressFold: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 快递表单禁用状态\r\n    rsOpExpressFormDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    serviceInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 新增属性，不再依赖$parent\r\n    foldState: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    serviceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    serviceObject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    formDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    payable: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    changeFold(serviceTypeId) {\r\n      this.$emit(\"changeFold\", serviceTypeId)\r\n    },\r\n    getFold(serviceTypeId) {\r\n      return this.foldState\r\n    },\r\n    // 更新快递数据\r\n    updateExpressData(data) {\r\n      this.$emit(\"update:rsOpExpress\", data)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(serviceTypeId) {\r\n      const serviceInstance = this.getServiceInstance(serviceTypeId)\r\n      if (!serviceInstance || !serviceInstance.supplierId) return \"\"\r\n\r\n      const supplier = this.supplierList.find(v => v.companyId === serviceInstance.supplierId)\r\n      return supplier ? supplier.staffEmail : \"\"\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceTypeId) {\r\n      const serviceInstance = this.getServiceInstance(serviceTypeId)\r\n      if (!serviceInstance) return \"\"\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 获取服务实例\r\n    getServiceInstance(serviceTypeId) {\r\n      return this.serviceInstance\r\n    },\r\n    // 获取服务对象\r\n    getServiceObject(serviceTypeId) {\r\n      return this.serviceObject\r\n    },\r\n    // 获取应付金额\r\n    getPayable(serviceTypeId) {\r\n      return this.payable\r\n    },\r\n    getFormDisable(serviceTypeId) {\r\n      return this.formDisable\r\n    },\r\n    // 事件转发给父组件\r\n    auditCharge(event) {\r\n      this.$emit(\"auditCharge\", event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    psaBookingCancel() {\r\n      this.$emit(\"psaBookingCancel\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceTypeId, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceTypeId, event, item)\r\n    },\r\n    // 物流进度相关方法\r\n    deleteLogItem(event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsOpLogList) {\r\n        serviceObject.rsOpLogList = serviceObject.rsOpLogList.filter(item => item !== event)\r\n      }\r\n    },\r\n    updateLogList(event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsOpLogList = event\r\n      }\r\n    },\r\n    // 费用列表相关方法\r\n    deleteAllCharge(serviceTypeId) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsChargeList = []\r\n      }\r\n    },\r\n    deleteChargeItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsChargeList) {\r\n        serviceObject.rsChargeList = serviceObject.rsChargeList.filter(item => item !== event)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// Express组件特定样式\r\n.express-component {\r\n  width: 100%;\r\n\r\n  .express-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title {\r\n      margin: 0;\r\n      width: 250px;\r\n      text-align: left;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;AA2JA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAG,IAAA;EACAC,UAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,UAAA,EAAAA;EACA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAC,eAAA;MACAJ,IAAA,GAAAK,KAAA,EAAAC,GAAA;MACAJ,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAI,IAAA;MACAP,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAK,WAAA;MACAR,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAM,0BAAA;MACAT,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAO,eAAA;MACAV,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACA;IACAU,sBAAA;MACAZ,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACA;IACAW,UAAA;MACAb,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACAY,WAAA;MACAd,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACAa,aAAA;MACAf,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACAc,UAAA;MACAhB,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACAe,SAAA;MACAjB,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACA;IACAgB,QAAA;MACAlB,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACAiB,OAAA;MACAnB,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACAkB,SAAA;MACApB,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACA;IACAmB,YAAA;MACArB,IAAA,EAAAK,KAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAmB,WAAA;MACAtB,IAAA,EAAAK,KAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAoB,SAAA;MACAvB,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACAsB,eAAA;MACAxB,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAsB,aAAA;MACAzB,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAuB,WAAA;MACA1B,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACAyB,OAAA;MACA3B,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACA0B,QAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAX,QAAA,SAAAE,SAAA;IACA;EACA;EACAU,OAAA;IACAC,UAAA,WAAAA,WAAAC,aAAA;MACA,KAAAC,KAAA,eAAAD,aAAA;IACA;IACAE,OAAA,WAAAA,QAAAF,aAAA;MACA,YAAAT,SAAA;IACA;IACA;IACAY,iBAAA,WAAAA,kBAAAC,IAAA;MACA,KAAAH,KAAA,uBAAAG,IAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAAL,aAAA;MACA,IAAAR,eAAA,QAAAc,kBAAA,CAAAN,aAAA;MACA,KAAAR,eAAA,KAAAA,eAAA,CAAAe,UAAA;MAEA,IAAAC,QAAA,QAAAnB,YAAA,CAAAoB,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,SAAA,KAAAnB,eAAA,CAAAe,UAAA;MAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAI,UAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAb,aAAA;MACA,IAAAR,eAAA,QAAAc,kBAAA,CAAAN,aAAA;MACA,KAAAR,eAAA;MACA,OAAAA,eAAA,CAAAsB,iBAAA,GAAAtB,eAAA,CAAAuB,WAAA;IACA;IACA;IACAT,kBAAA,WAAAA,mBAAAN,aAAA;MACA,YAAAR,eAAA;IACA;IACA;IACAwB,gBAAA,WAAAA,iBAAAhB,aAAA;MACA,YAAAP,aAAA;IACA;IACA;IACAwB,UAAA,WAAAA,WAAAjB,aAAA;MACA,YAAAL,OAAA;IACA;IACAuB,cAAA,WAAAA,eAAAlB,aAAA;MACA,YAAAN,WAAA;IACA;IACA;IACAyB,WAAA,WAAAA,YAAAC,KAAA;MACA,KAAAnB,KAAA,gBAAAmB,KAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA,EAAAC,KAAA,EAAAC,IAAA;MACA,KAAAvB,KAAA,oBAAAqB,KAAA,EAAAC,KAAA,EAAAC,IAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAxB,KAAA;IACA;IACAyB,WAAA,WAAAA,YAAAN,KAAA;MACA,KAAAnB,KAAA,gBAAAmB,KAAA;IACA;IACAO,eAAA,WAAAA,gBAAA3B,aAAA,EAAAoB,KAAA,EAAAI,IAAA;MACA,KAAAvB,KAAA,oBAAAD,aAAA,EAAAoB,KAAA,EAAAI,IAAA;IACA;IACA;IACAI,aAAA,WAAAA,cAAAR,KAAA;MACA,IAAA3B,aAAA,QAAAuB,gBAAA,CAAAhB,aAAA;MACA,IAAAP,aAAA,IAAAA,aAAA,CAAAoC,WAAA;QACApC,aAAA,CAAAoC,WAAA,GAAApC,aAAA,CAAAoC,WAAA,CAAAC,MAAA,WAAAN,IAAA;UAAA,OAAAA,IAAA,KAAAJ,KAAA;QAAA;MACA;IACA;IACAW,aAAA,WAAAA,cAAAX,KAAA;MACA,IAAA3B,aAAA,QAAAuB,gBAAA,CAAAhB,aAAA;MACA,IAAAP,aAAA;QACAA,aAAA,CAAAoC,WAAA,GAAAT,KAAA;MACA;IACA;IACA;IACAY,eAAA,WAAAA,gBAAAhC,aAAA;MACA,IAAAP,aAAA,QAAAuB,gBAAA,CAAAhB,aAAA;MACA,IAAAP,aAAA;QACAA,aAAA,CAAAwC,YAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAAlC,aAAA,EAAAoB,KAAA;MACA,IAAA3B,aAAA,QAAAuB,gBAAA,CAAAhB,aAAA;MACA,IAAAP,aAAA,IAAAA,aAAA,CAAAwC,YAAA;QACAxC,aAAA,CAAAwC,YAAA,GAAAxC,aAAA,CAAAwC,YAAA,CAAAH,MAAA,WAAAN,IAAA;UAAA,OAAAA,IAAA,KAAAJ,KAAA;QAAA;MACA;IACA;EACA;AACA;AAAAe,OAAA,CAAAjE,OAAA,GAAAkE,SAAA"}]}