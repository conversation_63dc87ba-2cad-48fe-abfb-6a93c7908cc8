{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\SeaLclComponent.vue?vue&type=style&index=0&id=5a77e3d5&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\SeaLclComponent.vue", "mtime": 1754646305906}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICdAL2Fzc2V0cy9zdHlsZXMvb3AtZG9jdW1lbnQnOw0KDQovLyBTZWFMY2znu4Tku7bnibnlrprmoLflvI8NCi5zZWEtbGNsLWNvbXBvbmVudCB7DQogIHdpZHRoOiAxMDAlOw0KDQogIC5zZWEtbGNsLWl0ZW0gew0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIH0NCg0KICAuc2VydmljZS1iYXIgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBtYXJnaW4tdG9wOiAxMHB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgd2lkdGg6IDEwMCU7DQoNCiAgICAuc2VydmljZS10b2dnbGUtaWNvbiB7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICBtYXJnaW4tcmlnaHQ6IDVweDsNCiAgICB9DQoNCiAgICAuc2VydmljZS10aXRsZS1ncm91cCB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIHdpZHRoOiAyNTBweDsNCg0KICAgICAgLnNlcnZpY2UtdGl0bGUgew0KICAgICAgICBtYXJnaW46IDA7DQogICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgIH0NCg0KICAgICAgLnNlcnZpY2UtYWN0aW9uLWJ0biB7DQogICAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5ib29raW5nLWJpbGwtY29udGFpbmVyIHsNCiAgICAgIG1hcmdpbi1sZWZ0OiBhdXRvOw0KDQogICAgICAuYm9va2luZy1iaWxsLWxpbmsgew0KICAgICAgICBjb2xvcjogYmx1ZTsNCiAgICAgICAgcGFkZGluZzogMDsNCiAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lOw0KICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLnNlcnZpY2UtY29udGVudC1hcmVhIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICAgIGRpc3BsYXk6IC13ZWJraXQtYm94Ow0KDQogICAgLnNlcnZpY2UtaW5mby1jb2wgew0KICAgICAgLmVsLWZvcm0taXRlbSB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgICB9DQoNCiAgICAgIC5jYW5jZWwtYnRuIHsNCiAgICAgICAgY29sb3I6IHJlZDsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAuYnJhbmNoLWluZm8tY29sIHsNCiAgICAgIC5ib29raW5nLXJlbWFyay1ncm91cCB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGdhcDogMTBweDsNCg0KICAgICAgICAuZWwtdGV4dGFyZWEgew0KICAgICAgICAgIGZsZXg6IDE7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAvLyDkvJjljJbooajljZXovpPlhaXmoYbmoLflvI8NCiAgLmVsLWlucHV0IHsNCiAgICB3aWR0aDogMTAwJTsNCiAgfQ0KDQogIC8vIOS8mOWMluaXpeacn+mAieaLqeWZqOagt+W8jw0KICAuZWwtZGF0ZS1waWNrZXIgew0KICAgIHdpZHRoOiAxMDAlOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["SeaLclComponent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAonBA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "SeaLclComponent.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div class=\"sea-lcl-component\">\r\n    <!--拼柜海运-->\r\n    <div v-for=\"(item, index) in seaLclList\" :key=\"`sea-lcl-${index}`\" class=\"sea-lcl-item\">\r\n      <!--标题栏-->\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <div class=\"service-bar\">\r\n            <a :class=\"[\r\n                'service-toggle-icon',\r\n                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeServiceFold(item.rsServiceInstances)\">\r\n                海运-LCL\r\n              </h3>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"addSeaLCL\"\r\n              >[+]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"deleteRsOpLclSea(item)\"\r\n              >[-]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"openChargeSelect(item)\"\r\n              >[CN...]\r\n              </el-button>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"item.rsServiceInstances\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(2)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @return=\"item = $event\"\r\n            />\r\n            <div class=\"booking-bill-container\">\r\n              <el-popover\r\n                v-for=\"(billConfig, billIndex) in bookingBillConfig\"\r\n                :key=\"`bill-${billIndex}`\"\r\n                placement=\"top\"\r\n                trigger=\"click\"\r\n                width=\"100\"\r\n              >\r\n                <el-button\r\n                  v-for=\"(template, templateIndex) in billConfig.templateList\"\r\n                  :key=\"`template-${templateIndex}`\"\r\n                  @click=\"handleBookingBill(item, template)\"\r\n                >\r\n                  {{ template }}\r\n                </el-button>\r\n                <a\r\n                  slot=\"reference\"\r\n                  class=\"booking-bill-link\"\r\n                  target=\"_blank\"\r\n                >\r\n                  [{{ billConfig.file }}]\r\n                </a>\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"item.rsServiceInstances.serviceFold\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNo\"\r\n                  :class=\"{ 'disable-form': form.sqdPsaNo }\"\r\n                  :disabled=\"!!form.sqdPsaNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(1, 2, item)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(item.rsServiceInstances.supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"item.rsServiceInstances.supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.rsServiceInstances)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"订舱状态\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input\r\n                      :value=\"getBookingStatus(item.bookingStatus)\"\r\n                      class=\"disable-form\"\r\n                      disabled\r\n                      placeholder=\"订舱状态\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-button\r\n                      :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                      class=\"cancel-btn\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"psaBookingCancel(item)\"\r\n                    >\r\n                      取消\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\" class=\"branch-info-col\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"商务单号\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input\r\n                          :class=\"{ 'disable-form': item.sqdPsaNo }\"\r\n                          :disabled=\"!!item.sqdPsaNo\"\r\n                          :value=\"item.sqdPsaNo\"\r\n                          @focus=\"selectPsaBookingOpen(item)\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button\r\n                          :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                          class=\"cancel-btn\"\r\n                          size=\"mini\"\r\n                          type=\"text\"\r\n                          @click=\"psaBookingCancel(item)\"\r\n                        >\r\n                          取消\r\n                        </el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"SO号码\">\r\n                    <el-input\r\n                      v-model=\"item.soNo\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"SO号码\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"提单号码\">\r\n                    <el-input\r\n                      v-model=\"item.blNo\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"提单号码\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"9\">\r\n                  <el-form-item label=\"柜号概览\">\r\n                    <el-input\r\n                      v-model=\"item.sqdContainersSealsSum\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"柜号概览\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"船公司\">\r\n                    <treeselect\r\n                      v-model=\"item.carrierId\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disable-branch-nodes=\"true\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      :disabled-fuzzy-matching=\"true\"\r\n                      :flat=\"false\"\r\n                      :flatten-search-results=\"true\"\r\n                      :multiple=\"false\"\r\n                      :normalizer=\"carrierNormalizer\"\r\n                      :options=\"carrierList\"\r\n                      :show-count=\"true\"\r\n                      placeholder=\"选择承运人\"\r\n                      @select=\"selectCarrier(item, $event)\"\r\n                    >\r\n                      <div slot=\"value-label\" slot-scope=\"{ node }\">\r\n                        {{ node.raw.carrierIntlCode || \" \" }}\r\n                      </div>\r\n                      <label\r\n                        slot=\"option-label\"\r\n                        slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                        :class=\"labelClassName\"\r\n                      >\r\n                        {{ formatCarrierLabel(node.label) }}\r\n                        <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                      </label>\r\n                    </treeselect>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程船名\">\r\n                    <el-input\r\n                      v-model=\"item.firstVessel\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"头程船名船次\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"二程船名\">\r\n                    <el-input\r\n                      v-model=\"item.secondVessel\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"二程船名船次\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"9\">\r\n                  <el-form-item label=\"船期\">\r\n                    <el-input\r\n                      v-model=\"item.inquiryScheduleSummary\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"航班时效\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程开船\">\r\n                    <el-input\r\n                      v-model=\"item.firstCyOpenTime\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"头程开船\"\r\n                      @change=\"addProgress(getServiceObject(item.serviceTypeId).rsOpLogList, 15)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程截重\">\r\n                    <el-input\r\n                      v-model=\"item.firstCyClosingTime\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      clearable\r\n                      placeholder=\"头程截重\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截关时间\">\r\n                    <el-input\r\n                      v-model=\"item.cvClosingTime\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"截关时间\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"ETD\">\r\n                    <el-date-picker\r\n                      v-model=\"item.etd\"\r\n                      :class=\"{ 'disable-form': isDateFieldDisabled(item) }\"\r\n                      :disabled=\"isDateFieldDisabled(item)\"\r\n                      clearable\r\n                      placeholder=\"ETD\"\r\n                      type=\"date\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"ETA\">\r\n                    <el-date-picker\r\n                      v-model=\"item.eta\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      clearable\r\n                      placeholder=\"ETA\"\r\n                      type=\"date\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截补料\">\r\n                    <el-input\r\n                      v-model=\"item.siClosingTime\"\r\n                      :class=\"{ 'disable-form': isDateFieldDisabled(item) }\"\r\n                      :disabled=\"isDateFieldDisabled(item)\"\r\n                      placeholder=\"截补料\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截VGM\">\r\n                    <el-input\r\n                      v-model=\"item.sqdVgmStatus\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"截VGM\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"AMS/ENS\">\r\n                    <el-input\r\n                      v-model=\"item.sqdAmsEnsPostStatus\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"AMS/ENS\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"结算价\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input\r\n                          v-model=\"item.settledRate\"\r\n                          :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"price1/price2/price3\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button type=\"text\" @click=\"handleSettledRate(item)\">确定</el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"19\">\r\n                  <el-form-item label=\"订舱备注\">\r\n                    <div class=\"booking-remark-group\">\r\n                      <el-input\r\n                        v-model=\"item.bookingChargeRemark\"\r\n                        :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                        :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                        :disabled=\"isFieldDisabled(item)\"\r\n                        placeholder=\"订舱费用备注\"\r\n                        type=\"textarea\"\r\n                      />\r\n                      <el-input\r\n                        v-model=\"item.bookingAgentRemark\"\r\n                        :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                        :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                        :disabled=\"isFieldDisabled(item)\"\r\n                        placeholder=\"订舱备注\"\r\n                        type=\"textarea\"\r\n                      />\r\n                    </div>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getServiceInstanceDisable(item.rsServiceInstances) || disabled || psaVerify\"\r\n                :logistics-progress-data=\"item.rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"1\"\r\n                @deleteItem=\"item.rsOpLogList = item.rsOpLogList.filter(log => log !== $event)\"\r\n                @return=\"item.rsOpLogList = $event\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"item.rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"getFormDisable(item.serviceTypeId) || disabled\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"item.payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"item.payableRMBTax\"\r\n              :pay-detail-u-s-d=\"item.payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"item.payableUSDTax\"\r\n              :service-type-id=\"1\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"item.rsChargeList = []\"\r\n              @deleteItem=\"item.rsChargeList = item.rsChargeList.filter(charge => charge !== $event)\"\r\n              @return=\"calculateCharge(2, $event, item)\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"../audit.vue\"\r\nimport LogisticsProgress from \"../logisticsProgress.vue\"\r\nimport ChargeList from \"../chargeList.vue\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\r\n\r\nexport default {\r\n  name: \"SeaLclComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    Treeselect\r\n  },\r\n  props: {\r\n    // 拼柜海运数据列表\r\n    seaLclList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    carrierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 方法函数\r\n    carrierNormalizer: {\r\n      type: Function,\r\n      default: () => {\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      bookingBillConfig: [{\r\n        file: \"订舱单\",\r\n        templateList: [\"bookingOrder1\"]\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    // 判断字段是否禁用\r\n    isFieldDisabled(item) {\r\n      return this.disabled || this.getServiceInstanceDisable(item.rsServiceInstances)\r\n    },\r\n    // 判断日期字段是否禁用（注意有些地方使用了getFormDisable）\r\n    isDateFieldDisabled(item) {\r\n      return this.disabled || this.getFormDisable(item.rsServiceInstances)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 格式化承运人标签\r\n    formatCarrierLabel(label) {\r\n      const commaIndex = label.indexOf(\",\")\r\n      return commaIndex !== -1 ? label.substring(0, commaIndex) : label\r\n    },\r\n    // 处理订舱单生成\r\n    handleBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceInstance) {\r\n      this.$emit(\"changeServiceFold\", serviceInstance)\r\n    },\r\n    addSeaLCL() {\r\n      this.$emit(\"addSeaLCL\")\r\n    },\r\n    deleteRsOpLclSea(item) {\r\n      this.$emit(\"deleteRsOpLclSea\", item)\r\n    },\r\n    openChargeSelect(item) {\r\n      this.$emit(\"openChargeSelect\", item)\r\n    },\r\n    auditCharge(item, event) {\r\n      this.$emit(\"auditCharge\", item, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    selectPsaBookingOpen(item) {\r\n      this.$emit(\"selectPsaBookingOpen\", item)\r\n    },\r\n    selectCarrier(item, event) {\r\n      this.$emit(\"selectCarrier\", item, event)\r\n    },\r\n    addProgress(logList, type) {\r\n      this.$emit(\"addProgress\", logList, type)\r\n    },\r\n    handleSettledRate(item) {\r\n      this.$emit(\"handleSettledRate\", item)\r\n    },\r\n    psaBookingCancel(item) {\r\n      this.$emit(\"psaBookingCancel\", item)\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceType, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceType, event, item)\r\n    },\r\n    getPayable(type) {\r\n      return this.$parent.getPayable ? this.$parent.getPayable(type) : null\r\n    },\r\n    getBookingStatus(status) {\r\n      return this.$parent.getBookingStatus ? this.$parent.getBookingStatus(status) : ''\r\n    },\r\n    getServiceInstanceDisable(serviceInstance) {\r\n      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false\r\n    },\r\n    getServiceObject(serviceTypeId) {\r\n      return this.$parent.getServiceObject ? this.$parent.getServiceObject(serviceTypeId) : {}\r\n    },\r\n    getFormDisable(serviceTypeId) {\r\n      return this.$parent.getFormDisable ? this.$parent.getFormDisable(serviceTypeId) : false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// SeaLcl组件特定样式\r\n.sea-lcl-component {\r\n  width: 100%;\r\n\r\n  .sea-lcl-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .service-action-btn {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .booking-bill-container {\r\n      margin-left: auto;\r\n\r\n      .booking-bill-link {\r\n        color: blue;\r\n        padding: 0;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .cancel-btn {\r\n        color: red;\r\n      }\r\n    }\r\n\r\n    .branch-info-col {\r\n      .booking-remark-group {\r\n        display: flex;\r\n        gap: 10px;\r\n\r\n        .el-textarea {\r\n          flex: 1;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}