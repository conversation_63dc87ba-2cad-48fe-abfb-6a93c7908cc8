{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\SeaLclComponent.vue?vue&type=template&id=5a77e3d5&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\SeaLclComponent.vue", "mtime": 1754646305906}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}