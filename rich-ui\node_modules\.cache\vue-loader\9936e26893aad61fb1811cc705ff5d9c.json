{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\ExpressComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\ExpressComponent.vue", "mtime": 1754646305902}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQXVkaXQgZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQvYXVkaXQudnVlIg0KaW1wb3J0IExvZ2lzdGljc1Byb2dyZXNzIGZyb20gIkAvdmlld3Mvc3lzdGVtL2RvY3VtZW50L2xvZ2lzdGljc1Byb2dyZXNzLnZ1ZSINCmltcG9ydCBDaGFyZ2VMaXN0IGZyb20gIkAvdmlld3Mvc3lzdGVtL2RvY3VtZW50L2NoYXJnZUxpc3QudnVlIg0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJFeHByZXNzQ29tcG9uZW50IiwNCiAgY29tcG9uZW50czogew0KICAgIEF1ZGl0LA0KICAgIExvZ2lzdGljc1Byb2dyZXNzLA0KICAgIENoYXJnZUxpc3QNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICBzZXJ2aWNlSXRlbTogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gKHt9KQ0KICAgIH0sDQogICAgLy8g5b+r6YCS5pyN5Yqh5pWw5o2u6ZuG5ZCIDQogICAgZXhwcmVzc1NlcnZpY2VzOiB7DQogICAgICB0eXBlOiBbQXJyYXksIFNldF0sDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgLy8g6KGo5Y2V5pWw5o2uDQogICAgZm9ybTogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gKHt9KQ0KICAgIH0sDQogICAgLy8g5b+r6YCS5pWw5o2u5a+56LGhDQogICAgcnNPcEV4cHJlc3M6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSkNCiAgICB9LA0KICAgIC8vIOW/q+mAkuacjeWKoeWunuS+iw0KICAgIHJzT3BFeHByZXNzU2VydmljZUluc3RhbmNlOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiAoe30pDQogICAgfSwNCiAgICAvLyDlv6vpgJLmipjlj6DnirbmgIENCiAgICByc09wRXhwcmVzc0ZvbGQ6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgLy8g5b+r6YCS6KGo5Y2V56aB55So54q25oCBDQogICAgcnNPcEV4cHJlc3NGb3JtRGlzYWJsZTogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICAvLyDmmL7npLrmjqfliLYNCiAgICBicmFuY2hJbmZvOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0sDQogICAgc2VydmljZUluZm86IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiB0cnVlDQogICAgfSwNCiAgICBsb2dpc3RpY3NJbmZvOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0sDQogICAgY2hhcmdlSW5mbzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUNCiAgICB9LA0KICAgIGF1ZGl0SW5mbzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICAvLyDnirbmgIHmjqfliLYNCiAgICBkaXNhYmxlZDogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICBib29raW5nOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIHBzYVZlcmlmeTogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICAvLyDmlbDmja7liJfooagNCiAgICBzdXBwbGllckxpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogKCkgPT4gW10NCiAgICB9LA0KICAgIGNvbXBhbnlMaXN0OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfSwNCiAgICAvLyDmlrDlop7lsZ7mgKfvvIzkuI3lho3kvp3otZYkcGFyZW50DQogICAgZm9sZFN0YXRlOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIHNlcnZpY2VJbnN0YW5jZTogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gKHt9KQ0KICAgIH0sDQogICAgc2VydmljZU9iamVjdDogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gKHt9KQ0KICAgIH0sDQogICAgZm9ybURpc2FibGU6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgcGF5YWJsZTogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogbnVsbA0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvLyDliKTmlq3mmK/lkKbnpoHnlKjnirbmgIENCiAgICBpc0Rpc2FibGVkKCkgew0KICAgICAgcmV0dXJuIHRoaXMuZGlzYWJsZWQgfHwgdGhpcy5wc2FWZXJpZnkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBjaGFuZ2VGb2xkKHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIHRoaXMuJGVtaXQoImNoYW5nZUZvbGQiLCBzZXJ2aWNlVHlwZUlkKQ0KICAgIH0sDQogICAgZ2V0Rm9sZChzZXJ2aWNlVHlwZUlkKSB7DQogICAgICByZXR1cm4gdGhpcy5mb2xkU3RhdGUNCiAgICB9LA0KICAgIC8vIOabtOaWsOW/q+mAkuaVsOaNrg0KICAgIHVwZGF0ZUV4cHJlc3NEYXRhKGRhdGEpIHsNCiAgICAgIHRoaXMuJGVtaXQoInVwZGF0ZTpyc09wRXhwcmVzcyIsIGRhdGEpDQogICAgfSwNCiAgICAvLyDojrflj5bkvpvlupTllYbpgq7nrrENCiAgICBnZXRTdXBwbGllckVtYWlsKHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIGNvbnN0IHNlcnZpY2VJbnN0YW5jZSA9IHRoaXMuZ2V0U2VydmljZUluc3RhbmNlKHNlcnZpY2VUeXBlSWQpDQogICAgICBpZiAoIXNlcnZpY2VJbnN0YW5jZSB8fCAhc2VydmljZUluc3RhbmNlLnN1cHBsaWVySWQpIHJldHVybiAiIg0KDQogICAgICBjb25zdCBzdXBwbGllciA9IHRoaXMuc3VwcGxpZXJMaXN0LmZpbmQodiA9PiB2LmNvbXBhbnlJZCA9PT0gc2VydmljZUluc3RhbmNlLnN1cHBsaWVySWQpDQogICAgICByZXR1cm4gc3VwcGxpZXIgPyBzdXBwbGllci5zdGFmZkVtYWlsIDogIiINCiAgICB9LA0KICAgIC8vIOiOt+WPluWQiOe6puaYvuekuuaWh+acrA0KICAgIGdldEFncmVlbWVudERpc3BsYXkoc2VydmljZVR5cGVJZCkgew0KICAgICAgY29uc3Qgc2VydmljZUluc3RhbmNlID0gdGhpcy5nZXRTZXJ2aWNlSW5zdGFuY2Uoc2VydmljZVR5cGVJZCkNCiAgICAgIGlmICghc2VydmljZUluc3RhbmNlKSByZXR1cm4gIiINCiAgICAgIHJldHVybiBzZXJ2aWNlSW5zdGFuY2UuYWdyZWVtZW50VHlwZUNvZGUgKyBzZXJ2aWNlSW5zdGFuY2UuYWdyZWVtZW50Tm8NCiAgICB9LA0KICAgIC8vIOiOt+WPluacjeWKoeWunuS+iw0KICAgIGdldFNlcnZpY2VJbnN0YW5jZShzZXJ2aWNlVHlwZUlkKSB7DQogICAgICByZXR1cm4gdGhpcy5zZXJ2aWNlSW5zdGFuY2UNCiAgICB9LA0KICAgIC8vIOiOt+WPluacjeWKoeWvueixoQ0KICAgIGdldFNlcnZpY2VPYmplY3Qoc2VydmljZVR5cGVJZCkgew0KICAgICAgcmV0dXJuIHRoaXMuc2VydmljZU9iamVjdA0KICAgIH0sDQogICAgLy8g6I635Y+W5bqU5LuY6YeR6aKdDQogICAgZ2V0UGF5YWJsZShzZXJ2aWNlVHlwZUlkKSB7DQogICAgICByZXR1cm4gdGhpcy5wYXlhYmxlDQogICAgfSwNCiAgICBnZXRGb3JtRGlzYWJsZShzZXJ2aWNlVHlwZUlkKSB7DQogICAgICByZXR1cm4gdGhpcy5mb3JtRGlzYWJsZQ0KICAgIH0sDQogICAgLy8g5LqL5Lu26L2s5Y+R57uZ54i257uE5Lu2DQogICAgYXVkaXRDaGFyZ2UoZXZlbnQpIHsNCiAgICAgIHRoaXMuJGVtaXQoImF1ZGl0Q2hhcmdlIiwgZXZlbnQpDQogICAgfSwNCiAgICBnZW5lcmF0ZUZyZWlnaHQodHlwZTEsIHR5cGUyLCBpdGVtKSB7DQogICAgICB0aGlzLiRlbWl0KCJnZW5lcmF0ZUZyZWlnaHQiLCB0eXBlMSwgdHlwZTIsIGl0ZW0pDQogICAgfSwNCiAgICBwc2FCb29raW5nQ2FuY2VsKCkgew0KICAgICAgdGhpcy4kZW1pdCgicHNhQm9va2luZ0NhbmNlbCIpDQogICAgfSwNCiAgICBjb3B5RnJlaWdodChldmVudCkgew0KICAgICAgdGhpcy4kZW1pdCgiY29weUZyZWlnaHQiLCBldmVudCkNCiAgICB9LA0KICAgIGNhbGN1bGF0ZUNoYXJnZShzZXJ2aWNlVHlwZUlkLCBldmVudCwgaXRlbSkgew0KICAgICAgdGhpcy4kZW1pdCgiY2FsY3VsYXRlQ2hhcmdlIiwgc2VydmljZVR5cGVJZCwgZXZlbnQsIGl0ZW0pDQogICAgfSwNCiAgICAvLyDnianmtYHov5vluqbnm7jlhbPmlrnms5UNCiAgICBkZWxldGVMb2dJdGVtKGV2ZW50KSB7DQogICAgICBjb25zdCBzZXJ2aWNlT2JqZWN0ID0gdGhpcy5nZXRTZXJ2aWNlT2JqZWN0KHNlcnZpY2VUeXBlSWQpDQogICAgICBpZiAoc2VydmljZU9iamVjdCAmJiBzZXJ2aWNlT2JqZWN0LnJzT3BMb2dMaXN0KSB7DQogICAgICAgIHNlcnZpY2VPYmplY3QucnNPcExvZ0xpc3QgPSBzZXJ2aWNlT2JqZWN0LnJzT3BMb2dMaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0gIT09IGV2ZW50KQ0KICAgICAgfQ0KICAgIH0sDQogICAgdXBkYXRlTG9nTGlzdChldmVudCkgew0KICAgICAgY29uc3Qgc2VydmljZU9iamVjdCA9IHRoaXMuZ2V0U2VydmljZU9iamVjdChzZXJ2aWNlVHlwZUlkKQ0KICAgICAgaWYgKHNlcnZpY2VPYmplY3QpIHsNCiAgICAgICAgc2VydmljZU9iamVjdC5yc09wTG9nTGlzdCA9IGV2ZW50DQogICAgICB9DQogICAgfSwNCiAgICAvLyDotLnnlKjliJfooajnm7jlhbPmlrnms5UNCiAgICBkZWxldGVBbGxDaGFyZ2Uoc2VydmljZVR5cGVJZCkgew0KICAgICAgY29uc3Qgc2VydmljZU9iamVjdCA9IHRoaXMuZ2V0U2VydmljZU9iamVjdChzZXJ2aWNlVHlwZUlkKQ0KICAgICAgaWYgKHNlcnZpY2VPYmplY3QpIHsNCiAgICAgICAgc2VydmljZU9iamVjdC5yc0NoYXJnZUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgZGVsZXRlQ2hhcmdlSXRlbShzZXJ2aWNlVHlwZUlkLCBldmVudCkgew0KICAgICAgY29uc3Qgc2VydmljZU9iamVjdCA9IHRoaXMuZ2V0U2VydmljZU9iamVjdChzZXJ2aWNlVHlwZUlkKQ0KICAgICAgaWYgKHNlcnZpY2VPYmplY3QgJiYgc2VydmljZU9iamVjdC5yc0NoYXJnZUxpc3QpIHsNCiAgICAgICAgc2VydmljZU9iamVjdC5yc0NoYXJnZUxpc3QgPSBzZXJ2aWNlT2JqZWN0LnJzQ2hhcmdlTGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtICE9PSBldmVudCkNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["ExpressComponent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2JA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ExpressComponent.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div class=\"express-component\">\r\n    <!--快递-->\r\n    <div class=\"express-item\">\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <!--标题栏-->\r\n          <div class=\"service-bar\">\r\n            <a\r\n              :class=\"[\r\n                'service-toggle-icon',\r\n                foldState ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <h3 class=\"service-title\" @click=\"changeFold(serviceItem.serviceTypeId)\">\r\n              快递-EXPRESS\r\n            </h3>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"rsOpExpressServiceInstance\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(serviceItem.serviceTypeId)\"\r\n              :rs-charge-list=\"serviceItem.rsChargeList\"\r\n              @auditFee=\"auditCharge($event)\"\r\n              @return=\"updateExpressData($event)\"\r\n            />\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"getFold(serviceItem.serviceTypeId)\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(5, serviceItem.serviceTypeId, getServiceObject(serviceItem.serviceTypeId))\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(serviceItem.serviceTypeId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"getServiceInstance(serviceItem.serviceTypeId).supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(serviceItem.serviceTypeId)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--主表信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"商务单号\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input :value=\"form.psaNo\" class=\"disable-form\" disabled/>\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button\r\n                          size=\"mini\"\r\n                          style=\"color: red\"\r\n                          type=\"text\"\r\n                          @click=\"psaBookingCancel\"\r\n                        >\r\n                          取消\r\n                        </el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"rsOpExpressFormDisable || disabled || psaVerify\"\r\n                :logistics-progress-data=\"getServiceObject(serviceItem.serviceTypeId).rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"40\"\r\n                @deleteItem=\"deleteLogItem($event)\"\r\n                @return=\"updateLogList($event)\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"getServiceObject(serviceItem.serviceTypeId).rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"rsOpExpressFormDisable || disabled\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"getServiceObject(serviceItem.serviceTypeId).payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"getServiceObject(serviceItem.serviceTypeId).payableRMBTax\"\r\n              :pay-detail-u-s-d=\"getServiceObject(serviceItem.serviceTypeId).payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"getServiceObject(serviceItem.serviceTypeId).payableUSDTax\"\r\n              :service-type-id=\"40\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"deleteAllCharge(serviceItem.serviceTypeId)\"\r\n              @deleteItem=\"deleteChargeItem(serviceItem.serviceTypeId, $event)\"\r\n              @return=\"calculateCharge(serviceItem.serviceTypeId, $event, getServiceObject(serviceItem.serviceTypeId))\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\n\r\nexport default {\r\n  name: \"ExpressComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    serviceItem: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 快递服务数据集合\r\n    expressServices: {\r\n      type: [Array, Set],\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 快递数据对象\r\n    rsOpExpress: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 快递服务实例\r\n    rsOpExpressServiceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 快递折叠状态\r\n    rsOpExpressFold: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 快递表单禁用状态\r\n    rsOpExpressFormDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    serviceInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 新增属性，不再依赖$parent\r\n    foldState: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    serviceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    serviceObject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    formDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    payable: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    changeFold(serviceTypeId) {\r\n      this.$emit(\"changeFold\", serviceTypeId)\r\n    },\r\n    getFold(serviceTypeId) {\r\n      return this.foldState\r\n    },\r\n    // 更新快递数据\r\n    updateExpressData(data) {\r\n      this.$emit(\"update:rsOpExpress\", data)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(serviceTypeId) {\r\n      const serviceInstance = this.getServiceInstance(serviceTypeId)\r\n      if (!serviceInstance || !serviceInstance.supplierId) return \"\"\r\n\r\n      const supplier = this.supplierList.find(v => v.companyId === serviceInstance.supplierId)\r\n      return supplier ? supplier.staffEmail : \"\"\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceTypeId) {\r\n      const serviceInstance = this.getServiceInstance(serviceTypeId)\r\n      if (!serviceInstance) return \"\"\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 获取服务实例\r\n    getServiceInstance(serviceTypeId) {\r\n      return this.serviceInstance\r\n    },\r\n    // 获取服务对象\r\n    getServiceObject(serviceTypeId) {\r\n      return this.serviceObject\r\n    },\r\n    // 获取应付金额\r\n    getPayable(serviceTypeId) {\r\n      return this.payable\r\n    },\r\n    getFormDisable(serviceTypeId) {\r\n      return this.formDisable\r\n    },\r\n    // 事件转发给父组件\r\n    auditCharge(event) {\r\n      this.$emit(\"auditCharge\", event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    psaBookingCancel() {\r\n      this.$emit(\"psaBookingCancel\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceTypeId, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceTypeId, event, item)\r\n    },\r\n    // 物流进度相关方法\r\n    deleteLogItem(event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsOpLogList) {\r\n        serviceObject.rsOpLogList = serviceObject.rsOpLogList.filter(item => item !== event)\r\n      }\r\n    },\r\n    updateLogList(event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsOpLogList = event\r\n      }\r\n    },\r\n    // 费用列表相关方法\r\n    deleteAllCharge(serviceTypeId) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsChargeList = []\r\n      }\r\n    },\r\n    deleteChargeItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsChargeList) {\r\n        serviceObject.rsChargeList = serviceObject.rsChargeList.filter(item => item !== event)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// Express组件特定样式\r\n.express-component {\r\n  width: 100%;\r\n\r\n  .express-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title {\r\n      margin: 0;\r\n      width: 250px;\r\n      text-align: left;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}