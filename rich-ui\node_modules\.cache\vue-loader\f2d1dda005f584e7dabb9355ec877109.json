{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\ClearanceComponent.vue?vue&type=style&index=0&id=707a3b73&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\ClearanceComponent.vue", "mtime": 1754646305898}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICdAL2Fzc2V0cy9zdHlsZXMvb3AtZG9jdW1lbnQnOw0KDQovLyBDbGVhcmFuY2Xnu4Tku7bnibnlrprmoLflvI8NCi5jbGVhcmFuY2UtY29tcG9uZW50IHsNCiAgd2lkdGg6IDEwMCU7DQoNCiAgLmNsZWFyYW5jZS1pdGVtIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICB9DQoNCiAgLnNlcnZpY2UtYmFyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgbWFyZ2luLXRvcDogMTBweDsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgIHdpZHRoOiAxMDAlOw0KDQogICAgLnNlcnZpY2UtdG9nZ2xlLWljb24gew0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7DQogICAgfQ0KDQogICAgLnNlcnZpY2UtdGl0bGUgew0KICAgICAgbWFyZ2luOiAwOw0KICAgICAgd2lkdGg6IDI1MHB4Ow0KICAgICAgdGV4dC1hbGlnbjogbGVmdDsNCiAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICB9DQoNCiAgICAuYm9va2luZy1iaWxsLWNvbnRhaW5lciB7DQogICAgICBtYXJnaW4tbGVmdDogYXV0bzsNCg0KICAgICAgLmJvb2tpbmctYmlsbC1saW5rIHsNCiAgICAgICAgY29sb3I6IGJsdWU7DQogICAgICAgIHBhZGRpbmc6IDA7DQogICAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTsNCiAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5zZXJ2aWNlLWNvbnRlbnQtYXJlYSB7DQogICAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgICBkaXNwbGF5OiAtd2Via2l0LWJveDsNCg0KICAgIC5zZXJ2aWNlLWluZm8tY29sIHsNCiAgICAgIC5lbC1mb3JtLWl0ZW0gew0KICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC8vIOS8mOWMluihqOWNlei+k+WFpeahhuagt+W8jw0KICAuZWwtaW5wdXQgew0KICAgIHdpZHRoOiAxMDAlOw0KICB9DQoNCiAgLy8g5LyY5YyW5pel5pyf6YCJ5oup5Zmo5qC35byPDQogIC5lbC1kYXRlLXBpY2tlciB7DQogICAgd2lkdGg6IDEwMCU7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["ClearanceComponent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+WA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "ClearanceComponent.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div class=\"clearance-component\">\r\n    <!--清关-->\r\n    <div class=\"clearance-item\">\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <!--标题栏-->\r\n          <div class=\"service-bar\">\r\n            <a\r\n              :class=\"[\r\n                'service-toggle-icon',\r\n                getFold(serviceItem.serviceTypeId) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <h3 class=\"service-title\" @click=\"changeFold(serviceItem.serviceTypeId)\">\r\n              清关-{{ serviceItem.serviceShortName }}\r\n            </h3>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"getServiceInstance(serviceItem.serviceTypeId)\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(serviceItem.serviceTypeId)\"\r\n              :rs-charge-list=\"serviceItem.rsChargeList\"\r\n              @auditFee=\"auditCharge(serviceItem.serviceTypeId, $event)\"\r\n              @return=\"changeServiceObject(serviceItem.serviceTypeId, $event)\"\r\n            />\r\n            <div class=\"booking-bill-container\">\r\n              <el-popover\r\n                v-for=\"(billConfig, billIndex) in bookingBillConfig\"\r\n                :key=\"`bill-${billIndex}`\"\r\n                placement=\"top\"\r\n                trigger=\"click\"\r\n                width=\"100\"\r\n              >\r\n                <el-button\r\n                  v-for=\"(template, templateIndex) in billConfig.templateList\"\r\n                  :key=\"`template-${templateIndex}`\"\r\n                  @click=\"getBookingBill(item, template)\"\r\n                >\r\n                  {{ template }}\r\n                </el-button>\r\n                <a\r\n                  slot=\"reference\"\r\n                  class=\"booking-bill-link\"\r\n                  target=\"_blank\"\r\n                >\r\n                  [{{ billConfig.file }}]\r\n                </a>\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"getFold(serviceItem.serviceTypeId)\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(7, serviceItem.serviceTypeId, getServiceObject(serviceItem.serviceTypeId))\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(serviceItem.serviceTypeId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"getServiceInstance(serviceItem.serviceTypeId).supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(serviceItem.serviceTypeId)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--主表信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"商务单号\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input :value=\"form.psaNo\" class=\"disable-form\" disabled/>\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button\r\n                          size=\"mini\"\r\n                          style=\"color: red\"\r\n                          type=\"text\"\r\n                          @click=\"psaBookingCancel\"\r\n                        >\r\n                          取消\r\n                        </el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getFormDisable(serviceItem.serviceTypeId) || disabled || psaVerify\"\r\n                :logistics-progress-data=\"getServiceObject(serviceItem.serviceTypeId).rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"60\"\r\n                @deleteItem=\"deleteLogItem(serviceItem.serviceTypeId, $event)\"\r\n                @return=\"updateLogList(serviceItem.serviceTypeId, $event)\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"getServiceObject(serviceItem.serviceTypeId).rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"getFormDisable(serviceItem.serviceTypeId) || disabled\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"getServiceObject(serviceItem.serviceTypeId).payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"getServiceObject(serviceItem.serviceTypeId).payableRMBTax\"\r\n              :pay-detail-u-s-d=\"getServiceObject(serviceItem.serviceTypeId).payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"getServiceObject(serviceItem.serviceTypeId).payableUSDTax\"\r\n              :service-type-id=\"serviceItem.serviceTypeId\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"deleteAllCharge(serviceItem.serviceTypeId)\"\r\n              @deleteItem=\"deleteChargeItem(serviceItem.serviceTypeId, $event)\"\r\n              @return=\"calculateCharge(serviceItem.serviceTypeId, $event, getServiceObject(serviceItem.serviceTypeId))\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\n\r\nexport default {\r\n  name: \"ClearanceComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    serviceItem: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 清关服务数据集合\r\n    clearanceServices: {\r\n      type: [Array, Set],\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 新增属性，不再依赖$parent\r\n    foldState: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    serviceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    serviceObject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    formDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    payable: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      bookingBillConfig: [{\r\n        file: \"订舱单\",\r\n        templateList: [\"bookingOrder1\"]\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(serviceTypeId) {\r\n      const serviceInstance = this.getServiceInstance(serviceTypeId)\r\n      if (!serviceInstance || !serviceInstance.supplierId) return ''\r\n\r\n      const supplier = this.supplierList.find(v => v.companyId === serviceInstance.supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceTypeId) {\r\n      const serviceInstance = this.getServiceInstance(serviceTypeId)\r\n      if (!serviceInstance) return ''\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 处理订舱单生成\r\n    getBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    // 事件转发给父组件\r\n    changeFold(serviceTypeId) {\r\n      this.$emit(\"changeFold\", serviceTypeId)\r\n    },\r\n    getFold(serviceTypeId) {\r\n      return this.foldState\r\n    },\r\n    getServiceInstance(serviceTypeId) {\r\n      return this.serviceInstance\r\n    },\r\n    getServiceObject(serviceTypeId) {\r\n      return this.serviceObject\r\n    },\r\n    getPayable(serviceTypeId) {\r\n      return this.payable\r\n    },\r\n    getFormDisable(serviceTypeId) {\r\n      return this.formDisable\r\n    },\r\n    changeServiceObject(serviceTypeId, serviceObject) {\r\n      this.$emit(\"changeServiceObject\", serviceTypeId, serviceObject)\r\n    },\r\n    auditCharge(serviceTypeId, event) {\r\n      this.$emit(\"auditCharge\", serviceTypeId, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    psaBookingCancel() {\r\n      this.$emit(\"psaBookingCancel\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceTypeId, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceTypeId, event, item)\r\n    },\r\n    // 物流进度相关方法\r\n    deleteLogItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsOpLogList) {\r\n        serviceObject.rsOpLogList = serviceObject.rsOpLogList.filter(item => item !== event)\r\n      }\r\n    },\r\n    updateLogList(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsOpLogList = event\r\n      }\r\n    },\r\n    // 费用列表相关方法\r\n    deleteAllCharge(serviceTypeId) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsChargeList = []\r\n      }\r\n    },\r\n    deleteChargeItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsChargeList) {\r\n        serviceObject.rsChargeList = serviceObject.rsChargeList.filter(item => item !== event)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// Clearance组件特定样式\r\n.clearance-component {\r\n  width: 100%;\r\n\r\n  .clearance-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title {\r\n      margin: 0;\r\n      width: 250px;\r\n      text-align: left;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .booking-bill-container {\r\n      margin-left: auto;\r\n\r\n      .booking-bill-link {\r\n        color: blue;\r\n        padding: 0;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}