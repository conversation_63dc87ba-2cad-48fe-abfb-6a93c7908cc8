{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\chargeList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\chargeList.vue", "mtime": 1754646305889}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_currency", "_interopRequireDefault", "require", "_vueTreeselect", "_js<PERSON><PERSON>yin", "_index", "_rich", "name", "components", "CompanySelect", "Treeselect", "props", "watch", "chargeData", "handler", "newVal", "oldVal", "$emit", "for<PERSON>ach", "item", "index", "oldItem", "currency", "amount", "exchangeRate", "inverseRate", "precision", "divide", "value", "subtotal", "dnUnitRate", "multiply", "add", "dutyRate", "error", "console", "deep", "immediate", "mounted", "_this", "length", "map", "$refs", "chargeTable", "toggleRowSelection", "profitCount", "computed", "hasConfirmRow", "result", "isAccountConfirmed", "data", "payTotalRMB", "payTotalUSD", "showClientName", "currencyCode", "profit", "profitTax", "services", "label", "service", "chargeRemark", "profitOpen", "profitTableData", "methods", "openProfit", "RMB", "receivable", "rsClientMessageReceivableRMB", "payable", "rsClientMessagePayableRMB", "rsClientMessageProfitRMB", "receivableTax", "rsClientMessageReceivableTaxRMB", "payableTax", "rsClientMessagePayableTaxRMB", "rsClientMessageProfitTaxRMB", "USD", "rsClientMessageReceivableUSD", "rsClientMessagePayableUSD", "rsClientMessageProfitUSD", "rsClientMessageReceivableTaxUSD", "rsClientMessagePayableTaxUSD", "rsClientMessageProfitTaxUSD", "push", "type", "_iterator", "_createForOfIteratorHelper2", "default", "$store", "state", "exchangeRateList", "_step", "s", "n", "done", "a", "ATD", "localCurrency", "overseaCurrency", "parseTime", "validFrom", "validTo", "settleRate", "base", "Date", "err", "e", "f", "auditStatus", "status", "selectCharge", "target", "row", "dnChargeNameId", "chargeId", "chargeName", "chargeLocalName", "handleSelectionChange", "val", "_this2", "isRecievingOrPaying", "dnCurrencyCode", "getServiceName", "id", "serviceName", "obj", "copyFreight", "companyList", "payClearingCompanyId", "companyId", "payCompanyName", "companyShortName", "_", "cloneDeep", "_objectSpread2", "copyAllFreight", "_this3", "$modal", "alertWarning", "charge", "changeUnitCost", "unit", "dnUnitCode", "$nextTick", "showCostUnit", "changeUnit", "showQuotationUnit", "handleChargeSelect", "showQuotationCharge", "changeCurrency", "showQuotationCurrency", "rowIndex", "_ref", "addReceivablePayable", "showClient", "showSupplier", "showCostCharge", "showCostCurrency", "showStrategy", "showUnitRate", "showAmount", "showCurrencyRate", "showDutyRate", "basicCurrencyRate", "dnAmount", "isReceivable", "clearingCompanyId", "serviceTypeId", "sqdServiceTypeId", "countProfit", "category", "unitRate", "currencyRate", "sqdDnCurrencyBalance", "$message", "deleteItem", "deleteAllItem", "companyNormalizer", "node", "companyLocalName", "pinyin", "getFullChars", "exports", "_default"], "sources": ["src/views/system/document/chargeList.vue"], "sourcesContent": ["<template>\r\n  <el-col :span=\"21.5\" :style=\"{'display':openChargeList?'':'none'}\" style=\"margin: 0;padding: 0;\">\r\n    <div :class=\"{'inactive':openChargeList==false,'active':openChargeList}\">\r\n      <el-table ref=\"chargeTable\" :data=\"chargeData\" :row-class-name=\"rowIndex\" border class=\"pd0\"\r\n                @selection-change=\"handleSelectionChange\"\r\n      >\r\n        <el-table-column v-if=\"isReceivable\" label=\"应收明细\" width=\"20px\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-row>\r\n              <el-col :span=\"11\">\r\n                <el-row>\r\n                  <el-col :span=\"4\">\r\n                    不含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{\r\n                      currency(rsClientMessageReceivableUSD, {\r\n                        separator: \",\",\r\n                        symbol: \"$\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(rsClientMessageReceivableRMB, {\r\n                        separator: \",\",\r\n                        symbol: \"￥\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"11\">\r\n                <el-row class=\"unHighlight-text\">\r\n                  <el-col :span=\"4\">\r\n                    含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{\r\n                      currency(rsClientMessageReceivableTaxUSD, {\r\n                        separator: \",\",\r\n                        symbol: \"$\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(rsClientMessageReceivableTaxRMB, {\r\n                        separator: \",\",\r\n                        symbol: \"￥\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"2\">\r\n                <el-button type=\"primary\" @click.native=\"profitOpen=true\">利润</el-button>\r\n                <el-dialog\r\n                  :visible.sync=\"profitOpen\"\r\n                  title=\"单票利润\"\r\n                  width=\"30%\"\r\n                  @open=\"openProfit\"\r\n                >\r\n                  <el-table\r\n                    :data=\"profitTableData\"\r\n                    border\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-table-column\r\n                      label=\"货币\"\r\n                      prop=\"currencyCode\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      label=\"应收\"\r\n                      prop=\"receivable\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      label=\"应付\"\r\n                      prop=\"payable\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      label=\"不含税利润\" prop=\"profit\"\r\n                      style=\"color: #0d0dfd\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      label=\"含税应收\"\r\n                      prop=\"receivableTax\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      label=\"含税应付\"\r\n                      prop=\"payableTax\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      label=\"含税利润\"\r\n                      prop=\"profitTax\"\r\n                    >\r\n                    </el-table-column>\r\n                  </el-table>\r\n\r\n                  <el-row>\r\n                    <el-col :span=\"5\">\r\n                      <el-form-item label=\"折合币种\" prop=\"rctOpDate\">\r\n                        <el-select v-model=\"currencyCode\" @change=\"profitCount(currencyCode)\">\r\n                          <el-option label=\"RMB\" value=\"RMB\"/>\r\n                          <el-option label=\"USD\" value=\"USD\"/>\r\n                        </el-select>\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"7\">\r\n                      <el-row>\r\n                        <el-col :span=\"12\">\r\n                          <div style=\"color: #0d0dfd\">不含税利润</div>\r\n                        </el-col>\r\n                        <el-col :span=\"12\">\r\n                          <el-input v-model=\"profit\" placeholder=\"不含税利润\"/>\r\n                        </el-col>\r\n                      </el-row>\r\n                    </el-col>\r\n                    <el-col :span=\"7\">\r\n                      <el-row>\r\n                        <el-col :span=\"12\">\r\n                          <div>含税利润</div>\r\n                        </el-col>\r\n                        <el-col :span=\"12\">\r\n                          <el-input v-model=\"profitTax\" placeholder=\"含税利润\"/>\r\n                        </el-col>\r\n                      </el-row>\r\n                    </el-col>\r\n                    <el-col :span=\"5\">\r\n                      <el-row>\r\n                        <el-col :span=\"12\">\r\n                          <div>折算汇率</div>\r\n                        </el-col>\r\n                        <el-col :span=\"12\">\r\n                          <el-input v-model=\"exchangeRate\" placeholder=\"含税利润\"/>\r\n                        </el-col>\r\n                      </el-row>\r\n                    </el-col>\r\n                  </el-row>\r\n                </el-dialog>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <el-table-column\r\n            align=\"center\"\r\n            type=\"selection\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"客户\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showClient\" style=\"width: 50px;height: 20px;\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showClient = true\"\r\n              >\r\n                {{ scope.row.companyName }}\r\n              </div>\r\n              <tree-select v-if=\"(companyList&&companyList.length>0)&&scope.row.showClient\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                           :multiple=\"false\" :pass=\"scope.row.clearingCompanyId\" :placeholder=\"'客户'\"\r\n                           :custom-options=\"companyList\" :flat=\"false\"\r\n                           :type=\"'clientCustom'\" @return=\"scope.row.clearingCompanyId=$event\"\r\n                           @close=\" showClientName==scope.row.companyName ? scope.row.showClient = false:null\"\r\n                           @returnData=\"showClientName=(($event.companyShortName&&$event.companyShortName!=='')?$event.companyShortName:$event.companyEnShortName)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用\" prop=\"quotationChargeId\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCharge\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCharge = true\"\r\n              >\r\n                {{ scope.row.chargeName }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationCharge\" :dbn=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                           :pass=\"scope.row.dnChargeNameId\" :placeholder=\"'运费'\" :type=\"'charge'\"\r\n                           :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                           @return=\"scope.row.dnChargeNameId = $event\"\r\n                           @returnData=\"handleChargeSelect(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"货币\" prop=\"quotationCurrencyId\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true\"\r\n              >\r\n                {{ scope.row.dnCurrencyCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationCurrency\" :pass=\"scope.row.dnCurrencyCode\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\" :type=\"'currency'\"\r\n                           @return=\"changeCurrency(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单价\" prop=\"quotationRate\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showUnitRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true\"\r\n              >\r\n                {{\r\n                  scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"¥\"\r\n                  }).format() : (scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"$\"\r\n                  }).format() : scope.row.dnUnitRate)\r\n                }}\r\n              </div>\r\n              <el-input-number v-show=\"scope.row.showUnitRate\" v-model=\"scope.row.dnUnitRate\" :controls=\"false\"\r\n                               :min=\"0.0001\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               :precision=\"4\" style=\"display:flex;width: 100%\"\r\n                               @blur=\"scope.row.showUnitRate=false\"\r\n                               @change=\"countProfit(scope.row,'unitRate')\"\r\n                               @input=\"countProfit(scope.row,'unitRate')\"\r\n                               @focusout.native=\"scope.row.showUnitRate=false\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单位\" prop=\"quotationUnitId\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationUnit\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationUnit = true\"\r\n              >\r\n                {{ scope.row.dnUnitCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationUnit\"\r\n                           :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\" :pass=\"scope.row.dnUnitCode\"\r\n                           :type=\"'unit'\" @return=\"changeUnit(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"数量\" prop=\"quotationAmount\" width=\"48px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showAmount\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true\"\r\n              >\r\n                {{ scope.row.dnAmount }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showAmount\" v-model=\"scope.row.dnAmount\" :controls=\"false\"\r\n                               :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                               placeholder=\"数量\" style=\"display:flex;width: 100%\"\r\n                               :min=\"0.00\" @blur=\"scope.row.showAmount=false\"\r\n                               @change=\"countProfit(scope.row,'amount')\"\r\n                               @input=\"countProfit(scope.row,'amount')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"汇率\" prop=\"quotationExchangeRate\" width=\"60\">\r\n            <template slot-scope=\"scope\" style=\"display:flex;\">\r\n              <div v-if=\"!scope.row.showCurrencyRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true\"\r\n              >\r\n                {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}\r\n              </div>\r\n              <el-input-number v-show=\"scope.row.showCurrencyRate\" v-model=\"scope.row.basicCurrencyRate\"\r\n                               :controls=\"false\"\r\n                               :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                               :precision=\"4\" :step=\"0.0001\" style=\"width: 100%\"\r\n                               :min=\"0.0001\" @blur=\"scope.row.showCurrencyRate=false\"\r\n                               @change=\"countProfit(scope.row,'currencyRate')\"\r\n                               @input=\"countProfit(scope.row,'currencyRate')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"税率\" prop=\"quotationTaxRate\" width=\"50px\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex;justify-content: center\">\r\n                <div v-if=\"!scope.row.showDutyRate\"\r\n                     @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true\"\r\n                >\r\n                  {{ scope.row.dutyRate }}\r\n                </div>\r\n                <el-input-number v-if=\"scope.row.showDutyRate\" v-model=\"scope.row.dutyRate\" :controls=\"false\"\r\n                                 :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                                 :min=\"0\" style=\"width: 75%\"\r\n                                 @blur=\"scope.row.showDutyRate=false\"\r\n                                 @change=\"countProfit(scope.row,'dutyRate')\"\r\n                                 @input=\"countProfit(scope.row,'dutyRate')\"\r\n                />\r\n                <div>%</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"小计\" prop=\"subtotal\" width=\"75\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                {{\r\n                  currency(scope.row.subtotal, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: (scope.row.dnCurrencyCode === \"RMB\" ? \"¥\" : \"$\")\r\n                  }).format()\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用备注\">\r\n            <template slot-scope=\"scope\">\r\n              <input v-model=\"scope.row.chargeRemark\"\r\n                     :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                     style=\"border: none;width: 100%;height: 100%;\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"审核状态\">\r\n            <template slot-scope=\"scope\">\r\n              {{ auditStatus(scope.row.isAccountConfirmed) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"已收金额\">\r\n            <template slot-scope=\"scope\">\r\n              {{\r\n                scope.row.sqdDnCurrencyPaid\r\n              }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未收余额\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.sqdDnCurrencyBalance }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"所属服务\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                <!--{{ getServiceName(scope.row.sqd_service_type_id) }}-->\r\n                {{ scope.row.serviceName ? scope.row.serviceName : getServiceName(scope.row.sqdServiceTypeId) }}\r\n              </div>\r\n              <!-- <el-select v-else v-model=\"scope.row.sqdServiceTypeId\" filterable placeholder=\"所属服务\">\r\n                 <el-option\r\n                   v-for=\"item in services\"\r\n                   :key=\"item.value\"\r\n                   :label=\"item.label\"\r\n                   :value=\"item.value\"\r\n                 >\r\n                 </el-option>\r\n               </el-select>-->\r\n            </template>\r\n          </el-table-column>\r\n        </el-table-column>\r\n\r\n        <!--应付-->\r\n        <el-table-column v-else label=\"应付明细\" width=\"20px\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-row>\r\n              <el-col :span=\"4\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">{{ currency(payTotalUSD, {separator: \",\", symbol: \"$\"}).format() }}</el-col>\r\n                  <el-col :span=\"12\">{{ currency(payTotalRMB, {separator: \",\", symbol: \"￥\"}).format() }}</el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"10\">\r\n                <el-row>\r\n                  <el-col :span=\"5\">\r\n                    不含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{ currency(payDetailUSD, {separator: \",\", symbol: \"$\"}).format() }}</el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(payDetailRMB, {separator: \",\", symbol: \"￥\"}).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"10\">\r\n                <el-row class=\"unHighlight-text\">\r\n                  <el-col :span=\"4\">\r\n                    含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{\r\n                      currency(payDetailUSDTax, {separator: \",\", symbol: \"$\"}).format()\r\n                    }}\r\n                  </el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(payDetailRMBTax, {\r\n                        separator: \",\",\r\n                        symbol: \"￥\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <el-table-column\r\n            align=\"center\"\r\n            type=\"selection\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column v-if=\"!hiddenSupplier\" align=\"center\" label=\"供应商\" width=\"90\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showSupplier\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showSupplier = true\"\r\n              >\r\n                {{ scope.row.companyName }}\r\n              </div>\r\n              <company-select v-if=\"(companyList && companyList.length>0)&&scope.row.showSupplier\"\r\n                              :class=\"disabled || scope.row.isAccountConfirmed == '1'?'disable-form':''\"\r\n                              :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\" :load-options=\"companyList\"\r\n                              :multiple=\"false\"\r\n                              :no-parent=\"true\"\r\n                              :pass=\"scope.row.clearingCompanyId\"\r\n                              :placeholder=\"'供应商'\"\r\n                              @return=\"scope.row.clearingCompanyId=$event\"\r\n                              @returnData=\"$event.companyShortName==scope.row.companyName?scope.row.showSupplier = false:null\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用\" prop=\"costChargeId\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showCostCharge\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCostCharge = true\"\r\n              >\r\n                {{ scope.row.chargeName }}\r\n              </div>\r\n              <tree-select v-if=\"scope.row.showCostCharge\" :dbn=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                           :pass=\"scope.row.dnChargeNameId\" :placeholder=\"'运费'\" :type=\"'charge'\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                           @return=\"scope.row.dnChargeNameId = $event\"\r\n                           @returnData=\"$event.chargeLocalName == scope.row.chargeName ? scope.row.showCostCharge=false:null\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"货币\" prop=\"costCurrencyId\" width=\"70px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true\"\r\n              >\r\n                {{ scope.row.dnCurrencyCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationCurrency\" :pass=\"scope.row.dnCurrencyCode\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\" :type=\"'currency'\"\r\n                           @return=\"changeCurrency(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单价\" prop=\"inquiryRate\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showUnitRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true\"\r\n              >\r\n                {{\r\n                  scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"¥\"\r\n                  }).format() : scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"$\"\r\n                  }).format() : scope.row.dnUnitRate\r\n                }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showUnitRate\" v-model=\"scope.row.dnUnitRate\" :controls=\"false\" :min=\"0\"\r\n                               style=\"display:flex;width: 100%\" @blur=\"scope.row.showUnitRate=false\"\r\n                               :precision=\"4\"\r\n                               @change=\"countProfit(scope.row,'unitRate')\"\r\n                               @input=\"countProfit(scope.row,'unitRate')\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单位\" prop=\"costUnitId\" width=\"50px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showCostUnit\"\r\n                   @click=\"(disabled || scope.row.isAccountConfirmed == '1')?null:scope.row.showCostUnit = true\"\r\n              >\r\n                {{ scope.row.dnUnitCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showCostUnit\" :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                           :pass=\"scope.row.dnUnitCode\"\r\n                           :type=\"'unit'\" @return=\"changeUnitCost(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"数量\" prop=\"costAmount\" width=\"48px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showAmount\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true\"\r\n              >\r\n                {{ scope.row.dnAmount }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showAmount\" v-model=\"scope.row.dnAmount\" :controls=\"false\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               placeholder=\"数量\" style=\"display:flex;width: 100%\"\r\n                               :min=\"0.00\" @blur=\"scope.row.showAmount=false\"\r\n                               @change=\"countProfit(scope.row,'amount')\"\r\n                               @input=\"countProfit(scope.row,'amount')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"汇率\" prop=\"costExchangeRate\" width=\"60px\">\r\n            <template slot-scope=\"scope\" style=\"display:flex;\">\r\n              <div v-if=\"!scope.row.showCurrencyRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true\"\r\n              >\r\n                {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showCurrencyRate\" v-model=\"scope.row.basicCurrencyRate\" :controls=\"false\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               :precision=\"4\" :step=\"0.0001\" style=\"width: 100%\"\r\n                               :min=\"0.0001\" @blur=\"scope.row.showCurrencyRate=false\"\r\n                               @change=\"countProfit(scope.row,'currencyRate')\"\r\n                               @input=\"countProfit(scope.row,'currencyRate')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"税率\" prop=\"costTaxRate\" width=\"68px\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex;justify-content: center\">\r\n                <div v-if=\"!scope.row.showDutyRate\"\r\n                     @click=\"(disabled || scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true\"\r\n                >\r\n                  {{ scope.row.dutyRate }}\r\n                </div>\r\n                <el-input-number v-if=\"scope.row.showDutyRate\" v-model=\"scope.row.dutyRate\" :controls=\"false\"\r\n                                 :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                                 :min=\"0\" style=\"width: 75%\"\r\n                                 @blur=\"scope.row.showDutyRate=false\"\r\n                                 @change=\"countProfit(scope.row,'dutyRate')\"\r\n                                 @input=\"countProfit(scope.row,'dutyRate')\"\r\n                />\r\n                <div>%</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"小计\" prop=\"costTotal\" width=\"65\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                {{\r\n                  currency(scope.row.subtotal, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: (scope.row.dnCurrencyCode === \"RMB\" ? \"¥\" : \"$\")\r\n                  }).format()\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用备注\">\r\n            <template slot-scope=\"scope\">\r\n              <input v-model=\"scope.row.chargeRemark\" :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                     style=\"border: none\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"审核状态\">\r\n            <template slot-scope=\"scope\">\r\n              {{ auditStatus(scope.row.isAccountConfirmed) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"已付金额\">\r\n            <template slot-scope=\"scope\">\r\n              {{\r\n                scope.row.sqdDnCurrencyPaid\r\n              }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未付余额\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.sqdDnCurrencyBalance }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"生成应收\" prop=\"costTotal\" width=\"65\">\r\n            <template slot=\"header\" slot-scope=\"scope\">\r\n              <el-button\r\n                :disabled=\"disabled\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"copyAllFreight()\"\r\n              >生成应收\r\n              </el-button>\r\n            </template>\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"copyFreight(scope.row)\"\r\n              >复制到应收\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table-column>\r\n\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-button\r\n              :disabled=\"disabled || hasConfirmRow\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"deleteAllItem()\"\r\n              style=\"color: red\"\r\n            >全部删除\r\n            </el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"deleteItem(scope.row)\"\r\n              :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    <el-button style=\"padding: 0\" type=\"text\"\r\n               @click=\"addReceivablePayable\"\r\n               :disabled=\"disabled\"\r\n    >[＋]\r\n    </el-button>\r\n  </el-col>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport {parseTime} from \"@/utils/rich\"\r\n\r\nexport default {\r\n  name: \"charges\",\r\n  components: {CompanySelect, Treeselect},\r\n  props: [\"chargeData\", \"companyList\", \"openChargeList\", \"isReceivable\", \"disabled\",\r\n    \"serviceTypeId\", \"serviceId\", \"hiddenSupplier\", \"rsClientMessageReceivableTaxUSD\",\r\n    \"rsClientMessageReceivableTaxRMB\", \"rsClientMessagePayableTaxUSD\", \"rsClientMessagePayableTaxRMB\",\r\n    \"rsClientMessageReceivableRMB\", \"rsClientMessageReceivableUSD\", \"rsClientMessagePayableRMB\",\r\n    \"rsClientMessagePayableUSD\", \"rsClientMessageProfit\", \"rsClientMessageProfitNoTax\", \"payDetailRMB\",\r\n    \"payDetailUSD\", \"payDetailRMBTax\", \"payDetailUSDTax\", \"rsClientMessageProfitUSD\", \"rsClientMessageProfitRMB\",\r\n    \"rsClientMessageProfitTaxRMB\", \"rsClientMessageProfitTaxUSD\", \"ATD\"],\r\n  watch: {\r\n    chargeData: {\r\n      handler: function (newVal, oldVal) {\r\n        if (!oldVal) {\r\n          this.$emit(\"return\", newVal)\r\n          return\r\n        }\r\n\r\n        // 遍历费用列表，检查币种变化\r\n        newVal ? newVal.forEach((item, index) => {\r\n          const oldItem = oldVal[index]\r\n\r\n          // 检查币种变化并计算小计\r\n          if (item.currency && item.amount) {\r\n            // 如果从 RMB 换成 USD，使用 1/汇率 计算\r\n            if (oldItem && oldItem.currency === \"RMB\" && item.currency === \"USD\") {\r\n              if (item.exchangeRate && item.exchangeRate !== 0) {\r\n                try {\r\n                  // 计算 1/汇率，保留4位小数\r\n                  const inverseRate = currency(1, {precision: 4}).divide(item.exchangeRate).value\r\n\r\n                  // 计算小计: 单价 * 数量 * 汇率 * (1 + 税率/100)\r\n                  item.subtotal = currency(item.dnUnitRate || 0, {precision: 4})\r\n                    .multiply(item.amount)\r\n                    .multiply(inverseRate)\r\n                    .multiply(currency(1).add(currency(item.dutyRate || 0).divide(100)))\r\n                    .value\r\n                } catch (error) {\r\n                  console.error(\"计算小计出错:\", error)\r\n                  item.subtotal = 0\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }) : null\r\n\r\n        this.$emit(\"return\", newVal ? newVal : [])\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => this.$refs.chargeTable.toggleRowSelection(item, true)) : null\r\n\r\n    this.profitCount(\"RMB\")\r\n  },\r\n  computed: {\r\n\r\n    hasConfirmRow() {\r\n      let result = false;\r\n      (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => {\r\n        if (item.isAccountConfirmed === \"1\") {\r\n          result = true\r\n        }\r\n      }) : null\r\n      return result\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      payTotalRMB: 0,\r\n      payTotalUSD: 0,\r\n      showClientName: null,\r\n      currencyCode: \"RMB\",\r\n      profit: 0,\r\n      profitTax: 0,\r\n      exchangeRate: 0,\r\n      services: [{\r\n        value: 1,\r\n        label: \"海运\"\r\n      }, {\r\n        value: 10,\r\n        label: \"空运\"\r\n      }, {\r\n        value: 20,\r\n        label: \"铁路\"\r\n      }, {\r\n        value: 40,\r\n        label: \"快递\"\r\n      }, {\r\n        value: 50,\r\n        label: \"拖车\"\r\n      }, {\r\n        value: 60,\r\n        label: \"报关\"\r\n      }, {\r\n        value: 70,\r\n        label: \"清关派送\"\r\n      }, {\r\n        value: 80,\r\n        label: \"码头仓储\"\r\n      }, {\r\n        value: 90,\r\n        label: \"检验证书\"\r\n      }, {\r\n        value: 100,\r\n        label: \"保险\"\r\n      }, {\r\n        value: 101,\r\n        label: \"扩展服务\"\r\n      }],\r\n      service: [{\r\n        value: 1,\r\n        label: \"基础服务\"\r\n      }, {\r\n        value: 4,\r\n        label: \"前程运输\"\r\n      }, {\r\n        value: 5,\r\n        label: \"出口报关\"\r\n      }, {\r\n        value: 6,\r\n        label: \"进口清关\"\r\n      }, {value: 2, label: \"海运\"}\r\n        , {value: 3, label: \"陆运\"}\r\n        , {value: 4, label: \"铁路\"}\r\n        , {value: 5, label: \"空运\"}\r\n        , {value: 6, label: \"快递\"}\r\n        , {value: 21, label: \"整柜海运\"}\r\n        , {value: 22, label: \"拼柜海运\"}\r\n        , {value: 23, label: \"散杂船\"}\r\n        , {value: 24, label: \"滚装船\"}\r\n        , {value: 41, label: \"整柜铁路\"}\r\n        , {value: 42, label: \"拼柜铁路\"}\r\n        , {value: 43, label: \"铁路车皮\"}\r\n        , {value: 51, label: \"空运普舱\"}\r\n        , {value: 52, label: \"空运包板\"}\r\n        , {value: 53, label: \"空运包机\"}\r\n        , {value: 54, label: \"空运行李\"}\r\n        , {value: 961, label: \"前程运输\"}\r\n        , {value: 964, label: \"进口清关\"}\r\n        , {value: 7, label: \"出口报关\"}\r\n      ],\r\n      chargeRemark: null,\r\n      profitOpen: false,\r\n      profitTableData: []\r\n    }\r\n  },\r\n  methods: {\r\n    openProfit() {\r\n      this.profitTableData = []\r\n\r\n      let RMB = {}\r\n      RMB.currencyCode = \"RMB\"\r\n      RMB.receivable = this.rsClientMessageReceivableRMB\r\n      RMB.payable = this.rsClientMessagePayableRMB\r\n      // 不含税利润\r\n      RMB.profit = this.rsClientMessageProfitRMB\r\n      // 含税应收\r\n      RMB.receivableTax = this.rsClientMessageReceivableTaxRMB\r\n      // 含税应付\r\n      RMB.payableTax = this.rsClientMessagePayableTaxRMB\r\n      // 含税利润\r\n      RMB.profitTax = this.rsClientMessageProfitTaxRMB\r\n\r\n      let USD = {}\r\n      USD.currencyCode = \"USD\"\r\n      USD.receivable = this.rsClientMessageReceivableUSD\r\n      USD.payable = this.rsClientMessagePayableUSD\r\n      USD.profit = this.rsClientMessageProfitUSD\r\n      USD.receivableTax = this.rsClientMessageReceivableTaxUSD\r\n      USD.payableTax = this.rsClientMessagePayableTaxUSD\r\n      USD.profitTax = this.rsClientMessageProfitTaxUSD\r\n\r\n      this.profitTableData.push(RMB)\r\n      this.profitTableData.push(USD)\r\n\r\n      this.profitCount(\"RMB\")\r\n    },\r\n    profitCount(type) {\r\n      let exchangeRate\r\n      for (const a of this.$store.state.data.exchangeRateList) {\r\n        if (this.ATD) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && \"USD\" == a.overseaCurrency\r\n            && parseTime(a.validFrom) <= parseTime(this.ATD)\r\n            && parseTime(this.ATD) <= parseTime(a.validTo)\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n        if (!exchangeRate) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && \"USD\" == a.overseaCurrency\r\n            && parseTime(a.validFrom) <= parseTime(new Date())\r\n            && parseTime(new Date()) <= parseTime(a.validTo)) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      }\r\n      this.exchangeRate = exchangeRate\r\n\r\n      if (type === \"RMB\") {\r\n        // 都折算成人民币\r\n        this.profit = currency(this.rsClientMessageProfitUSD).multiply(exchangeRate).add(this.rsClientMessageProfitRMB).value\r\n        this.profitTax = currency(this.rsClientMessageProfitTaxUSD).multiply(exchangeRate).add(this.rsClientMessageProfitTaxRMB).value\r\n      } else {\r\n        this.profit = currency(this.rsClientMessageProfitRMB).divide(exchangeRate).add(this.rsClientMessageProfitUSD).value\r\n        this.profitTax = currency(this.rsClientMessageProfitTaxRMB).divide(exchangeRate).add(this.rsClientMessageProfitTaxUSD).value\r\n      }\r\n    },\r\n    auditStatus(status) {\r\n      return status == 1 ? \"已审核\" : \"未审核\"\r\n    },\r\n    selectCharge(target, row) {\r\n      row.dnChargeNameId = target.chargeId\r\n      row.chargeName = target.chargeLocalName\r\n    },\r\n    handleSelectionChange(val) {\r\n      this.$emit(\"selectRow\", val)\r\n\r\n      this.payTotalUSD = 0\r\n      this.payTotalRMB = 0\r\n      val ? val.map(item => {\r\n        if (item.isRecievingOrPaying == 1) {\r\n          if (item.dnCurrencyCode === \"USD\") {\r\n            this.payTotalUSD = currency(this.payTotalUSD).add(item.subtotal)\r\n          } else {\r\n            this.payTotalRMB = currency(this.payTotalRMB).add(item.subtotal)\r\n          }\r\n\r\n        }\r\n      }) : null\r\n\r\n    },\r\n    currency,\r\n    getServiceName(id) {\r\n      let serviceName = \"\"\r\n      this.services.map(obj => {\r\n        obj.value === id ? serviceName = obj.label : null\r\n      })\r\n      return serviceName\r\n    },\r\n    copyFreight(row) {\r\n      if (this.companyList.length > 0) {\r\n        row.payClearingCompanyId = this.companyList[0].companyId\r\n        row.payCompanyName = this.companyList[0].companyShortName\r\n      }\r\n      row.isAccountConfirmed = 0\r\n      // 报价列表跳转订舱时没有公司列表,复制到应收没有客户信息\r\n      let data = this._.cloneDeep(row)\r\n\r\n      this.$emit(\"copyFreight\", {...data, chargeId: null})\r\n    },\r\n    copyAllFreight() {\r\n      if (!this.companyList.length > 0) {\r\n        this.$modal.alertWarning(\"请先选择委托单位或关联单位\")\r\n        return\r\n      }\r\n\r\n      this.chargeData.map(charge => {\r\n        charge.payClearingCompanyId = this.companyList[0].companyId\r\n        charge.payCompanyName = this.companyList[0].companyShortName\r\n        charge.isRecievingOrPaying = 0\r\n        charge.isAccountConfirmed = 0\r\n        charge.chargeId = null\r\n        this.$emit(\"copyFreight\", this._.cloneDeep(charge))\r\n      })\r\n    },\r\n    changeUnitCost(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showCostUnit = false\r\n      })\r\n    },\r\n    changeUnit(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showQuotationUnit = false\r\n      })\r\n    },\r\n    handleChargeSelect(row, data) {\r\n      if (row.chargeLocalName === data.chargeName) {\r\n        row.chargeName = data.chargeLocalName\r\n        row.showQuotationCharge = false\r\n      }\r\n      if (row.currencyCode == null && data.currencyCode) {\r\n        row.dnCurrencyCode = data.currencyCode\r\n      }\r\n    },\r\n    changeCurrency(row, currencyCode) {\r\n      row.dnCurrencyCode = currencyCode\r\n      /* let exchangeRate\r\n      if (currencyCode === \"USD\") {\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && row.dnCurrencyCode == a.overseaCurrency\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      } */\r\n\r\n      this.$nextTick(() => {\r\n        // row.basicCurrencyRate = exchangeRate ? exchangeRate : 1\r\n        row.showQuotationCurrency = false\r\n      })\r\n    },\r\n    /** 序号 */\r\n    rowIndex({ row, rowIndex }) {\r\n      row.id = rowIndex + 1\r\n    },\r\n    addReceivablePayable() {\r\n      let obj = {\r\n        showClient: true,\r\n        showSupplier: true,\r\n        showQuotationCharge: true,\r\n        showCostCharge: true,\r\n        showQuotationCurrency: true,\r\n        showCostCurrency: true,\r\n        showQuotationUnit: true,\r\n        showCostUnit: true,\r\n        showStrategy: true,\r\n        showUnitRate: true,\r\n        showAmount: true,\r\n        showCurrencyRate: true,\r\n        showDutyRate: true,\r\n        basicCurrencyRate: 1,\r\n        dutyRate: 0,\r\n        dnAmount: 1,\r\n        // 应收还是应付\r\n        isRecievingOrPaying: this.isReceivable ? 0 : 1,\r\n        clearingCompanyId: this.chargeData.length > 0 ? this.chargeData[this.chargeData.length - 1].clearingCompanyId : null\r\n      }\r\n      if (this.serviceTypeId === 1) obj.sqdServiceTypeId = 1\r\n      if (this.serviceTypeId === 10) obj.sqdServiceTypeId = 10\r\n      if (this.serviceTypeId === 20) obj.sqdServiceTypeId = 20\r\n      if (this.serviceTypeId === 40) obj.sqdServiceTypeId = 40\r\n      if (this.serviceTypeId === 50) obj.sqdServiceTypeId = 50\r\n      if (this.serviceTypeId === 60) obj.sqdServiceTypeId = 60\r\n      if (this.serviceTypeId === 70) obj.sqdServiceTypeId = 70\r\n      if (this.serviceTypeId === 80) obj.sqdServiceTypeId = 80\r\n      if (this.serviceTypeId === 90) obj.sqdServiceTypeId = 90\r\n      if (this.serviceTypeId === 100) obj.sqdServiceTypeId = 100\r\n      if (this.serviceTypeId === 101) obj.sqdServiceTypeId = 101\r\n      this.chargeData.push(obj)\r\n    },\r\n    countProfit(row, category) {\r\n      // 确保所有必要的值都存在且有效\r\n      if (!row) return\r\n\r\n      // 使用currency.js来处理数值,避免精度损失\r\n      const unitRate = row.dnUnitRate || 0\r\n      const amount = row.dnAmount || 0\r\n      const currencyRate = currency(row.basicCurrencyRate || 1, {precision: 4}).value\r\n      const dutyRate = currency(row.dutyRate || 0).value\r\n\r\n      try {\r\n        // 计算小计\r\n        const subtotal = currency(unitRate, {precision: 4})\r\n          .multiply(amount)\r\n          .multiply(currencyRate)\r\n          .multiply(currency(1).add(currency(dutyRate).divide(100)))\r\n          .value\r\n\r\n        // 更新行数据\r\n        row.subtotal = currency(subtotal, {precision: 2}).value\r\n        row.sqdDnCurrencyBalance = row.isAccountConfirmed === \"0\" ? currency(subtotal, {precision: 2}).value : row.sqdDnCurrencyBalance\r\n\r\n        // 根据不同的输入类型关闭对应的编辑状态\r\n        switch (category) {\r\n          case \"strategy\":\r\n            row.showStrategy = false\r\n            break\r\n          case \"unitRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"amount\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"currencyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"dutyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n        }\r\n\r\n        // 触发数据更新\r\n        this.$emit(\"return\", this.chargeData)\r\n\r\n      } catch (error) {\r\n        console.error(\"计算小计时出错:\", error)\r\n        this.$message.error(\"计算小计时出错,请检查输入值是否正确\")\r\n      }\r\n    },\r\n    deleteItem(row) {\r\n      this.$emit(\"deleteItem\", row)\r\n    },\r\n    deleteAllItem(row) {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n    companyNormalizer(node) {\r\n      return {\r\n        id: node.companyId,\r\n        label: (node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\") + \",\" + pinyin.getFullChars((node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\"))\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\ninput:focus {\r\n  outline: none;\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;AA6mBA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAK,IAAA;EACAC,UAAA;IAAAC,aAAA,EAAAA,cAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,KAAA,8EACA,mFACA,mGACA,6FACA,oGACA,8GACA;EACAC,KAAA;IACAC,UAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,KAAAA,MAAA;UACA,KAAAC,KAAA,WAAAF,MAAA;UACA;QACA;;QAEA;QACAA,MAAA,GAAAA,MAAA,CAAAG,OAAA,WAAAC,IAAA,EAAAC,KAAA;UACA,IAAAC,OAAA,GAAAL,MAAA,CAAAI,KAAA;;UAEA;UACA,IAAAD,IAAA,CAAAG,QAAA,IAAAH,IAAA,CAAAI,MAAA;YACA;YACA,IAAAF,OAAA,IAAAA,OAAA,CAAAC,QAAA,cAAAH,IAAA,CAAAG,QAAA;cACA,IAAAH,IAAA,CAAAK,YAAA,IAAAL,IAAA,CAAAK,YAAA;gBACA;kBACA;kBACA,IAAAC,WAAA,OAAAH,iBAAA;oBAAAI,SAAA;kBAAA,GAAAC,MAAA,CAAAR,IAAA,CAAAK,YAAA,EAAAI,KAAA;;kBAEA;kBACAT,IAAA,CAAAU,QAAA,OAAAP,iBAAA,EAAAH,IAAA,CAAAW,UAAA;oBAAAJ,SAAA;kBAAA,GACAK,QAAA,CAAAZ,IAAA,CAAAI,MAAA,EACAQ,QAAA,CAAAN,WAAA,EACAM,QAAA,KAAAT,iBAAA,KAAAU,GAAA,KAAAV,iBAAA,EAAAH,IAAA,CAAAc,QAAA,OAAAN,MAAA,QACAC,KAAA;gBACA,SAAAM,KAAA;kBACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;kBACAf,IAAA,CAAAU,QAAA;gBACA;cACA;YACA;UACA;QACA;QAEA,KAAAZ,KAAA,WAAAF,MAAA,GAAAA,MAAA;MACA;MACAqB,IAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAA1B,UAAA,SAAAA,UAAA,CAAA2B,MAAA,YAAA3B,UAAA,CAAA4B,GAAA,WAAAtB,IAAA;MAAA,OAAAoB,KAAA,CAAAG,KAAA,CAAAC,WAAA,CAAAC,kBAAA,CAAAzB,IAAA;IAAA;IAEA,KAAA0B,WAAA;EACA;EACAC,QAAA;IAEAC,aAAA,WAAAA,cAAA;MACA,IAAAC,MAAA;MACA,KAAAnC,UAAA,SAAAA,UAAA,CAAA2B,MAAA,YAAA3B,UAAA,CAAA4B,GAAA,WAAAtB,IAAA;QACA,IAAAA,IAAA,CAAA8B,kBAAA;UACAD,MAAA;QACA;MACA;MACA,OAAAA,MAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,WAAA;MACAC,cAAA;MACAC,YAAA;MACAC,MAAA;MACAC,SAAA;MACAhC,YAAA;MACAiC,QAAA;QACA7B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;MACAC,OAAA;QACA/B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;QACA9B,KAAA;QACA8B,KAAA;MACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,GACA;QAAA9B,KAAA;QAAA8B,KAAA;MAAA,EACA;MACAE,YAAA;MACAC,UAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAF,eAAA;MAEA,IAAAG,GAAA;MACAA,GAAA,CAAAX,YAAA;MACAW,GAAA,CAAAC,UAAA,QAAAC,4BAAA;MACAF,GAAA,CAAAG,OAAA,QAAAC,yBAAA;MACA;MACAJ,GAAA,CAAAV,MAAA,QAAAe,wBAAA;MACA;MACAL,GAAA,CAAAM,aAAA,QAAAC,+BAAA;MACA;MACAP,GAAA,CAAAQ,UAAA,QAAAC,4BAAA;MACA;MACAT,GAAA,CAAAT,SAAA,QAAAmB,2BAAA;MAEA,IAAAC,GAAA;MACAA,GAAA,CAAAtB,YAAA;MACAsB,GAAA,CAAAV,UAAA,QAAAW,4BAAA;MACAD,GAAA,CAAAR,OAAA,QAAAU,yBAAA;MACAF,GAAA,CAAArB,MAAA,QAAAwB,wBAAA;MACAH,GAAA,CAAAL,aAAA,QAAAS,+BAAA;MACAJ,GAAA,CAAAH,UAAA,QAAAQ,4BAAA;MACAL,GAAA,CAAApB,SAAA,QAAA0B,2BAAA;MAEA,KAAApB,eAAA,CAAAqB,IAAA,CAAAlB,GAAA;MACA,KAAAH,eAAA,CAAAqB,IAAA,CAAAP,GAAA;MAEA,KAAA/B,WAAA;IACA;IACAA,WAAA,WAAAA,YAAAuC,IAAA;MACA,IAAA5D,YAAA;MAAA,IAAA6D,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACA,KAAAC,MAAA,CAAAC,KAAA,CAAAvC,IAAA,CAAAwC,gBAAA;QAAAC,KAAA;MAAA;QAAA,KAAAN,SAAA,CAAAO,CAAA,MAAAD,KAAA,GAAAN,SAAA,CAAAQ,CAAA,IAAAC,IAAA;UAAA,IAAAC,CAAA,GAAAJ,KAAA,CAAA/D,KAAA;UACA,SAAAoE,GAAA;YACA,IAAAD,CAAA,CAAAE,aAAA,cACA,SAAAF,CAAA,CAAAG,eAAA,IACA,IAAAC,eAAA,EAAAJ,CAAA,CAAAK,SAAA,SAAAD,eAAA,OAAAH,GAAA,KACA,IAAAG,eAAA,OAAAH,GAAA,SAAAG,eAAA,EAAAJ,CAAA,CAAAM,OAAA,GACA;cACA7E,YAAA,OAAAF,iBAAA,EAAAyE,CAAA,CAAAO,UAAA,EAAA3E,MAAA,CAAAoE,CAAA,CAAAQ,IAAA,EAAA3E,KAAA;YACA;UACA;UACA,KAAAJ,YAAA;YACA,IAAAuE,CAAA,CAAAE,aAAA,cACA,SAAAF,CAAA,CAAAG,eAAA,IACA,IAAAC,eAAA,EAAAJ,CAAA,CAAAK,SAAA,SAAAD,eAAA,MAAAK,IAAA,OACA,IAAAL,eAAA,MAAAK,IAAA,WAAAL,eAAA,EAAAJ,CAAA,CAAAM,OAAA;cACA7E,YAAA,OAAAF,iBAAA,EAAAyE,CAAA,CAAAO,UAAA,EAAA3E,MAAA,CAAAoE,CAAA,CAAAQ,IAAA,EAAA3E,KAAA;YACA;UACA;QACA;MAAA,SAAA6E,GAAA;QAAApB,SAAA,CAAAqB,CAAA,CAAAD,GAAA;MAAA;QAAApB,SAAA,CAAAsB,CAAA;MAAA;MACA,KAAAnF,YAAA,GAAAA,YAAA;MAEA,IAAA4D,IAAA;QACA;QACA,KAAA7B,MAAA,OAAAjC,iBAAA,OAAAyD,wBAAA,EAAAhD,QAAA,CAAAP,YAAA,EAAAQ,GAAA,MAAAsC,wBAAA,EAAA1C,KAAA;QACA,KAAA4B,SAAA,OAAAlC,iBAAA,OAAA4D,2BAAA,EAAAnD,QAAA,CAAAP,YAAA,EAAAQ,GAAA,MAAA2C,2BAAA,EAAA/C,KAAA;MACA;QACA,KAAA2B,MAAA,OAAAjC,iBAAA,OAAAgD,wBAAA,EAAA3C,MAAA,CAAAH,YAAA,EAAAQ,GAAA,MAAA+C,wBAAA,EAAAnD,KAAA;QACA,KAAA4B,SAAA,OAAAlC,iBAAA,OAAAqD,2BAAA,EAAAhD,MAAA,CAAAH,YAAA,EAAAQ,GAAA,MAAAkD,2BAAA,EAAAtD,KAAA;MACA;IACA;IACAgF,WAAA,WAAAA,YAAAC,MAAA;MACA,OAAAA,MAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,MAAA,EAAAC,GAAA;MACAA,GAAA,CAAAC,cAAA,GAAAF,MAAA,CAAAG,QAAA;MACAF,GAAA,CAAAG,UAAA,GAAAJ,MAAA,CAAAK,eAAA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAtG,KAAA,cAAAqG,GAAA;MAEA,KAAAlE,WAAA;MACA,KAAAD,WAAA;MACAmE,GAAA,GAAAA,GAAA,CAAA7E,GAAA,WAAAtB,IAAA;QACA,IAAAA,IAAA,CAAAqG,mBAAA;UACA,IAAArG,IAAA,CAAAsG,cAAA;YACAF,MAAA,CAAAnE,WAAA,OAAA9B,iBAAA,EAAAiG,MAAA,CAAAnE,WAAA,EAAApB,GAAA,CAAAb,IAAA,CAAAU,QAAA;UACA;YACA0F,MAAA,CAAApE,WAAA,OAAA7B,iBAAA,EAAAiG,MAAA,CAAApE,WAAA,EAAAnB,GAAA,CAAAb,IAAA,CAAAU,QAAA;UACA;QAEA;MACA;IAEA;IACAP,QAAA,EAAAA,iBAAA;IACAoG,cAAA,WAAAA,eAAAC,EAAA;MACA,IAAAC,WAAA;MACA,KAAAnE,QAAA,CAAAhB,GAAA,WAAAoF,GAAA;QACAA,GAAA,CAAAjG,KAAA,KAAA+F,EAAA,GAAAC,WAAA,GAAAC,GAAA,CAAAnE,KAAA;MACA;MACA,OAAAkE,WAAA;IACA;IACAE,WAAA,WAAAA,YAAAd,GAAA;MACA,SAAAe,WAAA,CAAAvF,MAAA;QACAwE,GAAA,CAAAgB,oBAAA,QAAAD,WAAA,IAAAE,SAAA;QACAjB,GAAA,CAAAkB,cAAA,QAAAH,WAAA,IAAAI,gBAAA;MACA;MACAnB,GAAA,CAAA/D,kBAAA;MACA;MACA,IAAAC,IAAA,QAAAkF,CAAA,CAAAC,SAAA,CAAArB,GAAA;MAEA,KAAA/F,KAAA,oBAAAqH,cAAA,CAAA/C,OAAA,MAAA+C,cAAA,CAAA/C,OAAA,MAAArC,IAAA;QAAAgE,QAAA;MAAA;IACA;IACAqB,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,UAAAT,WAAA,CAAAvF,MAAA;QACA,KAAAiG,MAAA,CAAAC,YAAA;QACA;MACA;MAEA,KAAA7H,UAAA,CAAA4B,GAAA,WAAAkG,MAAA;QACAA,MAAA,CAAAX,oBAAA,GAAAQ,MAAA,CAAAT,WAAA,IAAAE,SAAA;QACAU,MAAA,CAAAT,cAAA,GAAAM,MAAA,CAAAT,WAAA,IAAAI,gBAAA;QACAQ,MAAA,CAAAnB,mBAAA;QACAmB,MAAA,CAAA1F,kBAAA;QACA0F,MAAA,CAAAzB,QAAA;QACAsB,MAAA,CAAAvH,KAAA,gBAAAuH,MAAA,CAAAJ,CAAA,CAAAC,SAAA,CAAAM,MAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAA5B,GAAA,EAAA6B,IAAA;MACA7B,GAAA,CAAA8B,UAAA,GAAAD,IAAA;MACA,KAAAE,SAAA;QACA/B,GAAA,CAAAgC,YAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAjC,GAAA,EAAA6B,IAAA;MACA7B,GAAA,CAAA8B,UAAA,GAAAD,IAAA;MACA,KAAAE,SAAA;QACA/B,GAAA,CAAAkC,iBAAA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAAnC,GAAA,EAAA9D,IAAA;MACA,IAAA8D,GAAA,CAAAI,eAAA,KAAAlE,IAAA,CAAAiE,UAAA;QACAH,GAAA,CAAAG,UAAA,GAAAjE,IAAA,CAAAkE,eAAA;QACAJ,GAAA,CAAAoC,mBAAA;MACA;MACA,IAAApC,GAAA,CAAA1D,YAAA,YAAAJ,IAAA,CAAAI,YAAA;QACA0D,GAAA,CAAAS,cAAA,GAAAvE,IAAA,CAAAI,YAAA;MACA;IACA;IACA+F,cAAA,WAAAA,eAAArC,GAAA,EAAA1D,YAAA;MACA0D,GAAA,CAAAS,cAAA,GAAAnE,YAAA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAEA,KAAAyF,SAAA;QACA;QACA/B,GAAA,CAAAsC,qBAAA;MACA;IACA;IACA,SACAC,QAAA,WAAAA,SAAAC,IAAA;MAAA,IAAAxC,GAAA,GAAAwC,IAAA,CAAAxC,GAAA;QAAAuC,QAAA,GAAAC,IAAA,CAAAD,QAAA;MACAvC,GAAA,CAAAW,EAAA,GAAA4B,QAAA;IACA;IACAE,oBAAA,WAAAA,qBAAA;MACA,IAAA5B,GAAA;QACA6B,UAAA;QACAC,YAAA;QACAP,mBAAA;QACAQ,cAAA;QACAN,qBAAA;QACAO,gBAAA;QACAX,iBAAA;QACAF,YAAA;QACAc,YAAA;QACAC,YAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,iBAAA;QACAlI,QAAA;QACAmI,QAAA;QACA;QACA5C,mBAAA,OAAA6C,YAAA;QACAC,iBAAA,OAAAzJ,UAAA,CAAA2B,MAAA,YAAA3B,UAAA,MAAAA,UAAA,CAAA2B,MAAA,MAAA8H,iBAAA;MACA;MACA,SAAAC,aAAA,QAAA1C,GAAA,CAAA2C,gBAAA;MACA,SAAAD,aAAA,SAAA1C,GAAA,CAAA2C,gBAAA;MACA,SAAAD,aAAA,SAAA1C,GAAA,CAAA2C,gBAAA;MACA,SAAAD,aAAA,SAAA1C,GAAA,CAAA2C,gBAAA;MACA,SAAAD,aAAA,SAAA1C,GAAA,CAAA2C,gBAAA;MACA,SAAAD,aAAA,SAAA1C,GAAA,CAAA2C,gBAAA;MACA,SAAAD,aAAA,SAAA1C,GAAA,CAAA2C,gBAAA;MACA,SAAAD,aAAA,SAAA1C,GAAA,CAAA2C,gBAAA;MACA,SAAAD,aAAA,SAAA1C,GAAA,CAAA2C,gBAAA;MACA,SAAAD,aAAA,UAAA1C,GAAA,CAAA2C,gBAAA;MACA,SAAAD,aAAA,UAAA1C,GAAA,CAAA2C,gBAAA;MACA,KAAA3J,UAAA,CAAAsE,IAAA,CAAA0C,GAAA;IACA;IACA4C,WAAA,WAAAA,YAAAzD,GAAA,EAAA0D,QAAA;MACA;MACA,KAAA1D,GAAA;;MAEA;MACA,IAAA2D,QAAA,GAAA3D,GAAA,CAAAlF,UAAA;MACA,IAAAP,MAAA,GAAAyF,GAAA,CAAAoD,QAAA;MACA,IAAAQ,YAAA,OAAAtJ,iBAAA,EAAA0F,GAAA,CAAAmD,iBAAA;QAAAzI,SAAA;MAAA,GAAAE,KAAA;MACA,IAAAK,QAAA,OAAAX,iBAAA,EAAA0F,GAAA,CAAA/E,QAAA,OAAAL,KAAA;MAEA;QACA;QACA,IAAAC,QAAA,OAAAP,iBAAA,EAAAqJ,QAAA;UAAAjJ,SAAA;QAAA,GACAK,QAAA,CAAAR,MAAA,EACAQ,QAAA,CAAA6I,YAAA,EACA7I,QAAA,KAAAT,iBAAA,KAAAU,GAAA,KAAAV,iBAAA,EAAAW,QAAA,EAAAN,MAAA,QACAC,KAAA;;QAEA;QACAoF,GAAA,CAAAnF,QAAA,OAAAP,iBAAA,EAAAO,QAAA;UAAAH,SAAA;QAAA,GAAAE,KAAA;QACAoF,GAAA,CAAA6D,oBAAA,GAAA7D,GAAA,CAAA/D,kBAAA,eAAA3B,iBAAA,EAAAO,QAAA;UAAAH,SAAA;QAAA,GAAAE,KAAA,GAAAoF,GAAA,CAAA6D,oBAAA;;QAEA;QACA,QAAAH,QAAA;UACA;YACA1D,GAAA,CAAA8C,YAAA;YACA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;YACA;YACA;QACA;;QAEA;QACA,KAAA7I,KAAA,gBAAAJ,UAAA;MAEA,SAAAqB,KAAA;QACAC,OAAA,CAAAD,KAAA,aAAAA,KAAA;QACA,KAAA4I,QAAA,CAAA5I,KAAA;MACA;IACA;IACA6I,UAAA,WAAAA,WAAA/D,GAAA;MACA,KAAA/F,KAAA,eAAA+F,GAAA;IACA;IACAgE,aAAA,WAAAA,cAAAhE,GAAA;MACA,KAAA/F,KAAA;IACA;IACAgK,iBAAA,WAAAA,kBAAAC,IAAA;MACA;QACAvD,EAAA,EAAAuD,IAAA,CAAAjD,SAAA;QACAvE,KAAA,GAAAwH,IAAA,CAAA/C,gBAAA,WAAA+C,IAAA,CAAA/C,gBAAA,gBAAA+C,IAAA,CAAAC,gBAAA,WAAAD,IAAA,CAAAC,gBAAA,eAAAC,iBAAA,CAAAC,YAAA,EAAAH,IAAA,CAAA/C,gBAAA,WAAA+C,IAAA,CAAA/C,gBAAA,gBAAA+C,IAAA,CAAAC,gBAAA,WAAAD,IAAA,CAAAC,gBAAA;MACA;IACA;EACA;AACA;AAAAG,OAAA,CAAA/F,OAAA,GAAAgG,QAAA"}]}