{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\SeaLclComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\SeaLclComponent.vue", "mtime": 1754646305906}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQXVkaXQgZnJvbSAiLi4vYXVkaXQudnVlIg0KaW1wb3J0IExvZ2lzdGljc1Byb2dyZXNzIGZyb20gIi4uL2xvZ2lzdGljc1Byb2dyZXNzLnZ1ZSINCmltcG9ydCBDaGFyZ2VMaXN0IGZyb20gIi4uL2NoYXJnZUxpc3QudnVlIg0KaW1wb3J0IFRyZWVzZWxlY3QgZnJvbSAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QiDQppbXBvcnQgIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0L2Rpc3QvdnVlLXRyZWVzZWxlY3QuY3NzIg0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJTZWFMY2xDb21wb25lbnQiLA0KICBjb21wb25lbnRzOiB7DQogICAgQXVkaXQsDQogICAgTG9naXN0aWNzUHJvZ3Jlc3MsDQogICAgQ2hhcmdlTGlzdCwNCiAgICBUcmVlc2VsZWN0DQogIH0sDQogIHByb3BzOiB7DQogICAgLy8g5ou85p+c5rW36L+Q5pWw5o2u5YiX6KGoDQogICAgc2VhTGNsTGlzdDogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgLy8g6KGo5Y2V5pWw5o2uDQogICAgZm9ybTogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gKHt9KQ0KICAgIH0sDQogICAgLy8g5pi+56S65o6n5Yi2DQogICAgYnJhbmNoSW5mbzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUNCiAgICB9LA0KICAgIGxvZ2lzdGljc0luZm86IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiB0cnVlDQogICAgfSwNCiAgICBjaGFyZ2VJbmZvOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0sDQogICAgYXVkaXRJbmZvOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIC8vIOeKtuaAgeaOp+WItg0KICAgIGRpc2FibGVkOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIGJvb2tpbmc6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgcHNhVmVyaWZ5OiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIC8vIOaVsOaNruWIl+ihqA0KICAgIHN1cHBsaWVyTGlzdDogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgY2Fycmllckxpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogKCkgPT4gW10NCiAgICB9LA0KICAgIGNvbXBhbnlMaXN0OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfSwNCiAgICAvLyDmlrnms5Xlh73mlbANCiAgICBjYXJyaWVyTm9ybWFsaXplcjogew0KICAgICAgdHlwZTogRnVuY3Rpb24sDQogICAgICBkZWZhdWx0OiAoKSA9PiB7DQogICAgICB9DQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBib29raW5nQmlsbENvbmZpZzogW3sNCiAgICAgICAgZmlsZTogIuiuouiIseWNlSIsDQogICAgICAgIHRlbXBsYXRlTGlzdDogWyJib29raW5nT3JkZXIxIl0NCiAgICAgIH1dDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8vIOWIpOaWreaYr+WQpuemgeeUqOeKtuaAgQ0KICAgIGlzRGlzYWJsZWQoKSB7DQogICAgICByZXR1cm4gdGhpcy5kaXNhYmxlZCB8fCB0aGlzLnBzYVZlcmlmeQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOWIpOaWreWtl+auteaYr+WQpuemgeeUqA0KICAgIGlzRmllbGREaXNhYmxlZChpdGVtKSB7DQogICAgICByZXR1cm4gdGhpcy5kaXNhYmxlZCB8fCB0aGlzLmdldFNlcnZpY2VJbnN0YW5jZURpc2FibGUoaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMpDQogICAgfSwNCiAgICAvLyDliKTmlq3ml6XmnJ/lrZfmrrXmmK/lkKbnpoHnlKjvvIjms6jmhI/mnInkupvlnLDmlrnkvb/nlKjkuoZnZXRGb3JtRGlzYWJsZe+8iQ0KICAgIGlzRGF0ZUZpZWxkRGlzYWJsZWQoaXRlbSkgew0KICAgICAgcmV0dXJuIHRoaXMuZGlzYWJsZWQgfHwgdGhpcy5nZXRGb3JtRGlzYWJsZShpdGVtLnJzU2VydmljZUluc3RhbmNlcykNCiAgICB9LA0KICAgIC8vIOiOt+WPluS+m+W6lOWVhumCrueusQ0KICAgIGdldFN1cHBsaWVyRW1haWwoc3VwcGxpZXJJZCkgew0KICAgICAgY29uc3Qgc3VwcGxpZXIgPSB0aGlzLnN1cHBsaWVyTGlzdC5maW5kKHYgPT4gdi5jb21wYW55SWQgPT09IHN1cHBsaWVySWQpDQogICAgICByZXR1cm4gc3VwcGxpZXIgPyBzdXBwbGllci5zdGFmZkVtYWlsIDogJycNCiAgICB9LA0KICAgIC8vIOiOt+WPluWQiOe6puaYvuekuuaWh+acrA0KICAgIGdldEFncmVlbWVudERpc3BsYXkoc2VydmljZUluc3RhbmNlKSB7DQogICAgICByZXR1cm4gc2VydmljZUluc3RhbmNlLmFncmVlbWVudFR5cGVDb2RlICsgc2VydmljZUluc3RhbmNlLmFncmVlbWVudE5vDQogICAgfSwNCiAgICAvLyDmoLzlvI/ljJbmib/ov5DkurrmoIfnrb4NCiAgICBmb3JtYXRDYXJyaWVyTGFiZWwobGFiZWwpIHsNCiAgICAgIGNvbnN0IGNvbW1hSW5kZXggPSBsYWJlbC5pbmRleE9mKCIsIikNCiAgICAgIHJldHVybiBjb21tYUluZGV4ICE9PSAtMSA/IGxhYmVsLnN1YnN0cmluZygwLCBjb21tYUluZGV4KSA6IGxhYmVsDQogICAgfSwNCiAgICAvLyDlpITnkIborqLoiLHljZXnlJ/miJANCiAgICBoYW5kbGVCb29raW5nQmlsbChpdGVtLCB0ZW1wbGF0ZSkgew0KICAgICAgdGhpcy4kZW1pdCgiZ2V0Qm9va2luZ0JpbGwiLCBpdGVtLCB0ZW1wbGF0ZSkNCiAgICB9LA0KICAgIC8vIOS6i+S7tui9rOWPkee7meeItue7hOS7tg0KICAgIGNoYW5nZVNlcnZpY2VGb2xkKHNlcnZpY2VJbnN0YW5jZSkgew0KICAgICAgdGhpcy4kZW1pdCgiY2hhbmdlU2VydmljZUZvbGQiLCBzZXJ2aWNlSW5zdGFuY2UpDQogICAgfSwNCiAgICBhZGRTZWFMQ0woKSB7DQogICAgICB0aGlzLiRlbWl0KCJhZGRTZWFMQ0wiKQ0KICAgIH0sDQogICAgZGVsZXRlUnNPcExjbFNlYShpdGVtKSB7DQogICAgICB0aGlzLiRlbWl0KCJkZWxldGVSc09wTGNsU2VhIiwgaXRlbSkNCiAgICB9LA0KICAgIG9wZW5DaGFyZ2VTZWxlY3QoaXRlbSkgew0KICAgICAgdGhpcy4kZW1pdCgib3BlbkNoYXJnZVNlbGVjdCIsIGl0ZW0pDQogICAgfSwNCiAgICBhdWRpdENoYXJnZShpdGVtLCBldmVudCkgew0KICAgICAgdGhpcy4kZW1pdCgiYXVkaXRDaGFyZ2UiLCBpdGVtLCBldmVudCkNCiAgICB9LA0KICAgIGdlbmVyYXRlRnJlaWdodCh0eXBlMSwgdHlwZTIsIGl0ZW0pIHsNCiAgICAgIHRoaXMuJGVtaXQoImdlbmVyYXRlRnJlaWdodCIsIHR5cGUxLCB0eXBlMiwgaXRlbSkNCiAgICB9LA0KICAgIHNlbGVjdFBzYUJvb2tpbmdPcGVuKGl0ZW0pIHsNCiAgICAgIHRoaXMuJGVtaXQoInNlbGVjdFBzYUJvb2tpbmdPcGVuIiwgaXRlbSkNCiAgICB9LA0KICAgIHNlbGVjdENhcnJpZXIoaXRlbSwgZXZlbnQpIHsNCiAgICAgIHRoaXMuJGVtaXQoInNlbGVjdENhcnJpZXIiLCBpdGVtLCBldmVudCkNCiAgICB9LA0KICAgIGFkZFByb2dyZXNzKGxvZ0xpc3QsIHR5cGUpIHsNCiAgICAgIHRoaXMuJGVtaXQoImFkZFByb2dyZXNzIiwgbG9nTGlzdCwgdHlwZSkNCiAgICB9LA0KICAgIGhhbmRsZVNldHRsZWRSYXRlKGl0ZW0pIHsNCiAgICAgIHRoaXMuJGVtaXQoImhhbmRsZVNldHRsZWRSYXRlIiwgaXRlbSkNCiAgICB9LA0KICAgIHBzYUJvb2tpbmdDYW5jZWwoaXRlbSkgew0KICAgICAgdGhpcy4kZW1pdCgicHNhQm9va2luZ0NhbmNlbCIsIGl0ZW0pDQogICAgfSwNCiAgICBjb3B5RnJlaWdodChldmVudCkgew0KICAgICAgdGhpcy4kZW1pdCgiY29weUZyZWlnaHQiLCBldmVudCkNCiAgICB9LA0KICAgIGNhbGN1bGF0ZUNoYXJnZShzZXJ2aWNlVHlwZSwgZXZlbnQsIGl0ZW0pIHsNCiAgICAgIHRoaXMuJGVtaXQoImNhbGN1bGF0ZUNoYXJnZSIsIHNlcnZpY2VUeXBlLCBldmVudCwgaXRlbSkNCiAgICB9LA0KICAgIGdldFBheWFibGUodHlwZSkgew0KICAgICAgcmV0dXJuIHRoaXMuJHBhcmVudC5nZXRQYXlhYmxlID8gdGhpcy4kcGFyZW50LmdldFBheWFibGUodHlwZSkgOiBudWxsDQogICAgfSwNCiAgICBnZXRCb29raW5nU3RhdHVzKHN0YXR1cykgew0KICAgICAgcmV0dXJuIHRoaXMuJHBhcmVudC5nZXRCb29raW5nU3RhdHVzID8gdGhpcy4kcGFyZW50LmdldEJvb2tpbmdTdGF0dXMoc3RhdHVzKSA6ICcnDQogICAgfSwNCiAgICBnZXRTZXJ2aWNlSW5zdGFuY2VEaXNhYmxlKHNlcnZpY2VJbnN0YW5jZSkgew0KICAgICAgcmV0dXJuIHRoaXMuJHBhcmVudC5nZXRTZXJ2aWNlSW5zdGFuY2VEaXNhYmxlID8gdGhpcy4kcGFyZW50LmdldFNlcnZpY2VJbnN0YW5jZURpc2FibGUoc2VydmljZUluc3RhbmNlKSA6IGZhbHNlDQogICAgfSwNCiAgICBnZXRTZXJ2aWNlT2JqZWN0KHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIHJldHVybiB0aGlzLiRwYXJlbnQuZ2V0U2VydmljZU9iamVjdCA/IHRoaXMuJHBhcmVudC5nZXRTZXJ2aWNlT2JqZWN0KHNlcnZpY2VUeXBlSWQpIDoge30NCiAgICB9LA0KICAgIGdldEZvcm1EaXNhYmxlKHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIHJldHVybiB0aGlzLiRwYXJlbnQuZ2V0Rm9ybURpc2FibGUgPyB0aGlzLiRwYXJlbnQuZ2V0Rm9ybURpc2FibGUoc2VydmljZVR5cGVJZCkgOiBmYWxzZQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["SeaLclComponent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAocA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SeaLclComponent.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div class=\"sea-lcl-component\">\r\n    <!--拼柜海运-->\r\n    <div v-for=\"(item, index) in seaLclList\" :key=\"`sea-lcl-${index}`\" class=\"sea-lcl-item\">\r\n      <!--标题栏-->\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <div class=\"service-bar\">\r\n            <a :class=\"[\r\n                'service-toggle-icon',\r\n                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeServiceFold(item.rsServiceInstances)\">\r\n                海运-LCL\r\n              </h3>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"addSeaLCL\"\r\n              >[+]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"deleteRsOpLclSea(item)\"\r\n              >[-]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"openChargeSelect(item)\"\r\n              >[CN...]\r\n              </el-button>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"item.rsServiceInstances\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(2)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @return=\"item = $event\"\r\n            />\r\n            <div class=\"booking-bill-container\">\r\n              <el-popover\r\n                v-for=\"(billConfig, billIndex) in bookingBillConfig\"\r\n                :key=\"`bill-${billIndex}`\"\r\n                placement=\"top\"\r\n                trigger=\"click\"\r\n                width=\"100\"\r\n              >\r\n                <el-button\r\n                  v-for=\"(template, templateIndex) in billConfig.templateList\"\r\n                  :key=\"`template-${templateIndex}`\"\r\n                  @click=\"handleBookingBill(item, template)\"\r\n                >\r\n                  {{ template }}\r\n                </el-button>\r\n                <a\r\n                  slot=\"reference\"\r\n                  class=\"booking-bill-link\"\r\n                  target=\"_blank\"\r\n                >\r\n                  [{{ billConfig.file }}]\r\n                </a>\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"item.rsServiceInstances.serviceFold\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNo\"\r\n                  :class=\"{ 'disable-form': form.sqdPsaNo }\"\r\n                  :disabled=\"!!form.sqdPsaNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(1, 2, item)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(item.rsServiceInstances.supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"item.rsServiceInstances.supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.rsServiceInstances)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"订舱状态\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input\r\n                      :value=\"getBookingStatus(item.bookingStatus)\"\r\n                      class=\"disable-form\"\r\n                      disabled\r\n                      placeholder=\"订舱状态\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-button\r\n                      :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                      class=\"cancel-btn\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"psaBookingCancel(item)\"\r\n                    >\r\n                      取消\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\" class=\"branch-info-col\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"商务单号\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input\r\n                          :class=\"{ 'disable-form': item.sqdPsaNo }\"\r\n                          :disabled=\"!!item.sqdPsaNo\"\r\n                          :value=\"item.sqdPsaNo\"\r\n                          @focus=\"selectPsaBookingOpen(item)\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button\r\n                          :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                          class=\"cancel-btn\"\r\n                          size=\"mini\"\r\n                          type=\"text\"\r\n                          @click=\"psaBookingCancel(item)\"\r\n                        >\r\n                          取消\r\n                        </el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"SO号码\">\r\n                    <el-input\r\n                      v-model=\"item.soNo\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"SO号码\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"提单号码\">\r\n                    <el-input\r\n                      v-model=\"item.blNo\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"提单号码\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"9\">\r\n                  <el-form-item label=\"柜号概览\">\r\n                    <el-input\r\n                      v-model=\"item.sqdContainersSealsSum\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"柜号概览\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"船公司\">\r\n                    <treeselect\r\n                      v-model=\"item.carrierId\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disable-branch-nodes=\"true\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      :disabled-fuzzy-matching=\"true\"\r\n                      :flat=\"false\"\r\n                      :flatten-search-results=\"true\"\r\n                      :multiple=\"false\"\r\n                      :normalizer=\"carrierNormalizer\"\r\n                      :options=\"carrierList\"\r\n                      :show-count=\"true\"\r\n                      placeholder=\"选择承运人\"\r\n                      @select=\"selectCarrier(item, $event)\"\r\n                    >\r\n                      <div slot=\"value-label\" slot-scope=\"{ node }\">\r\n                        {{ node.raw.carrierIntlCode || \" \" }}\r\n                      </div>\r\n                      <label\r\n                        slot=\"option-label\"\r\n                        slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                        :class=\"labelClassName\"\r\n                      >\r\n                        {{ formatCarrierLabel(node.label) }}\r\n                        <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                      </label>\r\n                    </treeselect>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程船名\">\r\n                    <el-input\r\n                      v-model=\"item.firstVessel\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"头程船名船次\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"二程船名\">\r\n                    <el-input\r\n                      v-model=\"item.secondVessel\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"二程船名船次\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"9\">\r\n                  <el-form-item label=\"船期\">\r\n                    <el-input\r\n                      v-model=\"item.inquiryScheduleSummary\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"航班时效\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程开船\">\r\n                    <el-input\r\n                      v-model=\"item.firstCyOpenTime\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"头程开船\"\r\n                      @change=\"addProgress(getServiceObject(item.serviceTypeId).rsOpLogList, 15)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程截重\">\r\n                    <el-input\r\n                      v-model=\"item.firstCyClosingTime\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      clearable\r\n                      placeholder=\"头程截重\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截关时间\">\r\n                    <el-input\r\n                      v-model=\"item.cvClosingTime\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"截关时间\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"ETD\">\r\n                    <el-date-picker\r\n                      v-model=\"item.etd\"\r\n                      :class=\"{ 'disable-form': isDateFieldDisabled(item) }\"\r\n                      :disabled=\"isDateFieldDisabled(item)\"\r\n                      clearable\r\n                      placeholder=\"ETD\"\r\n                      type=\"date\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"ETA\">\r\n                    <el-date-picker\r\n                      v-model=\"item.eta\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      clearable\r\n                      placeholder=\"ETA\"\r\n                      type=\"date\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截补料\">\r\n                    <el-input\r\n                      v-model=\"item.siClosingTime\"\r\n                      :class=\"{ 'disable-form': isDateFieldDisabled(item) }\"\r\n                      :disabled=\"isDateFieldDisabled(item)\"\r\n                      placeholder=\"截补料\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截VGM\">\r\n                    <el-input\r\n                      v-model=\"item.sqdVgmStatus\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"截VGM\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"AMS/ENS\">\r\n                    <el-input\r\n                      v-model=\"item.sqdAmsEnsPostStatus\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"AMS/ENS\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"结算价\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input\r\n                          v-model=\"item.settledRate\"\r\n                          :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"price1/price2/price3\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button type=\"text\" @click=\"handleSettledRate(item)\">确定</el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"19\">\r\n                  <el-form-item label=\"订舱备注\">\r\n                    <div class=\"booking-remark-group\">\r\n                      <el-input\r\n                        v-model=\"item.bookingChargeRemark\"\r\n                        :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                        :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                        :disabled=\"isFieldDisabled(item)\"\r\n                        placeholder=\"订舱费用备注\"\r\n                        type=\"textarea\"\r\n                      />\r\n                      <el-input\r\n                        v-model=\"item.bookingAgentRemark\"\r\n                        :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                        :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                        :disabled=\"isFieldDisabled(item)\"\r\n                        placeholder=\"订舱备注\"\r\n                        type=\"textarea\"\r\n                      />\r\n                    </div>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getServiceInstanceDisable(item.rsServiceInstances) || disabled || psaVerify\"\r\n                :logistics-progress-data=\"item.rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"1\"\r\n                @deleteItem=\"item.rsOpLogList = item.rsOpLogList.filter(log => log !== $event)\"\r\n                @return=\"item.rsOpLogList = $event\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"item.rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"getFormDisable(item.serviceTypeId) || disabled\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"item.payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"item.payableRMBTax\"\r\n              :pay-detail-u-s-d=\"item.payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"item.payableUSDTax\"\r\n              :service-type-id=\"1\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"item.rsChargeList = []\"\r\n              @deleteItem=\"item.rsChargeList = item.rsChargeList.filter(charge => charge !== $event)\"\r\n              @return=\"calculateCharge(2, $event, item)\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"../audit.vue\"\r\nimport LogisticsProgress from \"../logisticsProgress.vue\"\r\nimport ChargeList from \"../chargeList.vue\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\r\n\r\nexport default {\r\n  name: \"SeaLclComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    Treeselect\r\n  },\r\n  props: {\r\n    // 拼柜海运数据列表\r\n    seaLclList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    carrierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 方法函数\r\n    carrierNormalizer: {\r\n      type: Function,\r\n      default: () => {\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      bookingBillConfig: [{\r\n        file: \"订舱单\",\r\n        templateList: [\"bookingOrder1\"]\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    // 判断字段是否禁用\r\n    isFieldDisabled(item) {\r\n      return this.disabled || this.getServiceInstanceDisable(item.rsServiceInstances)\r\n    },\r\n    // 判断日期字段是否禁用（注意有些地方使用了getFormDisable）\r\n    isDateFieldDisabled(item) {\r\n      return this.disabled || this.getFormDisable(item.rsServiceInstances)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 格式化承运人标签\r\n    formatCarrierLabel(label) {\r\n      const commaIndex = label.indexOf(\",\")\r\n      return commaIndex !== -1 ? label.substring(0, commaIndex) : label\r\n    },\r\n    // 处理订舱单生成\r\n    handleBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceInstance) {\r\n      this.$emit(\"changeServiceFold\", serviceInstance)\r\n    },\r\n    addSeaLCL() {\r\n      this.$emit(\"addSeaLCL\")\r\n    },\r\n    deleteRsOpLclSea(item) {\r\n      this.$emit(\"deleteRsOpLclSea\", item)\r\n    },\r\n    openChargeSelect(item) {\r\n      this.$emit(\"openChargeSelect\", item)\r\n    },\r\n    auditCharge(item, event) {\r\n      this.$emit(\"auditCharge\", item, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    selectPsaBookingOpen(item) {\r\n      this.$emit(\"selectPsaBookingOpen\", item)\r\n    },\r\n    selectCarrier(item, event) {\r\n      this.$emit(\"selectCarrier\", item, event)\r\n    },\r\n    addProgress(logList, type) {\r\n      this.$emit(\"addProgress\", logList, type)\r\n    },\r\n    handleSettledRate(item) {\r\n      this.$emit(\"handleSettledRate\", item)\r\n    },\r\n    psaBookingCancel(item) {\r\n      this.$emit(\"psaBookingCancel\", item)\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceType, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceType, event, item)\r\n    },\r\n    getPayable(type) {\r\n      return this.$parent.getPayable ? this.$parent.getPayable(type) : null\r\n    },\r\n    getBookingStatus(status) {\r\n      return this.$parent.getBookingStatus ? this.$parent.getBookingStatus(status) : ''\r\n    },\r\n    getServiceInstanceDisable(serviceInstance) {\r\n      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false\r\n    },\r\n    getServiceObject(serviceTypeId) {\r\n      return this.$parent.getServiceObject ? this.$parent.getServiceObject(serviceTypeId) : {}\r\n    },\r\n    getFormDisable(serviceTypeId) {\r\n      return this.$parent.getFormDisable ? this.$parent.getFormDisable(serviceTypeId) : false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// SeaLcl组件特定样式\r\n.sea-lcl-component {\r\n  width: 100%;\r\n\r\n  .sea-lcl-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .service-action-btn {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .booking-bill-container {\r\n      margin-left: auto;\r\n\r\n      .booking-bill-link {\r\n        color: blue;\r\n        padding: 0;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .cancel-btn {\r\n        color: red;\r\n      }\r\n    }\r\n\r\n    .branch-info-col {\r\n      .booking-remark-group {\r\n        display: flex;\r\n        gap: 10px;\r\n\r\n        .el-textarea {\r\n          flex: 1;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}