package com.rich.system.service.processor;

import com.rich.system.service.enums.ServiceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 服务处理器工厂
 * 管理所有服务处理器的注册和获取
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ServiceProcessorFactory {
    
    @Autowired
    private List<ServiceProcessor> serviceProcessors;
    
    /**
     * 服务类型到处理器的映射
     */
    private final Map<ServiceTypeEnum, ServiceProcessor> processorMap = new ConcurrentHashMap<>();
    
    /**
     * 按优先级排序的处理器列表
     */
    private List<ServiceProcessor> sortedProcessors;
    
    /**
     * 初始化处理器映射
     */
    @PostConstruct
    public void initializeProcessors() {
        log.info("开始初始化服务处理器...");
        
        for (ServiceProcessor processor : serviceProcessors) {
            ServiceTypeEnum serviceType = processor.getSupportedServiceType();
            
            if (processorMap.containsKey(serviceType)) {
                log.warn("发现重复的服务处理器: {}, 已存在: {}, 新的: {}", 
                        serviceType, 
                        processorMap.get(serviceType).getClass().getSimpleName(),
                        processor.getClass().getSimpleName());
            }
            
            processorMap.put(serviceType, processor);
            log.debug("注册服务处理器: {} -> {}", serviceType, processor.getClass().getSimpleName());
        }
        
        // 按优先级排序处理器
        sortedProcessors = serviceProcessors.stream()
                .sorted(Comparator.comparingInt(ServiceProcessor::getPriority))
                .collect(Collectors.toList());
        
        log.info("服务处理器初始化完成，共注册 {} 个处理器", processorMap.size());
        
        // 打印处理器信息
        logProcessorInfo();
    }
    
    /**
     * 根据服务类型获取处理器
     * 
     * @param serviceType 服务类型
     * @return 对应的处理器，如果不存在则返回null
     */
    public ServiceProcessor getProcessor(ServiceTypeEnum serviceType) {
        return processorMap.get(serviceType);
    }
    
    /**
     * 根据服务类型ID获取处理器
     * 
     * @param serviceTypeId 服务类型ID
     * @return 对应的处理器，如果不存在则返回null
     */
    public ServiceProcessor getProcessor(Long serviceTypeId) {
        ServiceTypeEnum serviceType = ServiceTypeEnum.findByServiceTypeId(serviceTypeId);
        return serviceType != null ? getProcessor(serviceType) : null;
    }
    
    /**
     * 根据服务归属标识获取处理器
     * 
     * @param serviceBelongTo 服务归属标识
     * @return 对应的处理器，如果不存在则返回null
     */
    public ServiceProcessor getProcessor(String serviceBelongTo) {
        ServiceTypeEnum serviceType = ServiceTypeEnum.findByServiceBelongTo(serviceBelongTo);
        return serviceType != null ? getProcessor(serviceType) : null;
    }
    
    /**
     * 获取所有处理器
     * 
     * @return 所有处理器的集合
     */
    public Collection<ServiceProcessor> getAllProcessors() {
        return Collections.unmodifiableCollection(processorMap.values());
    }
    
    /**
     * 获取按优先级排序的处理器列表
     * 
     * @return 按优先级排序的处理器列表
     */
    public List<ServiceProcessor> getSortedProcessors() {
        return Collections.unmodifiableList(sortedProcessors);
    }
    
    /**
     * 获取关键服务处理器
     * 
     * @return 关键服务处理器列表
     */
    public List<ServiceProcessor> getCriticalProcessors() {
        return sortedProcessors.stream()
                .filter(ServiceProcessor::isCriticalService)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取支持异步处理的处理器
     * 
     * @return 支持异步处理的处理器列表
     */
    public List<ServiceProcessor> getAsyncProcessors() {
        return sortedProcessors.stream()
                .filter(ServiceProcessor::supportsAsyncProcessing)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取指定分类的处理器
     * 
     * @param category 服务分类
     * @return 指定分类的处理器列表
     */
    public List<ServiceProcessor> getProcessorsByCategory(ServiceTypeEnum.ServiceCategory category) {
        return sortedProcessors.stream()
                .filter(processor -> processor.getSupportedServiceType().getCategory() == category)
                .collect(Collectors.toList());
    }
    
    /**
     * 检查是否存在指定服务类型的处理器
     * 
     * @param serviceType 服务类型
     * @return 是否存在对应的处理器
     */
    public boolean hasProcessor(ServiceTypeEnum serviceType) {
        return processorMap.containsKey(serviceType);
    }
    
    /**
     * 获取处理器统计信息
     * 
     * @return 处理器统计信息
     */
    public Map<String, Object> getProcessorStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalProcessors", processorMap.size());
        stats.put("criticalProcessors", getCriticalProcessors().size());
        stats.put("asyncProcessors", getAsyncProcessors().size());
        
        // 按分类统计
        Map<ServiceTypeEnum.ServiceCategory, Long> categoryStats = sortedProcessors.stream()
                .collect(Collectors.groupingBy(
                        processor -> processor.getSupportedServiceType().getCategory(),
                        Collectors.counting()
                ));
        stats.put("categoryStats", categoryStats);
        
        // 按优先级统计
        Map<ServiceTypeEnum.ProcessingPriority, Long> priorityStats = sortedProcessors.stream()
                .collect(Collectors.groupingBy(
                        processor -> processor.getSupportedServiceType().getPriority(),
                        Collectors.counting()
                ));
        stats.put("priorityStats", priorityStats);
        
        return stats;
    }
    
    /**
     * 打印处理器信息
     */
    private void logProcessorInfo() {
        log.info("=== 服务处理器信息 ===");
        
        Map<ServiceTypeEnum.ServiceCategory, List<ServiceProcessor>> categoryMap = 
                sortedProcessors.stream()
                        .collect(Collectors.groupingBy(
                                processor -> processor.getSupportedServiceType().getCategory()
                        ));
        
        categoryMap.forEach((category, processors) -> {
            log.info("分类 {}: {} 个处理器", category, processors.size());
            processors.forEach(processor -> {
                ServiceTypeEnum serviceType = processor.getSupportedServiceType();
                log.info("  - {} (ID: {}, 优先级: {}, 异步: {})", 
                        serviceType.getDisplayName(),
                        serviceType.getServiceTypeId(),
                        serviceType.getPriority(),
                        processor.supportsAsyncProcessing());
            });
        });
        
        log.info("=== 处理器信息结束 ===");
    }
}
