package com.rich.system.service.monitor;

import com.rich.system.service.async.ServiceProcessingStartedEvent;
import com.rich.system.service.async.ServiceProcessingCompletedEvent;
import com.rich.system.service.async.ServiceProcessingFailedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 性能监控器
 * 监控服务处理的性能指标
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class PerformanceMonitor {
    
    /**
     * 服务性能统计
     */
    private final ConcurrentHashMap<String, ServicePerformanceStats> serviceStats = new ConcurrentHashMap<>();
    
    /**
     * 全局性能统计
     */
    private final GlobalPerformanceStats globalStats = new GlobalPerformanceStats();
    
    /**
     * 监听服务处理开始事件
     */
    @EventListener
    public void handleServiceProcessingStarted(ServiceProcessingStartedEvent event) {
        String serviceName = event.getServiceName();
        
        ServicePerformanceStats stats = serviceStats.computeIfAbsent(serviceName, 
                k -> new ServicePerformanceStats(serviceName));
        
        stats.recordStart();
        globalStats.recordStart();
        
        log.debug("服务处理开始监控: {} (操作单: {})", serviceName, event.getRctNo());
    }
    
    /**
     * 监听服务处理完成事件
     */
    @EventListener
    public void handleServiceProcessingCompleted(ServiceProcessingCompletedEvent event) {
        String serviceName = event.getServiceName();
        long duration = event.getDuration();
        
        ServicePerformanceStats stats = serviceStats.get(serviceName);
        if (stats != null) {
            stats.recordSuccess(duration);
        }
        
        globalStats.recordSuccess(duration);
        
        log.debug("服务处理完成监控: {} 耗时: {}ms (操作单: {})", 
                serviceName, duration, event.getRctNo());
    }
    
    /**
     * 监听服务处理失败事件
     */
    @EventListener
    public void handleServiceProcessingFailed(ServiceProcessingFailedEvent event) {
        String serviceName = event.getServiceName();
        long duration = event.getDuration();
        
        ServicePerformanceStats stats = serviceStats.get(serviceName);
        if (stats != null) {
            stats.recordFailure(duration);
        }
        
        globalStats.recordFailure(duration);
        
        log.warn("服务处理失败监控: {} 耗时: {}ms 错误: {} (操作单: {})", 
                serviceName, duration, event.getException().getMessage(), event.getRctNo());
    }
    
    /**
     * 获取服务性能统计
     */
    public ServicePerformanceStats getServiceStats(String serviceName) {
        return serviceStats.get(serviceName);
    }
    
    /**
     * 获取所有服务性能统计
     */
    public java.util.Map<String, ServicePerformanceStats> getAllServiceStats() {
        return new ConcurrentHashMap<>(serviceStats);
    }
    
    /**
     * 获取全局性能统计
     */
    public GlobalPerformanceStats getGlobalStats() {
        return globalStats;
    }
    
    /**
     * 重置统计信息
     */
    public void resetStats() {
        serviceStats.clear();
        globalStats.reset();
        log.info("性能监控统计信息已重置");
    }
    
    /**
     * 获取性能报告
     */
    public PerformanceReport generateReport() {
        return PerformanceReport.builder()
                .reportTime(LocalDateTime.now())
                .globalStats(globalStats)
                .serviceStats(new ConcurrentHashMap<>(serviceStats))
                .topSlowServices(getTopSlowServices(5))
                .topFailureServices(getTopFailureServices(5))
                .build();
    }
    
    /**
     * 获取最慢的服务
     */
    private java.util.List<ServicePerformanceStats> getTopSlowServices(int limit) {
        return serviceStats.values().stream()
                .sorted((s1, s2) -> Double.compare(s2.getAverageDuration(), s1.getAverageDuration()))
                .limit(limit)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取失败率最高的服务
     */
    private java.util.List<ServicePerformanceStats> getTopFailureServices(int limit) {
        return serviceStats.values().stream()
                .sorted((s1, s2) -> Double.compare(s2.getFailureRate(), s1.getFailureRate()))
                .limit(limit)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 服务性能统计
     */
    @lombok.Data
    public static class ServicePerformanceStats {
        private final String serviceName;
        private final AtomicLong totalRequests = new AtomicLong(0);
        private final AtomicLong successRequests = new AtomicLong(0);
        private final AtomicLong failureRequests = new AtomicLong(0);
        private final AtomicLong activeRequests = new AtomicLong(0);
        private final LongAdder totalDuration = new LongAdder();
        
        private volatile long maxDuration = 0;
        private volatile long minDuration = Long.MAX_VALUE;
        private volatile LocalDateTime lastRequestTime;
        
        public ServicePerformanceStats(String serviceName) {
            this.serviceName = serviceName;
        }
        
        public void recordStart() {
            totalRequests.incrementAndGet();
            activeRequests.incrementAndGet();
            lastRequestTime = LocalDateTime.now();
        }
        
        public void recordSuccess(long duration) {
            successRequests.incrementAndGet();
            activeRequests.decrementAndGet();
            recordDuration(duration);
        }
        
        public void recordFailure(long duration) {
            failureRequests.incrementAndGet();
            activeRequests.decrementAndGet();
            recordDuration(duration);
        }
        
        private void recordDuration(long duration) {
            totalDuration.add(duration);
            
            if (duration > maxDuration) {
                maxDuration = duration;
            }
            if (duration < minDuration) {
                minDuration = duration;
            }
        }
        
        public double getSuccessRate() {
            long total = totalRequests.get();
            return total > 0 ? (double) successRequests.get() / total * 100 : 0.0;
        }
        
        public double getFailureRate() {
            long total = totalRequests.get();
            return total > 0 ? (double) failureRequests.get() / total * 100 : 0.0;
        }
        
        public double getAverageDuration() {
            long total = totalRequests.get();
            return total > 0 ? (double) totalDuration.sum() / total : 0.0;
        }
        
        public long getThroughput() {
            // 简单实现：每分钟处理的请求数
            return successRequests.get(); // 实际应该基于时间窗口计算
        }
    }
    
    /**
     * 全局性能统计
     */
    @lombok.Data
    public static class GlobalPerformanceStats {
        private final AtomicLong totalRequests = new AtomicLong(0);
        private final AtomicLong successRequests = new AtomicLong(0);
        private final AtomicLong failureRequests = new AtomicLong(0);
        private final AtomicLong activeRequests = new AtomicLong(0);
        private final LongAdder totalDuration = new LongAdder();
        
        private volatile long maxDuration = 0;
        private volatile long minDuration = Long.MAX_VALUE;
        private volatile LocalDateTime startTime = LocalDateTime.now();
        
        public void recordStart() {
            totalRequests.incrementAndGet();
            activeRequests.incrementAndGet();
        }
        
        public void recordSuccess(long duration) {
            successRequests.incrementAndGet();
            activeRequests.decrementAndGet();
            recordDuration(duration);
        }
        
        public void recordFailure(long duration) {
            failureRequests.incrementAndGet();
            activeRequests.decrementAndGet();
            recordDuration(duration);
        }
        
        private void recordDuration(long duration) {
            totalDuration.add(duration);
            
            if (duration > maxDuration) {
                maxDuration = duration;
            }
            if (duration < minDuration) {
                minDuration = duration;
            }
        }
        
        public void reset() {
            totalRequests.set(0);
            successRequests.set(0);
            failureRequests.set(0);
            activeRequests.set(0);
            totalDuration.reset();
            maxDuration = 0;
            minDuration = Long.MAX_VALUE;
            startTime = LocalDateTime.now();
        }
        
        public double getSuccessRate() {
            long total = totalRequests.get();
            return total > 0 ? (double) successRequests.get() / total * 100 : 0.0;
        }
        
        public double getFailureRate() {
            long total = totalRequests.get();
            return total > 0 ? (double) failureRequests.get() / total * 100 : 0.0;
        }
        
        public double getAverageDuration() {
            long total = totalRequests.get();
            return total > 0 ? (double) totalDuration.sum() / total : 0.0;
        }
    }
    
    /**
     * 性能报告
     */
    @lombok.Data
    @lombok.Builder
    public static class PerformanceReport {
        private LocalDateTime reportTime;
        private GlobalPerformanceStats globalStats;
        private java.util.Map<String, ServicePerformanceStats> serviceStats;
        private java.util.List<ServicePerformanceStats> topSlowServices;
        private java.util.List<ServicePerformanceStats> topFailureServices;
    }
}
