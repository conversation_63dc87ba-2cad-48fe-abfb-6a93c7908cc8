package com.rich.system.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 优化服务配置测试
 * 验证配置是否正确加载
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class OptimizedServiceConfigTest {
    
    @Autowired
    @Qualifier("asyncServiceExecutor")
    private Executor asyncServiceExecutor;
    
    @Autowired
    private CacheManager cacheManager;
    
    @Test
    @DisplayName("测试异步服务执行器配置")
    void testAsyncServiceExecutorConfiguration() {
        // 验证异步服务执行器已正确配置
        assertNotNull(asyncServiceExecutor, "异步服务执行器应该已配置");
        
        // 验证执行器类型
        assertTrue(asyncServiceExecutor instanceof org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor,
                "执行器应该是ThreadPoolTaskExecutor类型");
    }
    
    @Test
    @DisplayName("测试缓存管理器配置")
    void testCacheManagerConfiguration() {
        // 验证缓存管理器已正确配置
        assertNotNull(cacheManager, "缓存管理器应该已配置");
        
        // 验证缓存名称
        assertTrue(cacheManager.getCacheNames().contains("serviceInstances"),
                "应该包含serviceInstances缓存");
    }
    
    @Test
    @DisplayName("测试异步执行功能")
    void testAsyncExecution() {
        // 简单测试异步执行是否工作
        assertDoesNotThrow(() -> {
            asyncServiceExecutor.execute(() -> {
                // 简单的异步任务
                System.out.println("异步任务执行成功");
            });
        }, "异步任务执行不应该抛出异常");
    }
}
