{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\WhsComponent.vue?vue&type=style&index=0&id=ceaddbe6&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\WhsComponent.vue", "mtime": 1754646305907}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCkBpbXBvcnQgJ0AvYXNzZXRzL3N0eWxlcy9vcC1kb2N1bWVudCc7DQoNCi8vIFdIU+e7hOS7tueJueWumuagt+W8jw0KLndocy1jb21wb25lbnQgew0KICB3aWR0aDogMTAwJTsNCg0KICAud2hzLWl0ZW0gew0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIH0NCg0KICAuc2VydmljZS1iYXIgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBtYXJnaW4tdG9wOiAxMHB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgd2lkdGg6IDEwMCU7DQoNCiAgICAuc2VydmljZS10b2dnbGUtaWNvbiB7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICBtYXJnaW4tcmlnaHQ6IDVweDsNCiAgICB9DQoNCiAgICAuc2VydmljZS10aXRsZSB7DQogICAgICBtYXJnaW46IDA7DQogICAgICB3aWR0aDogMjUwcHg7DQogICAgICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIH0NCg0KICAgIC5vdXRib3VuZC1wbGFuLWNvbnRhaW5lciB7DQogICAgICBtYXJnaW4tbGVmdDogYXV0bzsNCg0KICAgICAgLm91dGJvdW5kLXBsYW4tbGluayB7DQogICAgICAgIGNvbG9yOiBibHVlOw0KICAgICAgICBwYWRkaW5nOiAwOw0KICAgICAgICBtYXJnaW4tbGVmdDogNXB4Ow0KICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7DQogICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAuc2VydmljZS1jb250ZW50LWFyZWEgew0KICAgIG1hcmdpbi1ib3R0b206IDE1cHg7DQogICAgZGlzcGxheTogLXdlYmtpdC1ib3g7DQoNCiAgICAuc2VydmljZS1pbmZvLWNvbCB7DQogICAgICAuZWwtZm9ybS1pdGVtIHsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAvLyDkvJjljJbooajljZXovpPlhaXmoYbmoLflvI8NCiAgLmVsLWlucHV0IHsNCiAgICB3aWR0aDogMTAwJTsNCiAgfQ0KDQogIC8vIOS8mOWMluaXpeacn+mAieaLqeWZqOagt+W8jw0KICAuZWwtZGF0ZS1waWNrZXIgew0KICAgIHdpZHRoOiAxMDAlOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["WhsComponent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4VA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "WhsComponent.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div class=\"whs-component\">\r\n    <!--仓储-->\r\n    <div v-for=\"(item, index) in whsServices\" :key=\"`whs-${index}`\" class=\"whs-item\">\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <!--标题栏-->\r\n          <div class=\"service-bar\">\r\n            <a\r\n              :class=\"[\r\n                'service-toggle-icon',\r\n                getFold(item.serviceTypeId) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <h3 class=\"service-title\" @click=\"changeFold(item.serviceTypeId)\">\r\n              仓储-{{ item.serviceShortName }}\r\n            </h3>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"getServiceInstance(item.serviceTypeId)\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(item.serviceTypeId)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @auditFee=\"auditCharge(item.serviceTypeId, $event)\"\r\n              @return=\"changeServiceObject(item.serviceTypeId, $event)\"\r\n            />\r\n            <div class=\"outbound-plan-container\">\r\n              <a\r\n                class=\"outbound-plan-link\"\r\n                target=\"_blank\"\r\n                @click=\"outboundPlan\"\r\n              >\r\n                [出仓计划]\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"getFold(item.serviceTypeId)\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(item.serviceTypeId).inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(8, item.serviceTypeId, getServiceObject(item.serviceTypeId))\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail()\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <template #reference>\r\n                    <el-input\r\n                      :value=\"getServiceInstance().supplierName\"\r\n                      class=\"disable-form\"\r\n                      disabled\r\n                    />\r\n                  </template>\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.serviceTypeId)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(item.serviceTypeId).inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--主表信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\">\r\n              <el-form-item label=\"商务单号\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input :value=\"form.psaNo\" class=\"disable-form\" disabled/>\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      style=\"color: red\"\r\n                      type=\"text\"\r\n                      @click=\"psaBookingCancel\"\r\n                    >\r\n                      取消\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\">\r\n              <el-form-item label=\"入仓号\">\r\n                <el-input\r\n                  v-model=\"getServiceObject(item.serviceTypeId).warehousingNo\"\r\n                  :class=\"psaVerify || disabled ? 'disable-form' : ''\"\r\n                  :disabled=\"psaVerify || disabled\"\r\n                  placeholder=\"入仓号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--占位-->\r\n          <el-col v-if=\"branchInfo\" :span=\"9\"></el-col>\r\n\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getFormDisable() || disabled || psaVerify\"\r\n                :logistics-progress-data=\"getServiceObject().rsOpLogList || []\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"80\"\r\n                @deleteItem=\"deleteLogItem(item.serviceTypeId, $event)\"\r\n                @return=\"updateLogList(item.serviceTypeId, $event)\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"getServiceObject().rsChargeList || []\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"getFormDisable() || disabled\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"getServiceObject().payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"getServiceObject().payableRMBTax\"\r\n              :pay-detail-u-s-d=\"getServiceObject().payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"getServiceObject().payableUSDTax\"\r\n              :service-type-id=\"item.serviceTypeId\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"deleteAllCharge(item.serviceTypeId)\"\r\n              @deleteItem=\"deleteChargeItem(item.serviceTypeId, $event)\"\r\n              @return=\"calculateCharge(item.serviceTypeId, $event, getServiceObject())\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\n\r\nexport default {\r\n  name: \"WhsComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    // 码头与仓储服务数据集合\r\n    whsServices: {\r\n      type: [Array, Set],\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 新增的props，直接从父组件传入\r\n    foldState: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    serviceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    serviceObject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    formDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取供应商邮箱\r\n    getSupplierEmail() {\r\n      const serviceInstance = this.getServiceInstance()\r\n      if (!serviceInstance || !serviceInstance.supplierId) return ''\r\n\r\n      const supplier = this.supplierList.find(v => v.companyId === serviceInstance.supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay() {\r\n      const serviceInstance = this.getServiceInstance()\r\n      if (!serviceInstance) return ''\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 事件转发给父组件\r\n    changeFold(serviceTypeId) {\r\n      this.$emit(\"changeFold\", serviceTypeId)\r\n    },\r\n    getFold() {\r\n      return this.foldState\r\n    },\r\n    getServiceInstance() {\r\n      return this.serviceInstance || {}\r\n    },\r\n    getServiceObject() {\r\n      return this.serviceObject || {}\r\n    },\r\n    getPayable() {\r\n      return this.serviceObject && this.serviceObject.payable || null\r\n    },\r\n    getFormDisable() {\r\n      return this.formDisable\r\n    },\r\n    changeServiceObject(serviceTypeId, serviceObject) {\r\n      this.$emit(\"changeServiceObject\", serviceTypeId, serviceObject)\r\n    },\r\n    auditCharge(serviceTypeId, event) {\r\n      this.$emit(\"auditCharge\", serviceTypeId, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    psaBookingCancel() {\r\n      this.$emit(\"psaBookingCancel\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceTypeId, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceTypeId, event, item)\r\n    },\r\n    outboundPlan() {\r\n      this.$emit(\"outboundPlan\")\r\n    },\r\n    // 物流进度相关方法\r\n    deleteLogItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject()\r\n      if (serviceObject && serviceObject.rsOpLogList) {\r\n        serviceObject.rsOpLogList = serviceObject.rsOpLogList.filter(item => item !== event)\r\n      }\r\n    },\r\n    updateLogList(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject()\r\n      if (serviceObject) {\r\n        serviceObject.rsOpLogList = event\r\n      }\r\n    },\r\n    // 费用列表相关方法\r\n    deleteAllCharge(serviceTypeId) {\r\n      const serviceObject = this.getServiceObject()\r\n      if (serviceObject) {\r\n        serviceObject.rsChargeList = []\r\n      }\r\n    },\r\n    deleteChargeItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject()\r\n      if (serviceObject && serviceObject.rsChargeList) {\r\n        serviceObject.rsChargeList = serviceObject.rsChargeList.filter(item => item !== event)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// WHS组件特定样式\r\n.whs-component {\r\n  width: 100%;\r\n\r\n  .whs-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title {\r\n      margin: 0;\r\n      width: 250px;\r\n      text-align: left;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .outbound-plan-container {\r\n      margin-left: auto;\r\n\r\n      .outbound-plan-link {\r\n        color: blue;\r\n        padding: 0;\r\n        margin-left: 5px;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}