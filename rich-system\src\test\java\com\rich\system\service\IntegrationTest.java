package com.rich.system.service;

import com.rich.common.core.domain.entity.RsRct;
import com.rich.common.core.domain.entity.RsServiceInstances;
import com.rich.common.core.domain.entity.RsOpSeaFcl;
import com.rich.common.core.domain.entity.RsCharge;
import com.rich.common.core.domain.entity.RsClientMessage;
import com.rich.system.service.processor.ServiceProcessorFactory;
import com.rich.system.service.processor.ServiceProcessor;
import com.rich.system.service.enums.ServiceTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 集成测试
 * 验证整个优化系统的集成工作
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class IntegrationTest {
    
    @MockBean
    private OptimizedRsRctService optimizedRsRctService;
    
    @MockBean
    private ServiceProcessorFactory processorFactory;
    
    @Test
    @DisplayName("测试服务处理器工厂初始化")
    void testServiceProcessorFactoryInitialization() {
        // Given
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalProcessors", 5);
        stats.put("criticalProcessors", 1);
        stats.put("asyncProcessors", 2);
        
        when(processorFactory.getProcessorStatistics()).thenReturn(stats);
        
        // When
        Map<String, Object> result = processorFactory.getProcessorStatistics();
        
        // Then
        assertNotNull(result);
        assertEquals(5, result.get("totalProcessors"));
        assertEquals(1, result.get("criticalProcessors"));
        assertEquals(2, result.get("asyncProcessors"));
    }
    
    @Test
    @DisplayName("测试服务类型枚举功能")
    void testServiceTypeEnumFunctionality() {
        // 测试根据ID查找服务类型
        ServiceTypeEnum seaFcl = ServiceTypeEnum.findByServiceTypeId(1L);
        assertNotNull(seaFcl);
        assertEquals(ServiceTypeEnum.SEA_FCL, seaFcl);
        assertEquals("SeaFCL", seaFcl.getServiceBelongTo());
        assertEquals("海运整箱", seaFcl.getDisplayName());
        
        // 测试根据服务归属查找服务类型
        ServiceTypeEnum seaLcl = ServiceTypeEnum.findByServiceBelongTo("SeaLCL");
        assertNotNull(seaLcl);
        assertEquals(ServiceTypeEnum.SEA_LCL, seaLcl);
        assertEquals(2L, seaLcl.getServiceTypeId());
        
        // 测试优先级功能
        assertTrue(ServiceTypeEnum.CLIENT_MESSAGE.isCritical());
        assertTrue(ServiceTypeEnum.INSURANCE.canProcessAsync());
        assertTrue(ServiceTypeEnum.SEA_FCL.isListService());
        assertTrue(ServiceTypeEnum.EXPRESS.isSingleService());
        assertTrue(ServiceTypeEnum.THIRD_CERT.isExpandService());
    }
    
    @Test
    @DisplayName("测试完整的服务处理流程")
    void testCompleteServiceProcessingFlow() {
        // Given
        RsRct testRsRct = createCompleteTestRsRct();
        
        when(optimizedRsRctService.saveAllServicesOptimized(any(RsRct.class)))
                .thenReturn(testRsRct);
        
        // When
        RsRct result = optimizedRsRctService.saveAllServicesOptimized(testRsRct);
        
        // Then
        assertNotNull(result);
        assertEquals(testRsRct.getRctId(), result.getRctId());
        assertEquals(testRsRct.getRctNo(), result.getRctNo());
        
        // 验证包含各种服务类型
        assertNotNull(result.getRsClientMessage());
        assertNotNull(result.getRsOpSeaFclList());
        assertFalse(result.getRsOpSeaFclList().isEmpty());
        
        verify(optimizedRsRctService, times(1)).saveAllServicesOptimized(testRsRct);
    }
    
    @Test
    @DisplayName("测试错误处理机制")
    void testErrorHandlingMechanism() {
        // Given
        RsRct testRsRct = createCompleteTestRsRct();
        RuntimeException testException = new RuntimeException("模拟处理错误");
        
        when(optimizedRsRctService.saveAllServicesOptimized(any(RsRct.class)))
                .thenThrow(testException);
        
        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            optimizedRsRctService.saveAllServicesOptimized(testRsRct);
        });
        
        assertEquals("模拟处理错误", exception.getMessage());
        verify(optimizedRsRctService, times(1)).saveAllServicesOptimized(testRsRct);
    }
    
    @Test
    @DisplayName("测试服务处理器获取功能")
    void testServiceProcessorRetrieval() {
        // Given
        ServiceProcessor mockProcessor = mock(ServiceProcessor.class);
        when(mockProcessor.getSupportedServiceType()).thenReturn(ServiceTypeEnum.SEA_FCL);
        when(mockProcessor.getPriority()).thenReturn(2);
        when(mockProcessor.supportsAsyncProcessing()).thenReturn(false);
        
        when(processorFactory.getProcessor(ServiceTypeEnum.SEA_FCL)).thenReturn(mockProcessor);
        when(processorFactory.getProcessor(1L)).thenReturn(mockProcessor);
        when(processorFactory.getProcessor("SeaFCL")).thenReturn(mockProcessor);
        
        // When
        ServiceProcessor processor1 = processorFactory.getProcessor(ServiceTypeEnum.SEA_FCL);
        ServiceProcessor processor2 = processorFactory.getProcessor(1L);
        ServiceProcessor processor3 = processorFactory.getProcessor("SeaFCL");
        
        // Then
        assertNotNull(processor1);
        assertNotNull(processor2);
        assertNotNull(processor3);
        assertEquals(ServiceTypeEnum.SEA_FCL, processor1.getSupportedServiceType());
        assertEquals(2, processor1.getPriority());
        assertFalse(processor1.supportsAsyncProcessing());
    }
    
    @Test
    @DisplayName("测试数据验证功能")
    void testDataValidation() {
        // 测试空数据处理
        assertThrows(IllegalArgumentException.class, () -> {
            optimizedRsRctService.saveAllServicesOptimized(null);
        });
        
        // 测试无效操作单ID
        RsRct invalidRsRct = new RsRct();
        invalidRsRct.setRctId(null);
        invalidRsRct.setRctNo("INVALID-001");
        
        when(optimizedRsRctService.saveAllServicesOptimized(invalidRsRct))
                .thenThrow(new IllegalArgumentException("操作单ID不能为空"));
        
        assertThrows(IllegalArgumentException.class, () -> {
            optimizedRsRctService.saveAllServicesOptimized(invalidRsRct);
        });
    }
    
    /**
     * 创建完整的测试操作单数据
     */
    private RsRct createCompleteTestRsRct() {
        RsRct rsRct = new RsRct();
        rsRct.setRctId(12345L);
        rsRct.setRctNo("INTEGRATION-TEST-001");
        rsRct.setRctCreateTime(new Date());
        
        // 添加客户信息
        RsClientMessage clientMessage = new RsClientMessage();
        RsServiceInstances clientServiceInstance = new RsServiceInstances();
        clientServiceInstance.setServiceId(1000L);
        clientServiceInstance.setServiceBelongTo("client");
        clientServiceInstance.setServiceTypeId(0L);
        clientServiceInstance.setRctId(rsRct.getRctId());
        clientMessage.setRsServiceInstances(clientServiceInstance);
        
        List<RsCharge> clientCharges = new ArrayList<>();
        RsCharge clientCharge = new RsCharge();
        clientCharge.setChargeId(10001L);
        clientCharge.setServiceId(1000L);
        clientCharge.setDnAmount(500.0);
        clientCharge.setSubtotal(500.0);
        clientCharges.add(clientCharge);
        clientMessage.setRsChargeList(clientCharges);
        
        rsRct.setRsClientMessage(clientMessage);
        
        // 添加海运整箱服务
        List<RsOpSeaFcl> seaFclList = new ArrayList<>();
        RsOpSeaFcl seaFcl = new RsOpSeaFcl();
        seaFcl.setSeaId(2001L);
        seaFcl.setServiceId(2000L);
        seaFcl.setSqdRctNo(rsRct.getRctNo());
        seaFcl.setSqdServiceTypeId(1L);
        
        RsServiceInstances seaFclServiceInstance = new RsServiceInstances();
        seaFclServiceInstance.setServiceId(2000L);
        seaFclServiceInstance.setServiceBelongTo("SeaFCL");
        seaFclServiceInstance.setServiceTypeId(1L);
        seaFclServiceInstance.setRctId(rsRct.getRctId());
        seaFcl.setRsServiceInstances(seaFclServiceInstance);
        
        List<RsCharge> seaFclCharges = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            RsCharge charge = new RsCharge();
            charge.setChargeId(20000L + i);
            charge.setServiceId(2000L);
            charge.setDnAmount(1000.0 * i);
            charge.setSubtotal(1000.0 * i);
            seaFclCharges.add(charge);
        }
        seaFcl.setRsChargeList(seaFclCharges);
        
        seaFclList.add(seaFcl);
        rsRct.setRsOpSeaFclList(seaFclList);
        
        return rsRct;
    }
}
