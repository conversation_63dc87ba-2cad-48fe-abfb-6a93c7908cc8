package com.rich.system.service.async;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.ApplicationEvent;

/**
 * 服务处理完成事件
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ServiceProcessingCompletedEvent extends ApplicationEvent {
    
    /**
     * 操作单ID
     */
    private Long rctId;
    
    /**
     * 操作单号
     */
    private String rctNo;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 处理耗时（毫秒）
     */
    private long duration;
    
    /**
     * 完成时间
     */
    private long completedTime;
    
    public ServiceProcessingCompletedEvent(Long rctId, String rctNo, String serviceName, long duration) {
        super(serviceName);
        this.rctId = rctId;
        this.rctNo = rctNo;
        this.serviceName = serviceName;
        this.duration = duration;
        this.completedTime = System.currentTimeMillis();
    }
}
