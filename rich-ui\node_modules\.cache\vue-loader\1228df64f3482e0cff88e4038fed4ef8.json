{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteChargeList.vue?vue&type=style&index=0&id=1a95385c&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteChargeList.vue", "mtime": 1754645302063}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQppbnB1dDpmb2N1cyB7DQogIG91dGxpbmU6IG5vbmU7DQp9DQoNCi51bkhpZ2hsaWdodC10ZXh0IHsNCiAgY29sb3I6ICNiN2JiYzI7DQogIG1hcmdpbjogMDsNCn0NCg0KLy8g5ouW5ou95qC35byPDQo6OnYtZGVlcCAuc29ydGFibGUtZ2hvc3Qgew0KICBvcGFjaXR5OiAwLjU7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQp9DQoNCjo6di1kZWVwIC5zb3J0YWJsZS1kcmFnIHsNCiAgb3BhY2l0eTogMC44Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWNmNWZmOw0KICBib3JkZXI6IDFweCBkYXNoZWQgIzQwOWVmZjsNCn0NCg0KLy8g5ouW5ou95pe255qE6KGo5qC86KGM5qC35byPDQo6OnYtZGVlcCB0Ym9keSB0ciB7DQogIGN1cnNvcjogbW92ZTsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCn0NCg0KOjp2LWRlZXAgdGJvZHkgdHI6aG92ZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KfQ0KDQovLyDnpoHnlKjnirbmgIHkuIvkuI3mmL7npLrmi5bmi73lhYnmoIcNCjo6di1kZWVwIHRib2R5IHRyLmRpc2FibGVkIHsNCiAgY3Vyc29yOiBkZWZhdWx0Ow0KfQ0K"}, {"version": 3, "sources": ["debitNoteChargeList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs0BA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "debitNoteChargeList.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <el-col :style=\"{'display':openChargeList?'':'none'}\" style=\"margin: 0;padding: 0;\">\r\n    <div :class=\"{'inactive':openChargeList==false,'active':openChargeList}\">\r\n      <el-table\r\n        ref=\"chargeTable\"\r\n        :data=\"localChargeData\"\r\n        border\r\n        class=\"pd0\"\r\n        row-key=\"getItemKey\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        :row-class-name=\"setRowData\"\r\n      >\r\n        <el-table-column\r\n          align=\"center\"\r\n          type=\"selection\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"费用\" prop=\"quotationChargeId\" width=\"80px\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showQuotationCharge\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCharge = true\"\r\n            >\r\n              {{ scope.row.chargeName }}\r\n            </div>\r\n            <tree-select v-show=\"scope.row.showQuotationCharge\" :dbn=\"true\" :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\" :flat=\"false\"\r\n                         :multiple=\"false\" :pass=\"scope.row.dnChargeNameId\" :placeholder=\"'运费'\"\r\n                         :type=\"'charge'\"\r\n                         @return=\"scope.row.dnChargeNameId = $event\"\r\n                         @returnData=\"handleChargeSelect(scope.row,$event)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"货币\" prop=\"quotationCurrencyId\" width=\"70\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true\"\r\n            >\r\n              {{ scope.row.dnCurrencyCode }}\r\n            </div>\r\n            <tree-select v-show=\"scope.row.showQuotationCurrency\" :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                         :pass=\"scope.row.dnCurrencyCode\" :type=\"'currency'\"\r\n                         @return=\"changeCurrency(scope.row,$event)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"单价\" prop=\"quotationRate\" width=\"80px\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showUnitRate\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true\"\r\n            >\r\n              {{\r\n                scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                  separator: \",\",\r\n                  precision: 2,\r\n                  symbol: \"¥\"\r\n                }).format() : (scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                  separator: \",\",\r\n                  precision: 2,\r\n                  symbol: \"$\"\r\n                }).format() : scope.row.dnUnitRate)\r\n              }}\r\n            </div>\r\n            <el-input-number v-show=\"scope.row.showUnitRate\" v-model=\"scope.row.dnUnitRate\" :controls=\"false\"\r\n                             :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                             :min=\"0.0001\"\r\n                             :precision=\"4\" style=\"display:flex;width: 100%\"\r\n                             @blur=\"scope.row.showUnitRate=false\"\r\n                             @change=\"countProfit(scope.row,'unitRate')\"\r\n                             @input=\"countProfit(scope.row,'unitRate')\"\r\n                             @focusout.native=\"scope.row.showUnitRate=false\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"单位\" prop=\"quotationUnitId\" width=\"50\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showQuotationUnit\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationUnit = true\"\r\n            >\r\n              {{ scope.row.dnUnitCode }}\r\n            </div>\r\n            <tree-select v-show=\"scope.row.showQuotationUnit\"\r\n                         :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\" :pass=\"scope.row.dnUnitCode\"\r\n                         :type=\"'unit'\" @return=\"changeUnit(scope.row,$event)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"数量\" prop=\"quotationAmount\" width=\"48px\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showAmount\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true\"\r\n            >\r\n              {{ scope.row.dnAmount }}\r\n            </div>\r\n            <el-input-number v-if=\"scope.row.showAmount\" v-model=\"scope.row.dnAmount\" :controls=\"false\"\r\n                             :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                             :min=\"0.00\" placeholder=\"数量\"\r\n                             style=\"display:flex;width: 100%\" @blur=\"scope.row.showAmount=false\"\r\n                             @change=\"countProfit(scope.row,'amount')\"\r\n                             @input=\"countProfit(scope.row,'amount')\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"汇率\" prop=\"quotationExchangeRate\" width=\"60\">\r\n          <template slot-scope=\"scope\" style=\"display:flex;\">\r\n            <div v-if=\"!scope.row.showCurrencyRate\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true\"\r\n            >\r\n              {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}\r\n            </div>\r\n            <el-input-number v-show=\"scope.row.showCurrencyRate\" v-model=\"scope.row.basicCurrencyRate\"\r\n                             :controls=\"false\"\r\n                             :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                             :min=\"0.0001\" :precision=\"4\" :step=\"0.0001\"\r\n                             style=\"width: 100%\" @blur=\"scope.row.showCurrencyRate=false\"\r\n                             @change=\"countProfit(scope.row,'currencyRate')\"\r\n                             @input=\"countProfit(scope.row,'currencyRate')\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"税率\" prop=\"quotationTaxRate\" width=\"50px\">\r\n          <template slot-scope=\"scope\">\r\n            <div style=\"display: flex;justify-content: center\">\r\n              <div v-if=\"!scope.row.showDutyRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true\"\r\n              >\r\n                {{ scope.row.dutyRate }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showDutyRate\" v-model=\"scope.row.dutyRate\" :controls=\"false\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               :min=\"0\" style=\"width: 75%\"\r\n                               @blur=\"scope.row.showDutyRate=false\"\r\n                               @change=\"countProfit(scope.row,'dutyRate')\"\r\n                               @input=\"countProfit(scope.row,'dutyRate')\"\r\n              />\r\n              <div>%</div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"小计\" prop=\"subtotal\" width=\"75\">\r\n          <template slot-scope=\"scope\">\r\n            <div>\r\n              {{\r\n                currency(scope.row.subtotal, {\r\n                  separator: \",\",\r\n                  precision: 2,\r\n                  symbol: (scope.row.dnCurrencyCode === \"RMB\" ? \"¥\" : \"$\")\r\n                }).format()\r\n              }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"费用备注\">\r\n          <template slot-scope=\"scope\">\r\n            <input v-model=\"scope.row.chargeRemark\"\r\n                   :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                   style=\"border: none;width: 100%;height: 100%;\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"审核状态\">\r\n          <template slot-scope=\"scope\">\r\n            {{ auditStatus(scope.row.isAccountConfirmed) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"已收金额\">\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.sqdDnCurrencyPaid\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"未收余额\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.sqdDnCurrencyBalance }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"所属服务\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <div>\r\n              {{ scope.row.serviceName ? scope.row.serviceName : getServiceName(scope.row.sqdServiceTypeId) }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-button\r\n              :disabled=\"disabled || hasConfirmRow\"\r\n              size=\"mini\"\r\n              style=\"color: red\"\r\n              type=\"text\"\r\n              @click=\"deleteAllItem()\"\r\n            >全部删除\r\n            </el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"deleteItem(scope.row)\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    <el-button :disabled=\"disabled\" style=\"padding: 0\"\r\n               type=\"text\"\r\n               @click=\"addReceivablePayable\"\r\n    >[＋]\r\n    </el-button>\r\n  </el-col>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport Sortable from \"sortablejs\"\r\n\r\nexport default {\r\n  name: \"debitNoteChargeList\",\r\n  components: {CompanySelect, Treeselect},\r\n  props: [\"chargeData\", \"companyList\", \"openChargeList\", \"isReceivable\", \"disabled\",\r\n    \"hiddenSupplier\", \"rsClientMessageReceivableTaxUSD\",\r\n    \"rsClientMessageReceivableTaxRMB\", \"rsClientMessagePayableTaxUSD\", \"rsClientMessagePayableTaxRMB\",\r\n    \"rsClientMessageReceivableRMB\", \"rsClientMessageReceivableUSD\", \"rsClientMessagePayableRMB\",\r\n    \"rsClientMessagePayableUSD\", \"rsClientMessageProfit\", \"rsClientMessageProfitNoTax\", \"payDetailRMB\",\r\n    \"payDetailUSD\", \"payDetailRMBTax\", \"payDetailUSDTax\", \"rsClientMessageProfitUSD\", \"rsClientMessageProfitRMB\",\r\n    \"rsClientMessageProfitTaxRMB\", \"rsClientMessageProfitTaxUSD\", \"debitNote\", \"dragGroupName\"],\r\n  computed: {\r\n    localChargeData: {\r\n      get() {\r\n        return this.chargeData || []\r\n      },\r\n      set(value) {\r\n        this.$emit(\"return\", value)\r\n      }\r\n    },\r\n    hasConfirmRow() {\r\n      let result = false;\r\n      (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => {\r\n        if (item.isAccountConfirmed === \"1\") {\r\n          result = true\r\n        }\r\n      }) : null\r\n      return result\r\n    }\r\n  },\r\n  watch: {\r\n    chargeData: {\r\n      handler: function (newVal, oldVal) {\r\n        if (!oldVal) {\r\n          this.$emit(\"return\", newVal)\r\n          return\r\n        }\r\n\r\n        // 遍历费用列表，检查币种变化\r\n        newVal ? newVal.forEach((item, index) => {\r\n          const oldItem = oldVal[index]\r\n\r\n          // 检查币种变化并计算小计\r\n          if (item.currency && item.amount) {\r\n            // 如果从 RMB 换成 USD，使用 1/汇率 计算\r\n            if (oldItem && oldItem.currency === \"RMB\" && item.currency === \"USD\") {\r\n              if (item.exchangeRate && item.exchangeRate !== 0) {\r\n                try {\r\n                  // 计算 1/汇率，保留4位小数\r\n                  const inverseRate = currency(1, {precision: 4}).divide(item.exchangeRate).value\r\n\r\n                  // 计算小计: 单价 * 数量 * 汇率 * (1 + 税率/100)\r\n                  item.subtotal = currency(item.dnUnitRate || 0, {precision: 4})\r\n                    .multiply(item.amount)\r\n                    .multiply(inverseRate)\r\n                    .multiply(currency(1).add(currency(item.dutyRate || 0).divide(100)))\r\n                    .value\r\n                } catch (error) {\r\n                  console.error(\"计算小计出错:\", error)\r\n                  item.subtotal = 0\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }) : null\r\n\r\n        this.$emit(\"return\", newVal ? newVal : [])\r\n\r\n        // 数据变化后重新初始化拖拽，使用更安全的方式\r\n        this.reinitializeSortable()\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    },\r\n    disabled: {\r\n      handler: function (newVal) {\r\n        // 禁用状态改变时重新初始化拖拽\r\n        this.reinitializeSortable()\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initSortable()\r\n  },\r\n  beforeDestroy() {\r\n    // 清理定时器\r\n    if (this.reinitTimer) {\r\n      clearTimeout(this.reinitTimer)\r\n      this.reinitTimer = null\r\n    }\r\n    // 销毁拖拽实例\r\n    this.destroySortable()\r\n  },\r\n  data() {\r\n    return {\r\n      payTotalRMB: 0,\r\n      payTotalUSD: 0,\r\n      showClientName: null,\r\n      sortable: null,\r\n      reinitTimer: null,\r\n      dragStartColumn: -1, // 记录拖拽开始的列索引\r\n      services: [{\r\n        value: 1,\r\n        label: \"海运\"\r\n      }, {\r\n        value: 10,\r\n        label: \"空运\"\r\n      }, {\r\n        value: 20,\r\n        label: \"铁路\"\r\n      }, {\r\n        value: 40,\r\n        label: \"快递\"\r\n      }, {\r\n        value: 50,\r\n        label: \"拖车\"\r\n      }, {\r\n        value: 60,\r\n        label: \"报关\"\r\n      }, {\r\n        value: 70,\r\n        label: \"清关派送\"\r\n      }, {\r\n        value: 80,\r\n        label: \"码头仓储\"\r\n      }, {\r\n        value: 90,\r\n        label: \"检验证书\"\r\n      }, {\r\n        value: 100,\r\n        label: \"保险\"\r\n      }, {\r\n        value: 101,\r\n        label: \"扩展服务\"\r\n      }],\r\n      service: [{\r\n        value: 1,\r\n        label: \"基础服务\"\r\n      }, {\r\n        value: 4,\r\n        label: \"前程运输\"\r\n      }, {\r\n        value: 5,\r\n        label: \"出口报关\"\r\n      }, {\r\n        value: 6,\r\n        label: \"进口清关\"\r\n      }, {value: 2, label: \"海运\"}\r\n        , {value: 3, label: \"陆运\"}\r\n        , {value: 4, label: \"铁路\"}\r\n        , {value: 5, label: \"空运\"}\r\n        , {value: 6, label: \"快递\"}\r\n        , {value: 21, label: \"整柜海运\"}\r\n        , {value: 22, label: \"拼柜海运\"}\r\n        , {value: 23, label: \"散杂船\"}\r\n        , {value: 24, label: \"滚装船\"}\r\n        , {value: 41, label: \"整柜铁路\"}\r\n        , {value: 42, label: \"拼柜铁路\"}\r\n        , {value: 43, label: \"铁路车皮\"}\r\n        , {value: 51, label: \"空运普舱\"}\r\n        , {value: 52, label: \"空运包板\"}\r\n        , {value: 53, label: \"空运包机\"}\r\n        , {value: 54, label: \"空运行李\"}\r\n        , {value: 961, label: \"前程运输\"}\r\n        , {value: 964, label: \"进口清关\"}\r\n        , {value: 7, label: \"出口报关\"}\r\n      ],\r\n      chargeRemark: null\r\n    }\r\n  },\r\n  methods: {\r\n    destroySortable() {\r\n      if (this.sortable) {\r\n        try {\r\n          // 检查 sortable 实例是否还有效\r\n          if (this.sortable.el && this.sortable.el.parentNode) {\r\n            this.sortable.destroy()\r\n          }\r\n        } catch (error) {\r\n          console.warn('Error destroying sortable:', error)\r\n        } finally {\r\n          this.sortable = null\r\n        }\r\n      }\r\n    },\r\n    reinitializeSortable() {\r\n      // 使用防抖延迟重新初始化，避免频繁的创建销毁\r\n      if (this.reinitTimer) {\r\n        clearTimeout(this.reinitTimer)\r\n      }\r\n\r\n      this.reinitTimer = setTimeout(() => {\r\n        this.$nextTick(() => {\r\n          this.destroySortable()\r\n          // 确保 DOM 已更新后再初始化\r\n          this.$nextTick(() => {\r\n            this.initSortable()\r\n          })\r\n        })\r\n      }, 100)\r\n    },\r\n    initSortable() {\r\n      this.$nextTick(() => {\r\n        if (this.$refs.chargeTable && this.$refs.chargeTable.$el) {\r\n          const tbody = this.$refs.chargeTable.$el.querySelector('tbody')\r\n          if (tbody) {\r\n            try {\r\n              this.sortable = Sortable.create(tbody, {\r\n                group: {\r\n                  name: this.dragGroupName || 'debitNoteChargeGroup',\r\n                  pull: (to, from, dragEl, evt) => {\r\n                    // 根据拖拽列决定是否允许拖出（剪切或复制）\r\n                    if (this.dragStartColumn === 1) {\r\n                      return true; // 从第一列拖动时允许拖出（剪切）\r\n                    } else {\r\n                      return 'clone'; // 从其他列拖动时复制\r\n                    }\r\n                  },\r\n                  put: !this.disabled\r\n                },\r\n                animation: 150,\r\n                disabled: this.disabled,\r\n                ghostClass: 'sortable-ghost',\r\n                dragClass: 'sortable-drag',\r\n                filter: '.disabled',\r\n                onStart: (evt) => {\r\n                  // 拖拽开始\r\n                  const draggedItem = this.localChargeData[evt.oldIndex]\r\n\r\n                  // 获取拖拽开始的列索引\r\n                  this.dragStartColumn = -1 // 默认设置为-1\r\n                  try {\r\n                    // 获取拖拽事件的起始坐标\r\n                    const mouseX = evt.originalEvent.clientX\r\n                    const mouseY = evt.originalEvent.clientY\r\n\r\n                    // 尝试更准确地确定拖拽开始的列\r\n                    const cells = evt.item.querySelectorAll('td')\r\n                    if (cells && cells.length > 0) {\r\n                      // 计算每个单元格的位置，找到包含鼠标位置的单元格\r\n                      for (let i = 0; i < cells.length; i++) {\r\n                        const rect = cells[i].getBoundingClientRect()\r\n                        if (mouseX >= rect.left && mouseX <= rect.right &&\r\n                          mouseY >= rect.top && mouseY <= rect.bottom) {\r\n                          this.dragStartColumn = i\r\n                          break\r\n                        }\r\n                      }\r\n                    }\r\n\r\n                    // 备选方法：如果上面的方法没找到，使用表头定位\r\n                    if (this.dragStartColumn === -1) {\r\n                      const headerCells = this.$refs.chargeTable.$el.querySelectorAll('thead th')\r\n                      if (headerCells && headerCells.length > 0) {\r\n                        for (let i = 0; i < headerCells.length; i++) {\r\n                          const rect = headerCells[i].getBoundingClientRect()\r\n                          if (mouseX >= rect.left && mouseX <= rect.right) {\r\n                            this.dragStartColumn = i\r\n                            break\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n\r\n                    if (this.dragStartColumn === -1) {\r\n                      // 回退方案：如果通过坐标无法确定，则默认为非第一列\r\n                      this.dragStartColumn = 2 // 设置为非第一列，默认为复制模式\r\n                    }\r\n\r\n                  } catch (error) {\r\n                    console.error('确定拖拽开始列时出错:', error)\r\n                    this.dragStartColumn = 1 // 出错时默认为第一列\r\n                  }\r\n\r\n                  // 设置被拖拽元素的数据\r\n                  try {\r\n                    evt.item.setAttribute('data-drag-item', JSON.stringify(draggedItem))\r\n                    // 额外添加拖拽起始列信息\r\n                    evt.item.setAttribute('data-drag-column', this.dragStartColumn)\r\n                  } catch (error) {\r\n                    console.error('Failed to stringify drag item:', error)\r\n                    evt.item.setAttribute('data-drag-item', '{}')\r\n                    evt.item.setAttribute('data-drag-column', '-1')\r\n                  }\r\n\r\n                  this.$emit('dragStart', {\r\n                    item: draggedItem,\r\n                    index: evt.oldIndex,\r\n                    from: this,\r\n                    column: this.dragStartColumn\r\n                  })\r\n                },\r\n                onAdd: (evt) => {\r\n                  // 接收到新元素\r\n                  const item = evt.item\r\n                  let draggedItem = {}\r\n                  let dragStartColumn = -1\r\n\r\n                  try {\r\n                    const dragData = item.getAttribute('data-drag-item')\r\n                    if (dragData && dragData !== 'undefined') {\r\n                      draggedItem = JSON.parse(dragData)\r\n                    }\r\n\r\n                    // 获取拖拽起始列\r\n                    const columnData = item.getAttribute('data-drag-column')\r\n                    if (columnData && columnData !== 'undefined') {\r\n                      dragStartColumn = parseInt(columnData, 10)\r\n                    }\r\n                  } catch (error) {\r\n                    console.error('Failed to parse drag item data:', error)\r\n                  }\r\n\r\n                  // 处理新增元素到表格\r\n                  const newChargeData = [...this.localChargeData]\r\n\r\n                  // 无论是复制还是剪切，都需要添加项到目标位置\r\n                  // 但要给新项生成一个新的ID，表示这是一个全新的项\r\n                  newChargeData.splice(evt.newIndex, 0, {\r\n                    ...draggedItem,\r\n                    tempId: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\r\n                    chargeId: null // 清除原有ID，作为新增项\r\n                  })\r\n\r\n                  this.$emit('return', newChargeData)\r\n                  this.$emit('dragAdd', {\r\n                    item: draggedItem,\r\n                    newIndex: evt.newIndex,\r\n                    to: this,\r\n                    column: dragStartColumn\r\n                  })\r\n                },\r\n                onRemove: (evt) => {\r\n                  // 获取拖拽信息\r\n                  const item = evt.item\r\n                  let dragStartColumn = -1\r\n                  try {\r\n                    const columnData = item.getAttribute('data-drag-column')\r\n                    if (columnData && columnData !== 'undefined') {\r\n                      dragStartColumn = parseInt(columnData, 10)\r\n                    }\r\n                  } catch (error) {\r\n                    console.error('Failed to parse drag column data:', error)\r\n                  }\r\n\r\n                  const newChargeData = [...this.localChargeData]\r\n                  const removedItem = newChargeData[evt.oldIndex]\r\n\r\n                  // 只有在从第一列开始拖拽时才执行剪切操作\r\n                  // Sortable的clone选项已经控制了复制行为，这里我们只需处理剪切的情况\r\n                  if (dragStartColumn === 1) {\r\n                    newChargeData.splice(evt.oldIndex, 1)\r\n                    this.$emit('return', newChargeData)\r\n                  }\r\n\r\n                  this.$emit('dragRemove', {\r\n                    item: removedItem,\r\n                    oldIndex: evt.oldIndex,\r\n                    from: this,\r\n                    column: dragStartColumn,\r\n                    isCut: dragStartColumn === 1\r\n                  })\r\n                },\r\n                onUpdate: (evt) => {\r\n                  // 同一表格内排序\r\n                  const newChargeData = [...this.localChargeData]\r\n                  const item = newChargeData.splice(evt.oldIndex, 1)[0]\r\n                  newChargeData.splice(evt.newIndex, 0, item)\r\n\r\n                  this.$emit('return', newChargeData)\r\n                  this.$emit('dragUpdate', {\r\n                    item: item,\r\n                    oldIndex: evt.oldIndex,\r\n                    newIndex: evt.newIndex,\r\n                    column: this.dragStartColumn\r\n                  })\r\n                },\r\n                onEnd: (evt) => {\r\n                  // 拖拽结束\r\n                  this.$emit('dragEnd', {\r\n                    ...evt,\r\n                    dragColumn: this.dragStartColumn\r\n                  })\r\n                  // 重置拖拽列\r\n                  this.dragStartColumn = -1\r\n                }\r\n              })\r\n            } catch (error) {\r\n              console.error('Error creating sortable:', error)\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    setRowData({row, rowIndex}) {\r\n      // 为每行设置数据属性，用于拖拽传递数据\r\n      this.$nextTick(() => {\r\n        const tableRows = this.$refs.chargeTable.$el.querySelectorAll('tbody tr')\r\n        if (tableRows[rowIndex]) {\r\n          try {\r\n            tableRows[rowIndex].setAttribute('data-drag-item', JSON.stringify(row))\r\n          } catch (error) {\r\n            console.error('Failed to stringify row data:', error)\r\n            tableRows[rowIndex].setAttribute('data-drag-item', '{}')\r\n          }\r\n        }\r\n      })\r\n      return ''\r\n    },\r\n    auditStatus(status) {\r\n      return status == 1 ? \"已审核\" : \"未审核\"\r\n    },\r\n    selectCharge(target, row) {\r\n      row.dnChargeNameId = target.chargeId\r\n      row.chargeName = target.chargeLocalName\r\n    },\r\n    handleSelectionChange(val) {\r\n      // 处理选择变化\r\n      this.$emit(\"selectRow\", val)\r\n\r\n      this.payTotalUSD = 0\r\n      this.payTotalRMB = 0\r\n      val ? val.map(item => {\r\n        if (item.isRecievingOrPaying == 1) {\r\n          if (item.dnCurrencyCode === \"USD\") {\r\n            this.payTotalUSD = currency(this.payTotalUSD).add(item.subtotal)\r\n          } else {\r\n            this.payTotalRMB = currency(this.payTotalRMB).add(item.subtotal)\r\n          }\r\n\r\n        }\r\n      }) : null\r\n\r\n    },\r\n    currency,\r\n    // 获取项目的唯一键\r\n    getItemKey(item) {\r\n      return item.tempId || item.chargeId || item.id || `item_${Math.random().toString(36).substr(2, 9)}`\r\n    },\r\n    getServiceName(id) {\r\n      let serviceName = \"\"\r\n      this.services.map(obj => {\r\n        obj.value === id ? serviceName = obj.label : null\r\n      })\r\n      return serviceName\r\n    },\r\n    copyFreight(row) {\r\n      if (this.companyList.length > 0) {\r\n        row.payClearingCompanyId = this.companyList[0].companyId\r\n        row.payCompanyName = this.companyList[0].companyShortName\r\n      }\r\n      row.isAccountConfirmed = 0\r\n      // 报价列表跳转订舱时没有公司列表,复制到应收没有客户信息\r\n      let data = this._.cloneDeep(row)\r\n\r\n      this.$emit(\"copyFreight\", {...data, chargeId: null})\r\n    },\r\n    copyAllFreight() {\r\n      if (!this.companyList.length > 0) {\r\n        this.$modal.alertWarning(\"请先选择委托单位或关联单位\")\r\n        return\r\n      }\r\n\r\n      this.chargeData.map(charge => {\r\n        charge.payClearingCompanyId = this.companyList[0].companyId\r\n        charge.payCompanyName = this.companyList[0].companyShortName\r\n        charge.isRecievingOrPaying = 0\r\n        charge.isAccountConfirmed = 0\r\n        charge.chargeId = null\r\n        this.$emit(\"copyFreight\", this._.cloneDeep(charge))\r\n      })\r\n    },\r\n    changeUnitCost(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showCostUnit = false\r\n      })\r\n    },\r\n    changeUnit(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showQuotationUnit = false\r\n      })\r\n    },\r\n    handleChargeSelect(row, data) {\r\n      if (row.chargeLocalName === data.chargeName) {\r\n        row.chargeName = data.chargeLocalName\r\n        row.showQuotationCharge = false\r\n      }\r\n      if (row.currencyCode == null && data.currencyCode) {\r\n        row.dnCurrencyCode = data.currencyCode\r\n      }\r\n    },\r\n    changeCurrency(row, currencyCode) {\r\n      row.dnCurrencyCode = currencyCode\r\n      /* let exchangeRate\r\n      if (currencyCode === \"USD\") {\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && row.dnCurrencyCode == a.overseaCurrency\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      } */\r\n\r\n      this.$nextTick(() => {\r\n        // row.basicCurrencyRate = exchangeRate ? exchangeRate : 1\r\n        row.showQuotationCurrency = false\r\n      })\r\n    },\r\n    /** 序号 */\r\n    rowIndex({row, rowIndex}) {\r\n      row.id = rowIndex + 1\r\n    },\r\n    addReceivablePayable() {\r\n      let obj = {\r\n        showClient: true,\r\n        showSupplier: true,\r\n        showQuotationCharge: true,\r\n        showCostCharge: true,\r\n        showQuotationCurrency: true,\r\n        showCostCurrency: true,\r\n        showQuotationUnit: true,\r\n        showCostUnit: true,\r\n        showStrategy: true,\r\n        showUnitRate: true,\r\n        showAmount: true,\r\n        showCurrencyRate: true,\r\n        showDutyRate: true,\r\n        basicCurrencyRate: 1,\r\n        dutyRate: 0,\r\n        dnAmount: 1,\r\n        // 应收还是应付\r\n        isRecievingOrPaying: this.isReceivable ? 0 : 1,\r\n        clearingCompanyId: this.chargeData.length > 0 ? this.chargeData[this.chargeData.length - 1].clearingCompanyId : null\r\n      }\r\n      if (this.serviceTypeId === 1) obj.sqdServiceTypeId = 1\r\n      if (this.serviceTypeId === 10) obj.sqdServiceTypeId = 10\r\n      if (this.serviceTypeId === 20) obj.sqdServiceTypeId = 20\r\n      if (this.serviceTypeId === 40) obj.sqdServiceTypeId = 40\r\n      if (this.serviceTypeId === 50) obj.sqdServiceTypeId = 50\r\n      if (this.serviceTypeId === 60) obj.sqdServiceTypeId = 60\r\n      if (this.serviceTypeId === 70) obj.sqdServiceTypeId = 70\r\n      if (this.serviceTypeId === 80) obj.sqdServiceTypeId = 80\r\n      if (this.serviceTypeId === 90) obj.sqdServiceTypeId = 90\r\n      if (this.serviceTypeId === 100) obj.sqdServiceTypeId = 100\r\n      if (this.serviceTypeId === 101) obj.sqdServiceTypeId = 101\r\n      this.chargeData.push(obj)\r\n    },\r\n    countProfit(row, category) {\r\n      // 确保所有必要的值都存在且有效\r\n      if (!row) return\r\n\r\n      // 使用currency.js来处理数值,避免精度损失\r\n      const unitRate = row.dnUnitRate || 0\r\n      const amount = row.dnAmount || 0\r\n      const currencyRate = currency(row.basicCurrencyRate || 1, {precision: 4}).value\r\n      const dutyRate = currency(row.dutyRate || 0).value\r\n\r\n      try {\r\n        // 计算小计\r\n        const subtotal = currency(unitRate, {precision: 4})\r\n          .multiply(amount)\r\n          .multiply(currencyRate)\r\n          .multiply(currency(1).add(currency(dutyRate).divide(100)))\r\n          .value\r\n\r\n        // 更新行数据\r\n        row.subtotal = currency(subtotal, {precision: 2}).value\r\n        row.sqdDnCurrencyBalance = row.isAccountConfirmed === \"0\" ? currency(subtotal, {precision: 2}).value : row.sqdDnCurrencyBalance\r\n\r\n        // 根据不同的输入类型关闭对应的编辑状态\r\n        switch (category) {\r\n          case \"strategy\":\r\n            row.showStrategy = false\r\n            break\r\n          case \"unitRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"amount\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"currencyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"dutyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n        }\r\n\r\n        // 触发数据更新\r\n        this.$emit(\"return\", this.chargeData)\r\n\r\n      } catch (error) {\r\n        console.error(\"计算小计时出错:\", error)\r\n        this.$message.error(\"计算小计时出错,请检查输入值是否正确\")\r\n      }\r\n    },\r\n    deleteItem(row) {\r\n      this.$emit(\"deleteItem\", row)\r\n    },\r\n    deleteAllItem(row) {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n    companyNormalizer(node) {\r\n      return {\r\n        id: node.companyId,\r\n        label: (node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\") + \",\" + pinyin.getFullChars((node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\"))\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\ninput:focus {\r\n  outline: none;\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n\r\n// 拖拽样式\r\n::v-deep .sortable-ghost {\r\n  opacity: 0.5;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n::v-deep .sortable-drag {\r\n  opacity: 0.8;\r\n  background-color: #ecf5ff;\r\n  border: 1px dashed #409eff;\r\n}\r\n\r\n// 拖拽时的表格行样式\r\n::v-deep tbody tr {\r\n  cursor: move;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n::v-deep tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n// 禁用状态下不显示拖拽光标\r\n::v-deep tbody tr.disabled {\r\n  cursor: default;\r\n}\r\n</style>\r\n"]}]}