{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\chargeList.vue?vue&type=template&id=5e02ada0&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\chargeList.vue", "mtime": 1754646305889}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}