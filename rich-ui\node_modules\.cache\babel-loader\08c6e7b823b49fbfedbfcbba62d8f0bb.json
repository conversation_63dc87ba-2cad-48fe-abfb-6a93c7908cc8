{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\booking\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\booking\\index.vue", "mtime": 1754646305887}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "_vueTreeselect", "_store", "_rct", "_js<PERSON><PERSON>yin", "_currency", "_rich", "_moment2", "_index2", "_index3", "_rctFieldLabelMap", "_index4", "name", "components", "DataAggregatorBackGround", "DataAggregator", "CompanySelect", "Treeselect", "data", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "salesId", "verifyPsaId", "salesAssistantId", "opId", "belongList", "opList", "businessList", "rctList", "queryParams", "pageNum", "pageSize", "form", "statisticsOp", "rules", "openAggregator", "fieldLabelMap", "rctFieldLabelMap", "aggregatorRctList", "watch", "n", "mounted", "load", "$route", "query", "no", "newBookingNo", "getList", "then", "loadSales", "loadOp", "loadBusinesses", "loadStaffList", "computed", "moment", "props", "methods", "listAggregatorBooking", "params", "config", "JSON", "stringify", "handleOpenAggregator", "handleStatistics", "_this", "op", "response", "parseTime", "getBadge", "row", "type", "sqdShippingBookingStatus", "psaVerify", "hiddenDelete", "currency", "getReleaseType", "id", "handleVerify", "$tab", "openPage", "rId", "getName", "staff", "$store", "state", "allRsStaffList", "filter", "rsStaff", "staffId", "staffFamilyLocalName", "staffGivingLocalName", "staffShortName", "sqdDocDeliveryWay", "logisticsPaymentTerms", "v", "emergencyLevel", "difficultyLevel", "processStatus", "_this2", "salesList", "length", "redisList", "store", "dispatch", "_this3", "businessesList", "_this4", "_this5", "staffList", "_this6", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "permissionLevel", "user", "permissionLevelList", "C", "listVerifyList", "rows", "stop", "handleQuery", "searchValue", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "rctId", "handleAdd", "handleUpdate", "dbclick", "column", "event", "booking", "tableRowClassName", "_ref", "rowIndex", "handleDelete", "_this7", "rctIds", "$confirm", "customClass", "delRct", "$modal", "msgSuccess", "catch", "handleExport", "download", "_objectSpread2", "concat", "Date", "getTime", "staffNormalizer", "node", "children", "l", "role", "roleLocalName", "pinyin", "getFullChars", "dept", "deptLocalName", "staffCode", "staffGivingEnName", "roleId", "label", "isDisabled", "undefined", "deptId", "exports", "_default"], "sources": ["src/views/system/booking/index.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <el-row :gutter=\"20\" style=\"margin: 0;padding: 0;\">\r\n      <!--搜索条件-->\r\n      <el-col :span=\"showLeft\">\r\n        <el-form :model=\"queryParams\" class=\"query\" ref=\"queryForm\" size=\"mini\" :inline=\"true\" v-show=\"showSearch\">\r\n          <el-form-item label=\"单号\" prop=\"rctNo\">\r\n            <el-input v-model=\"queryParams.rctNo\" placeholder=\"操作单号\" @keydown.enter.native=\"handleQuery\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"操作\" prop=\"rctOpDate\">\r\n            <el-date-picker v-model=\"queryParams.rctOpDate\" clearable\r\n                            placeholder=\"操作日期\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"ATD\" prop=\"rctOpDate\">\r\n            <el-date-picker v-model=\"queryParams.ATDDate\" :default-time=\"['00:00:00', '23:59:59']\"\r\n                            clearable\r\n                            placeholder=\"ATD\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"ETD\" prop=\"rctOpDate\">\r\n            <el-date-picker v-model=\"queryParams.ETDDate\" clearable\r\n                            placeholder=\"ETD\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"紧急\" prop=\"urgencyDegree\">\r\n            <el-input v-model=\"queryParams.urgencyDegree\" placeholder=\"紧急程度\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"审核\" prop=\"pasVerifyTime\">\r\n            <el-date-picker v-model=\"queryParams.pasVerifyTime\" clearable\r\n                            placeholder=\"商务审核时间\"\r\n                            style=\"width:100%\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"标记\" prop=\"isOpAllotted\">\r\n            <el-select v-model=\"queryParams.isOpAllotted\" clearable placeholder=\"操作分配标记\" style=\"width: 100%\">\r\n              <el-option label=\"未分配\" value=\"0\">未分配</el-option>\r\n              <el-option label=\"已分配\" value=\"1\">已分配</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"客户\" prop=\"clientId\">\r\n            <company-select :multiple=\"false\" :no-parent=\"true\"\r\n                            :pass=\"queryParams.clientId\" :placeholder=\"'客户'\" :role-client=\"'1'\"\r\n                            :role-control=\"true\" :roleTypeId=\"1\" @return=\"queryParams.clientId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"放货\" prop=\"releaseTypeId\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\"\r\n                         :multiple=\"false\" :pass=\"queryParams.releaseTypeId\"\r\n                         :placeholder=\"'放货方式'\" :type=\"'releaseType'\"\r\n                         @return=\"queryParams.releaseTypeId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进度\" prop=\"processStatusId\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"queryParams.processStatusId\" :placeholder=\"'进度状态'\"\r\n                         :type=\"'processStatus'\" @return=\"queryParams.processStatusId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"物流\" prop=\"logisticsTypeId\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"queryParams.logisticsTypeId\" :placeholder=\"'物流类型'\"\r\n                         :type=\"'serviceType'\" @return=\"queryParams.logisticsTypeId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"启运\" prop=\"polIds\">\r\n            <location-select :multiple=\"true\" :no-parent=\"true\"\r\n                             :pass=\"queryParams.polIds\" :placeholder=\"'启运港'\" @return=\"queryParams.polIds=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"目的\" prop=\"destinationPortIds\">\r\n            <location-select :en=\"true\" :multiple=\"true\"\r\n                             :pass=\"queryParams.destinationPortIds\" :placeholder=\"'目的港'\"\r\n                             @return=\"queryParams.destinationPortIds=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"货量\" prop=\"revenueTons\">\r\n            <el-input v-model=\"queryParams.revenueTons\" placeholder=\"计费货量\" style=\"width: 100%\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"业务\" prop=\"salesId\">\r\n            <treeselect v-model=\"salesId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"belongList\" :show-count=\"true\" placeholder=\"业务员\"\r\n                        @open=\"loadSales\" @select=\"queryParams.salesId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.salesId = null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"助理\" prop=\"salesAssistantId\">\r\n            <treeselect v-model=\"salesAssistantId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"belongList\" :show-count=\"true\" placeholder=\"业务助理\"\r\n                        @open=\"loadSales\" @select=\"queryParams.salesAssistantId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.salesAssistantId=null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"标记\" prop=\"isPsaVerified\">\r\n            <el-select v-model=\"queryParams.isPsaVerified\" clearable placeholder=\"商务审核标记\" style=\"width: 100%\">\r\n              <el-option label=\"已审\" value=\"0\">已审</el-option>\r\n              <el-option label=\"未审\" value=\"1\">未审</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"商务\" prop=\"verifyPsaId\">\r\n            <treeselect v-model=\"verifyPsaId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :options=\"businessList\"\r\n                        :show-count=\"true\" placeholder=\"商务\"\r\n                        @open=\"loadBusinesses\" @select=\"queryParams.verifyPsaId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.verifyPsaId=null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"操作\" prop=\"opId\">\r\n            <treeselect v-model=\"opId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"opList.filter(v => {return v.role.roleLocalName=='操作员'})\"\r\n                        :show-count=\"true\" placeholder=\"操作员\"\r\n                        @open=\"loadOp\" @select=\"queryParams.opId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.opId = null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <!--顶部操作按钮-->\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <!--<el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:booking:add']\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>-->\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:booking:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <!--<el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:booking:remove']\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>-->\r\n\r\n          <el-col :span=\"1.5\">\r\n            <el-col :span=\"2\">\r\n              <el-popover\r\n                placement=\"right\"\r\n                trigger=\"click\"\r\n                width=\"400\"\r\n              >\r\n                <el-table :data=\"statisticsOp\">\r\n                  <el-table-column label=\"操作\" property=\"opName\" width=\"100\"></el-table-column>\r\n                  <el-table-column label=\"已派单\" property=\"number\" width=\"300\"></el-table-column>\r\n                </el-table>\r\n                <el-button v-if=\"type==='psa'\" slot=\"reference\" @click=\"handleStatistics\">派单统计</el-button>\r\n              </el-popover>\r\n            </el-col>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button @click=\"handleOpenAggregator\">数据汇总</el-button>\r\n            <el-dialog v-dialogDrag v-dialogDragWidth\r\n                       :visible.sync=\"openAggregator\" append-to-body width=\"80%\"\r\n            >\r\n              <data-aggregator-back-ground :aggregate-function=\"listAggregatorBooking\"\r\n                                           :config-type=\"'booking-agg'\" :data-source-type=\"'rct'\"\r\n                                           :field-label-map=\"fieldLabelMap\"/>\r\n            </el-dialog>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n        <!--表格-->\r\n        <el-table v-loading=\"loading\" :data=\"rctList\"\r\n                  stripe @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"dbclick\"\r\n        >\r\n          <el-table-column align=\"left\" label=\"序号\" type=\"index\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <el-badge\r\n                :value=\"getBadge(scope.row)\"\r\n                class=\"item\"\r\n              >\r\n                <div style=\"width: 15px\">{{ scope.$index + 1 }}</div>\r\n              </el-badge>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"操作单号\" prop=\"clientId\" show-overflow-tooltip width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"column-text highlight-text\" style=\"font-size: 18px;height: 23px\"\r\n              >{{ scope.row.rctNo }}\r\n              </div>\r\n              <div class=\"unHighlight-text\" style=\"width: 100px;\">{{\r\n                  parseTime(scope.row.rctCreateTime, \"{y}.{m}.{d}\") + \" \" + emergencyLevel(scope.row.emergencyLevel)\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"委托单位\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"column-text highlight-text\">\r\n                {{ scope.row.clientSummary ? scope.row.clientSummary.split(\"/\")[1] : null }}\r\n              </div>\r\n              <div class=\"unHighlight-text\" style=\"height: 23px\">\r\n                {{\r\n                  (scope.row.orderBelongsTo ? scope.row.orderBelongsTo : \"\") + \" \" + (scope.row.releaseType ? getReleaseType(scope.row.releaseType) : \"\") + \" \" + scope.row.paymentNode\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"物流类型\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" font-weight: 600;\">{{ scope.row.logisticsTypeEnName }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" color: #b7bbc2;height: 23px\">\r\n                  {{ scope.row.impExpType === \"1\" ? \"出口\" : \"\" }}\r\n                  {{ scope.row.impExpType === \"2\" ? \"进口\" : \"\" }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"启运港\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box \" style=\" font-size: 15px\">\r\n                  {{ scope.row.pol ? scope.row.pol.split(\"(\")[0] : scope.row.pol }}\r\n                </p>\r\n                <p class=\"unHighlight-text\">\r\n                  {{ scope.row.pol ? \"(\" + scope.row.pol.split(\"(\")[1] : \"\" }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"目的港\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"width: 95px;overflow: hidden\">\r\n                <p class=\"column-text bottom-box highlight-text\" style=\" \">\r\n                  {{ scope.row.destinationPort ? scope.row.destinationPort.split(\"(\")[0] : scope.row.destinationPort }}\r\n                </p>\r\n                <p class=\"unHighlight-text\">\r\n                  {{ scope.row.destinationPort ? \"(\" + scope.row.destinationPort.split(\"(\")[1] : \"\" }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"计费货量\" width=\"140\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box highlight-text\" style=\" \">{{ scope.row.revenueTon }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" \">{{ scope.row.goodsNameSummary }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"提单\" width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{\r\n                    (scope.row.blTypeCode ? scope.row.blTypeCode : \"\")\r\n                  }}\r\n                </p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" height: 23px\">\r\n                  {{ (scope.row.blFormCode ? scope.row.blFormCode : \"\") }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"订舱\" show-overflow-tooltip width=\"90\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text bottom-box\"\r\n                   style=\"text-overflow: ellipsis; white-space: nowrap;font-weight: 600;  font-size: 13px\"\r\n                >{{\r\n                    scope.row.carrierEnName\r\n                  }} <span class=\"column-text unHighlight-text\" style=\" font-size: 12px\">{{\r\n                    \"(\" + scope.row.agreementTypeCode + \")\"\r\n                    }}</span></p>\r\n                <p class=\"column-text top-box\" style=\"height: 23px \">{{ scope.row.supplierName }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"入仓与SO号\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{ scope.row.warehousingNo }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" \">{{ scope.row.soNo }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"提单与柜号\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{ scope.row.blNo }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" height: 23px\">{{ scope.row.sqdContainersSealsSum }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"订单状态\" show-overflow-tooltip width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{ processStatus(scope.row.processStatusId) }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"物流进度\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{\r\n                    (scope.row.podEta ? (\"ATD: \" + parseTime(scope.row.podEta, \"{m}-{d}\")) : (\"ETD: \" + parseTime(scope.row.etd, \"{m}-{d}\")))\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box\" style=\"height: 23px \">{{\r\n                    (scope.row.destinationPortEta ? (\"ATA: \" + parseTime(scope.row.destinationPortEta, \"{m}-{d}\")) : (\"ETA: \" + parseTime(scope.row.eta, \"{m}-{d}\")))\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"文件进度\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{\r\n                    (scope.row.docStatusA ? (scope.row.docStatusA.split(\":\")[0] + \": \" + moment(scope.row.docStatusA).format(\"MM.DD\")) : \"\")\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" \">\r\n                  {{\r\n                    (scope.row.docStatusB ? (scope.row.docStatusB.split(\":\")[0] + \": \" + moment(scope.row.docStatusB).format(\"MM.DD\")) : \"\")\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"收款\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{\r\n                    currency(scope.row.dnInRmb, {\r\n                      separator: \",\",\r\n                      symbol: \"¥\",\r\n                      precision: 2\r\n                    }).format()\r\n                  }}\r\n                </p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\"height: 23px \">{{\r\n                    currency(currency(scope.row.dnUsdBalance).value, {\r\n                      separator: \",\",\r\n                      symbol: \"$\",\r\n                      precision: 2\r\n                    }).format() + \" / \" + currency(currency(scope.row.dnRmbBalance).value, {\r\n                      separator: \",\",\r\n                      symbol: \"¥\",\r\n                      precision: 2\r\n                    }).format()\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"主服务付款\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box \">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{\r\n                    currency(scope.row.cnInRmb, {\r\n                      separator: \",\",\r\n                      symbol: \"¥\",\r\n                      precision: 2\r\n                    }).format()\r\n                  }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"业绩\" width=\"100\">\r\n            <template slot=\"header\" slot-scope=\"scope\">\r\n              <div style=\"margin-right: 5px\">\r\n                业绩\r\n              </div>\r\n            </template>\r\n            <template slot-scope=\"scope\">\r\n              <div :class=\"currency(scope.row.sqdProfitRmbSumVat).divide(scope.row.sqdDnRmbSumVat).value<0?'warning':''\"\r\n                   class=\"flex-box\"\r\n                   style=\"margin-right: 5px\"\r\n              >\r\n                <p class=\"column-text top-box\" style=\"height: 23px \">{{\r\n                    currency(scope.row.profitInRmb, {separator: \",\", symbol: \"¥\", precision: 2}).format()\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" height: 23px\">{{\r\n                    currency(scope.row.profitInRmb).divide(scope.row.cnInRmb).multiply(100).value + \"%\"\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"业务/助理\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\" style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box\" style=\"overflow: hidden\">{{\r\n                    (getName(scope.row.salesId) + (scope.row.salesId ? (\"/\" + getName(scope.row.salesAssistantId)) : getName(scope.row.salesAssistantId))) ? getName(scope.row.salesId) + (scope.row.salesId ? (\"/\" + getName(scope.row.salesAssistantId)) : getName(scope.row.salesAssistantId)) : null\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\"height: 23px \">\r\n                  {{ parseTime(scope.row.newBookingTime, \"{m}.{d}\") }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"商务审核\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\" style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box\" style=\" width: 55px;overflow: hidden \">{{\r\n                    getName(scope.row.verifyPsaId)\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" \">\r\n                  {{\r\n                    parseTime(scope.row.psaVerifyTime, \"{m}.{d}\") + \" \" + processStatus(scope.row.psaVerifyStatusId)\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"操作员\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{ getName(scope.row.opId) }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" \">\r\n                  {{\r\n                    parseTime(scope.row.rctCreateTime, \"{m}.{d}\") + \" \" + processStatus(scope.row.processStatusId)\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column v-if=\"type==='booking'\" align=\"left\" class-name=\"small-padding fixed-width\" label=\"操作\"\r\n                           width=\"50\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:booking:remove']\"\r\n                v-if=\"hiddenDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport booking from \"@/views/system/booking/index\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport store from \"@/store\"\r\nimport {\r\n  delRct,\r\n  listAggregatorBooking,\r\n  listAggregatorRct,\r\n  listRct,\r\n  listVerifyAggregatorList,\r\n  listVerifyList,\r\n  op\r\n} from \"@/api/system/rct\"\r\nimport pinyin from \"js-pinyin\"\r\nimport currency from \"currency.js\"\r\nimport {parseTime} from \"../../../utils/rich\"\r\nimport moment from \"moment/moment\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport DataAggregator from \"@/views/system/DataAggregator/index.vue\"\r\nimport {rctFieldLabelMap} from \"@/config/rctFieldLabelMap\"\r\nimport DataAggregatorBackGround from \"@/views/system/DataAggregatorBackGround/index.vue\"\r\n\r\nexport default {\r\n  name: \"bookingList\",\r\n  components: {DataAggregatorBackGround, DataAggregator, CompanySelect, Treeselect},\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 操作单列表表格数据\r\n      salesId: null,\r\n      verifyPsaId: null,\r\n      salesAssistantId: null,\r\n      opId: null,\r\n      belongList: [],\r\n      opList: [],\r\n      businessList: [],\r\n      rctList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      statisticsOp: [],\r\n      // 表单校验\r\n      rules: {},\r\n      openAggregator: false,\r\n      fieldLabelMap: rctFieldLabelMap,\r\n      aggregatorRctList: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    let load = false\r\n    if (this.$route.query.no) {\r\n      this.queryParams.newBookingNo = this.$route.query.no\r\n      this.getList().then(() => {\r\n        load = true\r\n      })\r\n    } else {\r\n      this.getList().then(() => {\r\n        load = true\r\n      })\r\n    }\r\n    if (load) {\r\n      this.loadSales()\r\n      this.loadOp()\r\n      this.loadBusinesses()\r\n    }\r\n    this.loadStaffList()\r\n  },\r\n  computed: {\r\n    moment() {\r\n      return moment\r\n    }\r\n  },\r\n  props: [\"type\"],\r\n  methods: {\r\n    listAggregatorBooking(params) {\r\n      params.config = JSON.stringify(params.config)\r\n      this.queryParams.params = params;\r\n      return listAggregatorBooking(this.queryParams)\r\n    },\r\n    handleOpenAggregator() {\r\n      this.openAggregator = true\r\n    },\r\n\r\n    handleStatistics() {\r\n      op().then(response => {\r\n        this.statisticsOp = response.data\r\n      })\r\n    },\r\n    parseTime,\r\n    getBadge(row) {\r\n      if (((this.type === \"booking\" && row.sqdShippingBookingStatus == \"0\") || (this.type === \"psa\" && row.sqdShippingBookingStatus == \"1\" && row.psaVerify == \"0\"))) {\r\n        return \"new\"\r\n      } else {\r\n        return \"\"\r\n      }\r\n    },\r\n    hiddenDelete(row) {\r\n      if (this.type === \"booking\" && row.sqdShippingBookingStatus == 0) {\r\n        return true\r\n      }\r\n      if (this.type === \"psa\") {\r\n        return false\r\n      }\r\n    },\r\n    currency,\r\n    getReleaseType(id) {\r\n      if (id == 1) return \"月结\"\r\n      if (id == 2) return \"押放\"\r\n      if (id == 3) return \"票结\"\r\n      if (id == 4) return \"签放\"\r\n      if (id == 5) return \"订金\"\r\n      if (id == 6) return \"预付\"\r\n      if (id == 7) return \"扣货\"\r\n      if (id == 9) return \"居间\"\r\n      return \"\"\r\n    },\r\n    handleVerify(row) {\r\n      this.$tab.openPage(\"操作单\", \"/opprocess/opdetail\", {rId: this.ids[0], psaVerify: true})\r\n    },\r\n    getName(id) {\r\n      if (id) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff) {\r\n          return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName\r\n        }\r\n      }\r\n    },\r\n    sqdDocDeliveryWay(type) {\r\n      if (type == 1) return \" 境外快递\"\r\n      if (type == 2) return \" 境内快递\"\r\n      if (type == 3) return \" 跑腿\"\r\n      if (type == 4) return \" 业务送达\"\r\n      if (type == 5) return \" 客户自取\"\r\n      if (type == 6) return \" QQ\"\r\n      if (type == 7) return \" 微信\"\r\n      if (type == 8) return \" 电邮\"\r\n      if (type == 9) return \" 公众号\"\r\n      if (type == 10) return \" 承运人系统\"\r\n      if (type == 11) return \" 订舱口系统\"\r\n      if (type == 12) return \" 第三方系统\"\r\n    },\r\n    logisticsPaymentTerms(v) {\r\n      if (v == 1) return \"月结\"\r\n      if (v == 2) return \"押单\"\r\n      if (v == 3) return \"此票结清\"\r\n      if (v == 4) return \"经理签单\"\r\n      if (v == 5) return \"预收订金\"\r\n      if (v == 6) return \"全额预付\"\r\n      if (v == 7) return \"扣货\"\r\n      if (v == 8) return \"背靠背\"\r\n    },\r\n    emergencyLevel(v) {\r\n      if (v == 0) return \"预定\"\r\n      if (v == 1) return \"当天\"\r\n      if (v == 2) return \"常规\"\r\n      if (v == 3) return \"紧急\"\r\n      if (v == 4) return \"立即\"\r\n    },\r\n    difficultyLevel(v) {\r\n      if (v == 0) return \"简易\"\r\n      if (v == 1) return \"标准\"\r\n      if (v == 2) return \"高级\"\r\n      if (v == 3) return \"特别\"\r\n    },\r\n    processStatus(v) {\r\n      if (v == 1) return \"等待\"\r\n      if (v == 2) return \"进行\"\r\n      if (v == 3) return \"变更\"\r\n      if (v == 4) return \"异常\"\r\n      if (v == 5) return \"质押\"\r\n      if (v == 6) return \"确认\"\r\n      if (v == 7) return \"完成\"\r\n      if (v == 8) return \"取消\"\r\n      if (v == 9) return \"驳回\"\r\n      if (v == 10) return \"回收\"\r\n    },\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch(\"getSalesList\").then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        store.dispatch(\"getBusinessesList\").then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    loadOp() {\r\n      if (this.$store.state.data.opList.length == 0 || this.$store.state.data.redisList.opList) {\r\n        store.dispatch(\"getOpList\").then(() => {\r\n          this.opList = this.$store.state.data.opList\r\n        })\r\n      } else {\r\n        this.opList = this.$store.state.data.opList\r\n      }\r\n    },\r\n    loadStaffList() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList\r\n      }\r\n    },\r\n    /** 查询操作单列表列表 */\r\n    async getList() {\r\n      this.loading = true\r\n      if (this.type === \"psa\") {\r\n        this.queryParams.sqdShippingBookingStatus = 1\r\n      }\r\n      /* if (this.type === 'booking')  {\r\n        this.queryParams.sqdShippingBookingStatus = 0\r\n      } */\r\n\r\n      this.queryParams.permissionLevel = this.$store.state.user.permissionLevelList.C\r\n      await listVerifyList(this.queryParams).then(response => {\r\n        this.rctList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.queryParams.searchValue = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.queryParams.searchValue = null\r\n      this.getList()\r\n    },\r\n    // handleStatusChange(row) {\r\n    //   let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n    //   this.$modal.confirm('确认要\"' + text + '吗？').then(function () {\r\n    //     return changeStatus(row.rctId, row.status);\r\n    //   }).then(() => {\r\n    //     this.$modal.msgSuccess(text + \"成功\");\r\n    //   }).catch(function () {\r\n    //     row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n    //   });\r\n    // },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.rctId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.$tab.openPage(\"操作单\", \"/opprocess/opdetail\", {})\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.$tab.openPage(\"操作单\", \"/opprocess/opdetail\", {rId: row.rctId})\r\n    },\r\n    dbclick(row, column, event) {\r\n      // 已经订舱了的不可以修改\r\n      /* if (this.type === 'booking' && row.sqdShippingBookingStatus == 1) {\r\n        return\r\n      } */\r\n      /* if (this.type === 'psa' && row.psaVerifyStatusId == 1) {\r\n        return\r\n      } */\r\n      if (this.type === \"booking\") {\r\n        this.$tab.openPage(\"订舱单明细\", \"/salesquotation/bookingDetail\", {rId: row.rctId, booking: true})\r\n      }\r\n      if (this.type === \"psa\") {\r\n        this.$tab.openPage(\"商务审核明细\", \"/psaVerify/psaDetail\", {rId: row.rctId, psaVerify: true})\r\n      }\r\n    },\r\n    tableRowClassName({row, rowIndex}) {\r\n      if (this.type === \"booking\" && row.sqdShippingBookingStatus == 0) {\r\n        return \"unconfirmed\"\r\n      }\r\n      if (this.type === \"psa\" && row.psaVerify != 1) {\r\n        return \"unconfirmed\"\r\n      }\r\n      return \"\"\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const rctIds = row.rctId || this.ids\r\n      this.$confirm(\"是否确认删除操作单列表编号为\\\"\" + rctIds + \"\\\"的数据项？\", \"提示\", {customClass: \"modal-confirm\"}).then(function () {\r\n        return delRct(rctIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/rct/bookingExport\", {\r\n        ...this.queryParams\r\n      }, `rct_${new Date().getTime()}.xlsx`)\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.column-text {\r\n  margin: 0;\r\n  padding: 0;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.highlight-text {\r\n  font-weight: 600;\r\n  font-size: 15px\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n\r\n::v-deep .el-table .success-row {\r\n  background: #f8f8f9;\r\n}\r\n\r\n.red {\r\n  color: rgb(103, 194, 58);\r\n}\r\n\r\n.item {\r\n  margin-top: 10px;\r\n  margin-right: 40px;\r\n}\r\n\r\n::v-deep .el-badge__content.is-fixed {\r\n  font-size: 12px;\r\n  top: 0px;\r\n  right: 2px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AAiiBA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,IAAA,GAAAH,OAAA;AASA,IAAAI,SAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,KAAA,GAAAN,OAAA;AACA,IAAAO,QAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,OAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,OAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,iBAAA,GAAAV,OAAA;AACA,IAAAW,OAAA,GAAAZ,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAY,IAAA;EACAC,UAAA;IAAAC,wBAAA,EAAAA,eAAA;IAAAC,cAAA,EAAAA,eAAA;IAAAC,aAAA,EAAAA,eAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,OAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,IAAA;MACAC,UAAA;MACAC,MAAA;MACAC,YAAA;MACAC,OAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA;MACAC,IAAA;MACAC,YAAA;MACA;MACAC,KAAA;MACAC,cAAA;MACAC,aAAA,EAAAC,kCAAA;MACAC,iBAAA;IACA;EACA;EACAC,KAAA;IACApB,UAAA,WAAAA,WAAAqB,CAAA;MACA,IAAAA,CAAA;QACA,KAAA1B,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACA4B,OAAA,WAAAA,QAAA;IACA,IAAAC,IAAA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,EAAA;MACA,KAAAhB,WAAA,CAAAiB,YAAA,QAAAH,MAAA,CAAAC,KAAA,CAAAC,EAAA;MACA,KAAAE,OAAA,GAAAC,IAAA;QACAN,IAAA;MACA;IACA;MACA,KAAAK,OAAA,GAAAC,IAAA;QACAN,IAAA;MACA;IACA;IACA,IAAAA,IAAA;MACA,KAAAO,SAAA;MACA,KAAAC,MAAA;MACA,KAAAC,cAAA;IACA;IACA,KAAAC,aAAA;EACA;EACAC,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,OAAAA,gBAAA;IACA;EACA;EACAC,KAAA;EACAC,OAAA;IACAC,qBAAA,WAAAA,sBAAAC,MAAA;MACAA,MAAA,CAAAC,MAAA,GAAAC,IAAA,CAAAC,SAAA,CAAAH,MAAA,CAAAC,MAAA;MACA,KAAA9B,WAAA,CAAA6B,MAAA,GAAAA,MAAA;MACA,WAAAD,0BAAA,OAAA5B,WAAA;IACA;IACAiC,oBAAA,WAAAA,qBAAA;MACA,KAAA3B,cAAA;IACA;IAEA4B,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,OAAA,IAAAjB,IAAA,WAAAkB,QAAA;QACAF,KAAA,CAAA/B,YAAA,GAAAiC,QAAA,CAAAtD,IAAA;MACA;IACA;IACAuD,SAAA,EAAAA,eAAA;IACAC,QAAA,WAAAA,SAAAC,GAAA;MACA,SAAAC,IAAA,kBAAAD,GAAA,CAAAE,wBAAA,gBAAAD,IAAA,cAAAD,GAAA,CAAAE,wBAAA,WAAAF,GAAA,CAAAG,SAAA;QACA;MACA;QACA;MACA;IACA;IACAC,YAAA,WAAAA,aAAAJ,GAAA;MACA,SAAAC,IAAA,kBAAAD,GAAA,CAAAE,wBAAA;QACA;MACA;MACA,SAAAD,IAAA;QACA;MACA;IACA;IACAI,QAAA,EAAAA,iBAAA;IACAC,cAAA,WAAAA,eAAAC,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAAR,GAAA;MACA,KAAAS,IAAA,CAAAC,QAAA;QAAAC,GAAA,OAAAhE,GAAA;QAAAwD,SAAA;MAAA;IACA;IACAS,OAAA,WAAAA,QAAAL,EAAA;MACA,IAAAA,EAAA;QACA,IAAAM,KAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAyE,cAAA,CAAAC,MAAA,WAAAC,OAAA;UAAA,OAAAA,OAAA,CAAAC,OAAA,IAAAZ,EAAA;QAAA;QACA,IAAAM,KAAA;UACA,OAAAA,KAAA,CAAAO,oBAAA,GAAAP,KAAA,CAAAQ,oBAAA,GAAAR,KAAA,CAAAS,cAAA;QACA;MACA;IACA;IACAC,iBAAA,WAAAA,kBAAAtB,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;IACA;IACAuB,qBAAA,WAAAA,sBAAAC,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;IACA;IACAC,cAAA,WAAAA,eAAAD,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;IACA;IACAE,eAAA,WAAAA,gBAAAF,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;IACA;IACAG,aAAA,WAAAA,cAAAH,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;IACA;IACA7C,SAAA,WAAAA,UAAA;MAAA,IAAAiD,MAAA;MACA,SAAAf,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAuF,SAAA,CAAAC,MAAA,cAAAjB,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAyF,SAAA,CAAAF,SAAA;QACAG,cAAA,CAAAC,QAAA,iBAAAvD,IAAA;UACAkD,MAAA,CAAAzE,UAAA,GAAAyE,MAAA,CAAAf,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAuF,SAAA;QACA;MACA;QACA,KAAA1E,UAAA,QAAA0D,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAuF,SAAA;MACA;IACA;IACAhD,cAAA,WAAAA,eAAA;MAAA,IAAAqD,MAAA;MACA,SAAArB,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAA6F,cAAA,CAAAL,MAAA,cAAAjB,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAyF,SAAA,CAAAI,cAAA;QACAH,cAAA,CAAAC,QAAA,sBAAAvD,IAAA;UACAwD,MAAA,CAAA7E,YAAA,GAAA6E,MAAA,CAAArB,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAA6F,cAAA;QACA;MACA;QACA,KAAA9E,YAAA,QAAAwD,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAA6F,cAAA;MACA;IACA;IACAvD,MAAA,WAAAA,OAAA;MAAA,IAAAwD,MAAA;MACA,SAAAvB,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAc,MAAA,CAAA0E,MAAA,cAAAjB,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAyF,SAAA,CAAA3E,MAAA;QACA4E,cAAA,CAAAC,QAAA,cAAAvD,IAAA;UACA0D,MAAA,CAAAhF,MAAA,GAAAgF,MAAA,CAAAvB,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAc,MAAA;QACA;MACA;QACA,KAAAA,MAAA,QAAAyD,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAc,MAAA;MACA;IACA;IACA0B,aAAA,WAAAA,cAAA;MAAA,IAAAuD,MAAA;MACA,SAAAxB,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAyE,cAAA,CAAAe,MAAA,cAAAjB,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAyF,SAAA,CAAAhB,cAAA;QACAiB,cAAA,CAAAC,QAAA,sBAAAvD,IAAA;UACA2D,MAAA,CAAAC,SAAA,GAAAD,MAAA,CAAAxB,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAyE,cAAA;QACA;MACA;QACA,KAAAuB,SAAA,QAAAzB,MAAA,CAAAC,KAAA,CAAAxE,IAAA,CAAAyE,cAAA;MACA;IACA;IACA,gBACAtC,OAAA,WAAAA,QAAA;MAAA,IAAA8D,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,MAAA,CAAA9F,OAAA;cACA,IAAA8F,MAAA,CAAAvC,IAAA;gBACAuC,MAAA,CAAAhF,WAAA,CAAA0C,wBAAA;cACA;cACA;AACA;AACA;;cAEAsC,MAAA,CAAAhF,WAAA,CAAA2F,eAAA,GAAAX,MAAA,CAAA1B,MAAA,CAAAC,KAAA,CAAAqC,IAAA,CAAAC,mBAAA,CAAAC,CAAA;cAAAN,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAK,mBAAA,EAAAf,MAAA,CAAAhF,WAAA,EAAAmB,IAAA,WAAAkB,QAAA;gBACA2C,MAAA,CAAAjF,OAAA,GAAAsC,QAAA,CAAA2D,IAAA;gBACAhB,MAAA,CAAAzF,KAAA,GAAA8C,QAAA,CAAA9C,KAAA;gBACAyF,MAAA,CAAA9F,OAAA;cACA;YAAA;YAAA;cAAA,OAAAsG,QAAA,CAAAS,IAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IACA;IACA,aACAa,WAAA,WAAAA,YAAA;MACA,KAAAlG,WAAA,CAAAC,OAAA;MACA,KAAAD,WAAA,CAAAmG,WAAA;MACA,KAAAjF,OAAA;IACA;IACA,aACAkF,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAArG,WAAA,CAAAmG,WAAA;MACA,KAAAjF,OAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAoF,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApH,GAAA,GAAAoH,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,KAAA;MAAA;MACA,KAAAtH,MAAA,GAAAmH,SAAA,CAAAhC,MAAA;MACA,KAAAlF,QAAA,IAAAkH,SAAA,CAAAhC,MAAA;IACA;IACA,aACAoC,SAAA,WAAAA,UAAA;MACA,KAAA1D,IAAA,CAAAC,QAAA;IACA;IACA,aACA0D,YAAA,WAAAA,aAAApE,GAAA;MACA,KAAAS,IAAA,CAAAC,QAAA;QAAAC,GAAA,EAAAX,GAAA,CAAAkE;MAAA;IACA;IACAG,OAAA,WAAAA,QAAArE,GAAA,EAAAsE,MAAA,EAAAC,KAAA;MACA;MACA;AACA;AACA;MACA;AACA;AACA;MACA,SAAAtE,IAAA;QACA,KAAAQ,IAAA,CAAAC,QAAA;UAAAC,GAAA,EAAAX,GAAA,CAAAkE,KAAA;UAAAM,OAAA;QAAA;MACA;MACA,SAAAvE,IAAA;QACA,KAAAQ,IAAA,CAAAC,QAAA;UAAAC,GAAA,EAAAX,GAAA,CAAAkE,KAAA;UAAA/D,SAAA;QAAA;MACA;IACA;IACAsE,iBAAA,WAAAA,kBAAAC,IAAA;MAAA,IAAA1E,GAAA,GAAA0E,IAAA,CAAA1E,GAAA;QAAA2E,QAAA,GAAAD,IAAA,CAAAC,QAAA;MACA,SAAA1E,IAAA,kBAAAD,GAAA,CAAAE,wBAAA;QACA;MACA;MACA,SAAAD,IAAA,cAAAD,GAAA,CAAAG,SAAA;QACA;MACA;MACA;IACA;IACA,aACAyE,YAAA,WAAAA,aAAA5E,GAAA;MAAA,IAAA6E,MAAA;MACA,IAAAC,MAAA,GAAA9E,GAAA,CAAAkE,KAAA,SAAAvH,GAAA;MACA,KAAAoI,QAAA,sBAAAD,MAAA;QAAAE,WAAA;MAAA,GAAArG,IAAA;QACA,WAAAsG,WAAA,EAAAH,MAAA;MACA,GAAAnG,IAAA;QACAkG,MAAA,CAAAnG,OAAA;QACAmG,MAAA,CAAAK,MAAA,CAAAC,UAAA;MACA,GAAAC,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,iCAAAC,cAAA,CAAA7C,OAAA,MACA,KAAAlF,WAAA,UAAAgI,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAA9D,MAAA;QACA,OAAA6D,IAAA,CAAAC,QAAA;MACA;MACA,IAAAC,CAAA;MACA,IAAAF,IAAA,CAAA/E,KAAA;QACA,IAAA+E,IAAA,CAAA/E,KAAA,CAAAO,oBAAA,YAAAwE,IAAA,CAAA/E,KAAA,CAAAQ,oBAAA;UACA,IAAAuE,IAAA,CAAAG,IAAA,CAAAC,aAAA;YACAF,CAAA,GAAAF,IAAA,CAAAG,IAAA,CAAAC,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAAN,IAAA,CAAAG,IAAA,CAAAC,aAAA;UACA;YACAF,CAAA,GAAAF,IAAA,CAAAO,IAAA,CAAAC,aAAA,SAAAH,iBAAA,CAAAC,YAAA,CAAAN,IAAA,CAAAO,IAAA,CAAAC,aAAA;UACA;QACA;UACAN,CAAA,GAAAF,IAAA,CAAA/E,KAAA,CAAAwF,SAAA,SAAAT,IAAA,CAAA/E,KAAA,CAAAO,oBAAA,GAAAwE,IAAA,CAAA/E,KAAA,CAAAQ,oBAAA,SAAAuE,IAAA,CAAA/E,KAAA,CAAAyF,iBAAA,SAAAL,iBAAA,CAAAC,YAAA,CAAAN,IAAA,CAAA/E,KAAA,CAAAO,oBAAA,GAAAwE,IAAA,CAAA/E,KAAA,CAAAQ,oBAAA;QACA;MACA;MACA,IAAAuE,IAAA,CAAAW,MAAA;QACA;UACAhG,EAAA,EAAAqF,IAAA,CAAAW,MAAA;UACAC,KAAA,EAAAV,CAAA;UACAD,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAY,UAAA,EAAAb,IAAA,CAAAzE,OAAA,YAAAyE,IAAA,CAAAC,QAAA,IAAAa;QACA;MACA;QACA;UACAnG,EAAA,EAAAqF,IAAA,CAAAe,MAAA;UACAH,KAAA,EAAAV,CAAA;UACAD,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAY,UAAA,EAAAb,IAAA,CAAAzE,OAAA,YAAAyE,IAAA,CAAAC,QAAA,IAAAa;QACA;MACA;IACA;EACA;AACA;AAAAE,OAAA,CAAAlE,OAAA,GAAAmE,QAAA"}]}