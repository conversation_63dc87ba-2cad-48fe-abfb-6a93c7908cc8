{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\login.vue?vue&type=style&index=0&id=7589b93f&lang=scss&rel=stylesheet%2Fscss&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\login.vue", "mtime": 1754646305884}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\" :show-message=\"false\"\r\n             v-loading=\"mac\" status-icon\r\n    >\r\n      <div class=\"title\">\r\n        <h3 style=\"margin: auto;width: fit-content\" @dblclick=\"toggleDebugMode\">瑞旗系统</h3>\r\n        <!-- 添加调试区域 -->\r\n        <!--<div v-if=\"showDebugOptions\" class=\"debug-options\">-->\r\n        <!--  <el-checkbox v-model=\"enableWechatVerify\" border size=\"mini\">启用微信验证</el-checkbox>-->\r\n        <!--</div>-->\r\n      </div>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"loginForm.username\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n          type=\"text\"\r\n        >\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"loginForm.password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          type=\"password\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item v-if=\"captchaEnabled\" prop=\"code\">\r\n        <el-input\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n        </el-input>\r\n        <div class=\"login-code\">\r\n          <img :src=\"codeUrl\" class=\"login-code-img\" @click=\"getCode\"/>\r\n        </div>\r\n      </el-form-item>\r\n      <el-checkbox v-model=\"loginForm.rememberMe\" style=\"margin:0px 0px 25px 0px;\">记住密码</el-checkbox>\r\n      <el-form-item style=\"width:100%;\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          style=\"width:100%;\"\r\n          type=\"primary\"\r\n          @click=\"handleLogin\"\r\n        >\r\n          <span v-if=\"!loading\">登 录</span>\r\n          <span v-else>登 录 中...</span>\r\n        </el-button>\r\n        <div v-if=\"register\" style=\"float: right;\">\r\n          <router-link :to=\"'/register'\" class=\"link-type\">立即注册</router-link>\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-login-footer\">\r\n      <span @click=\"incrementDebugCounter\">Copyright © 2009-2024 RichShipping All Rights Reserved.</span>\r\n    </div>\r\n\r\n    <!-- 微信扫码登录弹窗 -->\r\n    <wechat-scan\r\n      v-if=\"showWechatScan\"\r\n      :visible.sync=\"showWechatScan\"\r\n      :username=\"loginForm.username\"\r\n      @scan-success=\"handleWechatScanSuccess\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getCodeImg, getMac, checkNeedWechatScan} from '@/api/login'\r\nimport Cookies from 'js-cookie'\r\nimport {decrypt, encrypt} from '@/utils/jsencrypt'\r\nimport Fingerprint2 from 'fingerprintjs2'\r\nimport Fingerprint from '@fingerprintjs/fingerprintjs'\r\nimport WechatScan from '@/components/WechatScan'\r\n\r\nexport default {\r\n  name: 'Login',\r\n  components: {\r\n    WechatScan\r\n  },\r\n  data() {\r\n    return {\r\n      mac: false,\r\n      codeUrl: '',\r\n      loginForm: {\r\n        username: '',\r\n        password: '',\r\n        rememberMe: false,\r\n        code: '',\r\n        uuid: '',\r\n        unid: ''\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          {required: true, trigger: 'blur', message: '您的账号'}\r\n        ],\r\n        password: [\r\n          {required: true, trigger: 'blur', message: '您的密码'}\r\n        ],\r\n        code: [{required: true, trigger: 'change', message: '验证码'}]\r\n      },\r\n      loading: false,\r\n      // 验证码开关\r\n      captchaEnabled: true,\r\n      // 注册开关\r\n      register: false,\r\n      redirect: undefined,\r\n      // 微信扫码登录\r\n      showWechatScan: false,\r\n      // 调试选项\r\n      showDebugOptions: false,\r\n      debugClickCount: 0,\r\n      // 微信验证开关 - 根据环境设置，生产环境开启，开发环境关闭\r\n      enableWechatVerify: process.env.NODE_ENV === 'production',\r\n      // 微信扫码验证Cookie名\r\n      wechatScanCookieName: 'wechat_scan_verified'\r\n    }\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function (route) {\r\n        this.redirect = route.query && route.query.redirect\r\n      },\r\n      immediate: true\r\n    },\r\n    // 监听微信验证开关变化，保存到本地存储\r\n    enableWechatVerify: {\r\n      handler(val) {\r\n        localStorage.setItem('debug_enable_wechat_verify', val.toString());\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getCode()\r\n    this.getCookie()\r\n    // 根据环境设置微信验证开关，且允许本地存储覆盖(仅在调试模式下)\r\n    const storedSetting = localStorage.getItem('debug_enable_wechat_verify');\r\n    if (this.showDebugOptions && storedSetting !== null) {\r\n      this.enableWechatVerify = storedSetting === 'true';\r\n    } else {\r\n      // 默认根据环境设置：生产环境开启，开发环境关闭\r\n      this.enableWechatVerify = process.env.NODE_ENV === 'production';\r\n      // 更新本地存储\r\n      localStorage.setItem('debug_enable_wechat_verify', this.enableWechatVerify.toString());\r\n    }\r\n  },\r\n  methods: {\r\n    // 切换调试模式\r\n    toggleDebugMode() {\r\n      // 双击标题时显示调试选项\r\n      this.showDebugOptions = !this.showDebugOptions;\r\n      if (this.showDebugOptions) {\r\n        this.$message.success('已' + (this.enableWechatVerify ? '启用' : '关闭') + '微信验证');\r\n      }\r\n    },\r\n    // 点击版权信息增加计数器\r\n    incrementDebugCounter() {\r\n      this.debugClickCount++;\r\n      if (this.debugClickCount >= 5) {\r\n        this.showDebugOptions = true;\r\n        this.debugClickCount = 0;\r\n        this.$message.success('已' + (this.enableWechatVerify ? '启用' : '关闭') + '微信验证');\r\n      }\r\n    },\r\n    getMac() {\r\n      this.mac = true\r\n      this.getFingerPrint(v => {\r\n        this.$alert(v, '', {\r\n          callback: action => {\r\n            if (action == 'confirm') {\r\n              this.$message.success('已复制')\r\n              this.mac = false\r\n              navigator.clipboard.writeText(v)\r\n            }\r\n            if (action == 'cancel') {\r\n              this.mac = false\r\n            }\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async getFingerPrint(callback) {\r\n      let options = Fingerprint2.Options = {\r\n        excludes: {\r\n          webdriver: true,\r\n          userAgent: true,\r\n          language: true,\r\n          colorDepth: true,\r\n          deviceMemory: true,\r\n          pixelRatio: true,\r\n          hardwareConcurrency: true,\r\n          screenResolution: true,\r\n          availableScreenResolution: true,\r\n          timezoneOffset: true,\r\n          timezone: true,\r\n          sessionStorage: true,\r\n          localStorage: true,\r\n          indexedDb: true,\r\n          addBehavior: true,\r\n          openDatabase: true,\r\n          cpuClass: true,\r\n          platform: true,\r\n          doNotTrack: true,\r\n          plugins: true,\r\n          canvas: true,\r\n          webgl: false,\r\n          webglVendorAndRenderer: false,\r\n          adBlock: true,\r\n          hasLiedLanguages: true,\r\n          hasLiedResolution: true,\r\n          hasLiedOs: true,\r\n          hasLiedBrowser: true,\r\n          touchSupport: true,\r\n          fonts: true,\r\n          fontsFlash: true,\r\n          audio: false,\r\n          enumerateDevices: true\r\n        }\r\n      }\r\n      Fingerprint2.get(options, async (components) => {\r\n        const values = components.map(function (component, index) {\r\n            return component.value\r\n          })\r\n          const murmur = Fingerprint2.x64hash128(values.join(''), 31)\r\n        const fp = await Fingerprint.load()\r\n        const result = await fp.get()\r\n        callback(result.visitorId)\r\n        }\r\n      )\r\n    },\r\n    async logCode() {\r\n      const fp = await Fingerprint.load()\r\n      const result = await fp.get()\r\n      console.log('Browser fingerprint:', result.visitorId)\r\n      return result.visitorId\r\n    },\r\n    getCode() {\r\n      getCodeImg().then(res => {\r\n        this.captchaEnabled = res.captchaEnabled == undefined ? true : res.captchaEnabled\r\n        if (this.captchaEnabled) {\r\n          this.codeUrl = 'data:image/gif;base64,' + res.img\r\n          this.loginForm.uuid = res.uuid\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    getCookie() {\r\n      const username = Cookies.get('username')\r\n      const password = Cookies.get('password')\r\n      const rememberMe = Cookies.get('rememberMe')\r\n      this.loginForm = {\r\n        username: username == undefined ? this.loginForm.username : username,\r\n        password: password == undefined ? this.loginForm.password : decrypt(password),\r\n        rememberMe: rememberMe == undefined ? false : Boolean(rememberMe)\r\n      }\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true\r\n          if (this.loginForm.rememberMe) {\r\n            Cookies.set('username', this.loginForm.username, {expires: 30})\r\n            Cookies.set('password', encrypt(this.loginForm.password), {expires: 30})\r\n            Cookies.set('rememberMe', this.loginForm.rememberMe, {expires: 30})\r\n          } else {\r\n            Cookies.remove('username')\r\n            Cookies.remove('password')\r\n            Cookies.remove('rememberMe')\r\n          }\r\n          this.getFingerPrint(v => {\r\n            this.loginForm.unid = v\r\n\r\n            // 检查是否需要微信扫码验证\r\n            this.checkNeedWechatScan()\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 检查是否需要微信扫码验证\r\n    async checkNeedWechatScan() {\r\n      // 如果微信验证开关关闭，直接登录\r\n      if (!this.enableWechatVerify) {\r\n        console.log('微信验证已关闭，直接登录');\r\n        this.doLogin();\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const res = await checkNeedWechatScan(this.loginForm.username)\r\n        if (res.code === 200) {\r\n          const needScan = res.data.needScan\r\n          const isWechatBound = res.data.isWechatBound;\r\n\r\n          // 检查Cookie中是否存在微信扫码验证标记\r\n          const scanVerified = Cookies.get(this.wechatScanCookieName);\r\n          if (needScan && !scanVerified) {\r\n            // 需要微信扫码验证\r\n            this.showWechatScan = true;\r\n            this.loading = false;\r\n\r\n            // 如果用户未绑定微信，显示提示\r\n            if (!isWechatBound) {\r\n              this.$message.info('首次登录需要绑定微信账号进行验证');\r\n            }\r\n          } else {\r\n            // 不需要微信扫码验证，直接登录\r\n            this.doLogin();\r\n          }\r\n        } else {\r\n          // 检查失败，直接登录\r\n          this.doLogin();\r\n        }\r\n      } catch (error) {\r\n        console.error('检查微信扫码验证失败', error);\r\n        // 发生错误，直接登录\r\n        this.doLogin();\r\n      }\r\n    },\r\n\r\n    // 执行登录\r\n    doLogin() {\r\n      this.$store.dispatch('Login', this.loginForm).then(() => {\r\n        // 登录成功后，设置微信扫码验证标记Cookie，有效期24小时\r\n        Cookies.set(this.wechatScanCookieName, 'true', {expires: 1});\r\n        this.$router.push({path: this.redirect || '/'}).catch(() => {\r\n        })\r\n      }).catch(() => {\r\n        this.loading = false\r\n        if (this.captchaEnabled) {\r\n          this.getCode()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 微信扫码登录成功回调\r\n    handleWechatScanSuccess() {\r\n      this.showWechatScan = false\r\n      this.doLogin()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" rel=\"stylesheet/scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n\r\n.title {\r\n  margin: 0 auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n\r\n  .el-input {\r\n    height: 38px;\r\n\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n\r\n.login-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n\r\n.login-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n\r\n.el-login-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial, serif;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.login-code-img {\r\n  height: 38px;\r\n}\r\n\r\n/* 添加调试选项样式 */\r\n.debug-options {\r\n  margin-top: 10px;\r\n  padding: 5px;\r\n  background-color: rgba(255, 255, 100, 0.2);\r\n  border: 1px dashed #e6a23c;\r\n  border-radius: 4px;\r\n}\r\n</style>\r\n"]}]}