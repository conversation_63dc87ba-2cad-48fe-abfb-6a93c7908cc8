{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\ExpressComponent.vue?vue&type=style&index=0&id=3bd9dc1f&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\ExpressComponent.vue", "mtime": 1754646305902}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KQGltcG9ydCAnQC9hc3NldHMvc3R5bGVzL29wLWRvY3VtZW50JzsNCg0KLy8gRXhwcmVzc+e7hOS7tueJueWumuagt+W8jw0KLmV4cHJlc3MtY29tcG9uZW50IHsNCiAgd2lkdGg6IDEwMCU7DQoNCiAgLmV4cHJlc3MtaXRlbSB7DQogICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgfQ0KDQogIC5zZXJ2aWNlLWJhciB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIG1hcmdpbi10b3A6IDEwcHg7DQogICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICB3aWR0aDogMTAwJTsNCg0KICAgIC5zZXJ2aWNlLXRvZ2dsZS1pY29uIHsNCiAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgIG1hcmdpbi1yaWdodDogNXB4Ow0KICAgIH0NCg0KICAgIC5zZXJ2aWNlLXRpdGxlIHsNCiAgICAgIG1hcmdpbjogMDsNCiAgICAgIHdpZHRoOiAyNTBweDsNCiAgICAgIHRleHQtYWxpZ246IGxlZnQ7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgfQ0KICB9DQoNCiAgLnNlcnZpY2UtY29udGVudC1hcmVhIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICAgIGRpc3BsYXk6IC13ZWJraXQtYm94Ow0KDQogICAgLnNlcnZpY2UtaW5mby1jb2wgew0KICAgICAgLmVsLWZvcm0taXRlbSB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g5LyY5YyW6KGo5Y2V6L6T5YWl5qGG5qC35byPDQogIC5lbC1pbnB1dCB7DQogICAgd2lkdGg6IDEwMCU7DQogIH0NCg0KICAvLyDkvJjljJbml6XmnJ/pgInmi6nlmajmoLflvI8NCiAgLmVsLWRhdGUtcGlja2VyIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["ExpressComponent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuWA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "ExpressComponent.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div class=\"express-component\">\r\n    <!--快递-->\r\n    <div class=\"express-item\">\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <!--标题栏-->\r\n          <div class=\"service-bar\">\r\n            <a\r\n              :class=\"[\r\n                'service-toggle-icon',\r\n                foldState ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <h3 class=\"service-title\" @click=\"changeFold(serviceItem.serviceTypeId)\">\r\n              快递-EXPRESS\r\n            </h3>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"rsOpExpressServiceInstance\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(serviceItem.serviceTypeId)\"\r\n              :rs-charge-list=\"serviceItem.rsChargeList\"\r\n              @auditFee=\"auditCharge($event)\"\r\n              @return=\"updateExpressData($event)\"\r\n            />\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"getFold(serviceItem.serviceTypeId)\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(5, serviceItem.serviceTypeId, getServiceObject(serviceItem.serviceTypeId))\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(serviceItem.serviceTypeId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"getServiceInstance(serviceItem.serviceTypeId).supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(serviceItem.serviceTypeId)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--主表信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"商务单号\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input :value=\"form.psaNo\" class=\"disable-form\" disabled/>\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button\r\n                          size=\"mini\"\r\n                          style=\"color: red\"\r\n                          type=\"text\"\r\n                          @click=\"psaBookingCancel\"\r\n                        >\r\n                          取消\r\n                        </el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"rsOpExpressFormDisable || disabled || psaVerify\"\r\n                :logistics-progress-data=\"getServiceObject(serviceItem.serviceTypeId).rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"40\"\r\n                @deleteItem=\"deleteLogItem($event)\"\r\n                @return=\"updateLogList($event)\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"getServiceObject(serviceItem.serviceTypeId).rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"rsOpExpressFormDisable || disabled\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"getServiceObject(serviceItem.serviceTypeId).payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"getServiceObject(serviceItem.serviceTypeId).payableRMBTax\"\r\n              :pay-detail-u-s-d=\"getServiceObject(serviceItem.serviceTypeId).payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"getServiceObject(serviceItem.serviceTypeId).payableUSDTax\"\r\n              :service-type-id=\"40\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"deleteAllCharge(serviceItem.serviceTypeId)\"\r\n              @deleteItem=\"deleteChargeItem(serviceItem.serviceTypeId, $event)\"\r\n              @return=\"calculateCharge(serviceItem.serviceTypeId, $event, getServiceObject(serviceItem.serviceTypeId))\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\n\r\nexport default {\r\n  name: \"ExpressComponent\",\r\n  components: {\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    serviceItem: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 快递服务数据集合\r\n    expressServices: {\r\n      type: [Array, Set],\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 快递数据对象\r\n    rsOpExpress: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 快递服务实例\r\n    rsOpExpressServiceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 快递折叠状态\r\n    rsOpExpressFold: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 快递表单禁用状态\r\n    rsOpExpressFormDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    serviceInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 新增属性，不再依赖$parent\r\n    foldState: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    serviceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    serviceObject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    formDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    payable: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    changeFold(serviceTypeId) {\r\n      this.$emit(\"changeFold\", serviceTypeId)\r\n    },\r\n    getFold(serviceTypeId) {\r\n      return this.foldState\r\n    },\r\n    // 更新快递数据\r\n    updateExpressData(data) {\r\n      this.$emit(\"update:rsOpExpress\", data)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(serviceTypeId) {\r\n      const serviceInstance = this.getServiceInstance(serviceTypeId)\r\n      if (!serviceInstance || !serviceInstance.supplierId) return \"\"\r\n\r\n      const supplier = this.supplierList.find(v => v.companyId === serviceInstance.supplierId)\r\n      return supplier ? supplier.staffEmail : \"\"\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceTypeId) {\r\n      const serviceInstance = this.getServiceInstance(serviceTypeId)\r\n      if (!serviceInstance) return \"\"\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 获取服务实例\r\n    getServiceInstance(serviceTypeId) {\r\n      return this.serviceInstance\r\n    },\r\n    // 获取服务对象\r\n    getServiceObject(serviceTypeId) {\r\n      return this.serviceObject\r\n    },\r\n    // 获取应付金额\r\n    getPayable(serviceTypeId) {\r\n      return this.payable\r\n    },\r\n    getFormDisable(serviceTypeId) {\r\n      return this.formDisable\r\n    },\r\n    // 事件转发给父组件\r\n    auditCharge(event) {\r\n      this.$emit(\"auditCharge\", event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    psaBookingCancel() {\r\n      this.$emit(\"psaBookingCancel\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceTypeId, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceTypeId, event, item)\r\n    },\r\n    // 物流进度相关方法\r\n    deleteLogItem(event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsOpLogList) {\r\n        serviceObject.rsOpLogList = serviceObject.rsOpLogList.filter(item => item !== event)\r\n      }\r\n    },\r\n    updateLogList(event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsOpLogList = event\r\n      }\r\n    },\r\n    // 费用列表相关方法\r\n    deleteAllCharge(serviceTypeId) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsChargeList = []\r\n      }\r\n    },\r\n    deleteChargeItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsChargeList) {\r\n        serviceObject.rsChargeList = serviceObject.rsChargeList.filter(item => item !== event)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// Express组件特定样式\r\n.express-component {\r\n  width: 100%;\r\n\r\n  .express-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title {\r\n      margin: 0;\r\n      width: 250px;\r\n      text-align: left;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}