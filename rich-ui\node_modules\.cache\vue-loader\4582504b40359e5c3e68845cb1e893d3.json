{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteChargeList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteChargeList.vue", "mtime": 1754645302063}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgY3VycmVuY3kgZnJvbSAiY3VycmVuY3kuanMiDQppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCINCmltcG9ydCBwaW55aW4gZnJvbSAianMtcGlueWluIg0KaW1wb3J0IENvbXBhbnlTZWxlY3QgZnJvbSAiQC9jb21wb25lbnRzL0NvbXBhbnlTZWxlY3QvaW5kZXgudnVlIg0KaW1wb3J0IHtwYXJzZVRpbWV9IGZyb20gIkAvdXRpbHMvcmljaCINCmltcG9ydCBTb3J0YWJsZSBmcm9tICJzb3J0YWJsZWpzIg0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJkZWJpdE5vdGVDaGFyZ2VMaXN0IiwNCiAgY29tcG9uZW50czoge0NvbXBhbnlTZWxlY3QsIFRyZWVzZWxlY3R9LA0KICBwcm9wczogWyJjaGFyZ2VEYXRhIiwgImNvbXBhbnlMaXN0IiwgIm9wZW5DaGFyZ2VMaXN0IiwgImlzUmVjZWl2YWJsZSIsICJkaXNhYmxlZCIsDQogICAgImhpZGRlblN1cHBsaWVyIiwgInJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVUYXhVU0QiLA0KICAgICJyc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4Uk1CIiwgInJzQ2xpZW50TWVzc2FnZVBheWFibGVUYXhVU0QiLCAicnNDbGllbnRNZXNzYWdlUGF5YWJsZVRheFJNQiIsDQogICAgInJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVSTUIiLCAicnNDbGllbnRNZXNzYWdlUmVjZWl2YWJsZVVTRCIsICJyc0NsaWVudE1lc3NhZ2VQYXlhYmxlUk1CIiwNCiAgICAicnNDbGllbnRNZXNzYWdlUGF5YWJsZVVTRCIsICJyc0NsaWVudE1lc3NhZ2VQcm9maXQiLCAicnNDbGllbnRNZXNzYWdlUHJvZml0Tm9UYXgiLCAicGF5RGV0YWlsUk1CIiwNCiAgICAicGF5RGV0YWlsVVNEIiwgInBheURldGFpbFJNQlRheCIsICJwYXlEZXRhaWxVU0RUYXgiLCAicnNDbGllbnRNZXNzYWdlUHJvZml0VVNEIiwgInJzQ2xpZW50TWVzc2FnZVByb2ZpdFJNQiIsDQogICAgInJzQ2xpZW50TWVzc2FnZVByb2ZpdFRheFJNQiIsICJyc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhVU0QiLCAiZGViaXROb3RlIiwgImRyYWdHcm91cE5hbWUiXSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBsb2NhbENoYXJnZURhdGE6IHsNCiAgICAgIGdldCgpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuY2hhcmdlRGF0YSB8fCBbXQ0KICAgICAgfSwNCiAgICAgIHNldCh2YWx1ZSkgew0KICAgICAgICB0aGlzLiRlbWl0KCJyZXR1cm4iLCB2YWx1ZSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhc0NvbmZpcm1Sb3coKSB7DQogICAgICBsZXQgcmVzdWx0ID0gZmFsc2U7DQogICAgICAodGhpcy5jaGFyZ2VEYXRhICYmIHRoaXMuY2hhcmdlRGF0YS5sZW5ndGggPiAwKSA/IHRoaXMuY2hhcmdlRGF0YS5tYXAoaXRlbSA9PiB7DQogICAgICAgIGlmIChpdGVtLmlzQWNjb3VudENvbmZpcm1lZCA9PT0gIjEiKSB7DQogICAgICAgICAgcmVzdWx0ID0gdHJ1ZQ0KICAgICAgICB9DQogICAgICB9KSA6IG51bGwNCiAgICAgIHJldHVybiByZXN1bHQNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgY2hhcmdlRGF0YTogew0KICAgICAgaGFuZGxlcjogZnVuY3Rpb24gKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgIGlmICghb2xkVmFsKSB7DQogICAgICAgICAgdGhpcy4kZW1pdCgicmV0dXJuIiwgbmV3VmFsKQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6YGN5Y6G6LS555So5YiX6KGo77yM5qOA5p+l5biB56eN5Y+Y5YyWDQogICAgICAgIG5ld1ZhbCA/IG5ld1ZhbC5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICAgIGNvbnN0IG9sZEl0ZW0gPSBvbGRWYWxbaW5kZXhdDQoNCiAgICAgICAgICAvLyDmo4Dmn6XluIHnp43lj5jljJblubborqHnrpflsI/orqENCiAgICAgICAgICBpZiAoaXRlbS5jdXJyZW5jeSAmJiBpdGVtLmFtb3VudCkgew0KICAgICAgICAgICAgLy8g5aaC5p6c5LuOIFJNQiDmjaLmiJAgVVNE77yM5L2/55SoIDEv5rGH546HIOiuoeeulw0KICAgICAgICAgICAgaWYgKG9sZEl0ZW0gJiYgb2xkSXRlbS5jdXJyZW5jeSA9PT0gIlJNQiIgJiYgaXRlbS5jdXJyZW5jeSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgaWYgKGl0ZW0uZXhjaGFuZ2VSYXRlICYmIGl0ZW0uZXhjaGFuZ2VSYXRlICE9PSAwKSB7DQogICAgICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgICAgIC8vIOiuoeeulyAxL+axh+eOh++8jOS/neeVmTTkvY3lsI/mlbANCiAgICAgICAgICAgICAgICAgIGNvbnN0IGludmVyc2VSYXRlID0gY3VycmVuY3koMSwge3ByZWNpc2lvbjogNH0pLmRpdmlkZShpdGVtLmV4Y2hhbmdlUmF0ZSkudmFsdWUNCg0KICAgICAgICAgICAgICAgICAgLy8g6K6h566X5bCP6K6hOiDljZXku7cgKiDmlbDph48gKiDmsYfnjocgKiAoMSArIOeojueOhy8xMDApDQogICAgICAgICAgICAgICAgICBpdGVtLnN1YnRvdGFsID0gY3VycmVuY3koaXRlbS5kblVuaXRSYXRlIHx8IDAsIHtwcmVjaXNpb246IDR9KQ0KICAgICAgICAgICAgICAgICAgICAubXVsdGlwbHkoaXRlbS5hbW91bnQpDQogICAgICAgICAgICAgICAgICAgIC5tdWx0aXBseShpbnZlcnNlUmF0ZSkNCiAgICAgICAgICAgICAgICAgICAgLm11bHRpcGx5KGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShpdGVtLmR1dHlSYXRlIHx8IDApLmRpdmlkZSgxMDApKSkNCiAgICAgICAgICAgICAgICAgICAgLnZhbHVlDQogICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuiuoeeul+Wwj+iuoeWHuumUmToiLCBlcnJvcikNCiAgICAgICAgICAgICAgICAgIGl0ZW0uc3VidG90YWwgPSAwDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KSA6IG51bGwNCg0KICAgICAgICB0aGlzLiRlbWl0KCJyZXR1cm4iLCBuZXdWYWwgPyBuZXdWYWwgOiBbXSkNCg0KICAgICAgICAvLyDmlbDmja7lj5jljJblkI7ph43mlrDliJ3lp4vljJbmi5bmi73vvIzkvb/nlKjmm7TlronlhajnmoTmlrnlvI8NCiAgICAgICAgdGhpcy5yZWluaXRpYWxpemVTb3J0YWJsZSgpDQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQ0KICAgIH0sDQogICAgZGlzYWJsZWQ6IHsNCiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIChuZXdWYWwpIHsNCiAgICAgICAgLy8g56aB55So54q25oCB5pS55Y+Y5pe26YeN5paw5Yid5aeL5YyW5ouW5ou9DQogICAgICAgIHRoaXMucmVpbml0aWFsaXplU29ydGFibGUoKQ0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmluaXRTb3J0YWJsZSgpDQogIH0sDQogIGJlZm9yZURlc3Ryb3koKSB7DQogICAgLy8g5riF55CG5a6a5pe25ZmoDQogICAgaWYgKHRoaXMucmVpbml0VGltZXIpIHsNCiAgICAgIGNsZWFyVGltZW91dCh0aGlzLnJlaW5pdFRpbWVyKQ0KICAgICAgdGhpcy5yZWluaXRUaW1lciA9IG51bGwNCiAgICB9DQogICAgLy8g6ZSA5q+B5ouW5ou95a6e5L6LDQogICAgdGhpcy5kZXN0cm95U29ydGFibGUoKQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBwYXlUb3RhbFJNQjogMCwNCiAgICAgIHBheVRvdGFsVVNEOiAwLA0KICAgICAgc2hvd0NsaWVudE5hbWU6IG51bGwsDQogICAgICBzb3J0YWJsZTogbnVsbCwNCiAgICAgIHJlaW5pdFRpbWVyOiBudWxsLA0KICAgICAgZHJhZ1N0YXJ0Q29sdW1uOiAtMSwgLy8g6K6w5b2V5ouW5ou95byA5aeL55qE5YiX57Si5byVDQogICAgICBzZXJ2aWNlczogW3sNCiAgICAgICAgdmFsdWU6IDEsDQogICAgICAgIGxhYmVsOiAi5rW36L+QIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogMTAsDQogICAgICAgIGxhYmVsOiAi56m66L+QIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogMjAsDQogICAgICAgIGxhYmVsOiAi6ZOB6LevIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogNDAsDQogICAgICAgIGxhYmVsOiAi5b+r6YCSIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogNTAsDQogICAgICAgIGxhYmVsOiAi5ouW6L2mIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogNjAsDQogICAgICAgIGxhYmVsOiAi5oql5YWzIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogNzAsDQogICAgICAgIGxhYmVsOiAi5riF5YWz5rS+6YCBIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogODAsDQogICAgICAgIGxhYmVsOiAi56CB5aS05LuT5YKoIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogOTAsDQogICAgICAgIGxhYmVsOiAi5qOA6aqM6K+B5LmmIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogMTAwLA0KICAgICAgICBsYWJlbDogIuS/nemZqSINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDEwMSwNCiAgICAgICAgbGFiZWw6ICLmianlsZXmnI3liqEiDQogICAgICB9XSwNCiAgICAgIHNlcnZpY2U6IFt7DQogICAgICAgIHZhbHVlOiAxLA0KICAgICAgICBsYWJlbDogIuWfuuehgOacjeWKoSINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDQsDQogICAgICAgIGxhYmVsOiAi5YmN56iL6L+Q6L6TIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogNSwNCiAgICAgICAgbGFiZWw6ICLlh7rlj6PmiqXlhbMiDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiA2LA0KICAgICAgICBsYWJlbDogIui/m+WPo+a4heWFsyINCiAgICAgIH0sIHt2YWx1ZTogMiwgbGFiZWw6ICLmtbfov5AifQ0KICAgICAgICAsIHt2YWx1ZTogMywgbGFiZWw6ICLpmYbov5AifQ0KICAgICAgICAsIHt2YWx1ZTogNCwgbGFiZWw6ICLpk4Hot68ifQ0KICAgICAgICAsIHt2YWx1ZTogNSwgbGFiZWw6ICLnqbrov5AifQ0KICAgICAgICAsIHt2YWx1ZTogNiwgbGFiZWw6ICLlv6vpgJIifQ0KICAgICAgICAsIHt2YWx1ZTogMjEsIGxhYmVsOiAi5pW05p+c5rW36L+QIn0NCiAgICAgICAgLCB7dmFsdWU6IDIyLCBsYWJlbDogIuaLvOafnOa1t+i/kCJ9DQogICAgICAgICwge3ZhbHVlOiAyMywgbGFiZWw6ICLmlaPmnYLoiLkifQ0KICAgICAgICAsIHt2YWx1ZTogMjQsIGxhYmVsOiAi5rua6KOF6Ii5In0NCiAgICAgICAgLCB7dmFsdWU6IDQxLCBsYWJlbDogIuaVtOafnOmTgei3ryJ9DQogICAgICAgICwge3ZhbHVlOiA0MiwgbGFiZWw6ICLmi7zmn5zpk4Hot68ifQ0KICAgICAgICAsIHt2YWx1ZTogNDMsIGxhYmVsOiAi6ZOB6Lev6L2m55quIn0NCiAgICAgICAgLCB7dmFsdWU6IDUxLCBsYWJlbDogIuepuui/kOaZruiIsSJ9DQogICAgICAgICwge3ZhbHVlOiA1MiwgbGFiZWw6ICLnqbrov5DljIXmnb8ifQ0KICAgICAgICAsIHt2YWx1ZTogNTMsIGxhYmVsOiAi56m66L+Q5YyF5py6In0NCiAgICAgICAgLCB7dmFsdWU6IDU0LCBsYWJlbDogIuepuui/kOihjOadjiJ9DQogICAgICAgICwge3ZhbHVlOiA5NjEsIGxhYmVsOiAi5YmN56iL6L+Q6L6TIn0NCiAgICAgICAgLCB7dmFsdWU6IDk2NCwgbGFiZWw6ICLov5vlj6PmuIXlhbMifQ0KICAgICAgICAsIHt2YWx1ZTogNywgbGFiZWw6ICLlh7rlj6PmiqXlhbMifQ0KICAgICAgXSwNCiAgICAgIGNoYXJnZVJlbWFyazogbnVsbA0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGRlc3Ryb3lTb3J0YWJsZSgpIHsNCiAgICAgIGlmICh0aGlzLnNvcnRhYmxlKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgLy8g5qOA5p+lIHNvcnRhYmxlIOWunuS+i+aYr+WQpui/mOacieaViA0KICAgICAgICAgIGlmICh0aGlzLnNvcnRhYmxlLmVsICYmIHRoaXMuc29ydGFibGUuZWwucGFyZW50Tm9kZSkgew0KICAgICAgICAgICAgdGhpcy5zb3J0YWJsZS5kZXN0cm95KCkNCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCdFcnJvciBkZXN0cm95aW5nIHNvcnRhYmxlOicsIGVycm9yKQ0KICAgICAgICB9IGZpbmFsbHkgew0KICAgICAgICAgIHRoaXMuc29ydGFibGUgPSBudWxsDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIHJlaW5pdGlhbGl6ZVNvcnRhYmxlKCkgew0KICAgICAgLy8g5L2/55So6Ziy5oqW5bu26L+f6YeN5paw5Yid5aeL5YyW77yM6YG/5YWN6aKR57mB55qE5Yib5bu66ZSA5q+BDQogICAgICBpZiAodGhpcy5yZWluaXRUaW1lcikgew0KICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5yZWluaXRUaW1lcikNCiAgICAgIH0NCg0KICAgICAgdGhpcy5yZWluaXRUaW1lciA9IHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgdGhpcy5kZXN0cm95U29ydGFibGUoKQ0KICAgICAgICAgIC8vIOehruS/nSBET00g5bey5pu05paw5ZCO5YaN5Yid5aeL5YyWDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5pbml0U29ydGFibGUoKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICB9LCAxMDApDQogICAgfSwNCiAgICBpbml0U29ydGFibGUoKSB7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGlmICh0aGlzLiRyZWZzLmNoYXJnZVRhYmxlICYmIHRoaXMuJHJlZnMuY2hhcmdlVGFibGUuJGVsKSB7DQogICAgICAgICAgY29uc3QgdGJvZHkgPSB0aGlzLiRyZWZzLmNoYXJnZVRhYmxlLiRlbC5xdWVyeVNlbGVjdG9yKCd0Ym9keScpDQogICAgICAgICAgaWYgKHRib2R5KSB7DQogICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICB0aGlzLnNvcnRhYmxlID0gU29ydGFibGUuY3JlYXRlKHRib2R5LCB7DQogICAgICAgICAgICAgICAgZ3JvdXA6IHsNCiAgICAgICAgICAgICAgICAgIG5hbWU6IHRoaXMuZHJhZ0dyb3VwTmFtZSB8fCAnZGViaXROb3RlQ2hhcmdlR3JvdXAnLA0KICAgICAgICAgICAgICAgICAgcHVsbDogKHRvLCBmcm9tLCBkcmFnRWwsIGV2dCkgPT4gew0KICAgICAgICAgICAgICAgICAgICAvLyDmoLnmja7mi5bmi73liJflhrPlrprmmK/lkKblhYHorrjmi5blh7rvvIjliarliIfmiJblpI3liLbvvIkNCiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuZHJhZ1N0YXJ0Q29sdW1uID09PSAxKSB7DQogICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7IC8vIOS7juesrOS4gOWIl+aLluWKqOaXtuWFgeiuuOaLluWHuu+8iOWJquWIh++8iQ0KICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAnY2xvbmUnOyAvLyDku47lhbbku5bliJfmi5bliqjml7blpI3liLYNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgIHB1dDogIXRoaXMuZGlzYWJsZWQNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIGFuaW1hdGlvbjogMTUwLA0KICAgICAgICAgICAgICAgIGRpc2FibGVkOiB0aGlzLmRpc2FibGVkLA0KICAgICAgICAgICAgICAgIGdob3N0Q2xhc3M6ICdzb3J0YWJsZS1naG9zdCcsDQogICAgICAgICAgICAgICAgZHJhZ0NsYXNzOiAnc29ydGFibGUtZHJhZycsDQogICAgICAgICAgICAgICAgZmlsdGVyOiAnLmRpc2FibGVkJywNCiAgICAgICAgICAgICAgICBvblN0YXJ0OiAoZXZ0KSA9PiB7DQogICAgICAgICAgICAgICAgICAvLyDmi5bmi73lvIDlp4sNCiAgICAgICAgICAgICAgICAgIGNvbnN0IGRyYWdnZWRJdGVtID0gdGhpcy5sb2NhbENoYXJnZURhdGFbZXZ0Lm9sZEluZGV4XQ0KDQogICAgICAgICAgICAgICAgICAvLyDojrflj5bmi5bmi73lvIDlp4vnmoTliJfntKLlvJUNCiAgICAgICAgICAgICAgICAgIHRoaXMuZHJhZ1N0YXJ0Q29sdW1uID0gLTEgLy8g6buY6K6k6K6+572u5Li6LTENCiAgICAgICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgICAgIC8vIOiOt+WPluaLluaLveS6i+S7tueahOi1t+Wni+WdkOaghw0KICAgICAgICAgICAgICAgICAgICBjb25zdCBtb3VzZVggPSBldnQub3JpZ2luYWxFdmVudC5jbGllbnRYDQogICAgICAgICAgICAgICAgICAgIGNvbnN0IG1vdXNlWSA9IGV2dC5vcmlnaW5hbEV2ZW50LmNsaWVudFkNCg0KICAgICAgICAgICAgICAgICAgICAvLyDlsJ3or5Xmm7Tlh4bnoa7lnLDnoa7lrprmi5bmi73lvIDlp4vnmoTliJcNCiAgICAgICAgICAgICAgICAgICAgY29uc3QgY2VsbHMgPSBldnQuaXRlbS5xdWVyeVNlbGVjdG9yQWxsKCd0ZCcpDQogICAgICAgICAgICAgICAgICAgIGlmIChjZWxscyAmJiBjZWxscy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgICAgICAgLy8g6K6h566X5q+P5Liq5Y2V5YWD5qC855qE5L2N572u77yM5om+5Yiw5YyF5ZCr6byg5qCH5L2N572u55qE5Y2V5YWD5qC8DQogICAgICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjZWxscy5sZW5ndGg7IGkrKykgew0KICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVjdCA9IGNlbGxzW2ldLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpDQogICAgICAgICAgICAgICAgICAgICAgICBpZiAobW91c2VYID49IHJlY3QubGVmdCAmJiBtb3VzZVggPD0gcmVjdC5yaWdodCAmJg0KICAgICAgICAgICAgICAgICAgICAgICAgICBtb3VzZVkgPj0gcmVjdC50b3AgJiYgbW91c2VZIDw9IHJlY3QuYm90dG9tKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZHJhZ1N0YXJ0Q29sdW1uID0gaQ0KICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaw0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgICAgIC8vIOWkh+mAieaWueazle+8muWmguaenOS4iumdoueahOaWueazleayoeaJvuWIsO+8jOS9v+eUqOihqOWktOWumuS9jQ0KICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5kcmFnU3RhcnRDb2x1bW4gPT09IC0xKSB7DQogICAgICAgICAgICAgICAgICAgICAgY29uc3QgaGVhZGVyQ2VsbHMgPSB0aGlzLiRyZWZzLmNoYXJnZVRhYmxlLiRlbC5xdWVyeVNlbGVjdG9yQWxsKCd0aGVhZCB0aCcpDQogICAgICAgICAgICAgICAgICAgICAgaWYgKGhlYWRlckNlbGxzICYmIGhlYWRlckNlbGxzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgaGVhZGVyQ2VsbHMubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVjdCA9IGhlYWRlckNlbGxzW2ldLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpDQogICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChtb3VzZVggPj0gcmVjdC5sZWZ0ICYmIG1vdXNlWCA8PSByZWN0LnJpZ2h0KSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5kcmFnU3RhcnRDb2x1bW4gPSBpDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmRyYWdTdGFydENvbHVtbiA9PT0gLTEpIHsNCiAgICAgICAgICAgICAgICAgICAgICAvLyDlm57pgIDmlrnmoYjvvJrlpoLmnpzpgJrov4flnZDmoIfml6Dms5Xnoa7lrprvvIzliJnpu5jorqTkuLrpnZ7nrKzkuIDliJcNCiAgICAgICAgICAgICAgICAgICAgICB0aGlzLmRyYWdTdGFydENvbHVtbiA9IDIgLy8g6K6+572u5Li66Z2e56ys5LiA5YiX77yM6buY6K6k5Li65aSN5Yi25qih5byPDQogICAgICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign56Gu5a6a5ouW5ou95byA5aeL5YiX5pe25Ye66ZSZOicsIGVycm9yKQ0KICAgICAgICAgICAgICAgICAgICB0aGlzLmRyYWdTdGFydENvbHVtbiA9IDEgLy8g5Ye66ZSZ5pe26buY6K6k5Li656ys5LiA5YiXDQogICAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAgIC8vIOiuvue9ruiiq+aLluaLveWFg+e0oOeahOaVsOaNrg0KICAgICAgICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgICAgICAgZXZ0Lml0ZW0uc2V0QXR0cmlidXRlKCdkYXRhLWRyYWctaXRlbScsIEpTT04uc3RyaW5naWZ5KGRyYWdnZWRJdGVtKSkNCiAgICAgICAgICAgICAgICAgICAgLy8g6aKd5aSW5re75Yqg5ouW5ou96LW35aeL5YiX5L+h5oGvDQogICAgICAgICAgICAgICAgICAgIGV2dC5pdGVtLnNldEF0dHJpYnV0ZSgnZGF0YS1kcmFnLWNvbHVtbicsIHRoaXMuZHJhZ1N0YXJ0Q29sdW1uKQ0KICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHN0cmluZ2lmeSBkcmFnIGl0ZW06JywgZXJyb3IpDQogICAgICAgICAgICAgICAgICAgIGV2dC5pdGVtLnNldEF0dHJpYnV0ZSgnZGF0YS1kcmFnLWl0ZW0nLCAne30nKQ0KICAgICAgICAgICAgICAgICAgICBldnQuaXRlbS5zZXRBdHRyaWJ1dGUoJ2RhdGEtZHJhZy1jb2x1bW4nLCAnLTEnKQ0KICAgICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgICB0aGlzLiRlbWl0KCdkcmFnU3RhcnQnLCB7DQogICAgICAgICAgICAgICAgICAgIGl0ZW06IGRyYWdnZWRJdGVtLA0KICAgICAgICAgICAgICAgICAgICBpbmRleDogZXZ0Lm9sZEluZGV4LA0KICAgICAgICAgICAgICAgICAgICBmcm9tOiB0aGlzLA0KICAgICAgICAgICAgICAgICAgICBjb2x1bW46IHRoaXMuZHJhZ1N0YXJ0Q29sdW1uDQogICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgb25BZGQ6IChldnQpID0+IHsNCiAgICAgICAgICAgICAgICAgIC8vIOaOpeaUtuWIsOaWsOWFg+e0oA0KICAgICAgICAgICAgICAgICAgY29uc3QgaXRlbSA9IGV2dC5pdGVtDQogICAgICAgICAgICAgICAgICBsZXQgZHJhZ2dlZEl0ZW0gPSB7fQ0KICAgICAgICAgICAgICAgICAgbGV0IGRyYWdTdGFydENvbHVtbiA9IC0xDQoNCiAgICAgICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnN0IGRyYWdEYXRhID0gaXRlbS5nZXRBdHRyaWJ1dGUoJ2RhdGEtZHJhZy1pdGVtJykNCiAgICAgICAgICAgICAgICAgICAgaWYgKGRyYWdEYXRhICYmIGRyYWdEYXRhICE9PSAndW5kZWZpbmVkJykgew0KICAgICAgICAgICAgICAgICAgICAgIGRyYWdnZWRJdGVtID0gSlNPTi5wYXJzZShkcmFnRGF0YSkNCiAgICAgICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgICAgIC8vIOiOt+WPluaLluaLvei1t+Wni+WIlw0KICAgICAgICAgICAgICAgICAgICBjb25zdCBjb2x1bW5EYXRhID0gaXRlbS5nZXRBdHRyaWJ1dGUoJ2RhdGEtZHJhZy1jb2x1bW4nKQ0KICAgICAgICAgICAgICAgICAgICBpZiAoY29sdW1uRGF0YSAmJiBjb2x1bW5EYXRhICE9PSAndW5kZWZpbmVkJykgew0KICAgICAgICAgICAgICAgICAgICAgIGRyYWdTdGFydENvbHVtbiA9IHBhcnNlSW50KGNvbHVtbkRhdGEsIDEwKQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gcGFyc2UgZHJhZyBpdGVtIGRhdGE6JywgZXJyb3IpDQogICAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAgIC8vIOWkhOeQhuaWsOWinuWFg+e0oOWIsOihqOagvA0KICAgICAgICAgICAgICAgICAgY29uc3QgbmV3Q2hhcmdlRGF0YSA9IFsuLi50aGlzLmxvY2FsQ2hhcmdlRGF0YV0NCg0KICAgICAgICAgICAgICAgICAgLy8g5peg6K665piv5aSN5Yi26L+Y5piv5Ymq5YiH77yM6YO96ZyA6KaB5re75Yqg6aG55Yiw55uu5qCH5L2N572uDQogICAgICAgICAgICAgICAgICAvLyDkvYbopoHnu5nmlrDpobnnlJ/miJDkuIDkuKrmlrDnmoRJRO+8jOihqOekuui/meaYr+S4gOS4quWFqOaWsOeahOmhuQ0KICAgICAgICAgICAgICAgICAgbmV3Q2hhcmdlRGF0YS5zcGxpY2UoZXZ0Lm5ld0luZGV4LCAwLCB7DQogICAgICAgICAgICAgICAgICAgIC4uLmRyYWdnZWRJdGVtLA0KICAgICAgICAgICAgICAgICAgICB0ZW1wSWQ6IGB0ZW1wXyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YCwNCiAgICAgICAgICAgICAgICAgICAgY2hhcmdlSWQ6IG51bGwgLy8g5riF6Zmk5Y6f5pyJSUTvvIzkvZzkuLrmlrDlop7pobkNCiAgICAgICAgICAgICAgICAgIH0pDQoNCiAgICAgICAgICAgICAgICAgIHRoaXMuJGVtaXQoJ3JldHVybicsIG5ld0NoYXJnZURhdGEpDQogICAgICAgICAgICAgICAgICB0aGlzLiRlbWl0KCdkcmFnQWRkJywgew0KICAgICAgICAgICAgICAgICAgICBpdGVtOiBkcmFnZ2VkSXRlbSwNCiAgICAgICAgICAgICAgICAgICAgbmV3SW5kZXg6IGV2dC5uZXdJbmRleCwNCiAgICAgICAgICAgICAgICAgICAgdG86IHRoaXMsDQogICAgICAgICAgICAgICAgICAgIGNvbHVtbjogZHJhZ1N0YXJ0Q29sdW1uDQogICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgb25SZW1vdmU6IChldnQpID0+IHsNCiAgICAgICAgICAgICAgICAgIC8vIOiOt+WPluaLluaLveS/oeaBrw0KICAgICAgICAgICAgICAgICAgY29uc3QgaXRlbSA9IGV2dC5pdGVtDQogICAgICAgICAgICAgICAgICBsZXQgZHJhZ1N0YXJ0Q29sdW1uID0gLTENCiAgICAgICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvbHVtbkRhdGEgPSBpdGVtLmdldEF0dHJpYnV0ZSgnZGF0YS1kcmFnLWNvbHVtbicpDQogICAgICAgICAgICAgICAgICAgIGlmIChjb2x1bW5EYXRhICYmIGNvbHVtbkRhdGEgIT09ICd1bmRlZmluZWQnKSB7DQogICAgICAgICAgICAgICAgICAgICAgZHJhZ1N0YXJ0Q29sdW1uID0gcGFyc2VJbnQoY29sdW1uRGF0YSwgMTApDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBwYXJzZSBkcmFnIGNvbHVtbiBkYXRhOicsIGVycm9yKQ0KICAgICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgICBjb25zdCBuZXdDaGFyZ2VEYXRhID0gWy4uLnRoaXMubG9jYWxDaGFyZ2VEYXRhXQ0KICAgICAgICAgICAgICAgICAgY29uc3QgcmVtb3ZlZEl0ZW0gPSBuZXdDaGFyZ2VEYXRhW2V2dC5vbGRJbmRleF0NCg0KICAgICAgICAgICAgICAgICAgLy8g5Y+q5pyJ5Zyo5LuO56ys5LiA5YiX5byA5aeL5ouW5ou95pe25omN5omn6KGM5Ymq5YiH5pON5L2cDQogICAgICAgICAgICAgICAgICAvLyBTb3J0YWJsZeeahGNsb25l6YCJ6aG55bey57uP5o6n5Yi25LqG5aSN5Yi26KGM5Li677yM6L+Z6YeM5oiR5Lus5Y+q6ZyA5aSE55CG5Ymq5YiH55qE5oOF5Ya1DQogICAgICAgICAgICAgICAgICBpZiAoZHJhZ1N0YXJ0Q29sdW1uID09PSAxKSB7DQogICAgICAgICAgICAgICAgICAgIG5ld0NoYXJnZURhdGEuc3BsaWNlKGV2dC5vbGRJbmRleCwgMSkNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgncmV0dXJuJywgbmV3Q2hhcmdlRGF0YSkNCiAgICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgnZHJhZ1JlbW92ZScsIHsNCiAgICAgICAgICAgICAgICAgICAgaXRlbTogcmVtb3ZlZEl0ZW0sDQogICAgICAgICAgICAgICAgICAgIG9sZEluZGV4OiBldnQub2xkSW5kZXgsDQogICAgICAgICAgICAgICAgICAgIGZyb206IHRoaXMsDQogICAgICAgICAgICAgICAgICAgIGNvbHVtbjogZHJhZ1N0YXJ0Q29sdW1uLA0KICAgICAgICAgICAgICAgICAgICBpc0N1dDogZHJhZ1N0YXJ0Q29sdW1uID09PSAxDQogICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgb25VcGRhdGU6IChldnQpID0+IHsNCiAgICAgICAgICAgICAgICAgIC8vIOWQjOS4gOihqOagvOWGheaOkuW6jw0KICAgICAgICAgICAgICAgICAgY29uc3QgbmV3Q2hhcmdlRGF0YSA9IFsuLi50aGlzLmxvY2FsQ2hhcmdlRGF0YV0NCiAgICAgICAgICAgICAgICAgIGNvbnN0IGl0ZW0gPSBuZXdDaGFyZ2VEYXRhLnNwbGljZShldnQub2xkSW5kZXgsIDEpWzBdDQogICAgICAgICAgICAgICAgICBuZXdDaGFyZ2VEYXRhLnNwbGljZShldnQubmV3SW5kZXgsIDAsIGl0ZW0pDQoNCiAgICAgICAgICAgICAgICAgIHRoaXMuJGVtaXQoJ3JldHVybicsIG5ld0NoYXJnZURhdGEpDQogICAgICAgICAgICAgICAgICB0aGlzLiRlbWl0KCdkcmFnVXBkYXRlJywgew0KICAgICAgICAgICAgICAgICAgICBpdGVtOiBpdGVtLA0KICAgICAgICAgICAgICAgICAgICBvbGRJbmRleDogZXZ0Lm9sZEluZGV4LA0KICAgICAgICAgICAgICAgICAgICBuZXdJbmRleDogZXZ0Lm5ld0luZGV4LA0KICAgICAgICAgICAgICAgICAgICBjb2x1bW46IHRoaXMuZHJhZ1N0YXJ0Q29sdW1uDQogICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgb25FbmQ6IChldnQpID0+IHsNCiAgICAgICAgICAgICAgICAgIC8vIOaLluaLvee7k+adnw0KICAgICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgnZHJhZ0VuZCcsIHsNCiAgICAgICAgICAgICAgICAgICAgLi4uZXZ0LA0KICAgICAgICAgICAgICAgICAgICBkcmFnQ29sdW1uOiB0aGlzLmRyYWdTdGFydENvbHVtbg0KICAgICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAgIC8vIOmHjee9ruaLluaLveWIlw0KICAgICAgICAgICAgICAgICAgdGhpcy5kcmFnU3RhcnRDb2x1bW4gPSAtMQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHNvcnRhYmxlOicsIGVycm9yKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNldFJvd0RhdGEoe3Jvdywgcm93SW5kZXh9KSB7DQogICAgICAvLyDkuLrmr4/ooYzorr7nva7mlbDmja7lsZ7mgKfvvIznlKjkuo7mi5bmi73kvKDpgJLmlbDmja4NCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgY29uc3QgdGFibGVSb3dzID0gdGhpcy4kcmVmcy5jaGFyZ2VUYWJsZS4kZWwucXVlcnlTZWxlY3RvckFsbCgndGJvZHkgdHInKQ0KICAgICAgICBpZiAodGFibGVSb3dzW3Jvd0luZGV4XSkgew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICB0YWJsZVJvd3Nbcm93SW5kZXhdLnNldEF0dHJpYnV0ZSgnZGF0YS1kcmFnLWl0ZW0nLCBKU09OLnN0cmluZ2lmeShyb3cpKQ0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc3RyaW5naWZ5IHJvdyBkYXRhOicsIGVycm9yKQ0KICAgICAgICAgICAgdGFibGVSb3dzW3Jvd0luZGV4XS5zZXRBdHRyaWJ1dGUoJ2RhdGEtZHJhZy1pdGVtJywgJ3t9JykNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICByZXR1cm4gJycNCiAgICB9LA0KICAgIGF1ZGl0U3RhdHVzKHN0YXR1cykgew0KICAgICAgcmV0dXJuIHN0YXR1cyA9PSAxID8gIuW3suWuoeaguCIgOiAi5pyq5a6h5qC4Ig0KICAgIH0sDQogICAgc2VsZWN0Q2hhcmdlKHRhcmdldCwgcm93KSB7DQogICAgICByb3cuZG5DaGFyZ2VOYW1lSWQgPSB0YXJnZXQuY2hhcmdlSWQNCiAgICAgIHJvdy5jaGFyZ2VOYW1lID0gdGFyZ2V0LmNoYXJnZUxvY2FsTmFtZQ0KICAgIH0sDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHZhbCkgew0KICAgICAgLy8g5aSE55CG6YCJ5oup5Y+Y5YyWDQogICAgICB0aGlzLiRlbWl0KCJzZWxlY3RSb3ciLCB2YWwpDQoNCiAgICAgIHRoaXMucGF5VG90YWxVU0QgPSAwDQogICAgICB0aGlzLnBheVRvdGFsUk1CID0gMA0KICAgICAgdmFsID8gdmFsLm1hcChpdGVtID0+IHsNCiAgICAgICAgaWYgKGl0ZW0uaXNSZWNpZXZpbmdPclBheWluZyA9PSAxKSB7DQogICAgICAgICAgaWYgKGl0ZW0uZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICB0aGlzLnBheVRvdGFsVVNEID0gY3VycmVuY3kodGhpcy5wYXlUb3RhbFVTRCkuYWRkKGl0ZW0uc3VidG90YWwpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMucGF5VG90YWxSTUIgPSBjdXJyZW5jeSh0aGlzLnBheVRvdGFsUk1CKS5hZGQoaXRlbS5zdWJ0b3RhbCkNCiAgICAgICAgICB9DQoNCiAgICAgICAgfQ0KICAgICAgfSkgOiBudWxsDQoNCiAgICB9LA0KICAgIGN1cnJlbmN5LA0KICAgIC8vIOiOt+WPlumhueebrueahOWUr+S4gOmUrg0KICAgIGdldEl0ZW1LZXkoaXRlbSkgew0KICAgICAgcmV0dXJuIGl0ZW0udGVtcElkIHx8IGl0ZW0uY2hhcmdlSWQgfHwgaXRlbS5pZCB8fCBgaXRlbV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gDQogICAgfSwNCiAgICBnZXRTZXJ2aWNlTmFtZShpZCkgew0KICAgICAgbGV0IHNlcnZpY2VOYW1lID0gIiINCiAgICAgIHRoaXMuc2VydmljZXMubWFwKG9iaiA9PiB7DQogICAgICAgIG9iai52YWx1ZSA9PT0gaWQgPyBzZXJ2aWNlTmFtZSA9IG9iai5sYWJlbCA6IG51bGwNCiAgICAgIH0pDQogICAgICByZXR1cm4gc2VydmljZU5hbWUNCiAgICB9LA0KICAgIGNvcHlGcmVpZ2h0KHJvdykgew0KICAgICAgaWYgKHRoaXMuY29tcGFueUxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICByb3cucGF5Q2xlYXJpbmdDb21wYW55SWQgPSB0aGlzLmNvbXBhbnlMaXN0WzBdLmNvbXBhbnlJZA0KICAgICAgICByb3cucGF5Q29tcGFueU5hbWUgPSB0aGlzLmNvbXBhbnlMaXN0WzBdLmNvbXBhbnlTaG9ydE5hbWUNCiAgICAgIH0NCiAgICAgIHJvdy5pc0FjY291bnRDb25maXJtZWQgPSAwDQogICAgICAvLyDmiqXku7fliJfooajot7PovazorqLoiLHml7bmsqHmnInlhazlj7jliJfooags5aSN5Yi25Yiw5bqU5pS25rKh5pyJ5a6i5oi35L+h5oGvDQogICAgICBsZXQgZGF0YSA9IHRoaXMuXy5jbG9uZURlZXAocm93KQ0KDQogICAgICB0aGlzLiRlbWl0KCJjb3B5RnJlaWdodCIsIHsuLi5kYXRhLCBjaGFyZ2VJZDogbnVsbH0pDQogICAgfSwNCiAgICBjb3B5QWxsRnJlaWdodCgpIHsNCiAgICAgIGlmICghdGhpcy5jb21wYW55TGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmFsZXJ0V2FybmluZygi6K+35YWI6YCJ5oup5aeU5omY5Y2V5L2N5oiW5YWz6IGU5Y2V5L2NIikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIHRoaXMuY2hhcmdlRGF0YS5tYXAoY2hhcmdlID0+IHsNCiAgICAgICAgY2hhcmdlLnBheUNsZWFyaW5nQ29tcGFueUlkID0gdGhpcy5jb21wYW55TGlzdFswXS5jb21wYW55SWQNCiAgICAgICAgY2hhcmdlLnBheUNvbXBhbnlOYW1lID0gdGhpcy5jb21wYW55TGlzdFswXS5jb21wYW55U2hvcnROYW1lDQogICAgICAgIGNoYXJnZS5pc1JlY2lldmluZ09yUGF5aW5nID0gMA0KICAgICAgICBjaGFyZ2UuaXNBY2NvdW50Q29uZmlybWVkID0gMA0KICAgICAgICBjaGFyZ2UuY2hhcmdlSWQgPSBudWxsDQogICAgICAgIHRoaXMuJGVtaXQoImNvcHlGcmVpZ2h0IiwgdGhpcy5fLmNsb25lRGVlcChjaGFyZ2UpKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGNoYW5nZVVuaXRDb3N0KHJvdywgdW5pdCkgew0KICAgICAgcm93LmRuVW5pdENvZGUgPSB1bml0DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHJvdy5zaG93Q29zdFVuaXQgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGNoYW5nZVVuaXQocm93LCB1bml0KSB7DQogICAgICByb3cuZG5Vbml0Q29kZSA9IHVuaXQNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgcm93LnNob3dRdW90YXRpb25Vbml0ID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVDaGFyZ2VTZWxlY3Qocm93LCBkYXRhKSB7DQogICAgICBpZiAocm93LmNoYXJnZUxvY2FsTmFtZSA9PT0gZGF0YS5jaGFyZ2VOYW1lKSB7DQogICAgICAgIHJvdy5jaGFyZ2VOYW1lID0gZGF0YS5jaGFyZ2VMb2NhbE5hbWUNCiAgICAgICAgcm93LnNob3dRdW90YXRpb25DaGFyZ2UgPSBmYWxzZQ0KICAgICAgfQ0KICAgICAgaWYgKHJvdy5jdXJyZW5jeUNvZGUgPT0gbnVsbCAmJiBkYXRhLmN1cnJlbmN5Q29kZSkgew0KICAgICAgICByb3cuZG5DdXJyZW5jeUNvZGUgPSBkYXRhLmN1cnJlbmN5Q29kZQ0KICAgICAgfQ0KICAgIH0sDQogICAgY2hhbmdlQ3VycmVuY3kocm93LCBjdXJyZW5jeUNvZGUpIHsNCiAgICAgIHJvdy5kbkN1cnJlbmN5Q29kZSA9IGN1cnJlbmN5Q29kZQ0KICAgICAgLyogbGV0IGV4Y2hhbmdlUmF0ZQ0KICAgICAgaWYgKGN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgZm9yIChjb25zdCBhIG9mIHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuZXhjaGFuZ2VSYXRlTGlzdCkgew0KICAgICAgICAgIGlmIChhLmxvY2FsQ3VycmVuY3kgPT09ICJSTUIiDQogICAgICAgICAgICAmJiByb3cuZG5DdXJyZW5jeUNvZGUgPT0gYS5vdmVyc2VhQ3VycmVuY3kNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIGV4Y2hhbmdlUmF0ZSA9IGN1cnJlbmN5KGEuc2V0dGxlUmF0ZSkuZGl2aWRlKGEuYmFzZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0gKi8NCg0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAvLyByb3cuYmFzaWNDdXJyZW5jeVJhdGUgPSBleGNoYW5nZVJhdGUgPyBleGNoYW5nZVJhdGUgOiAxDQogICAgICAgIHJvdy5zaG93UXVvdGF0aW9uQ3VycmVuY3kgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDluo/lj7cgKi8NCiAgICByb3dJbmRleCh7cm93LCByb3dJbmRleH0pIHsNCiAgICAgIHJvdy5pZCA9IHJvd0luZGV4ICsgMQ0KICAgIH0sDQogICAgYWRkUmVjZWl2YWJsZVBheWFibGUoKSB7DQogICAgICBsZXQgb2JqID0gew0KICAgICAgICBzaG93Q2xpZW50OiB0cnVlLA0KICAgICAgICBzaG93U3VwcGxpZXI6IHRydWUsDQogICAgICAgIHNob3dRdW90YXRpb25DaGFyZ2U6IHRydWUsDQogICAgICAgIHNob3dDb3N0Q2hhcmdlOiB0cnVlLA0KICAgICAgICBzaG93UXVvdGF0aW9uQ3VycmVuY3k6IHRydWUsDQogICAgICAgIHNob3dDb3N0Q3VycmVuY3k6IHRydWUsDQogICAgICAgIHNob3dRdW90YXRpb25Vbml0OiB0cnVlLA0KICAgICAgICBzaG93Q29zdFVuaXQ6IHRydWUsDQogICAgICAgIHNob3dTdHJhdGVneTogdHJ1ZSwNCiAgICAgICAgc2hvd1VuaXRSYXRlOiB0cnVlLA0KICAgICAgICBzaG93QW1vdW50OiB0cnVlLA0KICAgICAgICBzaG93Q3VycmVuY3lSYXRlOiB0cnVlLA0KICAgICAgICBzaG93RHV0eVJhdGU6IHRydWUsDQogICAgICAgIGJhc2ljQ3VycmVuY3lSYXRlOiAxLA0KICAgICAgICBkdXR5UmF0ZTogMCwNCiAgICAgICAgZG5BbW91bnQ6IDEsDQogICAgICAgIC8vIOW6lOaUtui/mOaYr+W6lOS7mA0KICAgICAgICBpc1JlY2lldmluZ09yUGF5aW5nOiB0aGlzLmlzUmVjZWl2YWJsZSA/IDAgOiAxLA0KICAgICAgICBjbGVhcmluZ0NvbXBhbnlJZDogdGhpcy5jaGFyZ2VEYXRhLmxlbmd0aCA+IDAgPyB0aGlzLmNoYXJnZURhdGFbdGhpcy5jaGFyZ2VEYXRhLmxlbmd0aCAtIDFdLmNsZWFyaW5nQ29tcGFueUlkIDogbnVsbA0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gMSkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSAxDQogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSAxMCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSAxMA0KICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gMjApIG9iai5zcWRTZXJ2aWNlVHlwZUlkID0gMjANCiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDQwKSBvYmouc3FkU2VydmljZVR5cGVJZCA9IDQwDQogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSA1MCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSA1MA0KICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gNjApIG9iai5zcWRTZXJ2aWNlVHlwZUlkID0gNjANCiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDcwKSBvYmouc3FkU2VydmljZVR5cGVJZCA9IDcwDQogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSA4MCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSA4MA0KICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gOTApIG9iai5zcWRTZXJ2aWNlVHlwZUlkID0gOTANCiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDEwMCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSAxMDANCiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDEwMSkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSAxMDENCiAgICAgIHRoaXMuY2hhcmdlRGF0YS5wdXNoKG9iaikNCiAgICB9LA0KICAgIGNvdW50UHJvZml0KHJvdywgY2F0ZWdvcnkpIHsNCiAgICAgIC8vIOehruS/neaJgOacieW/heimgeeahOWAvOmDveWtmOWcqOS4lOacieaViA0KICAgICAgaWYgKCFyb3cpIHJldHVybg0KDQogICAgICAvLyDkvb/nlKhjdXJyZW5jeS5qc+adpeWkhOeQhuaVsOWAvCzpgb/lhY3nsr7luqbmjZ/lpLENCiAgICAgIGNvbnN0IHVuaXRSYXRlID0gcm93LmRuVW5pdFJhdGUgfHwgMA0KICAgICAgY29uc3QgYW1vdW50ID0gcm93LmRuQW1vdW50IHx8IDANCiAgICAgIGNvbnN0IGN1cnJlbmN5UmF0ZSA9IGN1cnJlbmN5KHJvdy5iYXNpY0N1cnJlbmN5UmF0ZSB8fCAxLCB7cHJlY2lzaW9uOiA0fSkudmFsdWUNCiAgICAgIGNvbnN0IGR1dHlSYXRlID0gY3VycmVuY3kocm93LmR1dHlSYXRlIHx8IDApLnZhbHVlDQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOiuoeeul+Wwj+iuoQ0KICAgICAgICBjb25zdCBzdWJ0b3RhbCA9IGN1cnJlbmN5KHVuaXRSYXRlLCB7cHJlY2lzaW9uOiA0fSkNCiAgICAgICAgICAubXVsdGlwbHkoYW1vdW50KQ0KICAgICAgICAgIC5tdWx0aXBseShjdXJyZW5jeVJhdGUpDQogICAgICAgICAgLm11bHRpcGx5KGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShkdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKQ0KICAgICAgICAgIC52YWx1ZQ0KDQogICAgICAgIC8vIOabtOaWsOihjOaVsOaNrg0KICAgICAgICByb3cuc3VidG90YWwgPSBjdXJyZW5jeShzdWJ0b3RhbCwge3ByZWNpc2lvbjogMn0pLnZhbHVlDQogICAgICAgIHJvdy5zcWREbkN1cnJlbmN5QmFsYW5jZSA9IHJvdy5pc0FjY291bnRDb25maXJtZWQgPT09ICIwIiA/IGN1cnJlbmN5KHN1YnRvdGFsLCB7cHJlY2lzaW9uOiAyfSkudmFsdWUgOiByb3cuc3FkRG5DdXJyZW5jeUJhbGFuY2UNCg0KICAgICAgICAvLyDmoLnmja7kuI3lkIznmoTovpPlhaXnsbvlnovlhbPpl63lr7nlupTnmoTnvJbovpHnirbmgIENCiAgICAgICAgc3dpdGNoIChjYXRlZ29yeSkgew0KICAgICAgICAgIGNhc2UgInN0cmF0ZWd5IjoNCiAgICAgICAgICAgIHJvdy5zaG93U3RyYXRlZ3kgPSBmYWxzZQ0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICBjYXNlICJ1bml0UmF0ZSI6DQogICAgICAgICAgICAvLyDkuI3lnKjov5nph4zlhbPpl63nvJbovpHnirbmgIEs5pS555SoQGJsdXLkuovku7YNCiAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgY2FzZSAiYW1vdW50IjoNCiAgICAgICAgICAgIC8vIOS4jeWcqOi/memHjOWFs+mXree8lui+keeKtuaAgSzmlLnnlKhAYmx1cuS6i+S7tg0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICBjYXNlICJjdXJyZW5jeVJhdGUiOg0KICAgICAgICAgICAgLy8g5LiN5Zyo6L+Z6YeM5YWz6Zet57yW6L6R54q25oCBLOaUueeUqEBibHVy5LqL5Lu2DQogICAgICAgICAgICBicmVhaw0KICAgICAgICAgIGNhc2UgImR1dHlSYXRlIjoNCiAgICAgICAgICAgIC8vIOS4jeWcqOi/memHjOWFs+mXree8lui+keeKtuaAgSzmlLnnlKhAYmx1cuS6i+S7tg0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOinpuWPkeaVsOaNruabtOaWsA0KICAgICAgICB0aGlzLiRlbWl0KCJyZXR1cm4iLCB0aGlzLmNoYXJnZURhdGEpDQoNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiuoeeul+Wwj+iuoeaXtuWHuumUmToiLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K6h566X5bCP6K6h5pe25Ye66ZSZLOivt+ajgOafpei+k+WFpeWAvOaYr+WQpuato+ehriIpDQogICAgICB9DQogICAgfSwNCiAgICBkZWxldGVJdGVtKHJvdykgew0KICAgICAgdGhpcy4kZW1pdCgiZGVsZXRlSXRlbSIsIHJvdykNCiAgICB9LA0KICAgIGRlbGV0ZUFsbEl0ZW0ocm93KSB7DQogICAgICB0aGlzLiRlbWl0KCJkZWxldGVBbGwiKQ0KICAgIH0sDQogICAgY29tcGFueU5vcm1hbGl6ZXIobm9kZSkgew0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgaWQ6IG5vZGUuY29tcGFueUlkLA0KICAgICAgICBsYWJlbDogKG5vZGUuY29tcGFueVNob3J0TmFtZSAhPSBudWxsID8gbm9kZS5jb21wYW55U2hvcnROYW1lIDogIiIpICsgIiAiICsgKG5vZGUuY29tcGFueUxvY2FsTmFtZSAhPSBudWxsID8gbm9kZS5jb21wYW55TG9jYWxOYW1lIDogIiIpICsgIiwiICsgcGlueWluLmdldEZ1bGxDaGFycygobm9kZS5jb21wYW55U2hvcnROYW1lICE9IG51bGwgPyBub2RlLmNvbXBhbnlTaG9ydE5hbWUgOiAiIikgKyAiICIgKyAobm9kZS5jb21wYW55TG9jYWxOYW1lICE9IG51bGwgPyBub2RlLmNvbXBhbnlMb2NhbE5hbWUgOiAiIikpDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["debitNoteChargeList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy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file": "debitNoteChargeList.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <el-col :style=\"{'display':openChargeList?'':'none'}\" style=\"margin: 0;padding: 0;\">\r\n    <div :class=\"{'inactive':openChargeList==false,'active':openChargeList}\">\r\n      <el-table\r\n        ref=\"chargeTable\"\r\n        :data=\"localChargeData\"\r\n        border\r\n        class=\"pd0\"\r\n        row-key=\"getItemKey\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        :row-class-name=\"setRowData\"\r\n      >\r\n        <el-table-column\r\n          align=\"center\"\r\n          type=\"selection\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"费用\" prop=\"quotationChargeId\" width=\"80px\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showQuotationCharge\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCharge = true\"\r\n            >\r\n              {{ scope.row.chargeName }}\r\n            </div>\r\n            <tree-select v-show=\"scope.row.showQuotationCharge\" :dbn=\"true\" :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\" :flat=\"false\"\r\n                         :multiple=\"false\" :pass=\"scope.row.dnChargeNameId\" :placeholder=\"'运费'\"\r\n                         :type=\"'charge'\"\r\n                         @return=\"scope.row.dnChargeNameId = $event\"\r\n                         @returnData=\"handleChargeSelect(scope.row,$event)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"货币\" prop=\"quotationCurrencyId\" width=\"70\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true\"\r\n            >\r\n              {{ scope.row.dnCurrencyCode }}\r\n            </div>\r\n            <tree-select v-show=\"scope.row.showQuotationCurrency\" :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                         :pass=\"scope.row.dnCurrencyCode\" :type=\"'currency'\"\r\n                         @return=\"changeCurrency(scope.row,$event)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"单价\" prop=\"quotationRate\" width=\"80px\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showUnitRate\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true\"\r\n            >\r\n              {{\r\n                scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                  separator: \",\",\r\n                  precision: 2,\r\n                  symbol: \"¥\"\r\n                }).format() : (scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                  separator: \",\",\r\n                  precision: 2,\r\n                  symbol: \"$\"\r\n                }).format() : scope.row.dnUnitRate)\r\n              }}\r\n            </div>\r\n            <el-input-number v-show=\"scope.row.showUnitRate\" v-model=\"scope.row.dnUnitRate\" :controls=\"false\"\r\n                             :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                             :min=\"0.0001\"\r\n                             :precision=\"4\" style=\"display:flex;width: 100%\"\r\n                             @blur=\"scope.row.showUnitRate=false\"\r\n                             @change=\"countProfit(scope.row,'unitRate')\"\r\n                             @input=\"countProfit(scope.row,'unitRate')\"\r\n                             @focusout.native=\"scope.row.showUnitRate=false\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"单位\" prop=\"quotationUnitId\" width=\"50\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showQuotationUnit\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationUnit = true\"\r\n            >\r\n              {{ scope.row.dnUnitCode }}\r\n            </div>\r\n            <tree-select v-show=\"scope.row.showQuotationUnit\"\r\n                         :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\" :pass=\"scope.row.dnUnitCode\"\r\n                         :type=\"'unit'\" @return=\"changeUnit(scope.row,$event)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"数量\" prop=\"quotationAmount\" width=\"48px\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showAmount\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true\"\r\n            >\r\n              {{ scope.row.dnAmount }}\r\n            </div>\r\n            <el-input-number v-if=\"scope.row.showAmount\" v-model=\"scope.row.dnAmount\" :controls=\"false\"\r\n                             :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                             :min=\"0.00\" placeholder=\"数量\"\r\n                             style=\"display:flex;width: 100%\" @blur=\"scope.row.showAmount=false\"\r\n                             @change=\"countProfit(scope.row,'amount')\"\r\n                             @input=\"countProfit(scope.row,'amount')\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"汇率\" prop=\"quotationExchangeRate\" width=\"60\">\r\n          <template slot-scope=\"scope\" style=\"display:flex;\">\r\n            <div v-if=\"!scope.row.showCurrencyRate\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true\"\r\n            >\r\n              {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}\r\n            </div>\r\n            <el-input-number v-show=\"scope.row.showCurrencyRate\" v-model=\"scope.row.basicCurrencyRate\"\r\n                             :controls=\"false\"\r\n                             :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                             :min=\"0.0001\" :precision=\"4\" :step=\"0.0001\"\r\n                             style=\"width: 100%\" @blur=\"scope.row.showCurrencyRate=false\"\r\n                             @change=\"countProfit(scope.row,'currencyRate')\"\r\n                             @input=\"countProfit(scope.row,'currencyRate')\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"税率\" prop=\"quotationTaxRate\" width=\"50px\">\r\n          <template slot-scope=\"scope\">\r\n            <div style=\"display: flex;justify-content: center\">\r\n              <div v-if=\"!scope.row.showDutyRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true\"\r\n              >\r\n                {{ scope.row.dutyRate }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showDutyRate\" v-model=\"scope.row.dutyRate\" :controls=\"false\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               :min=\"0\" style=\"width: 75%\"\r\n                               @blur=\"scope.row.showDutyRate=false\"\r\n                               @change=\"countProfit(scope.row,'dutyRate')\"\r\n                               @input=\"countProfit(scope.row,'dutyRate')\"\r\n              />\r\n              <div>%</div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"小计\" prop=\"subtotal\" width=\"75\">\r\n          <template slot-scope=\"scope\">\r\n            <div>\r\n              {{\r\n                currency(scope.row.subtotal, {\r\n                  separator: \",\",\r\n                  precision: 2,\r\n                  symbol: (scope.row.dnCurrencyCode === \"RMB\" ? \"¥\" : \"$\")\r\n                }).format()\r\n              }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"费用备注\">\r\n          <template slot-scope=\"scope\">\r\n            <input v-model=\"scope.row.chargeRemark\"\r\n                   :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                   style=\"border: none;width: 100%;height: 100%;\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"审核状态\">\r\n          <template slot-scope=\"scope\">\r\n            {{ auditStatus(scope.row.isAccountConfirmed) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"已收金额\">\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.sqdDnCurrencyPaid\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"未收余额\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.sqdDnCurrencyBalance }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"所属服务\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <div>\r\n              {{ scope.row.serviceName ? scope.row.serviceName : getServiceName(scope.row.sqdServiceTypeId) }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-button\r\n              :disabled=\"disabled || hasConfirmRow\"\r\n              size=\"mini\"\r\n              style=\"color: red\"\r\n              type=\"text\"\r\n              @click=\"deleteAllItem()\"\r\n            >全部删除\r\n            </el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"deleteItem(scope.row)\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    <el-button :disabled=\"disabled\" style=\"padding: 0\"\r\n               type=\"text\"\r\n               @click=\"addReceivablePayable\"\r\n    >[＋]\r\n    </el-button>\r\n  </el-col>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport Sortable from \"sortablejs\"\r\n\r\nexport default {\r\n  name: \"debitNoteChargeList\",\r\n  components: {CompanySelect, Treeselect},\r\n  props: [\"chargeData\", \"companyList\", \"openChargeList\", \"isReceivable\", \"disabled\",\r\n    \"hiddenSupplier\", \"rsClientMessageReceivableTaxUSD\",\r\n    \"rsClientMessageReceivableTaxRMB\", \"rsClientMessagePayableTaxUSD\", \"rsClientMessagePayableTaxRMB\",\r\n    \"rsClientMessageReceivableRMB\", \"rsClientMessageReceivableUSD\", \"rsClientMessagePayableRMB\",\r\n    \"rsClientMessagePayableUSD\", \"rsClientMessageProfit\", \"rsClientMessageProfitNoTax\", \"payDetailRMB\",\r\n    \"payDetailUSD\", \"payDetailRMBTax\", \"payDetailUSDTax\", \"rsClientMessageProfitUSD\", \"rsClientMessageProfitRMB\",\r\n    \"rsClientMessageProfitTaxRMB\", \"rsClientMessageProfitTaxUSD\", \"debitNote\", \"dragGroupName\"],\r\n  computed: {\r\n    localChargeData: {\r\n      get() {\r\n        return this.chargeData || []\r\n      },\r\n      set(value) {\r\n        this.$emit(\"return\", value)\r\n      }\r\n    },\r\n    hasConfirmRow() {\r\n      let result = false;\r\n      (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => {\r\n        if (item.isAccountConfirmed === \"1\") {\r\n          result = true\r\n        }\r\n      }) : null\r\n      return result\r\n    }\r\n  },\r\n  watch: {\r\n    chargeData: {\r\n      handler: function (newVal, oldVal) {\r\n        if (!oldVal) {\r\n          this.$emit(\"return\", newVal)\r\n          return\r\n        }\r\n\r\n        // 遍历费用列表，检查币种变化\r\n        newVal ? newVal.forEach((item, index) => {\r\n          const oldItem = oldVal[index]\r\n\r\n          // 检查币种变化并计算小计\r\n          if (item.currency && item.amount) {\r\n            // 如果从 RMB 换成 USD，使用 1/汇率 计算\r\n            if (oldItem && oldItem.currency === \"RMB\" && item.currency === \"USD\") {\r\n              if (item.exchangeRate && item.exchangeRate !== 0) {\r\n                try {\r\n                  // 计算 1/汇率，保留4位小数\r\n                  const inverseRate = currency(1, {precision: 4}).divide(item.exchangeRate).value\r\n\r\n                  // 计算小计: 单价 * 数量 * 汇率 * (1 + 税率/100)\r\n                  item.subtotal = currency(item.dnUnitRate || 0, {precision: 4})\r\n                    .multiply(item.amount)\r\n                    .multiply(inverseRate)\r\n                    .multiply(currency(1).add(currency(item.dutyRate || 0).divide(100)))\r\n                    .value\r\n                } catch (error) {\r\n                  console.error(\"计算小计出错:\", error)\r\n                  item.subtotal = 0\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }) : null\r\n\r\n        this.$emit(\"return\", newVal ? newVal : [])\r\n\r\n        // 数据变化后重新初始化拖拽，使用更安全的方式\r\n        this.reinitializeSortable()\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    },\r\n    disabled: {\r\n      handler: function (newVal) {\r\n        // 禁用状态改变时重新初始化拖拽\r\n        this.reinitializeSortable()\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initSortable()\r\n  },\r\n  beforeDestroy() {\r\n    // 清理定时器\r\n    if (this.reinitTimer) {\r\n      clearTimeout(this.reinitTimer)\r\n      this.reinitTimer = null\r\n    }\r\n    // 销毁拖拽实例\r\n    this.destroySortable()\r\n  },\r\n  data() {\r\n    return {\r\n      payTotalRMB: 0,\r\n      payTotalUSD: 0,\r\n      showClientName: null,\r\n      sortable: null,\r\n      reinitTimer: null,\r\n      dragStartColumn: -1, // 记录拖拽开始的列索引\r\n      services: [{\r\n        value: 1,\r\n        label: \"海运\"\r\n      }, {\r\n        value: 10,\r\n        label: \"空运\"\r\n      }, {\r\n        value: 20,\r\n        label: \"铁路\"\r\n      }, {\r\n        value: 40,\r\n        label: \"快递\"\r\n      }, {\r\n        value: 50,\r\n        label: \"拖车\"\r\n      }, {\r\n        value: 60,\r\n        label: \"报关\"\r\n      }, {\r\n        value: 70,\r\n        label: \"清关派送\"\r\n      }, {\r\n        value: 80,\r\n        label: \"码头仓储\"\r\n      }, {\r\n        value: 90,\r\n        label: \"检验证书\"\r\n      }, {\r\n        value: 100,\r\n        label: \"保险\"\r\n      }, {\r\n        value: 101,\r\n        label: \"扩展服务\"\r\n      }],\r\n      service: [{\r\n        value: 1,\r\n        label: \"基础服务\"\r\n      }, {\r\n        value: 4,\r\n        label: \"前程运输\"\r\n      }, {\r\n        value: 5,\r\n        label: \"出口报关\"\r\n      }, {\r\n        value: 6,\r\n        label: \"进口清关\"\r\n      }, {value: 2, label: \"海运\"}\r\n        , {value: 3, label: \"陆运\"}\r\n        , {value: 4, label: \"铁路\"}\r\n        , {value: 5, label: \"空运\"}\r\n        , {value: 6, label: \"快递\"}\r\n        , {value: 21, label: \"整柜海运\"}\r\n        , {value: 22, label: \"拼柜海运\"}\r\n        , {value: 23, label: \"散杂船\"}\r\n        , {value: 24, label: \"滚装船\"}\r\n        , {value: 41, label: \"整柜铁路\"}\r\n        , {value: 42, label: \"拼柜铁路\"}\r\n        , {value: 43, label: \"铁路车皮\"}\r\n        , {value: 51, label: \"空运普舱\"}\r\n        , {value: 52, label: \"空运包板\"}\r\n        , {value: 53, label: \"空运包机\"}\r\n        , {value: 54, label: \"空运行李\"}\r\n        , {value: 961, label: \"前程运输\"}\r\n        , {value: 964, label: \"进口清关\"}\r\n        , {value: 7, label: \"出口报关\"}\r\n      ],\r\n      chargeRemark: null\r\n    }\r\n  },\r\n  methods: {\r\n    destroySortable() {\r\n      if (this.sortable) {\r\n        try {\r\n          // 检查 sortable 实例是否还有效\r\n          if (this.sortable.el && this.sortable.el.parentNode) {\r\n            this.sortable.destroy()\r\n          }\r\n        } catch (error) {\r\n          console.warn('Error destroying sortable:', error)\r\n        } finally {\r\n          this.sortable = null\r\n        }\r\n      }\r\n    },\r\n    reinitializeSortable() {\r\n      // 使用防抖延迟重新初始化，避免频繁的创建销毁\r\n      if (this.reinitTimer) {\r\n        clearTimeout(this.reinitTimer)\r\n      }\r\n\r\n      this.reinitTimer = setTimeout(() => {\r\n        this.$nextTick(() => {\r\n          this.destroySortable()\r\n          // 确保 DOM 已更新后再初始化\r\n          this.$nextTick(() => {\r\n            this.initSortable()\r\n          })\r\n        })\r\n      }, 100)\r\n    },\r\n    initSortable() {\r\n      this.$nextTick(() => {\r\n        if (this.$refs.chargeTable && this.$refs.chargeTable.$el) {\r\n          const tbody = this.$refs.chargeTable.$el.querySelector('tbody')\r\n          if (tbody) {\r\n            try {\r\n              this.sortable = Sortable.create(tbody, {\r\n                group: {\r\n                  name: this.dragGroupName || 'debitNoteChargeGroup',\r\n                  pull: (to, from, dragEl, evt) => {\r\n                    // 根据拖拽列决定是否允许拖出（剪切或复制）\r\n                    if (this.dragStartColumn === 1) {\r\n                      return true; // 从第一列拖动时允许拖出（剪切）\r\n                    } else {\r\n                      return 'clone'; // 从其他列拖动时复制\r\n                    }\r\n                  },\r\n                  put: !this.disabled\r\n                },\r\n                animation: 150,\r\n                disabled: this.disabled,\r\n                ghostClass: 'sortable-ghost',\r\n                dragClass: 'sortable-drag',\r\n                filter: '.disabled',\r\n                onStart: (evt) => {\r\n                  // 拖拽开始\r\n                  const draggedItem = this.localChargeData[evt.oldIndex]\r\n\r\n                  // 获取拖拽开始的列索引\r\n                  this.dragStartColumn = -1 // 默认设置为-1\r\n                  try {\r\n                    // 获取拖拽事件的起始坐标\r\n                    const mouseX = evt.originalEvent.clientX\r\n                    const mouseY = evt.originalEvent.clientY\r\n\r\n                    // 尝试更准确地确定拖拽开始的列\r\n                    const cells = evt.item.querySelectorAll('td')\r\n                    if (cells && cells.length > 0) {\r\n                      // 计算每个单元格的位置，找到包含鼠标位置的单元格\r\n                      for (let i = 0; i < cells.length; i++) {\r\n                        const rect = cells[i].getBoundingClientRect()\r\n                        if (mouseX >= rect.left && mouseX <= rect.right &&\r\n                          mouseY >= rect.top && mouseY <= rect.bottom) {\r\n                          this.dragStartColumn = i\r\n                          break\r\n                        }\r\n                      }\r\n                    }\r\n\r\n                    // 备选方法：如果上面的方法没找到，使用表头定位\r\n                    if (this.dragStartColumn === -1) {\r\n                      const headerCells = this.$refs.chargeTable.$el.querySelectorAll('thead th')\r\n                      if (headerCells && headerCells.length > 0) {\r\n                        for (let i = 0; i < headerCells.length; i++) {\r\n                          const rect = headerCells[i].getBoundingClientRect()\r\n                          if (mouseX >= rect.left && mouseX <= rect.right) {\r\n                            this.dragStartColumn = i\r\n                            break\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n\r\n                    if (this.dragStartColumn === -1) {\r\n                      // 回退方案：如果通过坐标无法确定，则默认为非第一列\r\n                      this.dragStartColumn = 2 // 设置为非第一列，默认为复制模式\r\n                    }\r\n\r\n                  } catch (error) {\r\n                    console.error('确定拖拽开始列时出错:', error)\r\n                    this.dragStartColumn = 1 // 出错时默认为第一列\r\n                  }\r\n\r\n                  // 设置被拖拽元素的数据\r\n                  try {\r\n                    evt.item.setAttribute('data-drag-item', JSON.stringify(draggedItem))\r\n                    // 额外添加拖拽起始列信息\r\n                    evt.item.setAttribute('data-drag-column', this.dragStartColumn)\r\n                  } catch (error) {\r\n                    console.error('Failed to stringify drag item:', error)\r\n                    evt.item.setAttribute('data-drag-item', '{}')\r\n                    evt.item.setAttribute('data-drag-column', '-1')\r\n                  }\r\n\r\n                  this.$emit('dragStart', {\r\n                    item: draggedItem,\r\n                    index: evt.oldIndex,\r\n                    from: this,\r\n                    column: this.dragStartColumn\r\n                  })\r\n                },\r\n                onAdd: (evt) => {\r\n                  // 接收到新元素\r\n                  const item = evt.item\r\n                  let draggedItem = {}\r\n                  let dragStartColumn = -1\r\n\r\n                  try {\r\n                    const dragData = item.getAttribute('data-drag-item')\r\n                    if (dragData && dragData !== 'undefined') {\r\n                      draggedItem = JSON.parse(dragData)\r\n                    }\r\n\r\n                    // 获取拖拽起始列\r\n                    const columnData = item.getAttribute('data-drag-column')\r\n                    if (columnData && columnData !== 'undefined') {\r\n                      dragStartColumn = parseInt(columnData, 10)\r\n                    }\r\n                  } catch (error) {\r\n                    console.error('Failed to parse drag item data:', error)\r\n                  }\r\n\r\n                  // 处理新增元素到表格\r\n                  const newChargeData = [...this.localChargeData]\r\n\r\n                  // 无论是复制还是剪切，都需要添加项到目标位置\r\n                  // 但要给新项生成一个新的ID，表示这是一个全新的项\r\n                  newChargeData.splice(evt.newIndex, 0, {\r\n                    ...draggedItem,\r\n                    tempId: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\r\n                    chargeId: null // 清除原有ID，作为新增项\r\n                  })\r\n\r\n                  this.$emit('return', newChargeData)\r\n                  this.$emit('dragAdd', {\r\n                    item: draggedItem,\r\n                    newIndex: evt.newIndex,\r\n                    to: this,\r\n                    column: dragStartColumn\r\n                  })\r\n                },\r\n                onRemove: (evt) => {\r\n                  // 获取拖拽信息\r\n                  const item = evt.item\r\n                  let dragStartColumn = -1\r\n                  try {\r\n                    const columnData = item.getAttribute('data-drag-column')\r\n                    if (columnData && columnData !== 'undefined') {\r\n                      dragStartColumn = parseInt(columnData, 10)\r\n                    }\r\n                  } catch (error) {\r\n                    console.error('Failed to parse drag column data:', error)\r\n                  }\r\n\r\n                  const newChargeData = [...this.localChargeData]\r\n                  const removedItem = newChargeData[evt.oldIndex]\r\n\r\n                  // 只有在从第一列开始拖拽时才执行剪切操作\r\n                  // Sortable的clone选项已经控制了复制行为，这里我们只需处理剪切的情况\r\n                  if (dragStartColumn === 1) {\r\n                    newChargeData.splice(evt.oldIndex, 1)\r\n                    this.$emit('return', newChargeData)\r\n                  }\r\n\r\n                  this.$emit('dragRemove', {\r\n                    item: removedItem,\r\n                    oldIndex: evt.oldIndex,\r\n                    from: this,\r\n                    column: dragStartColumn,\r\n                    isCut: dragStartColumn === 1\r\n                  })\r\n                },\r\n                onUpdate: (evt) => {\r\n                  // 同一表格内排序\r\n                  const newChargeData = [...this.localChargeData]\r\n                  const item = newChargeData.splice(evt.oldIndex, 1)[0]\r\n                  newChargeData.splice(evt.newIndex, 0, item)\r\n\r\n                  this.$emit('return', newChargeData)\r\n                  this.$emit('dragUpdate', {\r\n                    item: item,\r\n                    oldIndex: evt.oldIndex,\r\n                    newIndex: evt.newIndex,\r\n                    column: this.dragStartColumn\r\n                  })\r\n                },\r\n                onEnd: (evt) => {\r\n                  // 拖拽结束\r\n                  this.$emit('dragEnd', {\r\n                    ...evt,\r\n                    dragColumn: this.dragStartColumn\r\n                  })\r\n                  // 重置拖拽列\r\n                  this.dragStartColumn = -1\r\n                }\r\n              })\r\n            } catch (error) {\r\n              console.error('Error creating sortable:', error)\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    setRowData({row, rowIndex}) {\r\n      // 为每行设置数据属性，用于拖拽传递数据\r\n      this.$nextTick(() => {\r\n        const tableRows = this.$refs.chargeTable.$el.querySelectorAll('tbody tr')\r\n        if (tableRows[rowIndex]) {\r\n          try {\r\n            tableRows[rowIndex].setAttribute('data-drag-item', JSON.stringify(row))\r\n          } catch (error) {\r\n            console.error('Failed to stringify row data:', error)\r\n            tableRows[rowIndex].setAttribute('data-drag-item', '{}')\r\n          }\r\n        }\r\n      })\r\n      return ''\r\n    },\r\n    auditStatus(status) {\r\n      return status == 1 ? \"已审核\" : \"未审核\"\r\n    },\r\n    selectCharge(target, row) {\r\n      row.dnChargeNameId = target.chargeId\r\n      row.chargeName = target.chargeLocalName\r\n    },\r\n    handleSelectionChange(val) {\r\n      // 处理选择变化\r\n      this.$emit(\"selectRow\", val)\r\n\r\n      this.payTotalUSD = 0\r\n      this.payTotalRMB = 0\r\n      val ? val.map(item => {\r\n        if (item.isRecievingOrPaying == 1) {\r\n          if (item.dnCurrencyCode === \"USD\") {\r\n            this.payTotalUSD = currency(this.payTotalUSD).add(item.subtotal)\r\n          } else {\r\n            this.payTotalRMB = currency(this.payTotalRMB).add(item.subtotal)\r\n          }\r\n\r\n        }\r\n      }) : null\r\n\r\n    },\r\n    currency,\r\n    // 获取项目的唯一键\r\n    getItemKey(item) {\r\n      return item.tempId || item.chargeId || item.id || `item_${Math.random().toString(36).substr(2, 9)}`\r\n    },\r\n    getServiceName(id) {\r\n      let serviceName = \"\"\r\n      this.services.map(obj => {\r\n        obj.value === id ? serviceName = obj.label : null\r\n      })\r\n      return serviceName\r\n    },\r\n    copyFreight(row) {\r\n      if (this.companyList.length > 0) {\r\n        row.payClearingCompanyId = this.companyList[0].companyId\r\n        row.payCompanyName = this.companyList[0].companyShortName\r\n      }\r\n      row.isAccountConfirmed = 0\r\n      // 报价列表跳转订舱时没有公司列表,复制到应收没有客户信息\r\n      let data = this._.cloneDeep(row)\r\n\r\n      this.$emit(\"copyFreight\", {...data, chargeId: null})\r\n    },\r\n    copyAllFreight() {\r\n      if (!this.companyList.length > 0) {\r\n        this.$modal.alertWarning(\"请先选择委托单位或关联单位\")\r\n        return\r\n      }\r\n\r\n      this.chargeData.map(charge => {\r\n        charge.payClearingCompanyId = this.companyList[0].companyId\r\n        charge.payCompanyName = this.companyList[0].companyShortName\r\n        charge.isRecievingOrPaying = 0\r\n        charge.isAccountConfirmed = 0\r\n        charge.chargeId = null\r\n        this.$emit(\"copyFreight\", this._.cloneDeep(charge))\r\n      })\r\n    },\r\n    changeUnitCost(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showCostUnit = false\r\n      })\r\n    },\r\n    changeUnit(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showQuotationUnit = false\r\n      })\r\n    },\r\n    handleChargeSelect(row, data) {\r\n      if (row.chargeLocalName === data.chargeName) {\r\n        row.chargeName = data.chargeLocalName\r\n        row.showQuotationCharge = false\r\n      }\r\n      if (row.currencyCode == null && data.currencyCode) {\r\n        row.dnCurrencyCode = data.currencyCode\r\n      }\r\n    },\r\n    changeCurrency(row, currencyCode) {\r\n      row.dnCurrencyCode = currencyCode\r\n      /* let exchangeRate\r\n      if (currencyCode === \"USD\") {\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && row.dnCurrencyCode == a.overseaCurrency\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      } */\r\n\r\n      this.$nextTick(() => {\r\n        // row.basicCurrencyRate = exchangeRate ? exchangeRate : 1\r\n        row.showQuotationCurrency = false\r\n      })\r\n    },\r\n    /** 序号 */\r\n    rowIndex({row, rowIndex}) {\r\n      row.id = rowIndex + 1\r\n    },\r\n    addReceivablePayable() {\r\n      let obj = {\r\n        showClient: true,\r\n        showSupplier: true,\r\n        showQuotationCharge: true,\r\n        showCostCharge: true,\r\n        showQuotationCurrency: true,\r\n        showCostCurrency: true,\r\n        showQuotationUnit: true,\r\n        showCostUnit: true,\r\n        showStrategy: true,\r\n        showUnitRate: true,\r\n        showAmount: true,\r\n        showCurrencyRate: true,\r\n        showDutyRate: true,\r\n        basicCurrencyRate: 1,\r\n        dutyRate: 0,\r\n        dnAmount: 1,\r\n        // 应收还是应付\r\n        isRecievingOrPaying: this.isReceivable ? 0 : 1,\r\n        clearingCompanyId: this.chargeData.length > 0 ? this.chargeData[this.chargeData.length - 1].clearingCompanyId : null\r\n      }\r\n      if (this.serviceTypeId === 1) obj.sqdServiceTypeId = 1\r\n      if (this.serviceTypeId === 10) obj.sqdServiceTypeId = 10\r\n      if (this.serviceTypeId === 20) obj.sqdServiceTypeId = 20\r\n      if (this.serviceTypeId === 40) obj.sqdServiceTypeId = 40\r\n      if (this.serviceTypeId === 50) obj.sqdServiceTypeId = 50\r\n      if (this.serviceTypeId === 60) obj.sqdServiceTypeId = 60\r\n      if (this.serviceTypeId === 70) obj.sqdServiceTypeId = 70\r\n      if (this.serviceTypeId === 80) obj.sqdServiceTypeId = 80\r\n      if (this.serviceTypeId === 90) obj.sqdServiceTypeId = 90\r\n      if (this.serviceTypeId === 100) obj.sqdServiceTypeId = 100\r\n      if (this.serviceTypeId === 101) obj.sqdServiceTypeId = 101\r\n      this.chargeData.push(obj)\r\n    },\r\n    countProfit(row, category) {\r\n      // 确保所有必要的值都存在且有效\r\n      if (!row) return\r\n\r\n      // 使用currency.js来处理数值,避免精度损失\r\n      const unitRate = row.dnUnitRate || 0\r\n      const amount = row.dnAmount || 0\r\n      const currencyRate = currency(row.basicCurrencyRate || 1, {precision: 4}).value\r\n      const dutyRate = currency(row.dutyRate || 0).value\r\n\r\n      try {\r\n        // 计算小计\r\n        const subtotal = currency(unitRate, {precision: 4})\r\n          .multiply(amount)\r\n          .multiply(currencyRate)\r\n          .multiply(currency(1).add(currency(dutyRate).divide(100)))\r\n          .value\r\n\r\n        // 更新行数据\r\n        row.subtotal = currency(subtotal, {precision: 2}).value\r\n        row.sqdDnCurrencyBalance = row.isAccountConfirmed === \"0\" ? currency(subtotal, {precision: 2}).value : row.sqdDnCurrencyBalance\r\n\r\n        // 根据不同的输入类型关闭对应的编辑状态\r\n        switch (category) {\r\n          case \"strategy\":\r\n            row.showStrategy = false\r\n            break\r\n          case \"unitRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"amount\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"currencyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"dutyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n        }\r\n\r\n        // 触发数据更新\r\n        this.$emit(\"return\", this.chargeData)\r\n\r\n      } catch (error) {\r\n        console.error(\"计算小计时出错:\", error)\r\n        this.$message.error(\"计算小计时出错,请检查输入值是否正确\")\r\n      }\r\n    },\r\n    deleteItem(row) {\r\n      this.$emit(\"deleteItem\", row)\r\n    },\r\n    deleteAllItem(row) {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n    companyNormalizer(node) {\r\n      return {\r\n        id: node.companyId,\r\n        label: (node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\") + \",\" + pinyin.getFullChars((node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\"))\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\ninput:focus {\r\n  outline: none;\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n\r\n// 拖拽样式\r\n::v-deep .sortable-ghost {\r\n  opacity: 0.5;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n::v-deep .sortable-drag {\r\n  opacity: 0.8;\r\n  background-color: #ecf5ff;\r\n  border: 1px dashed #409eff;\r\n}\r\n\r\n// 拖拽时的表格行样式\r\n::v-deep tbody tr {\r\n  cursor: move;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n::v-deep tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n// 禁用状态下不显示拖拽光标\r\n::v-deep tbody tr.disabled {\r\n  cursor: default;\r\n}\r\n</style>\r\n"]}]}