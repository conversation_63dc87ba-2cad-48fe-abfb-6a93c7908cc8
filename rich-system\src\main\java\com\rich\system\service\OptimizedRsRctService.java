package com.rich.system.service;

import com.rich.common.core.domain.entity.RsRct;
import com.rich.common.core.domain.entity.RsServiceInstances;
import com.rich.system.service.context.ServiceProcessingContext;
import com.rich.system.service.processor.ServiceProcessor;
import com.rich.system.service.processor.ServiceProcessorFactory;
import com.rich.system.mapper.RsServiceInstancesMapper;
import com.rich.system.mapper.OptimizedRsChargeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 优化的操作单服务类
 * 实现事务分解、异步处理和批量操作
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class OptimizedRsRctService {
    
    @Autowired
    private ServiceProcessorFactory processorFactory;
    
    @Autowired
    private RsServiceInstancesMapper rsServiceInstancesMapper;
    
    @Autowired
    private OptimizedRsChargeMapper optimizedRsChargeMapper;
    
    /**
     * 异步处理线程池
     */
    private final ExecutorService asyncExecutor = Executors.newFixedThreadPool(5);
    
    /**
     * 优化的保存所有服务方法
     * 使用事务分解和异步处理策略
     * 
     * @param rsRct 操作单数据
     * @return 处理后的操作单数据
     */
    public RsRct saveAllServicesOptimized(RsRct rsRct) {
        log.info("开始优化处理操作单服务，操作单号: {}", rsRct.getRctNo());
        
        try {
            // 第一阶段：准备和验证
            ServiceProcessingContext context = prepareServiceContext(rsRct);
            
            // 第二阶段：处理关键服务（同步）
            processCriticalServices(rsRct, context);
            
            // 第三阶段：处理高优先级服务（小事务）
            processHighPriorityServices(rsRct, context);
            
            // 第四阶段：处理中等优先级服务（批量处理）
            processMediumPriorityServices(rsRct, context);
            
            // 第五阶段：处理低优先级服务（异步）
            processLowPriorityServicesAsync(rsRct, context);
            
            // 第六阶段：清理和完成
            finalizeProcessing(rsRct, context);
            
            context.complete();
            
            log.info("操作单服务处理完成: {}", context.getProcessingSummary());
            
            if (context.hasErrors()) {
                log.warn("处理过程中发生错误: {}", context.getErrorSummary());
            }
            
            return rsRct;
            
        } catch (Exception e) {
            log.error("优化处理操作单服务失败，操作单号: {}，错误: {}", rsRct.getRctNo(), e.getMessage(), e);
            throw new RuntimeException("保存操作单服务失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 准备服务处理上下文
     */
    @Transactional(readOnly = true)
    public ServiceProcessingContext prepareServiceContext(RsRct rsRct) {
        log.debug("准备服务处理上下文，操作单ID: {}", rsRct.getRctId());
        
        // 查询现有服务实例
        List<RsServiceInstances> existingInstances = 
                rsServiceInstancesMapper.selectRsServiceInstancesByRctId(rsRct.getRctId());
        
        // 初始化上下文
        ServiceProcessingContext context = ServiceProcessingContext.initialize(rsRct, existingInstances);
        
        // 统计需要处理的服务
        countServicesToProcess(rsRct, context);
        
        log.debug("服务处理上下文准备完成，预计处理 {} 个服务", 
                context.getStatistics().getTotalServices().get());
        
        return context;
    }
    
    /**
     * 处理关键服务（同步，单独事务）
     */
    @Transactional(rollbackFor = Exception.class)
    public void processCriticalServices(RsRct rsRct, ServiceProcessingContext context) {
        log.debug("开始处理关键服务");
        
        List<ServiceProcessor> criticalProcessors = processorFactory.getCriticalProcessors();
        
        for (ServiceProcessor processor : criticalProcessors) {
            if (processor.canProcess(rsRct, context)) {
                try {
                    processor.process(rsRct, context);
                } catch (Exception e) {
                    log.error("关键服务处理失败: {}", processor.getProcessorName(), e);
                    throw e; // 关键服务失败时直接抛出异常
                }
            }
        }
        
        log.debug("关键服务处理完成");
    }
    
    /**
     * 处理高优先级服务（小事务）
     */
    public void processHighPriorityServices(RsRct rsRct, ServiceProcessingContext context) {
        log.debug("开始处理高优先级服务");
        
        List<ServiceProcessor> highPriorityProcessors = processorFactory.getSortedProcessors()
                .stream()
                .filter(p -> p.getSupportedServiceType().getPriority() == 
                        com.rich.system.service.enums.ServiceTypeEnum.ProcessingPriority.HIGH)
                .collect(Collectors.toList());
        
        for (ServiceProcessor processor : highPriorityProcessors) {
            if (processor.canProcess(rsRct, context)) {
                processServiceInSeparateTransaction(processor, rsRct, context);
            }
        }
        
        log.debug("高优先级服务处理完成");
    }
    
    /**
     * 处理中等优先级服务（批量处理）
     */
    @Transactional(rollbackFor = Exception.class)
    public void processMediumPriorityServices(RsRct rsRct, ServiceProcessingContext context) {
        log.debug("开始批量处理中等优先级服务");
        
        List<ServiceProcessor> mediumPriorityProcessors = processorFactory.getSortedProcessors()
                .stream()
                .filter(p -> p.getSupportedServiceType().getPriority() == 
                        com.rich.system.service.enums.ServiceTypeEnum.ProcessingPriority.MEDIUM)
                .collect(Collectors.toList());
        
        for (ServiceProcessor processor : mediumPriorityProcessors) {
            if (processor.canProcess(rsRct, context)) {
                try {
                    processor.process(rsRct, context);
                } catch (Exception e) {
                    log.warn("中等优先级服务处理失败，继续处理其他服务: {}", 
                            processor.getProcessorName(), e);
                    context.recordError(processor.getProcessorName(), e);
                }
            }
        }
        
        log.debug("中等优先级服务批量处理完成");
    }
    
    /**
     * 异步处理低优先级服务
     */
    public void processLowPriorityServicesAsync(RsRct rsRct, ServiceProcessingContext context) {
        log.debug("开始异步处理低优先级服务");
        
        List<ServiceProcessor> lowPriorityProcessors = processorFactory.getAsyncProcessors();
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (ServiceProcessor processor : lowPriorityProcessors) {
            if (processor.canProcess(rsRct, context)) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    processServiceInSeparateTransaction(processor, rsRct, context);
                }, asyncExecutor);
                
                futures.add(future);
            }
        }
        
        // 等待所有异步任务完成（设置超时）
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));
        
        try {
            allFutures.get(30, java.util.concurrent.TimeUnit.SECONDS);
            log.debug("低优先级服务异步处理完成");
        } catch (Exception e) {
            log.warn("部分低优先级服务异步处理超时或失败", e);
        }
    }
    
    /**
     * 在独立事务中处理服务
     */
    @Transactional(rollbackFor = Exception.class)
    public void processServiceInSeparateTransaction(ServiceProcessor processor, 
                                                   RsRct rsRct, 
                                                   ServiceProcessingContext context) {
        try {
            processor.process(rsRct, context);
        } catch (Exception e) {
            log.warn("服务处理失败: {}", processor.getProcessorName(), e);
            context.recordError(processor.getProcessorName(), e);
        }
    }
    
    /**
     * 完成处理和清理
     */
    @Transactional(rollbackFor = Exception.class)
    public void finalizeProcessing(RsRct rsRct, ServiceProcessingContext context) {
        log.debug("开始最终处理和清理");
        
        // 清理孤立的费用记录
        cleanupOrphanedCharges(rsRct, context);
        
        // 更新操作单状态等
        // updateRctStatus(rsRct, context);
        
        log.debug("最终处理和清理完成");
    }
    
    /**
     * 清理孤立的费用记录
     */
    private void cleanupOrphanedCharges(RsRct rsRct, ServiceProcessingContext context) {
        try {
            // 获取所有活跃的服务ID
            Set<Long> activeServiceIds = context.getExistingServiceInstances().values()
                    .stream()
                    .map(RsServiceInstances::getServiceId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            
            if (!activeServiceIds.isEmpty()) {
                int deletedCount = optimizedRsChargeMapper.deleteOrphanedCharges(
                        rsRct.getRctId(), activeServiceIds);
                
                if (deletedCount > 0) {
                    log.debug("清理了 {} 个孤立的费用记录", deletedCount);
                }
            }
        } catch (Exception e) {
            log.warn("清理孤立费用记录失败", e);
        }
    }
    
    /**
     * 统计需要处理的服务数量
     */
    private void countServicesToProcess(RsRct rsRct, ServiceProcessingContext context) {
        for (ServiceProcessor processor : processorFactory.getAllProcessors()) {
            if (processor.canProcess(rsRct, context)) {
                context.addServiceToStatistics(processor.getProcessorName());
            }
        }
    }
}
