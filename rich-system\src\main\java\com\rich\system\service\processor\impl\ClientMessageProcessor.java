package com.rich.system.service.processor.impl;

import com.rich.common.core.domain.entity.RsRct;
import com.rich.common.core.domain.entity.RsServiceInstances;
import com.rich.common.core.domain.entity.RsClientMessage;
import com.rich.system.service.context.ServiceProcessingContext;
import com.rich.system.service.enums.ServiceTypeEnum;
import com.rich.system.service.processor.AbstractServiceProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 客户信息处理器
 * 处理客户相关信息的保存和更新
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ClientMessageProcessor extends AbstractServiceProcessor {
    
    @Override
    public ServiceTypeEnum getSupportedServiceType() {
        return ServiceTypeEnum.CLIENT_MESSAGE;
    }
    
    @Override
    public boolean canProcess(RsRct rsRct, ServiceProcessingContext context) {
        return rsRct.getRsClientMessage() != null;
    }
    
    @Override
    protected void doProcess(RsRct rsRct, ServiceProcessingContext context) throws Exception {
        RsClientMessage clientMessage = rsRct.getRsClientMessage();
        if (clientMessage == null) {
            log.debug("客户信息为空，跳过处理");
            return;
        }
        
        // 处理服务实例
        RsServiceInstances serviceInstance = clientMessage.getRsServiceInstances();
        if (serviceInstance == null) {
            serviceInstance = getOrCreateServiceInstance(rsRct, context);
            clientMessage.setRsServiceInstances(serviceInstance);
        }
        
        // 处理服务实例
        serviceInstance = processServiceInstance(serviceInstance, rsRct, context);
        
        // 处理费用记录
        if (clientMessage.getRsChargeList() != null && !clientMessage.getRsChargeList().isEmpty()) {
            processCharges(clientMessage.getRsChargeList(), serviceInstance, rsRct);
        }
        
        // 处理操作日志
        if (clientMessage.getRsOpLogList() != null && !clientMessage.getRsOpLogList().isEmpty()) {
            processOpLogs(clientMessage.getRsOpLogList(), serviceInstance, rsRct);
        }
        
        log.debug("客户信息处理完成，服务ID: {}", serviceInstance.getServiceId());
    }
    
    @Override
    public void preProcess(RsRct rsRct, ServiceProcessingContext context) throws Exception {
        super.preProcess(rsRct, context);
        
        RsClientMessage clientMessage = rsRct.getRsClientMessage();
        if (clientMessage != null) {
            // 验证客户信息的必要字段
            if (clientMessage.getRsServiceInstances() == null) {
                log.debug("客户信息缺少服务实例，将自动创建");
            }
        }
    }
    
    @Override
    public long getEstimatedProcessingTime(RsRct rsRct) {
        // 客户信息处理相对简单，估计500毫秒
        return 500L;
    }
}
