package com.rich.system.service.processor.impl;

import com.rich.common.core.domain.entity.RsRct;
import com.rich.common.core.domain.entity.RsServiceInstances;
import com.rich.common.core.domain.entity.RsOpSeaFcl;
import com.rich.common.utils.DateUtils;
import com.rich.system.service.context.ServiceProcessingContext;
import com.rich.system.service.enums.ServiceTypeEnum;
import com.rich.system.service.processor.AbstractServiceProcessor;
import com.rich.system.mapper.RsOpSeaFclMapper;
import com.rich.common.utils.RedisIdGeneratorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 海运整箱服务处理器
 * 处理海运整箱相关的服务数据
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SeaFclProcessor extends AbstractServiceProcessor {
    
    @Autowired
    private RsOpSeaFclMapper rsOpSeaFclMapper;
    
    @Autowired
    private RedisIdGeneratorService redisIdGeneratorService;
    
    @Override
    public ServiceTypeEnum getSupportedServiceType() {
        return ServiceTypeEnum.SEA_FCL;
    }
    
    @Override
    public boolean canProcess(RsRct rsRct, ServiceProcessingContext context) {
        return rsRct.getRsOpSeaFclList() != null && !rsRct.getRsOpSeaFclList().isEmpty();
    }
    
    @Override
    protected void doProcess(RsRct rsRct, ServiceProcessingContext context) throws Exception {
        List<RsOpSeaFcl> seaFclList = rsRct.getRsOpSeaFclList();
        if (seaFclList == null || seaFclList.isEmpty()) {
            log.debug("海运整箱服务列表为空，跳过处理");
            return;
        }
        
        Long rctId = rsRct.getRctId();
        
        // 查询现有记录
        List<RsOpSeaFcl> existingList = rsOpSeaFclMapper.selectRsOpSeaFclByRctId(rctId, 1L);
        
        // 找出需要删除的记录
        List<RsOpSeaFcl> toDeleteList = existingList.stream()
                .filter(existing -> seaFclList.stream()
                        .noneMatch(newItem -> newItem.getSeaId() != null &&
                                newItem.getSeaId().equals(existing.getSeaId())))
                .collect(Collectors.toList());
        
        // 删除不再需要的记录
        deleteObsoleteRecords(toDeleteList);
        
        // 处理每个海运整箱服务
        for (RsOpSeaFcl seaFcl : seaFclList) {
            processSeaFclItem(seaFcl, rsRct, context);
        }
        
        log.debug("海运整箱服务处理完成，处理了 {} 个服务项", seaFclList.size());
    }
    
    /**
     * 处理单个海运整箱服务项
     */
    private void processSeaFclItem(RsOpSeaFcl seaFcl, RsRct rsRct, ServiceProcessingContext context) {
        // 处理服务实例
        RsServiceInstances serviceInstance = seaFcl.getRsServiceInstances();
        if (serviceInstance == null) {
            serviceInstance = getOrCreateServiceInstance(rsRct, context);
            seaFcl.setRsServiceInstances(serviceInstance);
        }
        
        serviceInstance = processServiceInstance(serviceInstance, rsRct, context);
        
        // 设置海运整箱服务的基本信息
        seaFcl.setServiceId(serviceInstance.getServiceId());
        seaFcl.setSqdRctNo(rsRct.getRctNo());
        seaFcl.setSqdServiceTypeId(1L);
        
        // 如果是新记录，生成PSA编号
        if (seaFcl.getSeaId() == null) {
            String psaNo = redisIdGeneratorService.generateUniqueId("psa_no");
            String date = DateUtils.dateTime();
            seaFcl.setSqdPsaNo("PSA" + date.substring(2, 6) + psaNo);
        }
        
        // 保存或更新海运整箱服务记录
        rsOpSeaFclMapper.upsertRsOpSeaFcl(seaFcl);
        
        // 处理费用记录
        if (seaFcl.getRsChargeList() != null && !seaFcl.getRsChargeList().isEmpty()) {
            processCharges(seaFcl.getRsChargeList(), serviceInstance, rsRct);
        }
        
        // 处理操作日志
        if (seaFcl.getRsOpLogList() != null && !seaFcl.getRsOpLogList().isEmpty()) {
            processOpLogs(seaFcl.getRsOpLogList(), serviceInstance, rsRct);
        }
    }
    
    /**
     * 删除过时的记录
     */
    private void deleteObsoleteRecords(List<RsOpSeaFcl> toDeleteList) {
        if (toDeleteList.isEmpty()) {
            return;
        }
        
        for (RsOpSeaFcl seaFcl : toDeleteList) {
            // 删除海运整箱服务记录
            rsOpSeaFclMapper.deleteRsOpSeaFclBySeaFclId(seaFcl.getSeaId());
            
            // 删除关联的服务实例
            if (seaFcl.getRsServiceInstances() != null && 
                seaFcl.getRsServiceInstances().getServiceId() != null) {
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(
                    seaFcl.getRsServiceInstances().getServiceId());
            }
        }
        
        log.debug("删除了 {} 个过时的海运整箱服务记录", toDeleteList.size());
    }
    
    @Override
    public void preProcess(RsRct rsRct, ServiceProcessingContext context) throws Exception {
        super.preProcess(rsRct, context);
        
        List<RsOpSeaFcl> seaFclList = rsRct.getRsOpSeaFclList();
        if (seaFclList != null) {
            // 验证海运整箱服务的必要字段
            for (RsOpSeaFcl seaFcl : seaFclList) {
                if (seaFcl.getRsServiceInstances() == null) {
                    log.debug("海运整箱服务缺少服务实例，将自动创建");
                }
            }
        }
    }
    
    @Override
    public long getEstimatedProcessingTime(RsRct rsRct) {
        List<RsOpSeaFcl> seaFclList = rsRct.getRsOpSeaFclList();
        if (seaFclList == null || seaFclList.isEmpty()) {
            return 0L;
        }
        
        // 每个海运整箱服务项估计处理时间为800毫秒
        return seaFclList.size() * 800L;
    }
}
