{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\login.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\login.vue", "mtime": 1754646305884}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "_js<PERSON><PERSON>ie", "_interopRequireDefault", "_jsencrypt", "_fingerprintjs", "_fingerprintjs2", "_WechatScan", "name", "components", "WechatScan", "data", "mac", "codeUrl", "loginForm", "username", "password", "rememberMe", "code", "uuid", "unid", "loginRules", "required", "trigger", "message", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "register", "redirect", "undefined", "showWechatScan", "showDebugOptions", "debugClickCount", "enableWechatVerify", "process", "env", "NODE_ENV", "wechatScanCookieName", "watch", "$route", "handler", "route", "query", "immediate", "val", "localStorage", "setItem", "toString", "created", "getCode", "<PERSON><PERSON><PERSON><PERSON>", "storedSetting", "getItem", "methods", "toggleDebugMode", "$message", "success", "incrementDebug<PERSON>ounter", "getMac", "_this", "getFingerPrint", "v", "$alert", "callback", "action", "navigator", "clipboard", "writeText", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee2", "options", "wrap", "_callee2$", "_context2", "prev", "next", "Fingerprint2", "Options", "excludes", "webdriver", "userAgent", "language", "colorDepth", "deviceMemory", "pixelRatio", "hardwareConcurrency", "screenResolution", "availableScreenResolution", "timezoneOffset", "timezone", "sessionStorage", "indexedDb", "add<PERSON>eh<PERSON>or", "openDatabase", "cpuClass", "platform", "doNotTrack", "plugins", "canvas", "webgl", "webglVendorAndRenderer", "adBlock", "hasLiedLanguages", "hasLiedResolution", "hasLiedOs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touchSupport", "fonts", "fontsFlash", "audio", "enumerateDevices", "get", "_ref", "_callee", "values", "murmur", "fp", "result", "_callee$", "_context", "map", "component", "index", "value", "x64hash128", "join", "Fingerprint", "load", "sent", "visitorId", "stop", "_x", "apply", "arguments", "logCode", "_callee3", "_callee3$", "_context3", "console", "log", "abrupt", "_this2", "getCodeImg", "then", "res", "img", "Cookies", "decrypt", "Boolean", "handleLogin", "_this3", "$refs", "validate", "valid", "set", "expires", "encrypt", "remove", "checkNeedWechatScan", "_this4", "_callee4", "needScan", "isWechatBound", "scanVerified", "_callee4$", "_context4", "do<PERSON><PERSON><PERSON>", "info", "t0", "error", "_this5", "$store", "dispatch", "$router", "push", "path", "catch", "handleWechatScanSuccess", "exports", "_default"], "sources": ["src/views/login.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\" :show-message=\"false\"\r\n             v-loading=\"mac\" status-icon\r\n    >\r\n      <div class=\"title\">\r\n        <h3 style=\"margin: auto;width: fit-content\" @dblclick=\"toggleDebugMode\">瑞旗系统</h3>\r\n        <!-- 添加调试区域 -->\r\n        <!--<div v-if=\"showDebugOptions\" class=\"debug-options\">-->\r\n        <!--  <el-checkbox v-model=\"enableWechatVerify\" border size=\"mini\">启用微信验证</el-checkbox>-->\r\n        <!--</div>-->\r\n      </div>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"loginForm.username\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n          type=\"text\"\r\n        >\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"loginForm.password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          type=\"password\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item v-if=\"captchaEnabled\" prop=\"code\">\r\n        <el-input\r\n          v-model=\"loginForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleLogin\"\r\n        >\r\n        </el-input>\r\n        <div class=\"login-code\">\r\n          <img :src=\"codeUrl\" class=\"login-code-img\" @click=\"getCode\"/>\r\n        </div>\r\n      </el-form-item>\r\n      <el-checkbox v-model=\"loginForm.rememberMe\" style=\"margin:0px 0px 25px 0px;\">记住密码</el-checkbox>\r\n      <el-form-item style=\"width:100%;\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          style=\"width:100%;\"\r\n          type=\"primary\"\r\n          @click=\"handleLogin\"\r\n        >\r\n          <span v-if=\"!loading\">登 录</span>\r\n          <span v-else>登 录 中...</span>\r\n        </el-button>\r\n        <div v-if=\"register\" style=\"float: right;\">\r\n          <router-link :to=\"'/register'\" class=\"link-type\">立即注册</router-link>\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-login-footer\">\r\n      <span @click=\"incrementDebugCounter\">Copyright © 2009-2024 RichShipping All Rights Reserved.</span>\r\n    </div>\r\n\r\n    <!-- 微信扫码登录弹窗 -->\r\n    <wechat-scan\r\n      v-if=\"showWechatScan\"\r\n      :visible.sync=\"showWechatScan\"\r\n      :username=\"loginForm.username\"\r\n      @scan-success=\"handleWechatScanSuccess\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getCodeImg, getMac, checkNeedWechatScan} from '@/api/login'\r\nimport Cookies from 'js-cookie'\r\nimport {decrypt, encrypt} from '@/utils/jsencrypt'\r\nimport Fingerprint2 from 'fingerprintjs2'\r\nimport Fingerprint from '@fingerprintjs/fingerprintjs'\r\nimport WechatScan from '@/components/WechatScan'\r\n\r\nexport default {\r\n  name: 'Login',\r\n  components: {\r\n    WechatScan\r\n  },\r\n  data() {\r\n    return {\r\n      mac: false,\r\n      codeUrl: '',\r\n      loginForm: {\r\n        username: '',\r\n        password: '',\r\n        rememberMe: false,\r\n        code: '',\r\n        uuid: '',\r\n        unid: ''\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          {required: true, trigger: 'blur', message: '您的账号'}\r\n        ],\r\n        password: [\r\n          {required: true, trigger: 'blur', message: '您的密码'}\r\n        ],\r\n        code: [{required: true, trigger: 'change', message: '验证码'}]\r\n      },\r\n      loading: false,\r\n      // 验证码开关\r\n      captchaEnabled: true,\r\n      // 注册开关\r\n      register: false,\r\n      redirect: undefined,\r\n      // 微信扫码登录\r\n      showWechatScan: false,\r\n      // 调试选项\r\n      showDebugOptions: false,\r\n      debugClickCount: 0,\r\n      // 微信验证开关 - 根据环境设置，生产环境开启，开发环境关闭\r\n      enableWechatVerify: process.env.NODE_ENV === 'production',\r\n      // 微信扫码验证Cookie名\r\n      wechatScanCookieName: 'wechat_scan_verified'\r\n    }\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function (route) {\r\n        this.redirect = route.query && route.query.redirect\r\n      },\r\n      immediate: true\r\n    },\r\n    // 监听微信验证开关变化，保存到本地存储\r\n    enableWechatVerify: {\r\n      handler(val) {\r\n        localStorage.setItem('debug_enable_wechat_verify', val.toString());\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getCode()\r\n    this.getCookie()\r\n    // 根据环境设置微信验证开关，且允许本地存储覆盖(仅在调试模式下)\r\n    const storedSetting = localStorage.getItem('debug_enable_wechat_verify');\r\n    if (this.showDebugOptions && storedSetting !== null) {\r\n      this.enableWechatVerify = storedSetting === 'true';\r\n    } else {\r\n      // 默认根据环境设置：生产环境开启，开发环境关闭\r\n      this.enableWechatVerify = process.env.NODE_ENV === 'production';\r\n      // 更新本地存储\r\n      localStorage.setItem('debug_enable_wechat_verify', this.enableWechatVerify.toString());\r\n    }\r\n  },\r\n  methods: {\r\n    // 切换调试模式\r\n    toggleDebugMode() {\r\n      // 双击标题时显示调试选项\r\n      this.showDebugOptions = !this.showDebugOptions;\r\n      if (this.showDebugOptions) {\r\n        this.$message.success('已' + (this.enableWechatVerify ? '启用' : '关闭') + '微信验证');\r\n      }\r\n    },\r\n    // 点击版权信息增加计数器\r\n    incrementDebugCounter() {\r\n      this.debugClickCount++;\r\n      if (this.debugClickCount >= 5) {\r\n        this.showDebugOptions = true;\r\n        this.debugClickCount = 0;\r\n        this.$message.success('已' + (this.enableWechatVerify ? '启用' : '关闭') + '微信验证');\r\n      }\r\n    },\r\n    getMac() {\r\n      this.mac = true\r\n      this.getFingerPrint(v => {\r\n        this.$alert(v, '', {\r\n          callback: action => {\r\n            if (action == 'confirm') {\r\n              this.$message.success('已复制')\r\n              this.mac = false\r\n              navigator.clipboard.writeText(v)\r\n            }\r\n            if (action == 'cancel') {\r\n              this.mac = false\r\n            }\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async getFingerPrint(callback) {\r\n      let options = Fingerprint2.Options = {\r\n        excludes: {\r\n          webdriver: true,\r\n          userAgent: true,\r\n          language: true,\r\n          colorDepth: true,\r\n          deviceMemory: true,\r\n          pixelRatio: true,\r\n          hardwareConcurrency: true,\r\n          screenResolution: true,\r\n          availableScreenResolution: true,\r\n          timezoneOffset: true,\r\n          timezone: true,\r\n          sessionStorage: true,\r\n          localStorage: true,\r\n          indexedDb: true,\r\n          addBehavior: true,\r\n          openDatabase: true,\r\n          cpuClass: true,\r\n          platform: true,\r\n          doNotTrack: true,\r\n          plugins: true,\r\n          canvas: true,\r\n          webgl: false,\r\n          webglVendorAndRenderer: false,\r\n          adBlock: true,\r\n          hasLiedLanguages: true,\r\n          hasLiedResolution: true,\r\n          hasLiedOs: true,\r\n          hasLiedBrowser: true,\r\n          touchSupport: true,\r\n          fonts: true,\r\n          fontsFlash: true,\r\n          audio: false,\r\n          enumerateDevices: true\r\n        }\r\n      }\r\n      Fingerprint2.get(options, async (components) => {\r\n        const values = components.map(function (component, index) {\r\n            return component.value\r\n          })\r\n          const murmur = Fingerprint2.x64hash128(values.join(''), 31)\r\n        const fp = await Fingerprint.load()\r\n        const result = await fp.get()\r\n        callback(result.visitorId)\r\n        }\r\n      )\r\n    },\r\n    async logCode() {\r\n      const fp = await Fingerprint.load()\r\n      const result = await fp.get()\r\n      console.log('Browser fingerprint:', result.visitorId)\r\n      return result.visitorId\r\n    },\r\n    getCode() {\r\n      getCodeImg().then(res => {\r\n        this.captchaEnabled = res.captchaEnabled == undefined ? true : res.captchaEnabled\r\n        if (this.captchaEnabled) {\r\n          this.codeUrl = 'data:image/gif;base64,' + res.img\r\n          this.loginForm.uuid = res.uuid\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    getCookie() {\r\n      const username = Cookies.get('username')\r\n      const password = Cookies.get('password')\r\n      const rememberMe = Cookies.get('rememberMe')\r\n      this.loginForm = {\r\n        username: username == undefined ? this.loginForm.username : username,\r\n        password: password == undefined ? this.loginForm.password : decrypt(password),\r\n        rememberMe: rememberMe == undefined ? false : Boolean(rememberMe)\r\n      }\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true\r\n          if (this.loginForm.rememberMe) {\r\n            Cookies.set('username', this.loginForm.username, {expires: 30})\r\n            Cookies.set('password', encrypt(this.loginForm.password), {expires: 30})\r\n            Cookies.set('rememberMe', this.loginForm.rememberMe, {expires: 30})\r\n          } else {\r\n            Cookies.remove('username')\r\n            Cookies.remove('password')\r\n            Cookies.remove('rememberMe')\r\n          }\r\n          this.getFingerPrint(v => {\r\n            this.loginForm.unid = v\r\n\r\n            // 检查是否需要微信扫码验证\r\n            this.checkNeedWechatScan()\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 检查是否需要微信扫码验证\r\n    async checkNeedWechatScan() {\r\n      // 如果微信验证开关关闭，直接登录\r\n      if (!this.enableWechatVerify) {\r\n        console.log('微信验证已关闭，直接登录');\r\n        this.doLogin();\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const res = await checkNeedWechatScan(this.loginForm.username)\r\n        if (res.code === 200) {\r\n          const needScan = res.data.needScan\r\n          const isWechatBound = res.data.isWechatBound;\r\n\r\n          // 检查Cookie中是否存在微信扫码验证标记\r\n          const scanVerified = Cookies.get(this.wechatScanCookieName);\r\n          if (needScan && !scanVerified) {\r\n            // 需要微信扫码验证\r\n            this.showWechatScan = true;\r\n            this.loading = false;\r\n\r\n            // 如果用户未绑定微信，显示提示\r\n            if (!isWechatBound) {\r\n              this.$message.info('首次登录需要绑定微信账号进行验证');\r\n            }\r\n          } else {\r\n            // 不需要微信扫码验证，直接登录\r\n            this.doLogin();\r\n          }\r\n        } else {\r\n          // 检查失败，直接登录\r\n          this.doLogin();\r\n        }\r\n      } catch (error) {\r\n        console.error('检查微信扫码验证失败', error);\r\n        // 发生错误，直接登录\r\n        this.doLogin();\r\n      }\r\n    },\r\n\r\n    // 执行登录\r\n    doLogin() {\r\n      this.$store.dispatch('Login', this.loginForm).then(() => {\r\n        // 登录成功后，设置微信扫码验证标记Cookie，有效期24小时\r\n        Cookies.set(this.wechatScanCookieName, 'true', {expires: 1});\r\n        this.$router.push({path: this.redirect || '/'}).catch(() => {\r\n        })\r\n      }).catch(() => {\r\n        this.loading = false\r\n        if (this.captchaEnabled) {\r\n          this.getCode()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 微信扫码登录成功回调\r\n    handleWechatScanSuccess() {\r\n      this.showWechatScan = false\r\n      this.doLogin()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" rel=\"stylesheet/scss\">\r\n.login {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n\r\n.title {\r\n  margin: 0 auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.login-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n\r\n  .el-input {\r\n    height: 38px;\r\n\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n\r\n.login-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n\r\n.login-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n\r\n.el-login-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial, serif;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.login-code-img {\r\n  height: 38px;\r\n}\r\n\r\n/* 添加调试选项样式 */\r\n.debug-options {\r\n  margin-top: 10px;\r\n  padding: 5px;\r\n  background-color: rgba(255, 255, 100, 0.2);\r\n  border: 1px dashed #e6a23c;\r\n  border-radius: 4px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AA6EA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,eAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,WAAA,GAAAJ,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAO,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;MACAC,OAAA;MACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,UAAA;QACAN,QAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,IAAA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,OAAA;MACA;MACAC,cAAA;MACA;MACAC,QAAA;MACAC,QAAA,EAAAC,SAAA;MACA;MACAC,cAAA;MACA;MACAC,gBAAA;MACAC,eAAA;MACA;MACAC,kBAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;MACA;MACAC,oBAAA;IACA;EACA;EACAC,KAAA;IACAC,MAAA;MACAC,OAAA,WAAAA,QAAAC,KAAA;QACA,KAAAb,QAAA,GAAAa,KAAA,CAAAC,KAAA,IAAAD,KAAA,CAAAC,KAAA,CAAAd,QAAA;MACA;MACAe,SAAA;IACA;IACA;IACAV,kBAAA;MACAO,OAAA,WAAAA,QAAAI,GAAA;QACAC,YAAA,CAAAC,OAAA,+BAAAF,GAAA,CAAAG,QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,SAAA;IACA;IACA,IAAAC,aAAA,GAAAN,YAAA,CAAAO,OAAA;IACA,SAAArB,gBAAA,IAAAoB,aAAA;MACA,KAAAlB,kBAAA,GAAAkB,aAAA;IACA;MACA;MACA,KAAAlB,kBAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;MACA;MACAS,YAAA,CAAAC,OAAA,oCAAAb,kBAAA,CAAAc,QAAA;IACA;EACA;EACAM,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA;MACA,KAAAvB,gBAAA,SAAAA,gBAAA;MACA,SAAAA,gBAAA;QACA,KAAAwB,QAAA,CAAAC,OAAA,aAAAvB,kBAAA;MACA;IACA;IACA;IACAwB,qBAAA,WAAAA,sBAAA;MACA,KAAAzB,eAAA;MACA,SAAAA,eAAA;QACA,KAAAD,gBAAA;QACA,KAAAC,eAAA;QACA,KAAAuB,QAAA,CAAAC,OAAA,aAAAvB,kBAAA;MACA;IACA;IACAyB,MAAA,WAAAA,OAAA;MAAA,IAAAC,KAAA;MACA,KAAA/C,GAAA;MACA,KAAAgD,cAAA,WAAAC,CAAA;QACAF,KAAA,CAAAG,MAAA,CAAAD,CAAA;UACAE,QAAA,WAAAA,SAAAC,MAAA;YACA,IAAAA,MAAA;cACAL,KAAA,CAAAJ,QAAA,CAAAC,OAAA;cACAG,KAAA,CAAA/C,GAAA;cACAqD,SAAA,CAAAC,SAAA,CAAAC,SAAA,CAAAN,CAAA;YACA;YACA,IAAAG,MAAA;cACAL,KAAA,CAAA/C,GAAA;YACA;UACA;QACA;MACA;IACA;IACAgD,cAAA,WAAAA,eAAAG,QAAA;MAAA,WAAAK,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,SAAA;QAAA,IAAAC,OAAA;QAAA,WAAAH,oBAAA,CAAAD,OAAA,IAAAK,IAAA,UAAAC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAC,IAAA,GAAAD,SAAA,CAAAE,IAAA;YAAA;cACAL,OAAA,GAAAM,sBAAA,CAAAC,OAAA;gBACAC,QAAA;kBACAC,SAAA;kBACAC,SAAA;kBACAC,QAAA;kBACAC,UAAA;kBACAC,YAAA;kBACAC,UAAA;kBACAC,mBAAA;kBACAC,gBAAA;kBACAC,yBAAA;kBACAC,cAAA;kBACAC,QAAA;kBACAC,cAAA;kBACAhD,YAAA;kBACAiD,SAAA;kBACAC,WAAA;kBACAC,YAAA;kBACAC,QAAA;kBACAC,QAAA;kBACAC,UAAA;kBACAC,OAAA;kBACAC,MAAA;kBACAC,KAAA;kBACAC,sBAAA;kBACAC,OAAA;kBACAC,gBAAA;kBACAC,iBAAA;kBACAC,SAAA;kBACAC,cAAA;kBACAC,YAAA;kBACAC,KAAA;kBACAC,UAAA;kBACAC,KAAA;kBACAC,gBAAA;gBACA;cACA;cACAlC,sBAAA,CAAAmC,GAAA,CAAAzC,OAAA;gBAAA,IAAA0C,IAAA,OAAA/C,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA6C,QAAA3G,UAAA;kBAAA,IAAA4G,MAAA,EAAAC,MAAA,EAAAC,EAAA,EAAAC,MAAA;kBAAA,WAAAlD,oBAAA,CAAAD,OAAA,IAAAK,IAAA,UAAA+C,SAAAC,QAAA;oBAAA,kBAAAA,QAAA,CAAA7C,IAAA,GAAA6C,QAAA,CAAA5C,IAAA;sBAAA;wBACAuC,MAAA,GAAA5G,UAAA,CAAAkH,GAAA,WAAAC,SAAA,EAAAC,KAAA;0BACA,OAAAD,SAAA,CAAAE,KAAA;wBACA;wBACAR,MAAA,GAAAvC,sBAAA,CAAAgD,UAAA,CAAAV,MAAA,CAAAW,IAAA;wBAAAN,QAAA,CAAA5C,IAAA;wBAAA,OACAmD,uBAAA,CAAAC,IAAA;sBAAA;wBAAAX,EAAA,GAAAG,QAAA,CAAAS,IAAA;wBAAAT,QAAA,CAAA5C,IAAA;wBAAA,OACAyC,EAAA,CAAAL,GAAA;sBAAA;wBAAAM,MAAA,GAAAE,QAAA,CAAAS,IAAA;wBACApE,QAAA,CAAAyD,MAAA,CAAAY,SAAA;sBAAA;sBAAA;wBAAA,OAAAV,QAAA,CAAAW,IAAA;oBAAA;kBAAA,GAAAjB,OAAA;gBAAA,CACA;gBAAA,iBAAAkB,EAAA;kBAAA,OAAAnB,IAAA,CAAAoB,KAAA,OAAAC,SAAA;gBAAA;cAAA,GACA;YAAA;YAAA;cAAA,OAAA5D,SAAA,CAAAyD,IAAA;UAAA;QAAA,GAAA7D,QAAA;MAAA;IACA;IACAiE,OAAA,WAAAA,QAAA;MAAA,WAAArE,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAmE,SAAA;QAAA,IAAAnB,EAAA,EAAAC,MAAA;QAAA,WAAAlD,oBAAA,CAAAD,OAAA,IAAAK,IAAA,UAAAiE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/D,IAAA,GAAA+D,SAAA,CAAA9D,IAAA;YAAA;cAAA8D,SAAA,CAAA9D,IAAA;cAAA,OACAmD,uBAAA,CAAAC,IAAA;YAAA;cAAAX,EAAA,GAAAqB,SAAA,CAAAT,IAAA;cAAAS,SAAA,CAAA9D,IAAA;cAAA,OACAyC,EAAA,CAAAL,GAAA;YAAA;cAAAM,MAAA,GAAAoB,SAAA,CAAAT,IAAA;cACAU,OAAA,CAAAC,GAAA,yBAAAtB,MAAA,CAAAY,SAAA;cAAA,OAAAQ,SAAA,CAAAG,MAAA,WACAvB,MAAA,CAAAY,SAAA;YAAA;YAAA;cAAA,OAAAQ,SAAA,CAAAP,IAAA;UAAA;QAAA,GAAAK,QAAA;MAAA;IACA;IACAzF,OAAA,WAAAA,QAAA;MAAA,IAAA+F,MAAA;MACA,IAAAC,iBAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAAtH,cAAA,GAAAyH,GAAA,CAAAzH,cAAA,IAAAG,SAAA,UAAAsH,GAAA,CAAAzH,cAAA;QACA,IAAAsH,MAAA,CAAAtH,cAAA;UACAsH,MAAA,CAAAnI,OAAA,8BAAAsI,GAAA,CAAAC,GAAA;UACAJ,MAAA,CAAAlI,SAAA,CAAAK,IAAA,GAAAgI,GAAA,CAAAhI,IAAA;QACA;MACA;IACA;IAEA+B,SAAA,WAAAA,UAAA;MACA,IAAAnC,QAAA,GAAAsI,iBAAA,CAAAnC,GAAA;MACA,IAAAlG,QAAA,GAAAqI,iBAAA,CAAAnC,GAAA;MACA,IAAAjG,UAAA,GAAAoI,iBAAA,CAAAnC,GAAA;MACA,KAAApG,SAAA;QACAC,QAAA,EAAAA,QAAA,IAAAc,SAAA,QAAAf,SAAA,CAAAC,QAAA,GAAAA,QAAA;QACAC,QAAA,EAAAA,QAAA,IAAAa,SAAA,QAAAf,SAAA,CAAAE,QAAA,OAAAsI,kBAAA,EAAAtI,QAAA;QACAC,UAAA,EAAAA,UAAA,IAAAY,SAAA,WAAA0H,OAAA,CAAAtI,UAAA;MACA;IACA;IACAuI,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA5I,SAAA,CAAA6I,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAhI,OAAA;UACA,IAAAgI,MAAA,CAAA3I,SAAA,CAAAG,UAAA;YACAoI,iBAAA,CAAAQ,GAAA,aAAAJ,MAAA,CAAA3I,SAAA,CAAAC,QAAA;cAAA+I,OAAA;YAAA;YACAT,iBAAA,CAAAQ,GAAA,iBAAAE,kBAAA,EAAAN,MAAA,CAAA3I,SAAA,CAAAE,QAAA;cAAA8I,OAAA;YAAA;YACAT,iBAAA,CAAAQ,GAAA,eAAAJ,MAAA,CAAA3I,SAAA,CAAAG,UAAA;cAAA6I,OAAA;YAAA;UACA;YACAT,iBAAA,CAAAW,MAAA;YACAX,iBAAA,CAAAW,MAAA;YACAX,iBAAA,CAAAW,MAAA;UACA;UACAP,MAAA,CAAA7F,cAAA,WAAAC,CAAA;YACA4F,MAAA,CAAA3I,SAAA,CAAAM,IAAA,GAAAyC,CAAA;;YAEA;YACA4F,MAAA,CAAAQ,mBAAA;UACA;QACA;MACA;IACA;IAEA;IACAA,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MAAA,WAAA9F,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA4F,SAAA;QAAA,IAAAhB,GAAA,EAAAiB,QAAA,EAAAC,aAAA,EAAAC,YAAA;QAAA,WAAAhG,oBAAA,CAAAD,OAAA,IAAAK,IAAA,UAAA6F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3F,IAAA,GAAA2F,SAAA,CAAA1F,IAAA;YAAA;cAAA,IAEAoF,MAAA,CAAAjI,kBAAA;gBAAAuI,SAAA,CAAA1F,IAAA;gBAAA;cAAA;cACA+D,OAAA,CAAAC,GAAA;cACAoB,MAAA,CAAAO,OAAA;cAAA,OAAAD,SAAA,CAAAzB,MAAA;YAAA;cAAAyB,SAAA,CAAA3F,IAAA;cAAA2F,SAAA,CAAA1F,IAAA;cAAA,OAKA,IAAAmF,0BAAA,EAAAC,MAAA,CAAApJ,SAAA,CAAAC,QAAA;YAAA;cAAAoI,GAAA,GAAAqB,SAAA,CAAArC,IAAA;cACA,IAAAgB,GAAA,CAAAjI,IAAA;gBACAkJ,QAAA,GAAAjB,GAAA,CAAAxI,IAAA,CAAAyJ,QAAA;gBACAC,aAAA,GAAAlB,GAAA,CAAAxI,IAAA,CAAA0J,aAAA,EAEA;gBACAC,YAAA,GAAAjB,iBAAA,CAAAnC,GAAA,CAAAgD,MAAA,CAAA7H,oBAAA;gBACA,IAAA+H,QAAA,KAAAE,YAAA;kBACA;kBACAJ,MAAA,CAAApI,cAAA;kBACAoI,MAAA,CAAAzI,OAAA;;kBAEA;kBACA,KAAA4I,aAAA;oBACAH,MAAA,CAAA3G,QAAA,CAAAmH,IAAA;kBACA;gBACA;kBACA;kBACAR,MAAA,CAAAO,OAAA;gBACA;cACA;gBACA;gBACAP,MAAA,CAAAO,OAAA;cACA;cAAAD,SAAA,CAAA1F,IAAA;cAAA;YAAA;cAAA0F,SAAA,CAAA3F,IAAA;cAAA2F,SAAA,CAAAG,EAAA,GAAAH,SAAA;cAEA3B,OAAA,CAAA+B,KAAA,eAAAJ,SAAA,CAAAG,EAAA;cACA;cACAT,MAAA,CAAAO,OAAA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAnC,IAAA;UAAA;QAAA,GAAA8B,QAAA;MAAA;IAEA;IAEA;IACAM,OAAA,WAAAA,QAAA;MAAA,IAAAI,MAAA;MACA,KAAAC,MAAA,CAAAC,QAAA,eAAAjK,SAAA,EAAAoI,IAAA;QACA;QACAG,iBAAA,CAAAQ,GAAA,CAAAgB,MAAA,CAAAxI,oBAAA;UAAAyH,OAAA;QAAA;QACAe,MAAA,CAAAG,OAAA,CAAAC,IAAA;UAAAC,IAAA,EAAAL,MAAA,CAAAjJ,QAAA;QAAA,GAAAuJ,KAAA,cACA;MACA,GAAAA,KAAA;QACAN,MAAA,CAAApJ,OAAA;QACA,IAAAoJ,MAAA,CAAAnJ,cAAA;UACAmJ,MAAA,CAAA5H,OAAA;QACA;MACA;IACA;IAEA;IACAmI,uBAAA,WAAAA,wBAAA;MACA,KAAAtJ,cAAA;MACA,KAAA2I,OAAA;IACA;EACA;AACA;AAAAY,OAAA,CAAAhH,OAAA,GAAAiH,QAAA"}]}