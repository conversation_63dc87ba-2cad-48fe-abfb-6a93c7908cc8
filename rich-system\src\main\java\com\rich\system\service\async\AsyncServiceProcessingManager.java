package com.rich.system.service.async;

import com.rich.common.core.domain.entity.RsRct;
import com.rich.system.service.context.ServiceProcessingContext;
import com.rich.system.service.processor.ServiceProcessor;
import com.rich.system.service.processor.ServiceProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 异步服务处理管理器
 * 负责管理异步服务处理任务
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class AsyncServiceProcessingManager {
    
    @Autowired
    private ServiceProcessorFactory processorFactory;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 异步处理专用线程池
     */
    private final ExecutorService asyncExecutor = Executors.newFixedThreadPool(
            Runtime.getRuntime().availableProcessors() * 2,
            r -> {
                Thread thread = new Thread(r, "async-service-processor-" + System.currentTimeMillis());
                thread.setDaemon(true);
                return thread;
            }
    );
    
    /**
     * 异步处理服务
     * 
     * @param processor 服务处理器
     * @param rsRct 操作单数据
     * @param context 处理上下文
     * @return 异步处理结果
     */
    @Async("threadPoolTaskExecutor")
    public CompletableFuture<ServiceProcessingResult> processServiceAsync(
            ServiceProcessor processor, 
            RsRct rsRct, 
            ServiceProcessingContext context) {
        
        return CompletableFuture.supplyAsync(() -> {
            String serviceName = processor.getProcessorName();
            long startTime = System.currentTimeMillis();
            
            try {
                log.debug("开始异步处理服务: {}", serviceName);
                
                // 发布处理开始事件
                eventPublisher.publishEvent(new ServiceProcessingStartedEvent(
                        rsRct.getRctId(), rsRct.getRctNo(), serviceName));
                
                // 在新事务中处理服务
                processServiceInNewTransaction(processor, rsRct, context);
                
                long duration = System.currentTimeMillis() - startTime;
                
                // 发布处理完成事件
                eventPublisher.publishEvent(new ServiceProcessingCompletedEvent(
                        rsRct.getRctId(), rsRct.getRctNo(), serviceName, duration));
                
                log.debug("异步处理服务完成: {}, 耗时: {}ms", serviceName, duration);
                
                return ServiceProcessingResult.success(serviceName, duration);
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                
                log.error("异步处理服务失败: {}, 耗时: {}ms, 错误: {}", 
                        serviceName, duration, e.getMessage(), e);
                
                // 记录错误到上下文
                context.recordError(serviceName, e);
                
                // 发布处理失败事件
                eventPublisher.publishEvent(new ServiceProcessingFailedEvent(
                        rsRct.getRctId(), rsRct.getRctNo(), serviceName, e, duration));
                
                return ServiceProcessingResult.failure(serviceName, e, duration);
            }
        }, asyncExecutor);
    }
    
    /**
     * 在新事务中处理服务
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void processServiceInNewTransaction(ServiceProcessor processor, 
                                              RsRct rsRct, 
                                              ServiceProcessingContext context) throws Exception {
        processor.process(rsRct, context);
    }
    
    /**
     * 批量异步处理服务
     * 
     * @param processors 处理器列表
     * @param rsRct 操作单数据
     * @param context 处理上下文
     * @param timeoutSeconds 超时时间（秒）
     * @return 处理结果列表
     */
    public java.util.List<ServiceProcessingResult> batchProcessServicesAsync(
            java.util.List<ServiceProcessor> processors,
            RsRct rsRct,
            ServiceProcessingContext context,
            long timeoutSeconds) {
        
        log.info("开始批量异步处理 {} 个服务", processors.size());
        
        java.util.List<CompletableFuture<ServiceProcessingResult>> futures = 
                new java.util.ArrayList<>();
        
        // 启动所有异步任务
        for (ServiceProcessor processor : processors) {
            if (processor.canProcess(rsRct, context)) {
                CompletableFuture<ServiceProcessingResult> future = 
                        processServiceAsync(processor, rsRct, context);
                futures.add(future);
            }
        }
        
        // 等待所有任务完成或超时
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));
        
        try {
            allFutures.get(timeoutSeconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("批量异步处理超时或失败", e);
        }
        
        // 收集结果
        java.util.List<ServiceProcessingResult> results = new java.util.ArrayList<>();
        for (CompletableFuture<ServiceProcessingResult> future : futures) {
            try {
                if (future.isDone()) {
                    results.add(future.get());
                } else {
                    // 超时的任务
                    results.add(ServiceProcessingResult.timeout("未知服务", timeoutSeconds * 1000));
                }
            } catch (Exception e) {
                log.warn("获取异步处理结果失败", e);
                results.add(ServiceProcessingResult.failure("未知服务", e, 0));
            }
        }
        
        log.info("批量异步处理完成，成功: {}, 失败: {}", 
                results.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum(),
                results.stream().mapToInt(r -> r.isSuccess() ? 0 : 1).sum());
        
        return results;
    }
    
    /**
     * 优雅关闭异步处理器
     */
    public void shutdown() {
        log.info("开始关闭异步服务处理器...");
        
        asyncExecutor.shutdown();
        
        try {
            if (!asyncExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                log.warn("异步处理器未能在30秒内正常关闭，强制关闭");
                asyncExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            log.warn("等待异步处理器关闭时被中断", e);
            asyncExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        log.info("异步服务处理器已关闭");
    }
    
    /**
     * 获取异步处理器状态
     */
    public AsyncProcessorStatus getStatus() {
        return AsyncProcessorStatus.builder()
                .isShutdown(asyncExecutor.isShutdown())
                .isTerminated(asyncExecutor.isTerminated())
                .activeThreadCount(getActiveThreadCount())
                .build();
    }
    
    /**
     * 获取活跃线程数
     */
    private int getActiveThreadCount() {
        if (asyncExecutor instanceof java.util.concurrent.ThreadPoolExecutor) {
            return ((java.util.concurrent.ThreadPoolExecutor) asyncExecutor).getActiveCount();
        }
        return -1; // 无法获取
    }
    
    /**
     * 异步处理器状态
     */
    @lombok.Data
    @lombok.Builder
    public static class AsyncProcessorStatus {
        private boolean isShutdown;
        private boolean isTerminated;
        private int activeThreadCount;
    }
}
