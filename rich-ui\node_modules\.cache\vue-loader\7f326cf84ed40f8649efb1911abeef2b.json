{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\chargeList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\chargeList.vue", "mtime": 1754646305889}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgY3VycmVuY3kgZnJvbSAiY3VycmVuY3kuanMiDQppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCINCmltcG9ydCBwaW55aW4gZnJvbSAianMtcGlueWluIg0KaW1wb3J0IENvbXBhbnlTZWxlY3QgZnJvbSAiQC9jb21wb25lbnRzL0NvbXBhbnlTZWxlY3QvaW5kZXgudnVlIg0KaW1wb3J0IHtwYXJzZVRpbWV9IGZyb20gIkAvdXRpbHMvcmljaCINCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiY2hhcmdlcyIsDQogIGNvbXBvbmVudHM6IHtDb21wYW55U2VsZWN0LCBUcmVlc2VsZWN0fSwNCiAgcHJvcHM6IFsiY2hhcmdlRGF0YSIsICJjb21wYW55TGlzdCIsICJvcGVuQ2hhcmdlTGlzdCIsICJpc1JlY2VpdmFibGUiLCAiZGlzYWJsZWQiLA0KICAgICJzZXJ2aWNlVHlwZUlkIiwgInNlcnZpY2VJZCIsICJoaWRkZW5TdXBwbGllciIsICJyc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4VVNEIiwNCiAgICAicnNDbGllbnRNZXNzYWdlUmVjZWl2YWJsZVRheFJNQiIsICJyc0NsaWVudE1lc3NhZ2VQYXlhYmxlVGF4VVNEIiwgInJzQ2xpZW50TWVzc2FnZVBheWFibGVUYXhSTUIiLA0KICAgICJyc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlUk1CIiwgInJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVVU0QiLCAicnNDbGllbnRNZXNzYWdlUGF5YWJsZVJNQiIsDQogICAgInJzQ2xpZW50TWVzc2FnZVBheWFibGVVU0QiLCAicnNDbGllbnRNZXNzYWdlUHJvZml0IiwgInJzQ2xpZW50TWVzc2FnZVByb2ZpdE5vVGF4IiwgInBheURldGFpbFJNQiIsDQogICAgInBheURldGFpbFVTRCIsICJwYXlEZXRhaWxSTUJUYXgiLCAicGF5RGV0YWlsVVNEVGF4IiwgInJzQ2xpZW50TWVzc2FnZVByb2ZpdFVTRCIsICJyc0NsaWVudE1lc3NhZ2VQcm9maXRSTUIiLA0KICAgICJyc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhSTUIiLCAicnNDbGllbnRNZXNzYWdlUHJvZml0VGF4VVNEIiwgIkFURCJdLA0KICB3YXRjaDogew0KICAgIGNoYXJnZURhdGE6IHsNCiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIChuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICBpZiAoIW9sZFZhbCkgew0KICAgICAgICAgIHRoaXMuJGVtaXQoInJldHVybiIsIG5ld1ZhbCkNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOmBjeWOhui0ueeUqOWIl+ihqO+8jOajgOafpeW4geenjeWPmOWMlg0KICAgICAgICBuZXdWYWwgPyBuZXdWYWwuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICBjb25zdCBvbGRJdGVtID0gb2xkVmFsW2luZGV4XQ0KDQogICAgICAgICAgLy8g5qOA5p+l5biB56eN5Y+Y5YyW5bm26K6h566X5bCP6K6hDQogICAgICAgICAgaWYgKGl0ZW0uY3VycmVuY3kgJiYgaXRlbS5hbW91bnQpIHsNCiAgICAgICAgICAgIC8vIOWmguaenOS7jiBSTUIg5o2i5oiQIFVTRO+8jOS9v+eUqCAxL+axh+eOhyDorqHnrpcNCiAgICAgICAgICAgIGlmIChvbGRJdGVtICYmIG9sZEl0ZW0uY3VycmVuY3kgPT09ICJSTUIiICYmIGl0ZW0uY3VycmVuY3kgPT09ICJVU0QiKSB7DQogICAgICAgICAgICAgIGlmIChpdGVtLmV4Y2hhbmdlUmF0ZSAmJiBpdGVtLmV4Y2hhbmdlUmF0ZSAhPT0gMCkgew0KICAgICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgICAvLyDorqHnrpcgMS/msYfnjofvvIzkv53nlZk05L2N5bCP5pWwDQogICAgICAgICAgICAgICAgICBjb25zdCBpbnZlcnNlUmF0ZSA9IGN1cnJlbmN5KDEsIHtwcmVjaXNpb246IDR9KS5kaXZpZGUoaXRlbS5leGNoYW5nZVJhdGUpLnZhbHVlDQoNCiAgICAgICAgICAgICAgICAgIC8vIOiuoeeul+Wwj+iuoTog5Y2V5Lu3ICog5pWw6YePICog5rGH546HICogKDEgKyDnqI7njocvMTAwKQ0KICAgICAgICAgICAgICAgICAgaXRlbS5zdWJ0b3RhbCA9IGN1cnJlbmN5KGl0ZW0uZG5Vbml0UmF0ZSB8fCAwLCB7cHJlY2lzaW9uOiA0fSkNCiAgICAgICAgICAgICAgICAgICAgLm11bHRpcGx5KGl0ZW0uYW1vdW50KQ0KICAgICAgICAgICAgICAgICAgICAubXVsdGlwbHkoaW52ZXJzZVJhdGUpDQogICAgICAgICAgICAgICAgICAgIC5tdWx0aXBseShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3koaXRlbS5kdXR5UmF0ZSB8fCAwKS5kaXZpZGUoMTAwKSkpDQogICAgICAgICAgICAgICAgICAgIC52YWx1ZQ0KICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCLorqHnrpflsI/orqHlh7rplJk6IiwgZXJyb3IpDQogICAgICAgICAgICAgICAgICBpdGVtLnN1YnRvdGFsID0gMA0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkgOiBudWxsDQoNCiAgICAgICAgdGhpcy4kZW1pdCgicmV0dXJuIiwgbmV3VmFsID8gbmV3VmFsIDogW10pDQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAodGhpcy5jaGFyZ2VEYXRhICYmIHRoaXMuY2hhcmdlRGF0YS5sZW5ndGggPiAwKSA/IHRoaXMuY2hhcmdlRGF0YS5tYXAoaXRlbSA9PiB0aGlzLiRyZWZzLmNoYXJnZVRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihpdGVtLCB0cnVlKSkgOiBudWxsDQoNCiAgICB0aGlzLnByb2ZpdENvdW50KCJSTUIiKQ0KICB9LA0KICBjb21wdXRlZDogew0KDQogICAgaGFzQ29uZmlybVJvdygpIHsNCiAgICAgIGxldCByZXN1bHQgPSBmYWxzZTsNCiAgICAgICh0aGlzLmNoYXJnZURhdGEgJiYgdGhpcy5jaGFyZ2VEYXRhLmxlbmd0aCA+IDApID8gdGhpcy5jaGFyZ2VEYXRhLm1hcChpdGVtID0+IHsNCiAgICAgICAgaWYgKGl0ZW0uaXNBY2NvdW50Q29uZmlybWVkID09PSAiMSIpIHsNCiAgICAgICAgICByZXN1bHQgPSB0cnVlDQogICAgICAgIH0NCiAgICAgIH0pIDogbnVsbA0KICAgICAgcmV0dXJuIHJlc3VsdA0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcGF5VG90YWxSTUI6IDAsDQogICAgICBwYXlUb3RhbFVTRDogMCwNCiAgICAgIHNob3dDbGllbnROYW1lOiBudWxsLA0KICAgICAgY3VycmVuY3lDb2RlOiAiUk1CIiwNCiAgICAgIHByb2ZpdDogMCwNCiAgICAgIHByb2ZpdFRheDogMCwNCiAgICAgIGV4Y2hhbmdlUmF0ZTogMCwNCiAgICAgIHNlcnZpY2VzOiBbew0KICAgICAgICB2YWx1ZTogMSwNCiAgICAgICAgbGFiZWw6ICLmtbfov5AiDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiAxMCwNCiAgICAgICAgbGFiZWw6ICLnqbrov5AiDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiAyMCwNCiAgICAgICAgbGFiZWw6ICLpk4Hot68iDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiA0MCwNCiAgICAgICAgbGFiZWw6ICLlv6vpgJIiDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiA1MCwNCiAgICAgICAgbGFiZWw6ICLmi5bovaYiDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiA2MCwNCiAgICAgICAgbGFiZWw6ICLmiqXlhbMiDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiA3MCwNCiAgICAgICAgbGFiZWw6ICLmuIXlhbPmtL7pgIEiDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiA4MCwNCiAgICAgICAgbGFiZWw6ICLnoIHlpLTku5PlgqgiDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiA5MCwNCiAgICAgICAgbGFiZWw6ICLmo4Dpqozor4HkuaYiDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiAxMDAsDQogICAgICAgIGxhYmVsOiAi5L+d6ZmpIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogMTAxLA0KICAgICAgICBsYWJlbDogIuaJqeWxleacjeWKoSINCiAgICAgIH1dLA0KICAgICAgc2VydmljZTogW3sNCiAgICAgICAgdmFsdWU6IDEsDQogICAgICAgIGxhYmVsOiAi5Z+656GA5pyN5YqhIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogNCwNCiAgICAgICAgbGFiZWw6ICLliY3nqIvov5DovpMiDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiA1LA0KICAgICAgICBsYWJlbDogIuWHuuWPo+aKpeWFsyINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDYsDQogICAgICAgIGxhYmVsOiAi6L+b5Y+j5riF5YWzIg0KICAgICAgfSwge3ZhbHVlOiAyLCBsYWJlbDogIua1t+i/kCJ9DQogICAgICAgICwge3ZhbHVlOiAzLCBsYWJlbDogIumZhui/kCJ9DQogICAgICAgICwge3ZhbHVlOiA0LCBsYWJlbDogIumTgei3ryJ9DQogICAgICAgICwge3ZhbHVlOiA1LCBsYWJlbDogIuepuui/kCJ9DQogICAgICAgICwge3ZhbHVlOiA2LCBsYWJlbDogIuW/q+mAkiJ9DQogICAgICAgICwge3ZhbHVlOiAyMSwgbGFiZWw6ICLmlbTmn5zmtbfov5AifQ0KICAgICAgICAsIHt2YWx1ZTogMjIsIGxhYmVsOiAi5ou85p+c5rW36L+QIn0NCiAgICAgICAgLCB7dmFsdWU6IDIzLCBsYWJlbDogIuaVo+adguiIuSJ9DQogICAgICAgICwge3ZhbHVlOiAyNCwgbGFiZWw6ICLmu5roo4XoiLkifQ0KICAgICAgICAsIHt2YWx1ZTogNDEsIGxhYmVsOiAi5pW05p+c6ZOB6LevIn0NCiAgICAgICAgLCB7dmFsdWU6IDQyLCBsYWJlbDogIuaLvOafnOmTgei3ryJ9DQogICAgICAgICwge3ZhbHVlOiA0MywgbGFiZWw6ICLpk4Hot6/ovabnmq4ifQ0KICAgICAgICAsIHt2YWx1ZTogNTEsIGxhYmVsOiAi56m66L+Q5pmu6IixIn0NCiAgICAgICAgLCB7dmFsdWU6IDUyLCBsYWJlbDogIuepuui/kOWMheadvyJ9DQogICAgICAgICwge3ZhbHVlOiA1MywgbGFiZWw6ICLnqbrov5DljIXmnLoifQ0KICAgICAgICAsIHt2YWx1ZTogNTQsIGxhYmVsOiAi56m66L+Q6KGM5p2OIn0NCiAgICAgICAgLCB7dmFsdWU6IDk2MSwgbGFiZWw6ICLliY3nqIvov5DovpMifQ0KICAgICAgICAsIHt2YWx1ZTogOTY0LCBsYWJlbDogIui/m+WPo+a4heWFsyJ9DQogICAgICAgICwge3ZhbHVlOiA3LCBsYWJlbDogIuWHuuWPo+aKpeWFsyJ9DQogICAgICBdLA0KICAgICAgY2hhcmdlUmVtYXJrOiBudWxsLA0KICAgICAgcHJvZml0T3BlbjogZmFsc2UsDQogICAgICBwcm9maXRUYWJsZURhdGE6IFtdDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgb3BlblByb2ZpdCgpIHsNCiAgICAgIHRoaXMucHJvZml0VGFibGVEYXRhID0gW10NCg0KICAgICAgbGV0IFJNQiA9IHt9DQogICAgICBSTUIuY3VycmVuY3lDb2RlID0gIlJNQiINCiAgICAgIFJNQi5yZWNlaXZhYmxlID0gdGhpcy5yc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlUk1CDQogICAgICBSTUIucGF5YWJsZSA9IHRoaXMucnNDbGllbnRNZXNzYWdlUGF5YWJsZVJNQg0KICAgICAgLy8g5LiN5ZCr56iO5Yip5ramDQogICAgICBSTUIucHJvZml0ID0gdGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRSTUINCiAgICAgIC8vIOWQq+eojuW6lOaUtg0KICAgICAgUk1CLnJlY2VpdmFibGVUYXggPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVUYXhSTUINCiAgICAgIC8vIOWQq+eojuW6lOS7mA0KICAgICAgUk1CLnBheWFibGVUYXggPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVBheWFibGVUYXhSTUINCiAgICAgIC8vIOWQq+eojuWIqea2pg0KICAgICAgUk1CLnByb2ZpdFRheCA9IHRoaXMucnNDbGllbnRNZXNzYWdlUHJvZml0VGF4Uk1CDQoNCiAgICAgIGxldCBVU0QgPSB7fQ0KICAgICAgVVNELmN1cnJlbmN5Q29kZSA9ICJVU0QiDQogICAgICBVU0QucmVjZWl2YWJsZSA9IHRoaXMucnNDbGllbnRNZXNzYWdlUmVjZWl2YWJsZVVTRA0KICAgICAgVVNELnBheWFibGUgPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVBheWFibGVVU0QNCiAgICAgIFVTRC5wcm9maXQgPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVByb2ZpdFVTRA0KICAgICAgVVNELnJlY2VpdmFibGVUYXggPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVUYXhVU0QNCiAgICAgIFVTRC5wYXlhYmxlVGF4ID0gdGhpcy5yc0NsaWVudE1lc3NhZ2VQYXlhYmxlVGF4VVNEDQogICAgICBVU0QucHJvZml0VGF4ID0gdGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhVU0QNCg0KICAgICAgdGhpcy5wcm9maXRUYWJsZURhdGEucHVzaChSTUIpDQogICAgICB0aGlzLnByb2ZpdFRhYmxlRGF0YS5wdXNoKFVTRCkNCg0KICAgICAgdGhpcy5wcm9maXRDb3VudCgiUk1CIikNCiAgICB9LA0KICAgIHByb2ZpdENvdW50KHR5cGUpIHsNCiAgICAgIGxldCBleGNoYW5nZVJhdGUNCiAgICAgIGZvciAoY29uc3QgYSBvZiB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmV4Y2hhbmdlUmF0ZUxpc3QpIHsNCiAgICAgICAgaWYgKHRoaXMuQVREKSB7DQogICAgICAgICAgaWYgKGEubG9jYWxDdXJyZW5jeSA9PT0gIlJNQiINCiAgICAgICAgICAgICYmICJVU0QiID09IGEub3ZlcnNlYUN1cnJlbmN5DQogICAgICAgICAgICAmJiBwYXJzZVRpbWUoYS52YWxpZEZyb20pIDw9IHBhcnNlVGltZSh0aGlzLkFURCkNCiAgICAgICAgICAgICYmIHBhcnNlVGltZSh0aGlzLkFURCkgPD0gcGFyc2VUaW1lKGEudmFsaWRUbykNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIGV4Y2hhbmdlUmF0ZSA9IGN1cnJlbmN5KGEuc2V0dGxlUmF0ZSkuZGl2aWRlKGEuYmFzZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaWYgKCFleGNoYW5nZVJhdGUpIHsNCiAgICAgICAgICBpZiAoYS5sb2NhbEN1cnJlbmN5ID09PSAiUk1CIg0KICAgICAgICAgICAgJiYgIlVTRCIgPT0gYS5vdmVyc2VhQ3VycmVuY3kNCiAgICAgICAgICAgICYmIHBhcnNlVGltZShhLnZhbGlkRnJvbSkgPD0gcGFyc2VUaW1lKG5ldyBEYXRlKCkpDQogICAgICAgICAgICAmJiBwYXJzZVRpbWUobmV3IERhdGUoKSkgPD0gcGFyc2VUaW1lKGEudmFsaWRUbykpIHsNCiAgICAgICAgICAgIGV4Y2hhbmdlUmF0ZSA9IGN1cnJlbmN5KGEuc2V0dGxlUmF0ZSkuZGl2aWRlKGEuYmFzZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuZXhjaGFuZ2VSYXRlID0gZXhjaGFuZ2VSYXRlDQoNCiAgICAgIGlmICh0eXBlID09PSAiUk1CIikgew0KICAgICAgICAvLyDpg73mipjnrpfmiJDkurrmsJHluIENCiAgICAgICAgdGhpcy5wcm9maXQgPSBjdXJyZW5jeSh0aGlzLnJzQ2xpZW50TWVzc2FnZVByb2ZpdFVTRCkubXVsdGlwbHkoZXhjaGFuZ2VSYXRlKS5hZGQodGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRSTUIpLnZhbHVlDQogICAgICAgIHRoaXMucHJvZml0VGF4ID0gY3VycmVuY3kodGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhVU0QpLm11bHRpcGx5KGV4Y2hhbmdlUmF0ZSkuYWRkKHRoaXMucnNDbGllbnRNZXNzYWdlUHJvZml0VGF4Uk1CKS52YWx1ZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5wcm9maXQgPSBjdXJyZW5jeSh0aGlzLnJzQ2xpZW50TWVzc2FnZVByb2ZpdFJNQikuZGl2aWRlKGV4Y2hhbmdlUmF0ZSkuYWRkKHRoaXMucnNDbGllbnRNZXNzYWdlUHJvZml0VVNEKS52YWx1ZQ0KICAgICAgICB0aGlzLnByb2ZpdFRheCA9IGN1cnJlbmN5KHRoaXMucnNDbGllbnRNZXNzYWdlUHJvZml0VGF4Uk1CKS5kaXZpZGUoZXhjaGFuZ2VSYXRlKS5hZGQodGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhVU0QpLnZhbHVlDQogICAgICB9DQogICAgfSwNCiAgICBhdWRpdFN0YXR1cyhzdGF0dXMpIHsNCiAgICAgIHJldHVybiBzdGF0dXMgPT0gMSA/ICLlt7LlrqHmoLgiIDogIuacquWuoeaguCINCiAgICB9LA0KICAgIHNlbGVjdENoYXJnZSh0YXJnZXQsIHJvdykgew0KICAgICAgcm93LmRuQ2hhcmdlTmFtZUlkID0gdGFyZ2V0LmNoYXJnZUlkDQogICAgICByb3cuY2hhcmdlTmFtZSA9IHRhcmdldC5jaGFyZ2VMb2NhbE5hbWUNCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZSh2YWwpIHsNCiAgICAgIHRoaXMuJGVtaXQoInNlbGVjdFJvdyIsIHZhbCkNCg0KICAgICAgdGhpcy5wYXlUb3RhbFVTRCA9IDANCiAgICAgIHRoaXMucGF5VG90YWxSTUIgPSAwDQogICAgICB2YWwgPyB2YWwubWFwKGl0ZW0gPT4gew0KICAgICAgICBpZiAoaXRlbS5pc1JlY2lldmluZ09yUGF5aW5nID09IDEpIHsNCiAgICAgICAgICBpZiAoaXRlbS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHRoaXMucGF5VG90YWxVU0QgPSBjdXJyZW5jeSh0aGlzLnBheVRvdGFsVVNEKS5hZGQoaXRlbS5zdWJ0b3RhbCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5wYXlUb3RhbFJNQiA9IGN1cnJlbmN5KHRoaXMucGF5VG90YWxSTUIpLmFkZChpdGVtLnN1YnRvdGFsKQ0KICAgICAgICAgIH0NCg0KICAgICAgICB9DQogICAgICB9KSA6IG51bGwNCg0KICAgIH0sDQogICAgY3VycmVuY3ksDQogICAgZ2V0U2VydmljZU5hbWUoaWQpIHsNCiAgICAgIGxldCBzZXJ2aWNlTmFtZSA9ICIiDQogICAgICB0aGlzLnNlcnZpY2VzLm1hcChvYmogPT4gew0KICAgICAgICBvYmoudmFsdWUgPT09IGlkID8gc2VydmljZU5hbWUgPSBvYmoubGFiZWwgOiBudWxsDQogICAgICB9KQ0KICAgICAgcmV0dXJuIHNlcnZpY2VOYW1lDQogICAgfSwNCiAgICBjb3B5RnJlaWdodChyb3cpIHsNCiAgICAgIGlmICh0aGlzLmNvbXBhbnlMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgcm93LnBheUNsZWFyaW5nQ29tcGFueUlkID0gdGhpcy5jb21wYW55TGlzdFswXS5jb21wYW55SWQNCiAgICAgICAgcm93LnBheUNvbXBhbnlOYW1lID0gdGhpcy5jb21wYW55TGlzdFswXS5jb21wYW55U2hvcnROYW1lDQogICAgICB9DQogICAgICByb3cuaXNBY2NvdW50Q29uZmlybWVkID0gMA0KICAgICAgLy8g5oql5Lu35YiX6KGo6Lez6L2s6K6i6Iix5pe25rKh5pyJ5YWs5Y+45YiX6KGoLOWkjeWItuWIsOW6lOaUtuayoeacieWuouaIt+S/oeaBrw0KICAgICAgbGV0IGRhdGEgPSB0aGlzLl8uY2xvbmVEZWVwKHJvdykNCg0KICAgICAgdGhpcy4kZW1pdCgiY29weUZyZWlnaHQiLCB7Li4uZGF0YSwgY2hhcmdlSWQ6IG51bGx9KQ0KICAgIH0sDQogICAgY29weUFsbEZyZWlnaHQoKSB7DQogICAgICBpZiAoIXRoaXMuY29tcGFueUxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5hbGVydFdhcm5pbmcoIuivt+WFiOmAieaLqeWnlOaJmOWNleS9jeaIluWFs+iBlOWNleS9jSIpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICB0aGlzLmNoYXJnZURhdGEubWFwKGNoYXJnZSA9PiB7DQogICAgICAgIGNoYXJnZS5wYXlDbGVhcmluZ0NvbXBhbnlJZCA9IHRoaXMuY29tcGFueUxpc3RbMF0uY29tcGFueUlkDQogICAgICAgIGNoYXJnZS5wYXlDb21wYW55TmFtZSA9IHRoaXMuY29tcGFueUxpc3RbMF0uY29tcGFueVNob3J0TmFtZQ0KICAgICAgICBjaGFyZ2UuaXNSZWNpZXZpbmdPclBheWluZyA9IDANCiAgICAgICAgY2hhcmdlLmlzQWNjb3VudENvbmZpcm1lZCA9IDANCiAgICAgICAgY2hhcmdlLmNoYXJnZUlkID0gbnVsbA0KICAgICAgICB0aGlzLiRlbWl0KCJjb3B5RnJlaWdodCIsIHRoaXMuXy5jbG9uZURlZXAoY2hhcmdlKSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBjaGFuZ2VVbml0Q29zdChyb3csIHVuaXQpIHsNCiAgICAgIHJvdy5kblVuaXRDb2RlID0gdW5pdA0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICByb3cuc2hvd0Nvc3RVbml0ID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBjaGFuZ2VVbml0KHJvdywgdW5pdCkgew0KICAgICAgcm93LmRuVW5pdENvZGUgPSB1bml0DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHJvdy5zaG93UXVvdGF0aW9uVW5pdCA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlQ2hhcmdlU2VsZWN0KHJvdywgZGF0YSkgew0KICAgICAgaWYgKHJvdy5jaGFyZ2VMb2NhbE5hbWUgPT09IGRhdGEuY2hhcmdlTmFtZSkgew0KICAgICAgICByb3cuY2hhcmdlTmFtZSA9IGRhdGEuY2hhcmdlTG9jYWxOYW1lDQogICAgICAgIHJvdy5zaG93UXVvdGF0aW9uQ2hhcmdlID0gZmFsc2UNCiAgICAgIH0NCiAgICAgIGlmIChyb3cuY3VycmVuY3lDb2RlID09IG51bGwgJiYgZGF0YS5jdXJyZW5jeUNvZGUpIHsNCiAgICAgICAgcm93LmRuQ3VycmVuY3lDb2RlID0gZGF0YS5jdXJyZW5jeUNvZGUNCiAgICAgIH0NCiAgICB9LA0KICAgIGNoYW5nZUN1cnJlbmN5KHJvdywgY3VycmVuY3lDb2RlKSB7DQogICAgICByb3cuZG5DdXJyZW5jeUNvZGUgPSBjdXJyZW5jeUNvZGUNCiAgICAgIC8qIGxldCBleGNoYW5nZVJhdGUNCiAgICAgIGlmIChjdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgIGZvciAoY29uc3QgYSBvZiB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmV4Y2hhbmdlUmF0ZUxpc3QpIHsNCiAgICAgICAgICBpZiAoYS5sb2NhbEN1cnJlbmN5ID09PSAiUk1CIg0KICAgICAgICAgICAgJiYgcm93LmRuQ3VycmVuY3lDb2RlID09IGEub3ZlcnNlYUN1cnJlbmN5DQogICAgICAgICAgKSB7DQogICAgICAgICAgICBleGNoYW5nZVJhdGUgPSBjdXJyZW5jeShhLnNldHRsZVJhdGUpLmRpdmlkZShhLmJhc2UpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9ICovDQoNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgLy8gcm93LmJhc2ljQ3VycmVuY3lSYXRlID0gZXhjaGFuZ2VSYXRlID8gZXhjaGFuZ2VSYXRlIDogMQ0KICAgICAgICByb3cuc2hvd1F1b3RhdGlvbkN1cnJlbmN5ID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5bqP5Y+3ICovDQogICAgcm93SW5kZXgoeyByb3csIHJvd0luZGV4IH0pIHsNCiAgICAgIHJvdy5pZCA9IHJvd0luZGV4ICsgMQ0KICAgIH0sDQogICAgYWRkUmVjZWl2YWJsZVBheWFibGUoKSB7DQogICAgICBsZXQgb2JqID0gew0KICAgICAgICBzaG93Q2xpZW50OiB0cnVlLA0KICAgICAgICBzaG93U3VwcGxpZXI6IHRydWUsDQogICAgICAgIHNob3dRdW90YXRpb25DaGFyZ2U6IHRydWUsDQogICAgICAgIHNob3dDb3N0Q2hhcmdlOiB0cnVlLA0KICAgICAgICBzaG93UXVvdGF0aW9uQ3VycmVuY3k6IHRydWUsDQogICAgICAgIHNob3dDb3N0Q3VycmVuY3k6IHRydWUsDQogICAgICAgIHNob3dRdW90YXRpb25Vbml0OiB0cnVlLA0KICAgICAgICBzaG93Q29zdFVuaXQ6IHRydWUsDQogICAgICAgIHNob3dTdHJhdGVneTogdHJ1ZSwNCiAgICAgICAgc2hvd1VuaXRSYXRlOiB0cnVlLA0KICAgICAgICBzaG93QW1vdW50OiB0cnVlLA0KICAgICAgICBzaG93Q3VycmVuY3lSYXRlOiB0cnVlLA0KICAgICAgICBzaG93RHV0eVJhdGU6IHRydWUsDQogICAgICAgIGJhc2ljQ3VycmVuY3lSYXRlOiAxLA0KICAgICAgICBkdXR5UmF0ZTogMCwNCiAgICAgICAgZG5BbW91bnQ6IDEsDQogICAgICAgIC8vIOW6lOaUtui/mOaYr+W6lOS7mA0KICAgICAgICBpc1JlY2lldmluZ09yUGF5aW5nOiB0aGlzLmlzUmVjZWl2YWJsZSA/IDAgOiAxLA0KICAgICAgICBjbGVhcmluZ0NvbXBhbnlJZDogdGhpcy5jaGFyZ2VEYXRhLmxlbmd0aCA+IDAgPyB0aGlzLmNoYXJnZURhdGFbdGhpcy5jaGFyZ2VEYXRhLmxlbmd0aCAtIDFdLmNsZWFyaW5nQ29tcGFueUlkIDogbnVsbA0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gMSkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSAxDQogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSAxMCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSAxMA0KICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gMjApIG9iai5zcWRTZXJ2aWNlVHlwZUlkID0gMjANCiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDQwKSBvYmouc3FkU2VydmljZVR5cGVJZCA9IDQwDQogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSA1MCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSA1MA0KICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gNjApIG9iai5zcWRTZXJ2aWNlVHlwZUlkID0gNjANCiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDcwKSBvYmouc3FkU2VydmljZVR5cGVJZCA9IDcwDQogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSA4MCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSA4MA0KICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gOTApIG9iai5zcWRTZXJ2aWNlVHlwZUlkID0gOTANCiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDEwMCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSAxMDANCiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDEwMSkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSAxMDENCiAgICAgIHRoaXMuY2hhcmdlRGF0YS5wdXNoKG9iaikNCiAgICB9LA0KICAgIGNvdW50UHJvZml0KHJvdywgY2F0ZWdvcnkpIHsNCiAgICAgIC8vIOehruS/neaJgOacieW/heimgeeahOWAvOmDveWtmOWcqOS4lOacieaViA0KICAgICAgaWYgKCFyb3cpIHJldHVybg0KDQogICAgICAvLyDkvb/nlKhjdXJyZW5jeS5qc+adpeWkhOeQhuaVsOWAvCzpgb/lhY3nsr7luqbmjZ/lpLENCiAgICAgIGNvbnN0IHVuaXRSYXRlID0gcm93LmRuVW5pdFJhdGUgfHwgMA0KICAgICAgY29uc3QgYW1vdW50ID0gcm93LmRuQW1vdW50IHx8IDANCiAgICAgIGNvbnN0IGN1cnJlbmN5UmF0ZSA9IGN1cnJlbmN5KHJvdy5iYXNpY0N1cnJlbmN5UmF0ZSB8fCAxLCB7cHJlY2lzaW9uOiA0fSkudmFsdWUNCiAgICAgIGNvbnN0IGR1dHlSYXRlID0gY3VycmVuY3kocm93LmR1dHlSYXRlIHx8IDApLnZhbHVlDQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOiuoeeul+Wwj+iuoQ0KICAgICAgICBjb25zdCBzdWJ0b3RhbCA9IGN1cnJlbmN5KHVuaXRSYXRlLCB7cHJlY2lzaW9uOiA0fSkNCiAgICAgICAgICAubXVsdGlwbHkoYW1vdW50KQ0KICAgICAgICAgIC5tdWx0aXBseShjdXJyZW5jeVJhdGUpDQogICAgICAgICAgLm11bHRpcGx5KGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShkdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKQ0KICAgICAgICAgIC52YWx1ZQ0KDQogICAgICAgIC8vIOabtOaWsOihjOaVsOaNrg0KICAgICAgICByb3cuc3VidG90YWwgPSBjdXJyZW5jeShzdWJ0b3RhbCwge3ByZWNpc2lvbjogMn0pLnZhbHVlDQogICAgICAgIHJvdy5zcWREbkN1cnJlbmN5QmFsYW5jZSA9IHJvdy5pc0FjY291bnRDb25maXJtZWQgPT09ICIwIiA/IGN1cnJlbmN5KHN1YnRvdGFsLCB7cHJlY2lzaW9uOiAyfSkudmFsdWUgOiByb3cuc3FkRG5DdXJyZW5jeUJhbGFuY2UNCg0KICAgICAgICAvLyDmoLnmja7kuI3lkIznmoTovpPlhaXnsbvlnovlhbPpl63lr7nlupTnmoTnvJbovpHnirbmgIENCiAgICAgICAgc3dpdGNoIChjYXRlZ29yeSkgew0KICAgICAgICAgIGNhc2UgInN0cmF0ZWd5IjoNCiAgICAgICAgICAgIHJvdy5zaG93U3RyYXRlZ3kgPSBmYWxzZQ0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICBjYXNlICJ1bml0UmF0ZSI6DQogICAgICAgICAgICAvLyDkuI3lnKjov5nph4zlhbPpl63nvJbovpHnirbmgIEs5pS555SoQGJsdXLkuovku7YNCiAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgY2FzZSAiYW1vdW50IjoNCiAgICAgICAgICAgIC8vIOS4jeWcqOi/memHjOWFs+mXree8lui+keeKtuaAgSzmlLnnlKhAYmx1cuS6i+S7tg0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICBjYXNlICJjdXJyZW5jeVJhdGUiOg0KICAgICAgICAgICAgLy8g5LiN5Zyo6L+Z6YeM5YWz6Zet57yW6L6R54q25oCBLOaUueeUqEBibHVy5LqL5Lu2DQogICAgICAgICAgICBicmVhaw0KICAgICAgICAgIGNhc2UgImR1dHlSYXRlIjoNCiAgICAgICAgICAgIC8vIOS4jeWcqOi/memHjOWFs+mXree8lui+keeKtuaAgSzmlLnnlKhAYmx1cuS6i+S7tg0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOinpuWPkeaVsOaNruabtOaWsA0KICAgICAgICB0aGlzLiRlbWl0KCJyZXR1cm4iLCB0aGlzLmNoYXJnZURhdGEpDQoNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiuoeeul+Wwj+iuoeaXtuWHuumUmToiLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K6h566X5bCP6K6h5pe25Ye66ZSZLOivt+ajgOafpei+k+WFpeWAvOaYr+WQpuato+ehriIpDQogICAgICB9DQogICAgfSwNCiAgICBkZWxldGVJdGVtKHJvdykgew0KICAgICAgdGhpcy4kZW1pdCgiZGVsZXRlSXRlbSIsIHJvdykNCiAgICB9LA0KICAgIGRlbGV0ZUFsbEl0ZW0ocm93KSB7DQogICAgICB0aGlzLiRlbWl0KCJkZWxldGVBbGwiKQ0KICAgIH0sDQogICAgY29tcGFueU5vcm1hbGl6ZXIobm9kZSkgew0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgaWQ6IG5vZGUuY29tcGFueUlkLA0KICAgICAgICBsYWJlbDogKG5vZGUuY29tcGFueVNob3J0TmFtZSAhPSBudWxsID8gbm9kZS5jb21wYW55U2hvcnROYW1lIDogIiIpICsgIiAiICsgKG5vZGUuY29tcGFueUxvY2FsTmFtZSAhPSBudWxsID8gbm9kZS5jb21wYW55TG9jYWxOYW1lIDogIiIpICsgIiwiICsgcGlueWluLmdldEZ1bGxDaGFycygobm9kZS5jb21wYW55U2hvcnROYW1lICE9IG51bGwgPyBub2RlLmNvbXBhbnlTaG9ydE5hbWUgOiAiIikgKyAiICIgKyAobm9kZS5jb21wYW55TG9jYWxOYW1lICE9IG51bGwgPyBub2RlLmNvbXBhbnlMb2NhbE5hbWUgOiAiIikpDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["chargeList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6mBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "chargeList.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <el-col :span=\"21.5\" :style=\"{'display':openChargeList?'':'none'}\" style=\"margin: 0;padding: 0;\">\r\n    <div :class=\"{'inactive':openChargeList==false,'active':openChargeList}\">\r\n      <el-table ref=\"chargeTable\" :data=\"chargeData\" :row-class-name=\"rowIndex\" border class=\"pd0\"\r\n                @selection-change=\"handleSelectionChange\"\r\n      >\r\n        <el-table-column v-if=\"isReceivable\" label=\"应收明细\" width=\"20px\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-row>\r\n              <el-col :span=\"11\">\r\n                <el-row>\r\n                  <el-col :span=\"4\">\r\n                    不含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{\r\n                      currency(rsClientMessageReceivableUSD, {\r\n                        separator: \",\",\r\n                        symbol: \"$\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(rsClientMessageReceivableRMB, {\r\n                        separator: \",\",\r\n                        symbol: \"￥\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"11\">\r\n                <el-row class=\"unHighlight-text\">\r\n                  <el-col :span=\"4\">\r\n                    含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{\r\n                      currency(rsClientMessageReceivableTaxUSD, {\r\n                        separator: \",\",\r\n                        symbol: \"$\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(rsClientMessageReceivableTaxRMB, {\r\n                        separator: \",\",\r\n                        symbol: \"￥\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"2\">\r\n                <el-button type=\"primary\" @click.native=\"profitOpen=true\">利润</el-button>\r\n                <el-dialog\r\n                  :visible.sync=\"profitOpen\"\r\n                  title=\"单票利润\"\r\n                  width=\"30%\"\r\n                  @open=\"openProfit\"\r\n                >\r\n                  <el-table\r\n                    :data=\"profitTableData\"\r\n                    border\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-table-column\r\n                      label=\"货币\"\r\n                      prop=\"currencyCode\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      label=\"应收\"\r\n                      prop=\"receivable\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      label=\"应付\"\r\n                      prop=\"payable\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      label=\"不含税利润\" prop=\"profit\"\r\n                      style=\"color: #0d0dfd\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      label=\"含税应收\"\r\n                      prop=\"receivableTax\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      label=\"含税应付\"\r\n                      prop=\"payableTax\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      label=\"含税利润\"\r\n                      prop=\"profitTax\"\r\n                    >\r\n                    </el-table-column>\r\n                  </el-table>\r\n\r\n                  <el-row>\r\n                    <el-col :span=\"5\">\r\n                      <el-form-item label=\"折合币种\" prop=\"rctOpDate\">\r\n                        <el-select v-model=\"currencyCode\" @change=\"profitCount(currencyCode)\">\r\n                          <el-option label=\"RMB\" value=\"RMB\"/>\r\n                          <el-option label=\"USD\" value=\"USD\"/>\r\n                        </el-select>\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"7\">\r\n                      <el-row>\r\n                        <el-col :span=\"12\">\r\n                          <div style=\"color: #0d0dfd\">不含税利润</div>\r\n                        </el-col>\r\n                        <el-col :span=\"12\">\r\n                          <el-input v-model=\"profit\" placeholder=\"不含税利润\"/>\r\n                        </el-col>\r\n                      </el-row>\r\n                    </el-col>\r\n                    <el-col :span=\"7\">\r\n                      <el-row>\r\n                        <el-col :span=\"12\">\r\n                          <div>含税利润</div>\r\n                        </el-col>\r\n                        <el-col :span=\"12\">\r\n                          <el-input v-model=\"profitTax\" placeholder=\"含税利润\"/>\r\n                        </el-col>\r\n                      </el-row>\r\n                    </el-col>\r\n                    <el-col :span=\"5\">\r\n                      <el-row>\r\n                        <el-col :span=\"12\">\r\n                          <div>折算汇率</div>\r\n                        </el-col>\r\n                        <el-col :span=\"12\">\r\n                          <el-input v-model=\"exchangeRate\" placeholder=\"含税利润\"/>\r\n                        </el-col>\r\n                      </el-row>\r\n                    </el-col>\r\n                  </el-row>\r\n                </el-dialog>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <el-table-column\r\n            align=\"center\"\r\n            type=\"selection\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"客户\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showClient\" style=\"width: 50px;height: 20px;\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showClient = true\"\r\n              >\r\n                {{ scope.row.companyName }}\r\n              </div>\r\n              <tree-select v-if=\"(companyList&&companyList.length>0)&&scope.row.showClient\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                           :multiple=\"false\" :pass=\"scope.row.clearingCompanyId\" :placeholder=\"'客户'\"\r\n                           :custom-options=\"companyList\" :flat=\"false\"\r\n                           :type=\"'clientCustom'\" @return=\"scope.row.clearingCompanyId=$event\"\r\n                           @close=\" showClientName==scope.row.companyName ? scope.row.showClient = false:null\"\r\n                           @returnData=\"showClientName=(($event.companyShortName&&$event.companyShortName!=='')?$event.companyShortName:$event.companyEnShortName)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用\" prop=\"quotationChargeId\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCharge\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCharge = true\"\r\n              >\r\n                {{ scope.row.chargeName }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationCharge\" :dbn=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                           :pass=\"scope.row.dnChargeNameId\" :placeholder=\"'运费'\" :type=\"'charge'\"\r\n                           :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                           @return=\"scope.row.dnChargeNameId = $event\"\r\n                           @returnData=\"handleChargeSelect(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"货币\" prop=\"quotationCurrencyId\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true\"\r\n              >\r\n                {{ scope.row.dnCurrencyCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationCurrency\" :pass=\"scope.row.dnCurrencyCode\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\" :type=\"'currency'\"\r\n                           @return=\"changeCurrency(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单价\" prop=\"quotationRate\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showUnitRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true\"\r\n              >\r\n                {{\r\n                  scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"¥\"\r\n                  }).format() : (scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"$\"\r\n                  }).format() : scope.row.dnUnitRate)\r\n                }}\r\n              </div>\r\n              <el-input-number v-show=\"scope.row.showUnitRate\" v-model=\"scope.row.dnUnitRate\" :controls=\"false\"\r\n                               :min=\"0.0001\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               :precision=\"4\" style=\"display:flex;width: 100%\"\r\n                               @blur=\"scope.row.showUnitRate=false\"\r\n                               @change=\"countProfit(scope.row,'unitRate')\"\r\n                               @input=\"countProfit(scope.row,'unitRate')\"\r\n                               @focusout.native=\"scope.row.showUnitRate=false\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单位\" prop=\"quotationUnitId\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationUnit\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationUnit = true\"\r\n              >\r\n                {{ scope.row.dnUnitCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationUnit\"\r\n                           :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\" :pass=\"scope.row.dnUnitCode\"\r\n                           :type=\"'unit'\" @return=\"changeUnit(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"数量\" prop=\"quotationAmount\" width=\"48px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showAmount\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true\"\r\n              >\r\n                {{ scope.row.dnAmount }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showAmount\" v-model=\"scope.row.dnAmount\" :controls=\"false\"\r\n                               :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                               placeholder=\"数量\" style=\"display:flex;width: 100%\"\r\n                               :min=\"0.00\" @blur=\"scope.row.showAmount=false\"\r\n                               @change=\"countProfit(scope.row,'amount')\"\r\n                               @input=\"countProfit(scope.row,'amount')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"汇率\" prop=\"quotationExchangeRate\" width=\"60\">\r\n            <template slot-scope=\"scope\" style=\"display:flex;\">\r\n              <div v-if=\"!scope.row.showCurrencyRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true\"\r\n              >\r\n                {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}\r\n              </div>\r\n              <el-input-number v-show=\"scope.row.showCurrencyRate\" v-model=\"scope.row.basicCurrencyRate\"\r\n                               :controls=\"false\"\r\n                               :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                               :precision=\"4\" :step=\"0.0001\" style=\"width: 100%\"\r\n                               :min=\"0.0001\" @blur=\"scope.row.showCurrencyRate=false\"\r\n                               @change=\"countProfit(scope.row,'currencyRate')\"\r\n                               @input=\"countProfit(scope.row,'currencyRate')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"税率\" prop=\"quotationTaxRate\" width=\"50px\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex;justify-content: center\">\r\n                <div v-if=\"!scope.row.showDutyRate\"\r\n                     @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true\"\r\n                >\r\n                  {{ scope.row.dutyRate }}\r\n                </div>\r\n                <el-input-number v-if=\"scope.row.showDutyRate\" v-model=\"scope.row.dutyRate\" :controls=\"false\"\r\n                                 :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                                 :min=\"0\" style=\"width: 75%\"\r\n                                 @blur=\"scope.row.showDutyRate=false\"\r\n                                 @change=\"countProfit(scope.row,'dutyRate')\"\r\n                                 @input=\"countProfit(scope.row,'dutyRate')\"\r\n                />\r\n                <div>%</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"小计\" prop=\"subtotal\" width=\"75\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                {{\r\n                  currency(scope.row.subtotal, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: (scope.row.dnCurrencyCode === \"RMB\" ? \"¥\" : \"$\")\r\n                  }).format()\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用备注\">\r\n            <template slot-scope=\"scope\">\r\n              <input v-model=\"scope.row.chargeRemark\"\r\n                     :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                     style=\"border: none;width: 100%;height: 100%;\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"审核状态\">\r\n            <template slot-scope=\"scope\">\r\n              {{ auditStatus(scope.row.isAccountConfirmed) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"已收金额\">\r\n            <template slot-scope=\"scope\">\r\n              {{\r\n                scope.row.sqdDnCurrencyPaid\r\n              }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未收余额\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.sqdDnCurrencyBalance }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"所属服务\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                <!--{{ getServiceName(scope.row.sqd_service_type_id) }}-->\r\n                {{ scope.row.serviceName ? scope.row.serviceName : getServiceName(scope.row.sqdServiceTypeId) }}\r\n              </div>\r\n              <!-- <el-select v-else v-model=\"scope.row.sqdServiceTypeId\" filterable placeholder=\"所属服务\">\r\n                 <el-option\r\n                   v-for=\"item in services\"\r\n                   :key=\"item.value\"\r\n                   :label=\"item.label\"\r\n                   :value=\"item.value\"\r\n                 >\r\n                 </el-option>\r\n               </el-select>-->\r\n            </template>\r\n          </el-table-column>\r\n        </el-table-column>\r\n\r\n        <!--应付-->\r\n        <el-table-column v-else label=\"应付明细\" width=\"20px\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-row>\r\n              <el-col :span=\"4\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">{{ currency(payTotalUSD, {separator: \",\", symbol: \"$\"}).format() }}</el-col>\r\n                  <el-col :span=\"12\">{{ currency(payTotalRMB, {separator: \",\", symbol: \"￥\"}).format() }}</el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"10\">\r\n                <el-row>\r\n                  <el-col :span=\"5\">\r\n                    不含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{ currency(payDetailUSD, {separator: \",\", symbol: \"$\"}).format() }}</el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(payDetailRMB, {separator: \",\", symbol: \"￥\"}).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"10\">\r\n                <el-row class=\"unHighlight-text\">\r\n                  <el-col :span=\"4\">\r\n                    含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{\r\n                      currency(payDetailUSDTax, {separator: \",\", symbol: \"$\"}).format()\r\n                    }}\r\n                  </el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(payDetailRMBTax, {\r\n                        separator: \",\",\r\n                        symbol: \"￥\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <el-table-column\r\n            align=\"center\"\r\n            type=\"selection\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column v-if=\"!hiddenSupplier\" align=\"center\" label=\"供应商\" width=\"90\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showSupplier\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showSupplier = true\"\r\n              >\r\n                {{ scope.row.companyName }}\r\n              </div>\r\n              <company-select v-if=\"(companyList && companyList.length>0)&&scope.row.showSupplier\"\r\n                              :class=\"disabled || scope.row.isAccountConfirmed == '1'?'disable-form':''\"\r\n                              :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\" :load-options=\"companyList\"\r\n                              :multiple=\"false\"\r\n                              :no-parent=\"true\"\r\n                              :pass=\"scope.row.clearingCompanyId\"\r\n                              :placeholder=\"'供应商'\"\r\n                              @return=\"scope.row.clearingCompanyId=$event\"\r\n                              @returnData=\"$event.companyShortName==scope.row.companyName?scope.row.showSupplier = false:null\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用\" prop=\"costChargeId\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showCostCharge\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCostCharge = true\"\r\n              >\r\n                {{ scope.row.chargeName }}\r\n              </div>\r\n              <tree-select v-if=\"scope.row.showCostCharge\" :dbn=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                           :pass=\"scope.row.dnChargeNameId\" :placeholder=\"'运费'\" :type=\"'charge'\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                           @return=\"scope.row.dnChargeNameId = $event\"\r\n                           @returnData=\"$event.chargeLocalName == scope.row.chargeName ? scope.row.showCostCharge=false:null\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"货币\" prop=\"costCurrencyId\" width=\"70px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true\"\r\n              >\r\n                {{ scope.row.dnCurrencyCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationCurrency\" :pass=\"scope.row.dnCurrencyCode\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\" :type=\"'currency'\"\r\n                           @return=\"changeCurrency(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单价\" prop=\"inquiryRate\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showUnitRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true\"\r\n              >\r\n                {{\r\n                  scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"¥\"\r\n                  }).format() : scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"$\"\r\n                  }).format() : scope.row.dnUnitRate\r\n                }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showUnitRate\" v-model=\"scope.row.dnUnitRate\" :controls=\"false\" :min=\"0\"\r\n                               style=\"display:flex;width: 100%\" @blur=\"scope.row.showUnitRate=false\"\r\n                               :precision=\"4\"\r\n                               @change=\"countProfit(scope.row,'unitRate')\"\r\n                               @input=\"countProfit(scope.row,'unitRate')\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单位\" prop=\"costUnitId\" width=\"50px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showCostUnit\"\r\n                   @click=\"(disabled || scope.row.isAccountConfirmed == '1')?null:scope.row.showCostUnit = true\"\r\n              >\r\n                {{ scope.row.dnUnitCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showCostUnit\" :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                           :pass=\"scope.row.dnUnitCode\"\r\n                           :type=\"'unit'\" @return=\"changeUnitCost(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"数量\" prop=\"costAmount\" width=\"48px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showAmount\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true\"\r\n              >\r\n                {{ scope.row.dnAmount }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showAmount\" v-model=\"scope.row.dnAmount\" :controls=\"false\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               placeholder=\"数量\" style=\"display:flex;width: 100%\"\r\n                               :min=\"0.00\" @blur=\"scope.row.showAmount=false\"\r\n                               @change=\"countProfit(scope.row,'amount')\"\r\n                               @input=\"countProfit(scope.row,'amount')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"汇率\" prop=\"costExchangeRate\" width=\"60px\">\r\n            <template slot-scope=\"scope\" style=\"display:flex;\">\r\n              <div v-if=\"!scope.row.showCurrencyRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true\"\r\n              >\r\n                {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showCurrencyRate\" v-model=\"scope.row.basicCurrencyRate\" :controls=\"false\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               :precision=\"4\" :step=\"0.0001\" style=\"width: 100%\"\r\n                               :min=\"0.0001\" @blur=\"scope.row.showCurrencyRate=false\"\r\n                               @change=\"countProfit(scope.row,'currencyRate')\"\r\n                               @input=\"countProfit(scope.row,'currencyRate')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"税率\" prop=\"costTaxRate\" width=\"68px\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex;justify-content: center\">\r\n                <div v-if=\"!scope.row.showDutyRate\"\r\n                     @click=\"(disabled || scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true\"\r\n                >\r\n                  {{ scope.row.dutyRate }}\r\n                </div>\r\n                <el-input-number v-if=\"scope.row.showDutyRate\" v-model=\"scope.row.dutyRate\" :controls=\"false\"\r\n                                 :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                                 :min=\"0\" style=\"width: 75%\"\r\n                                 @blur=\"scope.row.showDutyRate=false\"\r\n                                 @change=\"countProfit(scope.row,'dutyRate')\"\r\n                                 @input=\"countProfit(scope.row,'dutyRate')\"\r\n                />\r\n                <div>%</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"小计\" prop=\"costTotal\" width=\"65\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                {{\r\n                  currency(scope.row.subtotal, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: (scope.row.dnCurrencyCode === \"RMB\" ? \"¥\" : \"$\")\r\n                  }).format()\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用备注\">\r\n            <template slot-scope=\"scope\">\r\n              <input v-model=\"scope.row.chargeRemark\" :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                     style=\"border: none\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"审核状态\">\r\n            <template slot-scope=\"scope\">\r\n              {{ auditStatus(scope.row.isAccountConfirmed) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"已付金额\">\r\n            <template slot-scope=\"scope\">\r\n              {{\r\n                scope.row.sqdDnCurrencyPaid\r\n              }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未付余额\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.sqdDnCurrencyBalance }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"生成应收\" prop=\"costTotal\" width=\"65\">\r\n            <template slot=\"header\" slot-scope=\"scope\">\r\n              <el-button\r\n                :disabled=\"disabled\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"copyAllFreight()\"\r\n              >生成应收\r\n              </el-button>\r\n            </template>\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"copyFreight(scope.row)\"\r\n              >复制到应收\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table-column>\r\n\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-button\r\n              :disabled=\"disabled || hasConfirmRow\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"deleteAllItem()\"\r\n              style=\"color: red\"\r\n            >全部删除\r\n            </el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"deleteItem(scope.row)\"\r\n              :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    <el-button style=\"padding: 0\" type=\"text\"\r\n               @click=\"addReceivablePayable\"\r\n               :disabled=\"disabled\"\r\n    >[＋]\r\n    </el-button>\r\n  </el-col>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport {parseTime} from \"@/utils/rich\"\r\n\r\nexport default {\r\n  name: \"charges\",\r\n  components: {CompanySelect, Treeselect},\r\n  props: [\"chargeData\", \"companyList\", \"openChargeList\", \"isReceivable\", \"disabled\",\r\n    \"serviceTypeId\", \"serviceId\", \"hiddenSupplier\", \"rsClientMessageReceivableTaxUSD\",\r\n    \"rsClientMessageReceivableTaxRMB\", \"rsClientMessagePayableTaxUSD\", \"rsClientMessagePayableTaxRMB\",\r\n    \"rsClientMessageReceivableRMB\", \"rsClientMessageReceivableUSD\", \"rsClientMessagePayableRMB\",\r\n    \"rsClientMessagePayableUSD\", \"rsClientMessageProfit\", \"rsClientMessageProfitNoTax\", \"payDetailRMB\",\r\n    \"payDetailUSD\", \"payDetailRMBTax\", \"payDetailUSDTax\", \"rsClientMessageProfitUSD\", \"rsClientMessageProfitRMB\",\r\n    \"rsClientMessageProfitTaxRMB\", \"rsClientMessageProfitTaxUSD\", \"ATD\"],\r\n  watch: {\r\n    chargeData: {\r\n      handler: function (newVal, oldVal) {\r\n        if (!oldVal) {\r\n          this.$emit(\"return\", newVal)\r\n          return\r\n        }\r\n\r\n        // 遍历费用列表，检查币种变化\r\n        newVal ? newVal.forEach((item, index) => {\r\n          const oldItem = oldVal[index]\r\n\r\n          // 检查币种变化并计算小计\r\n          if (item.currency && item.amount) {\r\n            // 如果从 RMB 换成 USD，使用 1/汇率 计算\r\n            if (oldItem && oldItem.currency === \"RMB\" && item.currency === \"USD\") {\r\n              if (item.exchangeRate && item.exchangeRate !== 0) {\r\n                try {\r\n                  // 计算 1/汇率，保留4位小数\r\n                  const inverseRate = currency(1, {precision: 4}).divide(item.exchangeRate).value\r\n\r\n                  // 计算小计: 单价 * 数量 * 汇率 * (1 + 税率/100)\r\n                  item.subtotal = currency(item.dnUnitRate || 0, {precision: 4})\r\n                    .multiply(item.amount)\r\n                    .multiply(inverseRate)\r\n                    .multiply(currency(1).add(currency(item.dutyRate || 0).divide(100)))\r\n                    .value\r\n                } catch (error) {\r\n                  console.error(\"计算小计出错:\", error)\r\n                  item.subtotal = 0\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }) : null\r\n\r\n        this.$emit(\"return\", newVal ? newVal : [])\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => this.$refs.chargeTable.toggleRowSelection(item, true)) : null\r\n\r\n    this.profitCount(\"RMB\")\r\n  },\r\n  computed: {\r\n\r\n    hasConfirmRow() {\r\n      let result = false;\r\n      (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => {\r\n        if (item.isAccountConfirmed === \"1\") {\r\n          result = true\r\n        }\r\n      }) : null\r\n      return result\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      payTotalRMB: 0,\r\n      payTotalUSD: 0,\r\n      showClientName: null,\r\n      currencyCode: \"RMB\",\r\n      profit: 0,\r\n      profitTax: 0,\r\n      exchangeRate: 0,\r\n      services: [{\r\n        value: 1,\r\n        label: \"海运\"\r\n      }, {\r\n        value: 10,\r\n        label: \"空运\"\r\n      }, {\r\n        value: 20,\r\n        label: \"铁路\"\r\n      }, {\r\n        value: 40,\r\n        label: \"快递\"\r\n      }, {\r\n        value: 50,\r\n        label: \"拖车\"\r\n      }, {\r\n        value: 60,\r\n        label: \"报关\"\r\n      }, {\r\n        value: 70,\r\n        label: \"清关派送\"\r\n      }, {\r\n        value: 80,\r\n        label: \"码头仓储\"\r\n      }, {\r\n        value: 90,\r\n        label: \"检验证书\"\r\n      }, {\r\n        value: 100,\r\n        label: \"保险\"\r\n      }, {\r\n        value: 101,\r\n        label: \"扩展服务\"\r\n      }],\r\n      service: [{\r\n        value: 1,\r\n        label: \"基础服务\"\r\n      }, {\r\n        value: 4,\r\n        label: \"前程运输\"\r\n      }, {\r\n        value: 5,\r\n        label: \"出口报关\"\r\n      }, {\r\n        value: 6,\r\n        label: \"进口清关\"\r\n      }, {value: 2, label: \"海运\"}\r\n        , {value: 3, label: \"陆运\"}\r\n        , {value: 4, label: \"铁路\"}\r\n        , {value: 5, label: \"空运\"}\r\n        , {value: 6, label: \"快递\"}\r\n        , {value: 21, label: \"整柜海运\"}\r\n        , {value: 22, label: \"拼柜海运\"}\r\n        , {value: 23, label: \"散杂船\"}\r\n        , {value: 24, label: \"滚装船\"}\r\n        , {value: 41, label: \"整柜铁路\"}\r\n        , {value: 42, label: \"拼柜铁路\"}\r\n        , {value: 43, label: \"铁路车皮\"}\r\n        , {value: 51, label: \"空运普舱\"}\r\n        , {value: 52, label: \"空运包板\"}\r\n        , {value: 53, label: \"空运包机\"}\r\n        , {value: 54, label: \"空运行李\"}\r\n        , {value: 961, label: \"前程运输\"}\r\n        , {value: 964, label: \"进口清关\"}\r\n        , {value: 7, label: \"出口报关\"}\r\n      ],\r\n      chargeRemark: null,\r\n      profitOpen: false,\r\n      profitTableData: []\r\n    }\r\n  },\r\n  methods: {\r\n    openProfit() {\r\n      this.profitTableData = []\r\n\r\n      let RMB = {}\r\n      RMB.currencyCode = \"RMB\"\r\n      RMB.receivable = this.rsClientMessageReceivableRMB\r\n      RMB.payable = this.rsClientMessagePayableRMB\r\n      // 不含税利润\r\n      RMB.profit = this.rsClientMessageProfitRMB\r\n      // 含税应收\r\n      RMB.receivableTax = this.rsClientMessageReceivableTaxRMB\r\n      // 含税应付\r\n      RMB.payableTax = this.rsClientMessagePayableTaxRMB\r\n      // 含税利润\r\n      RMB.profitTax = this.rsClientMessageProfitTaxRMB\r\n\r\n      let USD = {}\r\n      USD.currencyCode = \"USD\"\r\n      USD.receivable = this.rsClientMessageReceivableUSD\r\n      USD.payable = this.rsClientMessagePayableUSD\r\n      USD.profit = this.rsClientMessageProfitUSD\r\n      USD.receivableTax = this.rsClientMessageReceivableTaxUSD\r\n      USD.payableTax = this.rsClientMessagePayableTaxUSD\r\n      USD.profitTax = this.rsClientMessageProfitTaxUSD\r\n\r\n      this.profitTableData.push(RMB)\r\n      this.profitTableData.push(USD)\r\n\r\n      this.profitCount(\"RMB\")\r\n    },\r\n    profitCount(type) {\r\n      let exchangeRate\r\n      for (const a of this.$store.state.data.exchangeRateList) {\r\n        if (this.ATD) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && \"USD\" == a.overseaCurrency\r\n            && parseTime(a.validFrom) <= parseTime(this.ATD)\r\n            && parseTime(this.ATD) <= parseTime(a.validTo)\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n        if (!exchangeRate) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && \"USD\" == a.overseaCurrency\r\n            && parseTime(a.validFrom) <= parseTime(new Date())\r\n            && parseTime(new Date()) <= parseTime(a.validTo)) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      }\r\n      this.exchangeRate = exchangeRate\r\n\r\n      if (type === \"RMB\") {\r\n        // 都折算成人民币\r\n        this.profit = currency(this.rsClientMessageProfitUSD).multiply(exchangeRate).add(this.rsClientMessageProfitRMB).value\r\n        this.profitTax = currency(this.rsClientMessageProfitTaxUSD).multiply(exchangeRate).add(this.rsClientMessageProfitTaxRMB).value\r\n      } else {\r\n        this.profit = currency(this.rsClientMessageProfitRMB).divide(exchangeRate).add(this.rsClientMessageProfitUSD).value\r\n        this.profitTax = currency(this.rsClientMessageProfitTaxRMB).divide(exchangeRate).add(this.rsClientMessageProfitTaxUSD).value\r\n      }\r\n    },\r\n    auditStatus(status) {\r\n      return status == 1 ? \"已审核\" : \"未审核\"\r\n    },\r\n    selectCharge(target, row) {\r\n      row.dnChargeNameId = target.chargeId\r\n      row.chargeName = target.chargeLocalName\r\n    },\r\n    handleSelectionChange(val) {\r\n      this.$emit(\"selectRow\", val)\r\n\r\n      this.payTotalUSD = 0\r\n      this.payTotalRMB = 0\r\n      val ? val.map(item => {\r\n        if (item.isRecievingOrPaying == 1) {\r\n          if (item.dnCurrencyCode === \"USD\") {\r\n            this.payTotalUSD = currency(this.payTotalUSD).add(item.subtotal)\r\n          } else {\r\n            this.payTotalRMB = currency(this.payTotalRMB).add(item.subtotal)\r\n          }\r\n\r\n        }\r\n      }) : null\r\n\r\n    },\r\n    currency,\r\n    getServiceName(id) {\r\n      let serviceName = \"\"\r\n      this.services.map(obj => {\r\n        obj.value === id ? serviceName = obj.label : null\r\n      })\r\n      return serviceName\r\n    },\r\n    copyFreight(row) {\r\n      if (this.companyList.length > 0) {\r\n        row.payClearingCompanyId = this.companyList[0].companyId\r\n        row.payCompanyName = this.companyList[0].companyShortName\r\n      }\r\n      row.isAccountConfirmed = 0\r\n      // 报价列表跳转订舱时没有公司列表,复制到应收没有客户信息\r\n      let data = this._.cloneDeep(row)\r\n\r\n      this.$emit(\"copyFreight\", {...data, chargeId: null})\r\n    },\r\n    copyAllFreight() {\r\n      if (!this.companyList.length > 0) {\r\n        this.$modal.alertWarning(\"请先选择委托单位或关联单位\")\r\n        return\r\n      }\r\n\r\n      this.chargeData.map(charge => {\r\n        charge.payClearingCompanyId = this.companyList[0].companyId\r\n        charge.payCompanyName = this.companyList[0].companyShortName\r\n        charge.isRecievingOrPaying = 0\r\n        charge.isAccountConfirmed = 0\r\n        charge.chargeId = null\r\n        this.$emit(\"copyFreight\", this._.cloneDeep(charge))\r\n      })\r\n    },\r\n    changeUnitCost(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showCostUnit = false\r\n      })\r\n    },\r\n    changeUnit(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showQuotationUnit = false\r\n      })\r\n    },\r\n    handleChargeSelect(row, data) {\r\n      if (row.chargeLocalName === data.chargeName) {\r\n        row.chargeName = data.chargeLocalName\r\n        row.showQuotationCharge = false\r\n      }\r\n      if (row.currencyCode == null && data.currencyCode) {\r\n        row.dnCurrencyCode = data.currencyCode\r\n      }\r\n    },\r\n    changeCurrency(row, currencyCode) {\r\n      row.dnCurrencyCode = currencyCode\r\n      /* let exchangeRate\r\n      if (currencyCode === \"USD\") {\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && row.dnCurrencyCode == a.overseaCurrency\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      } */\r\n\r\n      this.$nextTick(() => {\r\n        // row.basicCurrencyRate = exchangeRate ? exchangeRate : 1\r\n        row.showQuotationCurrency = false\r\n      })\r\n    },\r\n    /** 序号 */\r\n    rowIndex({ row, rowIndex }) {\r\n      row.id = rowIndex + 1\r\n    },\r\n    addReceivablePayable() {\r\n      let obj = {\r\n        showClient: true,\r\n        showSupplier: true,\r\n        showQuotationCharge: true,\r\n        showCostCharge: true,\r\n        showQuotationCurrency: true,\r\n        showCostCurrency: true,\r\n        showQuotationUnit: true,\r\n        showCostUnit: true,\r\n        showStrategy: true,\r\n        showUnitRate: true,\r\n        showAmount: true,\r\n        showCurrencyRate: true,\r\n        showDutyRate: true,\r\n        basicCurrencyRate: 1,\r\n        dutyRate: 0,\r\n        dnAmount: 1,\r\n        // 应收还是应付\r\n        isRecievingOrPaying: this.isReceivable ? 0 : 1,\r\n        clearingCompanyId: this.chargeData.length > 0 ? this.chargeData[this.chargeData.length - 1].clearingCompanyId : null\r\n      }\r\n      if (this.serviceTypeId === 1) obj.sqdServiceTypeId = 1\r\n      if (this.serviceTypeId === 10) obj.sqdServiceTypeId = 10\r\n      if (this.serviceTypeId === 20) obj.sqdServiceTypeId = 20\r\n      if (this.serviceTypeId === 40) obj.sqdServiceTypeId = 40\r\n      if (this.serviceTypeId === 50) obj.sqdServiceTypeId = 50\r\n      if (this.serviceTypeId === 60) obj.sqdServiceTypeId = 60\r\n      if (this.serviceTypeId === 70) obj.sqdServiceTypeId = 70\r\n      if (this.serviceTypeId === 80) obj.sqdServiceTypeId = 80\r\n      if (this.serviceTypeId === 90) obj.sqdServiceTypeId = 90\r\n      if (this.serviceTypeId === 100) obj.sqdServiceTypeId = 100\r\n      if (this.serviceTypeId === 101) obj.sqdServiceTypeId = 101\r\n      this.chargeData.push(obj)\r\n    },\r\n    countProfit(row, category) {\r\n      // 确保所有必要的值都存在且有效\r\n      if (!row) return\r\n\r\n      // 使用currency.js来处理数值,避免精度损失\r\n      const unitRate = row.dnUnitRate || 0\r\n      const amount = row.dnAmount || 0\r\n      const currencyRate = currency(row.basicCurrencyRate || 1, {precision: 4}).value\r\n      const dutyRate = currency(row.dutyRate || 0).value\r\n\r\n      try {\r\n        // 计算小计\r\n        const subtotal = currency(unitRate, {precision: 4})\r\n          .multiply(amount)\r\n          .multiply(currencyRate)\r\n          .multiply(currency(1).add(currency(dutyRate).divide(100)))\r\n          .value\r\n\r\n        // 更新行数据\r\n        row.subtotal = currency(subtotal, {precision: 2}).value\r\n        row.sqdDnCurrencyBalance = row.isAccountConfirmed === \"0\" ? currency(subtotal, {precision: 2}).value : row.sqdDnCurrencyBalance\r\n\r\n        // 根据不同的输入类型关闭对应的编辑状态\r\n        switch (category) {\r\n          case \"strategy\":\r\n            row.showStrategy = false\r\n            break\r\n          case \"unitRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"amount\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"currencyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"dutyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n        }\r\n\r\n        // 触发数据更新\r\n        this.$emit(\"return\", this.chargeData)\r\n\r\n      } catch (error) {\r\n        console.error(\"计算小计时出错:\", error)\r\n        this.$message.error(\"计算小计时出错,请检查输入值是否正确\")\r\n      }\r\n    },\r\n    deleteItem(row) {\r\n      this.$emit(\"deleteItem\", row)\r\n    },\r\n    deleteAllItem(row) {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n    companyNormalizer(node) {\r\n      return {\r\n        id: node.companyId,\r\n        label: (node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\") + \",\" + pinyin.getFullChars((node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\"))\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\ninput:focus {\r\n  outline: none;\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n</style>\r\n"]}]}