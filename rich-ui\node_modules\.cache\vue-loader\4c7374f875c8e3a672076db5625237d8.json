{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregatorBackGround\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregatorBackGround\\index.vue", "mtime": 1754646305886}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgbW9tZW50IGZyb20gIm1vbWVudCINCmltcG9ydCBjdXJyZW5jeSBmcm9tICJjdXJyZW5jeS5qcyINCmltcG9ydCB7c2F2ZUFnZ3JlZ2F0b3JDb25maWcsIGxvYWRBZ2dyZWdhdG9yQ29uZmlncywgZGVsZXRlQWdncmVnYXRvckNvbmZpZ30gZnJvbSAiQC9hcGkvc3lzdGVtL2FnZ3JlZ2F0b3IiDQppbXBvcnQgaHRtbDJwZGYgZnJvbSAiaHRtbDJwZGYuanMiDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkRhdGFBZ2dyZWdhdG9yQmFja0dyb3VuZCIsDQogIHByb3BzOiB7DQogICAgLy8g5Y+v55So5a2X5q616YWN572u5pig5bCEDQogICAgZmllbGRMYWJlbE1hcDogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICBkZWZhdWx0OiAoKSA9PiAoe30pDQogICAgfSwNCiAgICAvLyDmjIflrprmlbDmja7mnaXmupDnsbvlnovvvIznlKjkuo7lkI7nq6/mn6Xor6INCiAgICBkYXRhU291cmNlVHlwZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICBkZWZhdWx0OiAncmN0JyAvLyDpu5jorqTmmK/mk43kvZzljZXmlbDmja4NCiAgICB9LA0KICAgIC8vIOS7jueItue7hOS7tuS8oOWFpeeahOaxh+aAu+WHveaVsA0KICAgIGFnZ3JlZ2F0ZUZ1bmN0aW9uOiB7DQogICAgICB0eXBlOiBGdW5jdGlvbiwNCiAgICAgIHJlcXVpcmVkOiB0cnVlDQogICAgfSwNCiAgICBjb25maWdUeXBlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICByZXF1aXJlZDogZmFsc2UNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGNvbmZpZ05hbWU6ICIiLA0KICAgICAgY29uZmlnOiB7DQogICAgICAgIG5hbWU6ICIiLA0KICAgICAgICBwcmltYXJ5RmllbGQ6ICIiLA0KICAgICAgICBtYXRjaE9wdGlvbnM6IHsNCiAgICAgICAgICBleGFjdDogdHJ1ZSwNCiAgICAgICAgICBjYXNlU2Vuc2l0aXZlOiBmYWxzZQ0KICAgICAgICB9LA0KICAgICAgICBkYXRlRmllbGQ6ICIiLA0KICAgICAgICBkYXRlT3B0aW9uczogew0KICAgICAgICAgIGNvbnZlcnRUb051bWJlcjogZmFsc2UsDQogICAgICAgICAgZm9ybWF0VHlwZTogImRheSINCiAgICAgICAgfSwNCiAgICAgICAgc2hvd0RldGFpbHM6IGZhbHNlLA0KICAgICAgICBmaWVsZHM6IFtdLA0KICAgICAgICBmaWx0ZXJzOiBbXSAvLyDlrZjlgqjnrZvpgInmnaHku7YNCiAgICAgIH0sDQogICAgICBkYXRlT3B0aW9uczogWw0KICAgICAgICB7bGFiZWw6ICLmjInlubQiLCB2YWx1ZTogInllYXIifSwNCiAgICAgICAge2xhYmVsOiAi5oyJ5pyIIiwgdmFsdWU6ICJtb250aCJ9LA0KICAgICAgICB7bGFiZWw6ICLmjInlkagiLCB2YWx1ZTogIndlZWsifSwNCiAgICAgICAge2xhYmVsOiAi5oyJ5pelIiwgdmFsdWU6ICJkYXkifSwNCiAgICAgICAge2xhYmVsOiAi5oyJ5pe2IiwgdmFsdWU6ICJob3VyIn0sDQogICAgICAgIHtsYWJlbDogIuaMieWIhiIsIHZhbHVlOiAibWludXRlIn0NCiAgICAgIF0sDQogICAgICBhZ2dyZWdhdGlvbk9wdGlvbnM6IFsNCiAgICAgICAge2xhYmVsOiAi6K6h5pWwIiwgdmFsdWU6ICJjb3VudCJ9LA0KICAgICAgICB7bGFiZWw6ICLmsYLlkowiLCB2YWx1ZTogInN1bSJ9LA0KICAgICAgICB7bGFiZWw6ICLlubPlnYflgLwiLCB2YWx1ZTogImF2ZyJ9LA0KICAgICAgICB7bGFiZWw6ICLmlrnlt64iLCB2YWx1ZTogInZhcmlhbmNlIn0sDQogICAgICAgIHtsYWJlbDogIuacgOWkp+WAvCIsIHZhbHVlOiAibWF4In0sDQogICAgICAgIHtsYWJlbDogIuacgOWwj+WAvCIsIHZhbHVlOiAibWluIn0NCiAgICAgIF0sDQogICAgICBvcGVyYXRvck9wdGlvbnM6IFsNCiAgICAgICAge2xhYmVsOiAi562J5LqOIiwgdmFsdWU6ICJlcSJ9LA0KICAgICAgICB7bGFiZWw6ICLkuI3nrYnkuo4iLCB2YWx1ZTogIm5lIn0sDQogICAgICAgIHtsYWJlbDogIuWkp+S6jiIsIHZhbHVlOiAiZ3QifSwNCiAgICAgICAge2xhYmVsOiAi5aSn5LqO562J5LqOIiwgdmFsdWU6ICJnZSJ9LA0KICAgICAgICB7bGFiZWw6ICLlsI/kuo4iLCB2YWx1ZTogImx0In0sDQogICAgICAgIHtsYWJlbDogIuWwj+S6juetieS6jiIsIHZhbHVlOiAibGUifSwNCiAgICAgICAge2xhYmVsOiAi5YyF5ZCrIiwgdmFsdWU6ICJjb250YWlucyJ9LA0KICAgICAgICB7bGFiZWw6ICLlvIDlp4vkuo4iLCB2YWx1ZTogInN0YXJ0c1dpdGgifSwNCiAgICAgICAge2xhYmVsOiAi57uT5p2f5LqOIiwgdmFsdWU6ICJlbmRzV2l0aCJ9DQogICAgICBdLA0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBjb25maWdEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGZpbHRlckRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgc2F2ZWRDb25maWdzOiBbXSwNCiAgICAgIGNvbmZpZ0xvYWRpbmc6IGZhbHNlLA0KICAgICAgaXNMYW5kc2NhcGU6IGZhbHNlLA0KICAgICAgc2hvd1Jlc3VsdDogZmFsc2UsDQogICAgICBwcm9jZXNzZWREYXRhOiBbXSwNCiAgICAgIGN1cnJlbnRGaWx0ZXI6IHsNCiAgICAgICAgZmllbGQ6ICIiLA0KICAgICAgICBvcGVyYXRvcjogImVxIiwNCiAgICAgICAgdmFsdWU6ICIiDQogICAgICB9DQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8vIOWPr+eUqOWtl+auteWIl+ihqA0KICAgIGF2YWlsYWJsZUZpZWxkcygpIHsNCiAgICAgIC8vIOi/lOWbnuWcqCBmaWVsZExhYmVsTWFwIOS4reWumuS5ieeahOaJgOacieWtl+autQ0KICAgICAgcmV0dXJuIE9iamVjdC5rZXlzKHRoaXMuZmllbGRMYWJlbE1hcCkNCiAgICB9LA0KDQogICAgLy8g5pel5pyf57G75Z6L5a2X5q615YiX6KGoDQogICAgZGF0ZUZpZWxkcygpIHsNCiAgICAgIHJldHVybiB0aGlzLmF2YWlsYWJsZUZpZWxkcy5maWx0ZXIoZmllbGQgPT4gew0KICAgICAgICAvLyDmo4Dmn6UgZmllbGRMYWJlbE1hcCDkuK3nmoQgZGlzcGxheSDlsZ7mgKcNCiAgICAgICAgcmV0dXJuIHRoaXMuZmllbGRMYWJlbE1hcFtmaWVsZF0gJiYgdGhpcy5maWVsZExhYmVsTWFwW2ZpZWxkXS5kaXNwbGF5ID09PSAiZGF0ZSINCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOWIhue7hOWtl+auteWQjeensA0KICAgIGdyb3VwRmllbGROYW1lKCkgew0KICAgICAgaWYgKHRoaXMuY29uZmlnLnByaW1hcnlGaWVsZCAmJiB0aGlzLmNvbmZpZy5kYXRlRmllbGQpIHsNCiAgICAgICAgcmV0dXJuIGAke3RoaXMuZ2V0RmllbGRMYWJlbCh0aGlzLmNvbmZpZy5kYXRlRmllbGQpfSske3RoaXMuZ2V0RmllbGRMYWJlbCh0aGlzLmNvbmZpZy5wcmltYXJ5RmllbGQpfWANCiAgICAgIH0gZWxzZSBpZiAodGhpcy5jb25maWcucHJpbWFyeUZpZWxkKSB7DQogICAgICAgIHJldHVybiB0aGlzLmdldEZpZWxkTGFiZWwodGhpcy5jb25maWcucHJpbWFyeUZpZWxkKQ0KICAgICAgfSBlbHNlIGlmICh0aGlzLmNvbmZpZy5kYXRlRmllbGQpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0RmllbGRMYWJlbCh0aGlzLmNvbmZpZy5kYXRlRmllbGQpDQogICAgICB9DQogICAgICByZXR1cm4gIuWIhue7hCINCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKioNCiAgICAgKiDmoLnmja7lrZfmrrXplK7ojrflj5blrZfmrrXmoIfnrb4NCiAgICAgKiBAcGFyYW0ge1N0cmluZ30gZmllbGQgLSDlrZfmrrXnmoTplK4NCiAgICAgKiBAcmV0dXJucyB7U3RyaW5nfSDlrZfmrrXnmoTmoIfnrb4NCiAgICAgKi8NCiAgICBnZXRGaWVsZExhYmVsKGZpZWxkKSB7DQogICAgICByZXR1cm4gdGhpcy5maWVsZExhYmVsTWFwW2ZpZWxkXT8ubmFtZSB8fCBmaWVsZA0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDmoLnmja7mk43kvZznrKbku6PnoIHojrflj5bmk43kvZznrKbmoIfnrb4NCiAgICAgKiBAcGFyYW0ge1N0cmluZ30gb3AgLSDmk43kvZznrKbku6PnoIENCiAgICAgKiBAcmV0dXJucyB7U3RyaW5nfSDmk43kvZznrKbmoIfnrb4NCiAgICAgKi8NCiAgICBnZXRPcGVyYXRvckxhYmVsKG9wKSB7DQogICAgICBjb25zdCBvcGVyYXRvciA9IHRoaXMub3BlcmF0b3JPcHRpb25zLmZpbmQoaXRlbSA9PiBpdGVtLnZhbHVlID09PSBvcCkNCiAgICAgIHJldHVybiBvcGVyYXRvciA/IG9wZXJhdG9yLmxhYmVsIDogb3ANCiAgICB9LA0KDQogICAgLyoqDQogICAgICog5omT5byA562b6YCJ5p2h5Lu25a+56K+d5qGGDQogICAgICovDQogICAgb3BlbkZpbHRlckRpYWxvZygpIHsNCiAgICAgIHRoaXMuY3VycmVudEZpbHRlciA9IHsNCiAgICAgICAgZmllbGQ6ICIiLA0KICAgICAgICBvcGVyYXRvcjogImVxIiwNCiAgICAgICAgdmFsdWU6ICIiDQogICAgICB9DQogICAgICB0aGlzLmZpbHRlckRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOa3u+WKoOetm+mAieadoeS7tg0KICAgICAqLw0KICAgIGFkZEZpbHRlcigpIHsNCiAgICAgIGlmICghdGhpcy5jdXJyZW50RmlsdGVyLmZpZWxkKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36YCJ5oup562b6YCJ5a2X5q61IikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIGlmICghdGhpcy5jdXJyZW50RmlsdGVyLnZhbHVlICYmIHRoaXMuY3VycmVudEZpbHRlci52YWx1ZSAhPT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+i+k+WFpeetm+mAieWAvCIpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDliJ3lp4vljJZmaWx0ZXJz5pWw57uE77yI5aaC5p6c5LiN5a2Y5Zyo77yJDQogICAgICBpZiAoIXRoaXMuY29uZmlnLmZpbHRlcnMpIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuY29uZmlnLCAnZmlsdGVycycsIFtdKQ0KICAgICAgfQ0KDQogICAgICAvLyDmt7vliqDmlrDnmoTnrZvpgInmnaHku7YNCiAgICAgIHRoaXMuY29uZmlnLmZpbHRlcnMucHVzaCh7Li4udGhpcy5jdXJyZW50RmlsdGVyfSkNCg0KICAgICAgLy8g6YeN572u5b2T5YmN562b6YCJ5p2h5Lu2DQogICAgICB0aGlzLmN1cnJlbnRGaWx0ZXIgPSB7DQogICAgICAgIGZpZWxkOiAiIiwNCiAgICAgICAgb3BlcmF0b3I6ICJlcSIsDQogICAgICAgIHZhbHVlOiAiIg0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDnp7vpmaTnrZvpgInmnaHku7YNCiAgICAgKiBAcGFyYW0ge051bWJlcn0gaW5kZXggLSDopoHnp7vpmaTnmoTnrZvpgInmnaHku7bntKLlvJUNCiAgICAgKi8NCiAgICByZW1vdmVGaWx0ZXIoaW5kZXgpIHsNCiAgICAgIHRoaXMuY29uZmlnLmZpbHRlcnMuc3BsaWNlKGluZGV4LCAxKQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDojrflj5blrZfmrrXnmoTmmL7npLrnsbvlnosNCiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZmllbGRLZXkgLSDlrZfmrrXmoIfor4YNCiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfSDlrZfmrrXmmL7npLrnsbvlnovvvIh0ZXh0L251bWJlci9kYXRlL2Jvb2xlYW4vY3VzdG9t77yJDQogICAgICovDQogICAgZ2V0RmllbGREaXNwbGF5KGZpZWxkS2V5KSB7DQogICAgICBjb25zdCBmaWVsZENvbmZpZyA9IHRoaXMuZmllbGRMYWJlbE1hcFtmaWVsZEtleV0NCiAgICAgIGlmICghZmllbGRDb25maWcpIHJldHVybiAidGV4dCINCg0KICAgICAgLy8g5qOA5p+l5piv5ZCm5piv6Ieq5a6a5LmJ5pa55rOVDQogICAgICBpZiAoZmllbGRDb25maWcuZGlzcGxheSAmJiB0eXBlb2YgdGhpc1tmaWVsZENvbmZpZy5kaXNwbGF5XSA9PT0gImZ1bmN0aW9uIikgew0KICAgICAgICByZXR1cm4gImN1c3RvbSINCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIGZpZWxkQ29uZmlnLmRpc3BsYXkgfHwgInRleHQiDQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOWIpOaWreWtl+auteaYr+WQpuWPr+S7pei/m+ihjOaxh+aAu+iuoeeulw0KICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmaWVsZEtleSAtIOWtl+auteagh+ivhg0KICAgICAqIEByZXR1cm5zIHtib29sZWFufSDmmK/lkKblj6/msYfmgLsNCiAgICAgKi8NCiAgICBpc0FnZ3JlZ2F0YWJsZShmaWVsZEtleSkgew0KICAgICAgY29uc3QgZmllbGRDb25maWcgPSB0aGlzLmZpZWxkTGFiZWxNYXBbZmllbGRLZXldDQogICAgICByZXR1cm4gZmllbGRDb25maWc/LmFnZ3JlZ2F0ZWQgfHwgZmFsc2UNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog5re75Yqg5paw55qE5a2X5q616YWN572u6KGMDQogICAgICovDQogICAgYWRkRmllbGQoKSB7DQogICAgICB0aGlzLmNvbmZpZy5maWVsZHMucHVzaCh7DQogICAgICAgIGZpZWxkS2V5OiAiIiwNCiAgICAgICAgYWdncmVnYXRpb246ICJub25lIiwNCiAgICAgICAgZm9ybWF0OiAibm9uZSIsDQogICAgICAgIHNvcnQ6ICJub25lIiwNCiAgICAgICAgaGlkZVplcm9WYWx1ZXM6IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDliKDpmaTmjIflrprntKLlvJXnmoTlrZfmrrXphY3nva7ooYwNCiAgICAgKiBAcGFyYW0ge251bWJlcn0gaW5kZXggLSDopoHliKDpmaTnmoTlrZfmrrXntKLlvJUNCiAgICAgKi8NCiAgICByZW1vdmVGaWVsZChpbmRleCkgew0KICAgICAgdGhpcy5jb25maWcuZmllbGRzLnNwbGljZShpbmRleCwgMSkNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog56e75Yqo5a2X5q616YWN572u6KGM55qE5L2N572uDQogICAgICogQHBhcmFtIHtudW1iZXJ9IGluZGV4IC0g5b2T5YmN5a2X5q6155qE57Si5byVDQogICAgICogQHBhcmFtIHtzdHJpbmd9IGRpcmVjdGlvbiAtIOenu+WKqOaWueWQke+8jCd1cCcg5oiWICdkb3duJw0KICAgICAqLw0KICAgIG1vdmVGaWVsZChpbmRleCwgZGlyZWN0aW9uKSB7DQogICAgICBjb25zdCBmaWVsZHMgPSBbLi4udGhpcy5jb25maWcuZmllbGRzXSAvLyDliJvlu7rmlbDnu4Tlia/mnKwNCg0KICAgICAgaWYgKGRpcmVjdGlvbiA9PT0gInVwIiAmJiBpbmRleCA+IDApIHsNCiAgICAgICAgLy8g5ZCR5LiK56e75Yqo77yM5LiO5LiK5LiA5Liq5YWD57Sg5Lqk5o2i5L2N572uDQogICAgICAgIFtmaWVsZHNbaW5kZXhdLCBmaWVsZHNbaW5kZXggLSAxXV0gPSBbZmllbGRzW2luZGV4IC0gMV0sIGZpZWxkc1tpbmRleF1dDQogICAgICB9IGVsc2UgaWYgKGRpcmVjdGlvbiA9PT0gImRvd24iICYmIGluZGV4IDwgZmllbGRzLmxlbmd0aCAtIDEpIHsNCiAgICAgICAgLy8g5ZCR5LiL56e75Yqo77yM5LiO5LiL5LiA5Liq5YWD57Sg5Lqk5o2i5L2N572uDQogICAgICAgIFtmaWVsZHNbaW5kZXhdLCBmaWVsZHNbaW5kZXggKyAxXV0gPSBbZmllbGRzW2luZGV4ICsgMV0sIGZpZWxkc1tpbmRleF1dDQogICAgICB9DQoNCiAgICAgIC8vIOS9v+eUqOaVtOS4quaWsOaVsOe7hOabv+aNou+8jOehruS/neWTjeW6lOW8j+abtOaWsA0KICAgICAgdGhpcy4kc2V0KHRoaXMuY29uZmlnLCAiZmllbGRzIiwgZmllbGRzKQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDlpITnkIblrZfmrrXpgInmi6nlj5jmm7Tkuovku7YNCiAgICAgKiBAcGFyYW0ge251bWJlcn0gaW5kZXggLSDlj5jmm7TnmoTlrZfmrrXntKLlvJUNCiAgICAgKi8NCiAgICBoYW5kbGVGaWVsZFNlbGVjdChpbmRleCkgew0KICAgICAgY29uc3QgZmllbGQgPSB0aGlzLmNvbmZpZy5maWVsZHNbaW5kZXhdDQogICAgICBjb25zdCBmaWVsZENvbmZpZyA9IHRoaXMuZmllbGRMYWJlbE1hcFtmaWVsZC5maWVsZEtleV0NCiAgICAgIGlmIChmaWVsZENvbmZpZykgew0KICAgICAgICAvLyDmoLnmja7lrZfmrrXphY3nva7orr7nva7pu5jorqTlgLwNCiAgICAgICAgZmllbGQuZm9ybWF0ID0gdGhpcy5nZXREZWZhdWx0Rm9ybWF0KGZpZWxkQ29uZmlnLmRpc3BsYXkpDQogICAgICAgIGZpZWxkLmFnZ3JlZ2F0aW9uID0gZmllbGRDb25maWcuYWdncmVnYXRlZCA/ICJzdW0iIDogIm5vbmUiDQogICAgICAgIGZpZWxkLnNvcnQgPSAibm9uZSIgLy8g6buY6K6k5LiN5o6S5bqPDQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOagueaNruaYvuekuuexu+Wei+iOt+WPlum7mOiupOeahOagvOW8j+WMluaWueW8jw0KICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkaXNwbGF5VHlwZSAtIOaYvuekuuexu+Weiw0KICAgICAqIEByZXR1cm5zIHtzdHJpbmd9IOm7mOiupOagvOW8jw0KICAgICAqLw0KICAgIGdldERlZmF1bHRGb3JtYXQoZGlzcGxheVR5cGUpIHsNCiAgICAgIHN3aXRjaCAoZGlzcGxheVR5cGUpIHsNCiAgICAgICAgY2FzZSAiZGF0ZSI6DQogICAgICAgICAgcmV0dXJuICJZWVlZLU1NLUREIg0KICAgICAgICBjYXNlICJudW1iZXIiOg0KICAgICAgICAgIHJldHVybiAiZGVjaW1hbCINCiAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICByZXR1cm4gIm5vbmUiDQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOmHjee9rumFjee9ruWIsOWIneWni+eKtuaAgQ0KICAgICAqLw0KICAgIHJlc2V0Q29uZmlnKCkgew0KICAgICAgdGhpcy5jb25maWcgPSB7DQogICAgICAgIG5hbWU6ICIiLA0KICAgICAgICBwcmltYXJ5RmllbGQ6ICIiLA0KICAgICAgICBtYXRjaE9wdGlvbnM6IHsNCiAgICAgICAgICBleGFjdDogdHJ1ZSwNCiAgICAgICAgICBjYXNlU2Vuc2l0aXZlOiBmYWxzZQ0KICAgICAgICB9LA0KICAgICAgICBkYXRlRmllbGQ6ICIiLA0KICAgICAgICBkYXRlT3B0aW9uczogew0KICAgICAgICAgIGNvbnZlcnRUb051bWJlcjogZmFsc2UsDQogICAgICAgICAgZm9ybWF0VHlwZTogImRheSINCiAgICAgICAgfSwNCiAgICAgICAgc2hvd0RldGFpbHM6IGZhbHNlLA0KICAgICAgICBmaWVsZHM6IFtdLA0KICAgICAgICBmaWx0ZXJzOiBbXQ0KICAgICAgfQ0KICAgICAgdGhpcy5zaG93UmVzdWx0ID0gZmFsc2UNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog6I635Y+W5pel5pyf5qC85byP5YyW5qih5byPDQogICAgICogQHJldHVybnMge1N0cmluZ30g5pel5pyf5qC85byPDQogICAgICovDQogICAgZ2V0RGF0ZUZvcm1hdCgpIHsNCiAgICAgIHN3aXRjaCAodGhpcy5jb25maWcuZGF0ZU9wdGlvbnMuZm9ybWF0VHlwZSkgew0KICAgICAgICBjYXNlICJ5ZWFyIjoNCiAgICAgICAgICByZXR1cm4gIllZWVkiDQogICAgICAgIGNhc2UgIm1vbnRoIjoNCiAgICAgICAgICByZXR1cm4gIllZWVktTU0iDQogICAgICAgIGNhc2UgImRheSI6DQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgcmV0dXJuICJZWVlZLU1NLUREIg0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDosIPnlKjlkI7nq6/mnI3liqHov5vooYzmlbDmja7msYfmgLsNCiAgICAgKi8NCiAgICBhc3luYyBoYW5kbGVTZXJ2ZXJBZ2dyZWdhdGUoKSB7DQogICAgICAvLyDpqozor4HliIbnu4Tkvp3mja7lkozliIbnu4Tml6XmnJ/oh7PlsJHloavlhpnkuIDkuKoNCiAgICAgIGlmICghdGhpcy5jb25maWcucHJpbWFyeUZpZWxkICYmICF0aGlzLmNvbmZpZy5kYXRlRmllbGQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7foh7PlsJHpgInmi6nliIbnu4Tkvp3mja7miJbliIbnu4Tml6XmnJ/lhbbkuK3kuYvkuIAiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDpqozor4HmmK/lkKbmnInlrZfmrrXphY3nva4NCiAgICAgIGlmICghdGhpcy5jb25maWcuZmllbGRzLmxlbmd0aCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+a3u+WKoOiHs+WwkeS4gOS4quWtl+autSIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOmqjOivgeWtl+autemFjee9ruaYr+WQpuWujOaVtA0KICAgICAgY29uc3QgaW5jb21wbGV0ZUZpZWxkID0gdGhpcy5jb25maWcuZmllbGRzLmZpbmQoZmllbGQgPT4gIWZpZWxkLmZpZWxkS2V5KTsNCiAgICAgIGlmIChpbmNvbXBsZXRlRmllbGQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flrozmiJDmiYDmnInlrZfmrrXnmoTphY3nva4iKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDnoa7kv53msYfmgLvlh73mlbDlt7Lnu4/kvKDlhaUNCiAgICAgIGlmICghdGhpcy5hZ2dyZWdhdGVGdW5jdGlvbikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmsYfmgLvlh73mlbDmnKrlrprkuYkiKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KDQogICAgICAgIC8vIOWHhuWkh+ivt+axguWPguaVsA0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgZGF0YVNvdXJjZVR5cGU6IHRoaXMuZGF0YVNvdXJjZVR5cGUsDQogICAgICAgICAgY29uZmlnOiB7DQogICAgICAgICAgICBwcmltYXJ5RmllbGQ6IHRoaXMuY29uZmlnLnByaW1hcnlGaWVsZCwNCiAgICAgICAgICAgIG1hdGNoT3B0aW9uczogdGhpcy5jb25maWcubWF0Y2hPcHRpb25zLA0KICAgICAgICAgICAgZGF0ZUZpZWxkOiB0aGlzLmNvbmZpZy5kYXRlRmllbGQsDQogICAgICAgICAgICBkYXRlT3B0aW9uczogdGhpcy5jb25maWcuZGF0ZU9wdGlvbnMsDQogICAgICAgICAgICBzaG93RGV0YWlsczogdGhpcy5jb25maWcuc2hvd0RldGFpbHMsDQogICAgICAgICAgICBmaWVsZHM6IHRoaXMuY29uZmlnLmZpZWxkcywNCiAgICAgICAgICAgIGZpbHRlcnM6IHRoaXMuY29uZmlnLmZpbHRlcnMgfHwgW10NCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAvLyDosIPnlKjku47niLbnu4Tku7bkvKDlhaXnmoTmsYfmgLvlh73mlbANCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmFnZ3JlZ2F0ZUZ1bmN0aW9uKHBhcmFtcykNCg0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgLy8g6L+H5ruk6Zu25YC86K6w5b2VDQogICAgICAgICAgdGhpcy5wcm9jZXNzZWREYXRhID0gdGhpcy5maWx0ZXJaZXJvVmFsdWVSZWNvcmRzKHJlc3BvbnNlLmRhdGEpDQogICAgICAgICAgdGhpcy5zaG93UmVzdWx0ID0gdHJ1ZQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICLmsYfmgLvmlbDmja7lpLHotKUiKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLmlbDmja7msYfmgLvlpLHotKU6IiwgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaxh+aAu+WkhOeQhuWksei0pe+8miIgKyAoZXJyb3IubWVzc2FnZSB8fCAi5pyq55+l6ZSZ6K+vIikpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDov4fmu6Tpm7blgLzorrDlvZUNCiAgICAgKiBAcGFyYW0ge0FycmF5fSBkYXRhIC0g5Y6f5aeL5pWw5o2uDQogICAgICogQHJldHVybnMge0FycmF5fSDov4fmu6TlkI7nmoTmlbDmja4NCiAgICAgKi8NCiAgICBmaWx0ZXJaZXJvVmFsdWVSZWNvcmRzKGRhdGEpIHsNCiAgICAgIC8vIOaJvuWHuuiuvue9ruS6hmhpZGVaZXJvVmFsdWVz5Li6dHJ1ZeeahOWtl+autQ0KICAgICAgY29uc3QgemVyb0ZpbHRlckZpZWxkcyA9IHRoaXMuY29uZmlnLmZpZWxkcw0KICAgICAgICAuZmlsdGVyKGZpZWxkID0+IGZpZWxkLmhpZGVaZXJvVmFsdWVzID09PSB0cnVlKQ0KICAgICAgICAubWFwKGZpZWxkID0+ICh7DQogICAgICAgICAga2V5OiBmaWVsZC5maWVsZEtleSwNCiAgICAgICAgICBhZ2dQcm9wOiB0aGlzLmdldFJlc3VsdFByb3AoZmllbGQpDQogICAgICAgIH0pKTsNCg0KICAgICAgLy8g5aaC5p6c5rKh5pyJ6ZyA6KaB6L+H5ruk55qE5a2X5q6177yM55u05o6l6L+U5Zue5Y6f5aeL5pWw5o2uDQogICAgICBpZiAoemVyb0ZpbHRlckZpZWxkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIGRhdGE7DQogICAgICB9DQoNCiAgICAgIC8vIOi/h+a7pOaVsOaNrg0KICAgICAgcmV0dXJuIGRhdGEuZmlsdGVyKHJlY29yZCA9PiB7DQogICAgICAgIC8vIOajgOafpeavj+S4qumcgOimgei/h+a7pOmbtuWAvOeahOWtl+autQ0KICAgICAgICBmb3IgKGNvbnN0IGZpZWxkIG9mIHplcm9GaWx0ZXJGaWVsZHMpIHsNCiAgICAgICAgICBjb25zdCB2YWx1ZSA9IHJlY29yZFtmaWVsZC5hZ2dQcm9wXTsNCiAgICAgICAgICAvLyDlpoLmnpzlrZfmrrXlgLzkuLow77yM6L+H5ruk5o6J6L+Z5p2h6K6w5b2VDQogICAgICAgICAgaWYgKHZhbHVlID09PSAwIHx8IHZhbHVlID09PSAiMCIgfHwgdmFsdWUgPT09ICIwLjAwIikgew0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAvLyDmiYDmnInlrZfmrrXpg73kuI3kuLrpm7bvvIzkv53nlZnov5nmnaHorrDlvZUNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog6I635Y+W57uT5p6c5pWw5o2u55qE5bGe5oCn5ZCNDQogICAgICogQHBhcmFtIHtPYmplY3R9IGZpZWxkIC0g5a2X5q616YWN572uDQogICAgICogQHJldHVybnMge3N0cmluZ30g5bGe5oCn5ZCNDQogICAgICovDQogICAgZ2V0UmVzdWx0UHJvcChmaWVsZCkgew0KICAgICAgLy8g5aaC5p6c5pyJ5rGH5oC75pa55byP77yM5bGe5oCn5ZCN5Li6IGZpZWxkS2V5X2FnZ3JlZ2F0aW9uDQogICAgICBpZiAoZmllbGQuYWdncmVnYXRpb24gJiYgZmllbGQuYWdncmVnYXRpb24gIT09ICJub25lIikgew0KICAgICAgICByZXR1cm4gYCR7ZmllbGQuZmllbGRLZXl9XyR7ZmllbGQuYWdncmVnYXRpb259YA0KICAgICAgfQ0KICAgICAgcmV0dXJuIGZpZWxkLmZpZWxkS2V5DQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOiOt+WPlue7k+aenOihqOagvOeahOWIl+agh+mimA0KICAgICAqIEBwYXJhbSB7T2JqZWN0fSBmaWVsZCAtIOWtl+autemFjee9rg0KICAgICAqIEByZXR1cm5zIHtzdHJpbmd9IOWIl+agh+mimA0KICAgICAqLw0KICAgIGdldFJlc3VsdExhYmVsKGZpZWxkKSB7DQogICAgICBjb25zdCBiYXNlTGFiZWwgPSB0aGlzLmdldEZpZWxkTGFiZWwoZmllbGQuZmllbGRLZXkpDQoNCiAgICAgIC8vIOWmguaenOacieaxh+aAu+aWueW8j++8jOWcqOagh+etvuS4rea3u+WKoOaxh+aAu+aWueW8j+S/oeaBrw0KICAgICAgaWYgKGZpZWxkLmFnZ3JlZ2F0aW9uICYmIGZpZWxkLmFnZ3JlZ2F0aW9uICE9PSAibm9uZSIpIHsNCiAgICAgICAgLy8g6I635Y+W5rGH5oC75pa55byP55qE5Lit5paH5ZCN56ewDQogICAgICAgIGNvbnN0IGFnZ3JlZ2F0aW9uTGFiZWwgPSB0aGlzLmFnZ3JlZ2F0aW9uT3B0aW9ucy5maW5kKG9wdCA9PiBvcHQudmFsdWUgPT09IGZpZWxkLmFnZ3JlZ2F0aW9uKT8ubGFiZWwgfHwgZmllbGQuYWdncmVnYXRpb24NCiAgICAgICAgcmV0dXJuIGAke2Jhc2VMYWJlbH0oJHthZ2dyZWdhdGlvbkxhYmVsfSlgDQogICAgICB9DQoNCiAgICAgIHJldHVybiBiYXNlTGFiZWwNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog5qC85byP5YyW5Y2V5YWD5qC855qE5YC8DQogICAgICogQHBhcmFtIHsqfSB2YWx1ZSAtIOWOn+Wni+WAvA0KICAgICAqIEBwYXJhbSB7T2JqZWN0fSBmaWVsZCAtIOWtl+autemFjee9rg0KICAgICAqIEByZXR1cm5zIHtzdHJpbmd9IOagvOW8j+WMluWQjueahOWAvA0KICAgICAqLw0KICAgIGZvcm1hdENlbGxWYWx1ZSh2YWx1ZSwgZmllbGQpIHsNCiAgICAgIGlmICh2YWx1ZSA9PSBudWxsKSByZXR1cm4gIi0iDQoNCiAgICAgIGNvbnN0IGZpZWxkQ29uZmlnID0gdGhpcy5maWVsZExhYmVsTWFwW2ZpZWxkLmZpZWxkS2V5XQ0KICAgICAgaWYgKCFmaWVsZENvbmZpZykgcmV0dXJuIHZhbHVlDQoNCiAgICAgIC8vIOWkhOeQhuiHquWumuS5iSBkaXNwbGF5IOaWueazlQ0KICAgICAgaWYgKGZpZWxkQ29uZmlnLmRpc3BsYXkgJiYgdHlwZW9mIHRoaXNbZmllbGRDb25maWcuZGlzcGxheV0gPT09ICJmdW5jdGlvbiIpIHsNCiAgICAgICAgLy8g6LCD55So57uE5Lu25Lit5a6a5LmJ55qE5pa55rOVDQogICAgICAgIHJldHVybiB0aGlzW2ZpZWxkQ29uZmlnLmRpc3BsYXldKHZhbHVlKQ0KICAgICAgfQ0KDQogICAgICAvLyDmoLnmja7lrZfmrrXnsbvlnovov5vooYzmoLzlvI/ljJYNCiAgICAgIHN3aXRjaCAoZmllbGRDb25maWcuZGlzcGxheSkgew0KICAgICAgICBjYXNlICJudW1iZXIiOg0KICAgICAgICAgIGNvbnN0IG51bVZhbHVlID0gTnVtYmVyKHZhbHVlKQ0KICAgICAgICAgIGlmIChpc05hTihudW1WYWx1ZSkpIHJldHVybiAiLSINCg0KICAgICAgICAgIHN3aXRjaCAoZmllbGQuZm9ybWF0KSB7DQogICAgICAgICAgICBjYXNlICJkZWNpbWFsIjoNCiAgICAgICAgICAgICAgcmV0dXJuIG51bVZhbHVlLnRvRml4ZWQoMikNCiAgICAgICAgICAgIGNhc2UgInBlcmNlbnQiOg0KICAgICAgICAgICAgICByZXR1cm4gKG51bVZhbHVlICogMTAwKS50b0ZpeGVkKDIpICsgIiUiDQogICAgICAgICAgICBjYXNlICJjdXJyZW5jeSI6DQogICAgICAgICAgICAgIHJldHVybiAiwqUiICsgbnVtVmFsdWUudG9GaXhlZCgyKQ0KICAgICAgICAgICAgY2FzZSAidXNkIjoNCiAgICAgICAgICAgICAgcmV0dXJuICIkIiArIG51bVZhbHVlLnRvRml4ZWQoMikNCiAgICAgICAgICAgIGNhc2UgImhpZGVaZXJvIjoNCiAgICAgICAgICAgICAgcmV0dXJuIG51bVZhbHVlID09PSAwID8gIi0iIDogbnVtVmFsdWUudG9GaXhlZCgyKQ0KICAgICAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICAgICAgcmV0dXJuIG51bVZhbHVlLnRvRml4ZWQoMikNCiAgICAgICAgICB9DQoNCiAgICAgICAgY2FzZSAiZGF0ZSI6DQogICAgICAgICAgcmV0dXJuIG1vbWVudCh2YWx1ZSkuZm9ybWF0KGZpZWxkLmZvcm1hdCB8fCAiWVlZWS1NTS1ERCIpDQoNCiAgICAgICAgY2FzZSAiYm9vbGVhbiI6DQogICAgICAgICAgaWYgKGZpZWxkLmFnZ3JlZ2F0aW9uID09PSAiYXZnIikgew0KICAgICAgICAgICAgcmV0dXJuIChOdW1iZXIodmFsdWUpICogMTAwKS50b0ZpeGVkKDIpICsgIiUiDQogICAgICAgICAgfQ0KICAgICAgICAgIHJldHVybiB2YWx1ZSA/ICLmmK8iIDogIuWQpiINCg0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIHJldHVybiB2YWx1ZQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDmoLzlvI/ljJbliIbnu4TplK4NCiAgICAgKiBAcGFyYW0ge09iamVjdHxzdHJpbmd9IGdyb3VwS2V5IC0g5YiG57uE6ZSuDQogICAgICogQHJldHVybnMge3N0cmluZ30g5qC85byP5YyW5ZCO55qE5YiG57uE6ZSuDQogICAgICovDQogICAgZm9ybWF0R3JvdXBLZXkoZ3JvdXBLZXkpIHsNCiAgICAgIGlmICh0eXBlb2YgZ3JvdXBLZXkgPT09ICJvYmplY3QiICYmIGdyb3VwS2V5ICE9PSBudWxsKSB7DQogICAgICAgIGlmIChncm91cEtleS5wcmltYXJ5ICE9PSB1bmRlZmluZWQgJiYgZ3JvdXBLZXkuZGF0ZSAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgLy8g6I635Y+W5Li75YiG57uE5a2X5q6155qE6YWN572uDQogICAgICAgICAgY29uc3QgcHJpbWFyeUZpZWxkQ29uZmlnID0gdGhpcy5maWVsZExhYmVsTWFwW3RoaXMuY29uZmlnLnByaW1hcnlGaWVsZF0NCiAgICAgICAgICBsZXQgcHJpbWFyeVZhbHVlID0gZ3JvdXBLZXkucHJpbWFyeQ0KDQogICAgICAgICAgLy8g5aaC5p6c5Li75YiG57uE5a2X5q615pyJ6Ieq5a6a5LmJIGRpc3BsYXkg5pa55rOV77yM5bqU55So5a6DDQogICAgICAgICAgaWYgKHByaW1hcnlGaWVsZENvbmZpZyAmJiBwcmltYXJ5RmllbGRDb25maWcuZGlzcGxheSAmJg0KICAgICAgICAgICAgdHlwZW9mIHRoaXNbcHJpbWFyeUZpZWxkQ29uZmlnLmRpc3BsYXldID09PSAiZnVuY3Rpb24iKSB7DQogICAgICAgICAgICBwcmltYXJ5VmFsdWUgPSB0aGlzW3ByaW1hcnlGaWVsZENvbmZpZy5kaXNwbGF5XShwcmltYXJ5VmFsdWUpDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5pel5pyf5YC85Zyo5YmN77yM5Li75YC85Zyo5ZCODQogICAgICAgICAgcmV0dXJuIGAke2dyb3VwS2V5LmRhdGV9ICR7cHJpbWFyeVZhbHVlfWANCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAvLyDlpoLmnpzmmK/nroDljZXlgLzvvIzmo4Dmn6XmmK/lkKbpnIDopoHlupTnlKjoh6rlrprkuYkgZGlzcGxheSDmlrnms5UNCiAgICAgIGlmICh0aGlzLmNvbmZpZy5wcmltYXJ5RmllbGQpIHsNCiAgICAgICAgY29uc3QgZmllbGRDb25maWcgPSB0aGlzLmZpZWxkTGFiZWxNYXBbdGhpcy5jb25maWcucHJpbWFyeUZpZWxkXQ0KICAgICAgICBpZiAoZmllbGRDb25maWcgJiYgZmllbGRDb25maWcuZGlzcGxheSAmJg0KICAgICAgICAgIHR5cGVvZiB0aGlzW2ZpZWxkQ29uZmlnLmRpc3BsYXldID09PSAiZnVuY3Rpb24iKSB7DQogICAgICAgICAgcmV0dXJuIHRoaXNbZmllbGRDb25maWcuZGlzcGxheV0oZ3JvdXBLZXkpDQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIFN0cmluZyhncm91cEtleSB8fCAiIikNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog6K6h566X6KGo5qC85ZCI6K6h6KGMDQogICAgICogQHBhcmFtIHtPYmplY3R9IHBhcmFtMCAtIOWMheWQq+WIl+S/oeaBr+WSjOaVsOaNrueahOWvueixoQ0KICAgICAqIEByZXR1cm5zIHtBcnJheX0g5ZCI6K6h6KGM5pWw5o2uDQogICAgICovDQogICAgZ2V0U3VtbWFyeSh7Y29sdW1ucywgZGF0YX0pIHsNCiAgICAgIGNvbnN0IHN1bXMgPSBbXQ0KDQogICAgICBjb2x1bW5zLmZvckVhY2goKGNvbHVtbiwgaW5kZXgpID0+IHsNCiAgICAgICAgLy8g56ys5LiA5YiX5pi+56S6IuWQiOiuoSLmlofmnKwNCiAgICAgICAgaWYgKGluZGV4ID09PSAwKSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSAi5ZCI6K6hIg0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5L2/55So57Si5byV6I635Y+W5b2T5YmN5YiX5a+55bqU55qE5a2X5q616YWN572uDQogICAgICAgIGNvbnN0IGZpZWxkSW5kZXggPSBpbmRleCAtIDENCiAgICAgICAgY29uc3QgZmllbGQgPSB0aGlzLmNvbmZpZy5maWVsZHNbZmllbGRJbmRleF0NCg0KICAgICAgICBpZiAoIWZpZWxkIHx8ICFmaWVsZC5maWVsZEtleSkgew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gIiINCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOajgOafpeWtl+auteaYr+WQpumFjee9ruS6huaxh+aAu+aWueW8jw0KICAgICAgICBpZiAoIWZpZWxkLmFnZ3JlZ2F0aW9uIHx8IGZpZWxkLmFnZ3JlZ2F0aW9uID09PSAibm9uZSIpIHsNCiAgICAgICAgICBzdW1zW2luZGV4XSA9ICIiDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICAvLyDojrflj5blrZfmrrXphY3nva4NCiAgICAgICAgY29uc3QgZmllbGRDb25maWcgPSB0aGlzLmZpZWxkTGFiZWxNYXBbZmllbGQuZmllbGRLZXldDQoNCiAgICAgICAgLy8g5a+55LqO5LiN5piv5pWw5a2X57G75Z6L55qE5a2X5q615L2G5pyJcGVyY2VudGFnZeaYvuekuuaWueazleeahOeJueauiuWkhOeQhg0KICAgICAgICBpZiAoIWZpZWxkQ29uZmlnKSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSAiIg0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKGZpZWxkQ29uZmlnLmRpc3BsYXkgIT09ICJudW1iZXIiICYmDQogICAgICAgICAgZmllbGRDb25maWcuZGlzcGxheSAhPT0gInBlcmNlbnRhZ2UiICYmDQogICAgICAgICAgdHlwZW9mIHRoaXNbZmllbGRDb25maWcuZGlzcGxheV0gIT09ICJmdW5jdGlvbiIpIHsNCiAgICAgICAgICBzdW1zW2luZGV4XSA9ICIiDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICAvLyDojrflj5bliJfmlbDmja7lubbovazmjaLkuLrmlbDlrZcNCiAgICAgICAgY29uc3QgdmFsdWVzID0gZGF0YS5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgY29uc3QgcHJvcCA9IHRoaXMuZ2V0UmVzdWx0UHJvcChmaWVsZCkNCiAgICAgICAgICBjb25zdCB2YWwgPSBOdW1iZXIoaXRlbVtwcm9wXSkNCiAgICAgICAgICByZXR1cm4gaXNOYU4odmFsKSA/IDAgOiB2YWwgLy8g5aSE55CG6Z2e5pWw5a2X5YC8DQogICAgICAgIH0pLmZpbHRlcih2YWwgPT4gIWlzTmFOKHZhbCkpDQoNCiAgICAgICAgaWYgKHZhbHVlcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICBzdW1zW2luZGV4XSA9ICIiDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICAvLyDmoLnmja7msYfmgLvmlrnlvI/orqHnrpfnu5PmnpwNCiAgICAgICAgbGV0IHN1bSA9IDANCiAgICAgICAgc3dpdGNoIChmaWVsZC5hZ2dyZWdhdGlvbikgew0KICAgICAgICAgIGNhc2UgInN1bSI6DQogICAgICAgICAgICBzdW0gPSB2YWx1ZXMucmVkdWNlKChhLCBiKSA9PiBhICsgYiwgMCkNCiAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgY2FzZSAiYXZnIjoNCiAgICAgICAgICAgIHN1bSA9IHZhbHVlcy5yZWR1Y2UoKGEsIGIpID0+IGEgKyBiLCAwKSAvIHZhbHVlcy5sZW5ndGgNCiAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgY2FzZSAibWF4IjoNCiAgICAgICAgICAgIHN1bSA9IE1hdGgubWF4KC4uLnZhbHVlcykNCiAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgY2FzZSAibWluIjoNCiAgICAgICAgICAgIHN1bSA9IE1hdGgubWluKC4uLnZhbHVlcykNCiAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgY2FzZSAidmFyaWFuY2UiOg0KICAgICAgICAgICAgY29uc3QgbWVhbiA9IHZhbHVlcy5yZWR1Y2UoKGEsIGIpID0+IGEgKyBiLCAwKSAvIHZhbHVlcy5sZW5ndGgNCiAgICAgICAgICAgIHN1bSA9IHZhbHVlcy5yZWR1Y2UoKGEsIGIpID0+IGEgKyBNYXRoLnBvdyhiIC0gbWVhbiwgMiksIDApIC8gdmFsdWVzLmxlbmd0aA0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgICAgc3VtID0gdmFsdWVzLnJlZHVjZSgoYSwgYikgPT4gYSArIGIsIDApDQogICAgICAgIH0NCg0KICAgICAgICAvLyDmoLnmja7lrZfmrrXmmL7npLrnsbvlnovlkozmoLzlvI/orr7nva7mnaXmoLzlvI/ljJbnu5PmnpwNCiAgICAgICAgaWYgKGZpZWxkQ29uZmlnLmRpc3BsYXkgPT09ICJwZXJjZW50YWdlIiB8fCBmaWVsZENvbmZpZy5kaXNwbGF5ID09PSAicGVyY2VudCIpIHsNCiAgICAgICAgICAvLyDkvb/nlKhwZXJjZW50YWdl5pa55rOV5qC85byP5YyWDQogICAgICAgICAgc3Vtc1tpbmRleF0gPSB0aGlzLnBlcmNlbnRhZ2Uoc3VtKQ0KICAgICAgICB9IGVsc2UgaWYgKGZpZWxkLmZvcm1hdCA9PT0gImRlY2ltYWwiKSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSBzdW0udG9GaXhlZCgyKQ0KICAgICAgICB9IGVsc2UgaWYgKGZpZWxkLmZvcm1hdCA9PT0gInBlcmNlbnQiKSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSAoc3VtICogMTAwKS50b0ZpeGVkKDIpICsgIiUiDQogICAgICAgIH0gZWxzZSBpZiAoZmllbGQuZm9ybWF0ID09PSAiY3VycmVuY3kiKSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSAiwqUiICsgc3VtLnRvRml4ZWQoMikNCiAgICAgICAgfSBlbHNlIGlmIChmaWVsZC5mb3JtYXQgPT09ICJ1c2QiKSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSAiJCIgKyBzdW0udG9GaXhlZCgyKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gc3VtLnRvRml4ZWQoMikNCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgcmV0dXJuIHN1bXMNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog5L+d5a2Y5b2T5YmN6YWN572uDQogICAgICovDQogICAgYXN5bmMgc2F2ZUNvbmZpZygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOmqjOivgemFjee9ruWQjeensA0KICAgICAgICBpZiAoIXRoaXMuY29uZmlnLm5hbWUpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+i+k+WFpemAn+afpeWQjeensCIpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICAvLyDpqozor4HliIbnu4Tkvp3mja7lkozliIbnu4Tml6XmnJ/oh7PlsJHloavlhpnkuIDkuKoNCiAgICAgICAgaWYgKCF0aGlzLmNvbmZpZy5wcmltYXJ5RmllbGQgJiYgIXRoaXMuY29uZmlnLmRhdGVGaWVsZCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36Iez5bCR6YCJ5oup5YiG57uE5L6d5o2u5oiW5YiG57uE5pel5pyf5YW25Lit5LmL5LiAIikNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIGlmICghdGhpcy5jb25maWcuZmllbGRzLmxlbmd0aCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35re75Yqg6Iez5bCR5LiA5Liq5a2X5q61IikNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOmqjOivgeWtl+autemFjee9ruaYr+WQpuWujOaVtA0KICAgICAgICBjb25zdCBpbmNvbXBsZXRlRmllbGQgPSB0aGlzLmNvbmZpZy5maWVsZHMuZmluZChmaWVsZCA9PiAhZmllbGQuZmllbGRLZXkpDQogICAgICAgIGlmIChpbmNvbXBsZXRlRmllbGQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WujOaIkOaJgOacieWtl+auteeahOmFjee9riIpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICAvLyDmnoTpgKDnrKblkIggQWdncmVnYXRvckNvbmZpZ0RUTyDnmoTmlbDmja7nu5PmnoQNCiAgICAgICAgY29uc3QgY29uZmlnVG9TYXZlID0gew0KICAgICAgICAgIG5hbWU6IHRoaXMuY29uZmlnLm5hbWUsDQogICAgICAgICAgdHlwZTogdGhpcy5jb25maWdUeXBlLA0KICAgICAgICAgIGNvbmZpZzogdGhpcy5jb25maWcNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWPkemAgeivt+axgg0KICAgICAgICBhd2FpdCBzYXZlQWdncmVnYXRvckNvbmZpZyhjb25maWdUb1NhdmUpDQoNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLphY3nva7kv53lrZjmiJDlip8iKQ0KICAgICAgfSBjYXRjaCAoZXJyKSB7DQogICAgICAgIGlmIChlcnIgIT09ICJjYW5jZWwiKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5L+d5a2Y6YWN572u5aSx6LSl77yaIiArIChlcnIubWVzc2FnZSB8fCAi5pyq55+l6ZSZ6K+vIikpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqDQogICAgICog5Yqg6L295L+d5a2Y55qE6YWN572u5YiX6KGoDQogICAgICovDQogICAgYXN5bmMgbG9hZENvbmZpZ3MoKSB7DQogICAgICB0aGlzLmNvbmZpZ0xvYWRpbmcgPSB0cnVlDQogICAgICB0aGlzLmNvbmZpZ0RpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBsb2FkQWdncmVnYXRvckNvbmZpZ3Moe2NvbmZpZ1R5cGU6IHRoaXMuY29uZmlnVHlwZX0pDQogICAgICAgIHRoaXMuc2F2ZWRDb25maWdzID0gcmVzdWx0LnJvd3MNCiAgICAgIH0gY2F0Y2ggKGVycikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLliqDovb3phY3nva7lpLHotKU6IiwgZXJyKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKA0KICAgICAgICAgIGVyci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fA0KICAgICAgICAgIGVyci5tZXNzYWdlIHx8DQogICAgICAgICAgIuWKoOi9vemFjee9ruWIl+ihqOWksei0pe+8jOivt+eojeWQjumHjeivlSINCiAgICAgICAgKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5jb25maWdMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqDQogICAgICog6YCJ5oup5bm25Yqg6L296YWN572uDQogICAgICogQHBhcmFtIHtPYmplY3R9IHJvdyAtIOmAieS4reeahOmFjee9ruihjA0KICAgICAqLw0KICAgIGFzeW5jIGhhbmRsZUNvbmZpZ1NlbGVjdChyb3cpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOino+aekOmFjee9rkpTT04NCiAgICAgICAgdmFyIGNvbmZpZyA9IEpTT04ucGFyc2Uocm93LmNvbmZpZykNCiAgICAgICAgY29uZmlnLm5hbWUgPSByb3cubmFtZQ0KICAgICAgICB0aGlzLmNvbmZpZyA9IGNvbmZpZw0KICAgICAgICAvLyB0aGlzLmNvbmZpZy5uYW1lID0gcm93Lm5hbWUNCg0KICAgICAgICB0aGlzLmNvbmZpZ0RpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIumFjee9ruWKoOi9veaIkOWKnyIpDQogICAgICB9IGNhdGNoIChlcnIpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5Yqg6L296YWN572u5aSx6LSlOiIsIGVycikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Yqg6L296YWN572u5aSx6LSl77yaIiArIGVyci5tZXNzYWdlKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDliKDpmaTphY3nva4NCiAgICAgKiBAcGFyYW0ge09iamVjdH0gcm93IC0g6KaB5Yig6Zmk55qE6YWN572u6KGMDQogICAgICovDQogICAgYXN5bmMgZGVsZXRlQ29uZmlnKHJvdykgew0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgdGhpcy4kY29uZmlybSgi56Gu6K6k5Yig6Zmk6K+l6YWN572u77yfIiwgIuaPkOekuiIsIHsNCiAgICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgICAgfSkNCg0KICAgICAgICBhd2FpdCBkZWxldGVBZ2dyZWdhdG9yQ29uZmlnKHJvdy5pZCkNCiAgICAgICAgdGhpcy5zYXZlZENvbmZpZ3MgPSB0aGlzLnNhdmVkQ29uZmlncy5maWx0ZXIoY29uZmlnID0+IGNvbmZpZy5pZCAhPT0gcm93LmlkKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIumFjee9ruWIoOmZpOaIkOWKnyIpDQogICAgICB9IGNhdGNoIChlcnIpIHsNCiAgICAgICAgaWYgKGVyciAhPT0gImNhbmNlbCIpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliKDpmaTphY3nva7lpLHotKXvvJoiICsgZXJyLm1lc3NhZ2UpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqDQogICAgICog5omT5Y2w6KGo5qC8DQogICAgICovDQogICAgcHJpbnRUYWJsZSgpIHsNCiAgICAgIC8vIOWunueOsOS4juWOn+e7hOS7tuebuOWQjOeahOaJk+WNsOWKn+iDvQ0KICAgICAgY29uc3QgcHJpbnRXaW5kb3cgPSB3aW5kb3cub3BlbigiIiwgIl9ibGFuayIpDQogICAgICBjb25zdCB0YWJsZSA9IHRoaXMuJHJlZnMucmVzdWx0VGFibGUuJGVsLmNsb25lTm9kZSh0cnVlKQ0KICAgICAgY29uc3QgdGl0bGUgPSAi5rGH5oC75pWw5o2uIg0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCkudG9Mb2NhbGVEYXRlU3RyaW5nKCkNCg0KICAgICAgLy8g5YWs5Y+45qCH5b+X5ZKM5qCH6aKY55qESFRNTOaooeadvw0KICAgICAgY29uc3QgaGVhZGVyVGVtcGxhdGUgPSBgDQogICAgICAgIDxkaXYgY2xhc3M9ImNvbXBhbnktaGVhZGVyIj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJjb21wYW55LWxvZ28iPg0KICAgICAgICAgICAgPGltZyBzcmM9Ii9sb2dvLnBuZyIgYWx0PSJSaWNoIFNoaXBwaW5nIExvZ28iIC8+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb21wYW55LW5hbWUiPg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb21wYW55LW5hbWUtY24iPuW5v+W3nueRnuaXl+WbvemZhei0p+i/kOS7o+eQhuaciemZkOWFrOWPuDwvZGl2Pg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb21wYW55LW5hbWUtZW4iPkdVQU5HWkhPVSBSSUNIIFNISVBQSU5HIElOVCdMIENPLixMVEQuPC9kaXY+DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJkb2N1bWVudC10aXRsZSI+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJ0aXRsZS1jbiI+PC9kaXY+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJ0aXRsZS1lbiI+PC9kaXY+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgIDwvZGl2Pg0KICAgICAgYA0KICAgICAgcHJpbnRXaW5kb3cuZG9jdW1lbnQud3JpdGUoYA0KICAgICAgICA8aHRtbCBsYW5nPSIiPg0KICAgICAgICAgIDxoZWFkPg0KICAgICAgICAgICAgPHRpdGxlPiR7dGl0bGV9PC90aXRsZT4NCiAgICAgICAgICAgIDxzdHlsZT4NCiAgICAgICAgICAgICAgLyog5Z+656GA5qC35byPICovDQogICAgICAgICAgICAgIGJvZHkgew0KICAgICAgICAgICAgICAgIG1hcmdpbjogMDsNCiAgICAgICAgICAgICAgICBwYWRkaW5nOiAwOw0KICAgICAgICAgICAgICAgIGZvbnQtZmFtaWx5OiBBcmlhbCwgc2Fucy1zZXJpZjsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8qIOaJk+WNsOagt+W8jyAtIOW/hemhu+aUvuWcqOi/memHjOaJjeiDveeUn+aViCAqLw0KICAgICAgICAgICAgICBAbWVkaWEgcHJpbnQgew0KICAgICAgICAgICAgICAgIEBwYWdlIHsNCiAgICAgICAgICAgICAgICAgIHNpemU6ICR7dGhpcy5pc0xhbmRzY2FwZSA/ICJsYW5kc2NhcGUiIDogInBvcnRyYWl0In07DQogICAgICAgICAgICAgICAgICBtYXJnaW46IDEuNWNtIDFjbSAxY20gMWNtOw0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIC8qIOmHjeimge+8muS9v+eUqOmHjeWkjeihqOWktOaKgOacryAqLw0KICAgICAgICAgICAgICAgIHRoZWFkIHsNCiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IHRhYmxlLWhlYWRlci1ncm91cDsNCiAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAvKiDpobXnnInkvZzkuLrooajmoLznmoTkuIDpg6jliIbvvIzmlL7lnKh0aGVhZOS4rSAqLw0KICAgICAgICAgICAgICAgIC5wYWdlLWhlYWRlciB7DQogICAgICAgICAgICAgICAgICBkaXNwbGF5OiB0YWJsZS1oZWFkZXItZ3JvdXA7DQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgLyog5YaF5a656YOo5YiGICovDQogICAgICAgICAgICAgICAgLnBhZ2UtY29udGVudCB7DQogICAgICAgICAgICAgICAgICBkaXNwbGF5OiB0YWJsZS1yb3ctZ3JvdXA7DQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgLyog6YG/5YWN5YWD57Sg5YaF6YOo5YiG6aG1ICovDQogICAgICAgICAgICAgICAgLmNvbXBhbnktaGVhZGVyLCAuaGVhZGVyLWNvbnRlbnQgew0KICAgICAgICAgICAgICAgICAgcGFnZS1icmVhay1pbnNpZGU6IGF2b2lkOw0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIC8qIOihqOagvOagt+W8jyAqLw0KICAgICAgICAgICAgICAgIHRhYmxlLm1haW4tdGFibGUgew0KICAgICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgICAgICAgICBib3JkZXItY29sbGFwc2U6IGNvbGxhcHNlOw0KICAgICAgICAgICAgICAgICAgYm9yZGVyOiBub25lOw0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIC8qIOehruS/neihqOWktOWcqOavj+mhtemDveaYvuekuiAqLw0KICAgICAgICAgICAgICAgIHRhYmxlLmRhdGEtdGFibGUgdGhlYWQgew0KICAgICAgICAgICAgICAgICAgZGlzcGxheTogdGFibGUtaGVhZGVyLWdyb3VwOw0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIC8qIOmBv+WFjeihjOWGheWIhumhtSAqLw0KICAgICAgICAgICAgICAgIHRhYmxlLmRhdGEtdGFibGUgdHIgew0KICAgICAgICAgICAgICAgICAgcGFnZS1icmVhay1pbnNpZGU6IGF2b2lkOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8qIOihqOagvOagt+W8jyAqLw0KICAgICAgICAgICAgICB0YWJsZS5kYXRhLXRhYmxlIHsNCiAgICAgICAgICAgICAgICBib3JkZXItY29sbGFwc2U6IGNvbGxhcHNlOw0KICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgICAgICAgIG1hcmdpbi10b3A6IDIwcHg7DQogICAgICAgICAgICAgICAgdGFibGUtbGF5b3V0OiBmaXhlZDsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIHRhYmxlLmRhdGEtdGFibGUgdGgsIHRhYmxlLmRhdGEtdGFibGUgdGQgew0KICAgICAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7DQogICAgICAgICAgICAgICAgcGFkZGluZzogOHB4Ow0KICAgICAgICAgICAgICAgIHRleHQtYWxpZ246IGxlZnQ7DQogICAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgICAgICAgIHdvcmQtd3JhcDogYnJlYWstd29yZDsNCiAgICAgICAgICAgICAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7DQogICAgICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vcm1hbDsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIHRhYmxlLmRhdGEtdGFibGUgdGggew0KICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmMmYyZjI7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAvKiBFbGVtZW50IFVJIOihqOagvOagt+W8j+aooeaLnyAqLw0KICAgICAgICAgICAgICAuZWwtdGFibGUgew0KICAgICAgICAgICAgICAgIGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7DQogICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgICAgICAgdGFibGUtbGF5b3V0OiBmaXhlZDsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC5lbC10YWJsZSB0aCwgLmVsLXRhYmxlIHRkIHsNCiAgICAgICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZGRkOw0KICAgICAgICAgICAgICAgIHBhZGRpbmc6IDhweDsNCiAgICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICAgICAgICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7DQogICAgICAgICAgICAgICAgd29yZC1icmVhazogYnJlYWstYWxsOw0KICAgICAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3JtYWw7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAuZWwtdGFibGUgdGggew0KICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmMmYyZjI7DQogICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAuZWwtdGFibGVfX2Zvb3RlciB7DQogICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjhmOTsNCiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC5lbC10YWJsZV9fZm9vdGVyIHRkIHsNCiAgICAgICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZGRkOw0KICAgICAgICAgICAgICAgIHBhZGRpbmc6IDhweDsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8qIOWFrOWPuOagh+mimOWSjOagh+W/l+agt+W8jyAqLw0KICAgICAgICAgICAgICAuY29tcGFueS1oZWFkZXIgew0KICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICMwMDA7DQogICAgICAgICAgICAgICAgcGFkZGluZy1ib3R0b206IDEwcHg7DQogICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAuY29tcGFueS1sb2dvIHsNCiAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAuY29tcGFueS1sb2dvIGltZyB7DQogICAgICAgICAgICAgICAgaGVpZ2h0OiA1MHB4Ow0KICAgICAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMTBweDsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC5jb21wYW55LW5hbWUgew0KICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC5jb21wYW55LW5hbWUtY24gew0KICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICAgICAgICBjb2xvcjogI2ZmMDAwMDsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC5jb21wYW55LW5hbWUtZW4gew0KICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC5kb2N1bWVudC10aXRsZSB7DQogICAgICAgICAgICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAudGl0bGUtY24gew0KICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC50aXRsZS1lbiB7DQogICAgICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgLyog5riF6Zmk6KGo5qC86L655qGGICovDQogICAgICAgICAgICAgIHRhYmxlLm1haW4tdGFibGUsIHRhYmxlLm1haW4tdGFibGUgdGQgew0KICAgICAgICAgICAgICAgIGJvcmRlcjogbm9uZTsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8qIOmhteecieWuueWZqCAqLw0KICAgICAgICAgICAgICAuaGVhZGVyLWNvbnRhaW5lciB7DQogICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgPC9zdHlsZT4NCiAgICAgICAgICA8L2hlYWQ+DQogICAgICAgICAgPGJvZHk+DQogICAgICAgICAgICA8IS0tIOS9v+eUqOihqOagvOW4g+WxgOehruS/nemhteecieWcqOavj+mhtemHjeWkjSAtLT4NCiAgICAgICAgICAgIDx0YWJsZSBjbGFzcz0ibWFpbi10YWJsZSI+DQogICAgICAgICAgICAgIDx0aGVhZCBjbGFzcz0icGFnZS1oZWFkZXIiPg0KICAgICAgICAgICAgICAgIDx0cj4NCiAgICAgICAgICAgICAgICAgIDx0ZD4NCiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyLWNvbnRhaW5lciI+DQogICAgICAgICAgICAgICAgICAgICAgJHtoZWFkZXJUZW1wbGF0ZX0NCiAgICAgICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICAgICAgICA8L3RkPg0KICAgICAgICAgICAgICAgIDwvdHI+DQogICAgICAgICAgICAgIDwvdGhlYWQ+DQogICAgICAgICAgICAgIDx0Ym9keSBjbGFzcz0icGFnZS1jb250ZW50Ij4NCiAgICAgICAgICAgICAgICA8dHI+DQogICAgICAgICAgICAgICAgICA8dGQ+DQogICAgICAgICAgICAgICAgICAgIDwhLS0g5L+d55WZ5Y6f5aeL6KGo5qC855qE57G75ZCN5bm25re75YqgZGF0YS10YWJsZeexuyAtLT4NCiAgICAgICAgICAgICAgICAgICAgJHt0YWJsZS5vdXRlckhUTUwucmVwbGFjZSgnPHRhYmxlJywgJzx0YWJsZSBjbGFzcz0iZWwtdGFibGUgZGF0YS10YWJsZSInKX0NCiAgICAgICAgICAgICAgICAgIDwvdGQ+DQogICAgICAgICAgICAgICAgPC90cj4NCiAgICAgICAgICAgICAgPC90Ym9keT4NCiAgICAgICAgICAgIDwvdGFibGU+DQogICAgICAgICAgPC9ib2R5Pg0KICAgICAgICA8L2h0bWw+DQogICAgICBgKQ0KDQogICAgICBwcmludFdpbmRvdy5kb2N1bWVudC5jbG9zZSgpDQoNCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0cnkgew0KICAgICAgICAgIHByaW50V2luZG93LmZvY3VzKCk7DQogICAgICAgICAgcHJpbnRXaW5kb3cucHJpbnQoKTsNCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuaJk+WNsOi/h+eoi+S4reWPkeeUn+mUmeivrzoiLCBlKTsNCiAgICAgICAgfQ0KICAgICAgfSwgMTAwMCkNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog5a+85Ye6UERGDQogICAgICovDQogICAgYXN5bmMgZXhwb3J0VG9QREYoKSB7DQogICAgICB0cnkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICAgIGNvbnN0IGVsZW1lbnQgPSB0aGlzLiRyZWZzLnJlc3VsdFRhYmxlLiRlbA0KICAgICAgICBjb25zdCBvcHQgPSB7DQogICAgICAgICAgbWFyZ2luOiBbMC44LCAwLjgsIDAuOCwgMC44XSwNCiAgICAgICAgICBmaWxlbmFtZTogIuaxh+aAu+aVsOaNri5wZGYiLA0KICAgICAgICAgIGltYWdlOiB7dHlwZTogImpwZWciLCBxdWFsaXR5OiAwLjk4fSwNCiAgICAgICAgICBodG1sMmNhbnZhczoge3NjYWxlOiAyfSwNCiAgICAgICAgICBqc1BERjogew0KICAgICAgICAgICAgdW5pdDogImluIiwNCiAgICAgICAgICAgIGZvcm1hdDogImEzIiwNCiAgICAgICAgICAgIG9yaWVudGF0aW9uOiB0aGlzLmlzTGFuZHNjYXBlID8gImxhbmRzY2FwZSIgOiAicG9ydHJhaXQiDQogICAgICAgICAgfSwNCiAgICAgICAgICBwYWdlYnJlYWs6IHttb2RlOiBbImF2b2lkLWFsbCIsICJjc3MiLCAibGVnYWN5Il19LA0KICAgICAgICAgIGhlYWRlcjogWw0KICAgICAgICAgICAge3RleHQ6ICLmsYfmgLvmlbDmja4iLCBzdHlsZTogImhlYWRlclN0eWxlIn0sDQogICAgICAgICAgICB7dGV4dDogbmV3IERhdGUoKS50b0xvY2FsZURhdGVTdHJpbmcoKSwgc3R5bGU6ICJoZWFkZXJTdHlsZSIsIGFsaWdubWVudDogInJpZ2h0In0NCiAgICAgICAgICBdLA0KICAgICAgICAgIGZvb3Rlcjogew0KICAgICAgICAgICAgaGVpZ2h0OiAiMjBweCIsDQogICAgICAgICAgICBjb250ZW50czogew0KICAgICAgICAgICAgICBkZWZhdWx0OiAiPHNwYW4gc3R5bGU9XCJmbG9hdDpyaWdodFwiPnt7cGFnZX19L3t7cGFnZXN9fTwvc3Bhbj4iDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgYXdhaXQgaHRtbDJwZGYoKS5zZXQob3B0KS5mcm9tKGVsZW1lbnQpLnNhdmUoKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIlBERuWvvOWHuuaIkOWKnyIpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCJQREblr7zlh7rlpLHotKXvvJoiICsgZXJyb3IubWVzc2FnZSkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOiOt+WPluS6uuWRmOWnk+WQjQ0KICAgICAqIEBwYXJhbSB7TnVtYmVyfSBpZCAtIOS6uuWRmElEDQogICAgICogQHJldHVybnMge1N0cmluZ30g5Lq65ZGY5aeT5ZCNDQogICAgICovDQogICAgZ2V0TmFtZShpZCkgew0KICAgICAgaWYgKGlkICE9PSBudWxsKSB7DQogICAgICAgIGxldCBzdGFmZiA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKHJzU3RhZmYgPT4gcnNTdGFmZi5zdGFmZklkID09IGlkKVswXQ0KICAgICAgICBpZiAoc3RhZmYgJiYgc3RhZmYgIT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgIHJldHVybiBzdGFmZi5zdGFmZlNob3J0TmFtZSArIHN0YWZmLnN0YWZmRmFtaWx5RW5OYW1lDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiAiIg0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDmoLzlvI/ljJbnmb7liIbmr5TlgLwNCiAgICAgKiBAcGFyYW0geyp9IHZhbHVlIC0g6KaB5qC85byP5YyW55qE5YC8DQogICAgICogQHJldHVybnMge3N0cmluZ30g5qC85byP5YyW5ZCO55qE55m+5YiG5q+UDQogICAgICovDQogICAgcGVyY2VudGFnZSh2YWx1ZSkgew0KICAgICAgaWYgKHZhbHVlID09IG51bGwgfHwgdmFsdWUgPT09ICcnKSB7DQogICAgICAgIHJldHVybiAnLSc7DQogICAgICB9DQoNCiAgICAgIC8vIOWkhOeQhuW3sue7j+W4puaciSXnmoTmg4XlhrUNCiAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnICYmIHZhbHVlLmluY2x1ZGVzKCclJykpIHsNCiAgICAgICAgcmV0dXJuIHZhbHVlOw0KICAgICAgfQ0KDQogICAgICAvLyDlsIbmlbDlgLzovazmjaLkuLrnmb7liIbmr5TmoLzlvI8NCiAgICAgIGNvbnN0IG51bVZhbHVlID0gTnVtYmVyKHZhbHVlKTsNCiAgICAgIGlmIChpc05hTihudW1WYWx1ZSkpIHsNCiAgICAgICAgcmV0dXJuICctJzsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5YC85bey57uP5piv55m+5YiG5q+U5b2i5byPKOS+i+WmgjAuMjXooajnpLoyNSUp77yM5YiZ55u05o6l5LmY5LulMTAwDQogICAgICAvLyDlpoLmnpzlgLzlt7Lnu4/mmK/mlbTmlbDlvaLlvI8o5L6L5aaCMjXooajnpLoyNSUp77yM5YiZ5LiN6ZyA6KaB5LmY5LulMTAwDQogICAgICBjb25zdCBpc0RlY2ltYWwgPSBudW1WYWx1ZSA+IDAgJiYgbnVtVmFsdWUgPD0gMTsNCiAgICAgIGNvbnN0IHBlcmNlbnRWYWx1ZSA9IGlzRGVjaW1hbCA/IG51bVZhbHVlICogMTAwIDogbnVtVmFsdWU7DQoNCiAgICAgIC8vIOagvOW8j+WMluS4ujLkvY3lsI/mlbDnmoTnmb7liIbmr5QNCiAgICAgIHJldHVybiBwZXJjZW50VmFsdWUudG9GaXhlZCgyKSArICclJzsNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog6I635Y+W5YiX5a+56b2Q5pa55byPDQogICAgICogQHBhcmFtIHtzdHJpbmd9IGZpZWxkS2V5IC0g5a2X5q616ZSuDQogICAgICogQHJldHVybnMge3N0cmluZ30g5a+56b2Q5pa55byPDQogICAgICovDQogICAgZ2V0Q29sdW1uQWxpZ24oZmllbGRLZXkpIHsNCiAgICAgIGNvbnN0IGZpZWxkQ29uZmlnID0gdGhpcy5maWVsZExhYmVsTWFwW2ZpZWxkS2V5XQ0KICAgICAgcmV0dXJuIGZpZWxkQ29uZmlnICYmIGZpZWxkQ29uZmlnLmFsaWduID8gZmllbGRDb25maWcuYWxpZ24gOiAnbGVmdCcNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog6I635Y+W5YiX5a695bqmDQogICAgICogQHBhcmFtIHtzdHJpbmd9IGZpZWxkS2V5IC0g5a2X5q616ZSuDQogICAgICogQHJldHVybnMge3N0cmluZ3xudW1iZXJ9IOWIl+WuveW6pg0KICAgICAqLw0KICAgIGdldENvbHVtbldpZHRoKGZpZWxkS2V5KSB7DQogICAgICBjb25zdCBmaWVsZENvbmZpZyA9IHRoaXMuZmllbGRMYWJlbE1hcFtmaWVsZEtleV0NCiAgICAgIHJldHVybiBmaWVsZENvbmZpZyAmJiBmaWVsZENvbmZpZy53aWR0aCA/IGZpZWxkQ29uZmlnLndpZHRoIDogJycNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqXA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/DataAggregatorBackGround", "sourcesContent": ["<template>\r\n  <div class=\"data-aggregator\">\r\n    <el-row :gutter=\"20\">\r\n      <!-- 配置区域 - 左侧 -->\r\n      <el-col :span=\"showResult ? 10 : 10\">\r\n        <el-card class=\"config-card\">\r\n          <template #header>\r\n            <div class=\"header-with-operations\">\r\n              <span>汇总配置</span>\r\n            </div>\r\n          </template>\r\n          <el-form class=\"edit\" label-width=\"80px\">\r\n            <!-- 速查名称 -->\r\n            <el-form-item label=\"速查名称\" required>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"18\">\r\n                  <el-input v-model=\"config.name\" placeholder=\"请输入速查名称\"/>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-button size=\"small\" type=\"text\" @click=\"saveConfig\">[↗]</el-button>\r\n                  <el-button size=\"small\" type=\"text\" @click=\"loadConfigs\">[...]</el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 分组依据 -->\r\n            <el-form-item label=\"分组依据\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <el-select v-model=\"config.primaryField\" clearable filterable placeholder=\"操作单号\">\r\n                    <el-option\r\n                      v-for=\"field in availableFields\"\r\n                      :key=\"field\"\r\n                      :label=\"getFieldLabel(field)\"\r\n                      :value=\"field\"\r\n                    />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"15\">\r\n                  <el-checkbox v-model=\"config.matchOptions.exact\">精确匹配</el-checkbox>\r\n                  <el-checkbox v-model=\"config.matchOptions.caseSensitive\">区分大小写</el-checkbox>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 分组日期 -->\r\n            <el-form-item label=\"分组日期\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <el-select v-model=\"config.dateField\" clearable filterable placeholder=\"分组日期\">\r\n                    <el-option\r\n                      v-for=\"field in dateFields\"\r\n                      :key=\"field\"\r\n                      :label=\"getFieldLabel(field)\"\r\n                      :value=\"field\"\r\n                    />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"15\">\r\n                  <el-checkbox v-model=\"config.dateOptions.convertToNumber\">转换为数字</el-checkbox>\r\n                  <el-radio-group v-model=\"config.dateOptions.formatType\" style=\"display: flex;line-height: 26px\">\r\n                    <el-radio label=\"year\">按年</el-radio>\r\n                    <el-radio label=\"month\">按月</el-radio>\r\n                    <el-radio label=\"day\">按天</el-radio>\r\n                  </el-radio-group>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 数据筛选 -->\r\n            <!--<el-form-item label=\"数据筛选\">-->\r\n            <!--  <el-button type=\"primary\" plain size=\"small\" @click=\"openFilterDialog\">设置筛选条件</el-button>-->\r\n            <!--  <div v-if=\"config.filters && config.filters.length\" class=\"filter-tags\">-->\r\n            <!--    <el-tag-->\r\n            <!--      v-for=\"(filter, index) in config.filters\"-->\r\n            <!--      :key=\"index\"-->\r\n            <!--      closable-->\r\n            <!--      @close=\"removeFilter(index)\"-->\r\n            <!--    >-->\r\n            <!--      {{getFieldLabel(filter.field)}} {{getOperatorLabel(filter.operator)}} {{filter.value}}-->\r\n            <!--    </el-tag>-->\r\n            <!--  </div>-->\r\n            <!--</el-form-item>-->\r\n\r\n            <!-- 显示方式 -->\r\n            <el-form-item label=\"显示方式\">\r\n              <el-checkbox v-model=\"config.showDetails\" style=\"padding-left: 5px;\">含明细</el-checkbox>\r\n            </el-form-item>\r\n\r\n            <!-- 动态字段配置 -->\r\n            <el-table\r\n              :data=\"config.fields\"\r\n              border\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-table-column\r\n                align=\"center\"\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"60\"\r\n              />\r\n\r\n              <el-table-column label=\"表头名称\" min-width=\"100\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.fieldKey\"\r\n                    filterable\r\n                    placeholder=\"选择字段\"\r\n                    style=\"width: 100%\"\r\n                    @change=\"handleFieldSelect(scope.$index)\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(config, key) in fieldLabelMap\"\r\n                      :key=\"key\"\r\n                      :label=\"config.name\"\r\n                      :value=\"key\"\r\n                    />\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"排序\" width=\"60\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.sort\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <el-option label=\"∧\" value=\"asc\"/>\r\n                    <el-option label=\"∨ \" value=\"desc\"/>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"汇总方式\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.aggregation\"\r\n                    :disabled=\"!isAggregatable(scope.row.fieldKey)\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <el-option label=\"计数\" value=\"count\"/>\r\n                    <el-option label=\"求和\" value=\"sum\"/>\r\n                    <el-option label=\"平均值\" value=\"avg\"/>\r\n                    <el-option label=\"最大值\" value=\"max\"/>\r\n                    <el-option label=\"最小值\" value=\"min\"/>\r\n                    <el-option label=\"方差\" value=\"variance\"/>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"显示格式\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.format\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <template v-if=\"getFieldDisplay(scope.row.fieldKey) === 'date'\">\r\n                      <el-option label=\"YYYYMM\" value=\"YYYYMM\"/>\r\n                      <el-option label=\"MM-DD\" value=\"MM-DD\"/>\r\n                      <el-option label=\"YYYY-MM-DD\" value=\"YYYY-MM-DD\"/>\r\n                    </template>\r\n                    <template v-if=\"getFieldDisplay(scope.row.fieldKey) === 'number'\">\r\n                      <el-option label=\"0.00\" value=\"decimal\"/>\r\n                      <el-option label=\"0.00%\" value=\"percent\"/>\r\n                      <el-option label=\"¥0.00\" value=\"currency\"/>\r\n                      <el-option label=\"$0.00\" value=\"usd\"/>\r\n                      <el-option label=\"0不显示\" value=\"hideZero\"/>\r\n                    </template>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column align=\"center\" label=\"0隐藏\" width=\"40\">\r\n                <template #default=\"scope\">\r\n                  <el-checkbox\r\n                    v-model=\"scope.row.hideZeroValues\"\r\n                    :disabled=\"getFieldDisplay(scope.row.fieldKey) !== 'number'\"\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column align=\"center\" label=\"操作\" width=\"100\">\r\n                <template #default=\"scope\">\r\n                  <el-button-group>\r\n                    <el-button\r\n                      :disabled=\"scope.$index === 0\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"moveField(scope.$index, 'up')\"\r\n                    >[∧]\r\n                    </el-button>\r\n                    <el-button\r\n                      :disabled=\"scope.$index === config.fields.length - 1\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"moveField(scope.$index, 'down')\"\r\n                    >[∨]\r\n                    </el-button>\r\n                    <el-button\r\n                      icon=\"el-icon-delete\"\r\n                      size=\"mini\"\r\n                      style=\"color: red\"\r\n                      type=\"text\"\r\n                      @click=\"removeField(scope.$index)\"\r\n                    >\r\n                    </el-button>\r\n                  </el-button-group>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <div style=\"margin-top: 10px;\">\r\n              <el-button plain type=\"text\" @click=\"addField\">[ + ]</el-button>\r\n            </div>\r\n\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleServerAggregate\">分类汇总</el-button>\r\n              <el-button @click=\"resetConfig\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 结果展示 - 右侧 -->\r\n      <el-col v-if=\"showResult\" :span=\"14\">\r\n        <el-card class=\"result-card\">\r\n          <template #header>\r\n            <div class=\"header-with-operations\">\r\n              <span>汇总结果</span>\r\n              <div class=\"operations\">\r\n                <el-switch\r\n                  v-model=\"isLandscape\"\r\n                  active-text=\"横向\"\r\n                  inactive-text=\"纵向\"\r\n                  style=\"margin-right: 15px\"\r\n                />\r\n                <el-button size=\"small\" @click=\"printTable\">打印</el-button>\r\n                <el-button size=\"small\" type=\"primary\" @click=\"exportToPDF\">导出PDF</el-button>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <el-table\r\n            ref=\"resultTable\"\r\n            v-loading=\"loading\"\r\n            :data=\"processedData\"\r\n            :summary-method=\"getSummary\"\r\n            border\r\n            show-summary\r\n            style=\"width: 100%\"\r\n          >\r\n            <!-- 分组字段列 -->\r\n            <el-table-column\r\n              :align=\"config.primaryField ? fieldLabelMap[config.primaryField].align : 'left'\"\r\n              :label=\"groupFieldName\"\r\n              :width=\"config.primaryField ? fieldLabelMap[config.primaryField].width : ''\"\r\n            >\r\n              <template #default=\"scope\">\r\n                {{ formatGroupKey(scope.row.groupKey) }}\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <!-- 动态字段列 - 按照config.fields的顺序渲染 -->\r\n            <template v-for=\"(field, fieldIndex) in config.fields\">\r\n              <el-table-column\r\n                v-if=\"field.fieldKey\"\r\n                :key=\"field.fieldKey + '_' + fieldIndex\"\r\n                :align=\"getColumnAlign(field.fieldKey)\"\r\n                :label=\"getResultLabel(field)\"\r\n                :width=\"getColumnWidth(field.fieldKey)\"\r\n              >\r\n                <template #default=\"scope\">\r\n                  {{ formatCellValue(scope.row[getResultProp(field)], field) }}\r\n                </template>\r\n              </el-table-column>\r\n            </template>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 加载配置对话框 -->\r\n    <el-dialog :visible.sync=\"configDialogVisible\" append-to-body title=\"加载配置\" width=\"550px\">\r\n      <el-table\r\n        v-loading=\"configLoading\"\r\n        :data=\"savedConfigs\"\r\n        style=\"width: 100%\"\r\n        @row-click=\"handleConfigSelect\"\r\n      >\r\n        <el-table-column label=\"配置名称\" prop=\"name\"/>\r\n        <el-table-column label=\"创建时间\" prop=\"createTime\"/>\r\n        <el-table-column width=\"50\">\r\n          <template #default=\"scope\">\r\n            <el-button type=\"text\" @click.stop=\"deleteConfig(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n\r\n    <!-- 筛选条件对话框 -->\r\n    <el-dialog :visible.sync=\"filterDialogVisible\" title=\"设置筛选条件\" width=\"650px\">\r\n      <el-form :model=\"currentFilter\" label-width=\"100px\">\r\n        <el-form-item label=\"字段\">\r\n          <el-select v-model=\"currentFilter.field\" filterable placeholder=\"选择字段\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"(config, key) in fieldLabelMap\"\r\n              :key=\"key\"\r\n              :label=\"config.name\"\r\n              :value=\"key\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"操作符\">\r\n          <el-select v-model=\"currentFilter.operator\" placeholder=\"选择操作符\" style=\"width: 100%\">\r\n            <el-option label=\"等于\" value=\"eq\"/>\r\n            <el-option label=\"不等于\" value=\"ne\"/>\r\n            <el-option label=\"大于\" value=\"gt\"/>\r\n            <el-option label=\"大于等于\" value=\"ge\"/>\r\n            <el-option label=\"小于\" value=\"lt\"/>\r\n            <el-option label=\"小于等于\" value=\"le\"/>\r\n            <el-option label=\"包含\" value=\"contains\"/>\r\n            <el-option label=\"开始于\" value=\"startsWith\"/>\r\n            <el-option label=\"结束于\" value=\"endsWith\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"值\">\r\n          <el-input v-model=\"currentFilter.value\" placeholder=\"输入筛选值\"/>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"addFilter\">添加筛选条件</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div v-if=\"config.filters && config.filters.length\">\r\n        <h4>已添加的筛选条件</h4>\r\n        <el-table :data=\"config.filters\" border>\r\n          <el-table-column label=\"字段\" prop=\"field\">\r\n            <template #default=\"scope\">\r\n              {{ getFieldLabel(scope.row.field) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作符\" prop=\"operator\">\r\n            <template #default=\"scope\">\r\n              {{ getOperatorLabel(scope.row.operator) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"值\" prop=\"value\"/>\r\n          <el-table-column label=\"操作\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <el-button\r\n                circle\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                @click=\"removeFilter(scope.$index)\"\r\n              ></el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"filterDialogVisible = false\">关闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\nimport {saveAggregatorConfig, loadAggregatorConfigs, deleteAggregatorConfig} from \"@/api/system/aggregator\"\r\nimport html2pdf from \"html2pdf.js\"\r\n\r\nexport default {\r\n  name: \"DataAggregatorBackGround\",\r\n  props: {\r\n    // 可用字段配置映射\r\n    fieldLabelMap: {\r\n      type: Object,\r\n      required: true,\r\n      default: () => ({})\r\n    },\r\n    // 指定数据来源类型，用于后端查询\r\n    dataSourceType: {\r\n      type: String,\r\n      required: true,\r\n      default: 'rct' // 默认是操作单数据\r\n    },\r\n    // 从父组件传入的汇总函数\r\n    aggregateFunction: {\r\n      type: Function,\r\n      required: true\r\n    },\r\n    configType: {\r\n      type: String,\r\n      required: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      configName: \"\",\r\n      config: {\r\n        name: \"\",\r\n        primaryField: \"\",\r\n        matchOptions: {\r\n          exact: true,\r\n          caseSensitive: false\r\n        },\r\n        dateField: \"\",\r\n        dateOptions: {\r\n          convertToNumber: false,\r\n          formatType: \"day\"\r\n        },\r\n        showDetails: false,\r\n        fields: [],\r\n        filters: [] // 存储筛选条件\r\n      },\r\n      dateOptions: [\r\n        {label: \"按年\", value: \"year\"},\r\n        {label: \"按月\", value: \"month\"},\r\n        {label: \"按周\", value: \"week\"},\r\n        {label: \"按日\", value: \"day\"},\r\n        {label: \"按时\", value: \"hour\"},\r\n        {label: \"按分\", value: \"minute\"}\r\n      ],\r\n      aggregationOptions: [\r\n        {label: \"计数\", value: \"count\"},\r\n        {label: \"求和\", value: \"sum\"},\r\n        {label: \"平均值\", value: \"avg\"},\r\n        {label: \"方差\", value: \"variance\"},\r\n        {label: \"最大值\", value: \"max\"},\r\n        {label: \"最小值\", value: \"min\"}\r\n      ],\r\n      operatorOptions: [\r\n        {label: \"等于\", value: \"eq\"},\r\n        {label: \"不等于\", value: \"ne\"},\r\n        {label: \"大于\", value: \"gt\"},\r\n        {label: \"大于等于\", value: \"ge\"},\r\n        {label: \"小于\", value: \"lt\"},\r\n        {label: \"小于等于\", value: \"le\"},\r\n        {label: \"包含\", value: \"contains\"},\r\n        {label: \"开始于\", value: \"startsWith\"},\r\n        {label: \"结束于\", value: \"endsWith\"}\r\n      ],\r\n      loading: false,\r\n      configDialogVisible: false,\r\n      filterDialogVisible: false,\r\n      savedConfigs: [],\r\n      configLoading: false,\r\n      isLandscape: false,\r\n      showResult: false,\r\n      processedData: [],\r\n      currentFilter: {\r\n        field: \"\",\r\n        operator: \"eq\",\r\n        value: \"\"\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 可用字段列表\r\n    availableFields() {\r\n      // 返回在 fieldLabelMap 中定义的所有字段\r\n      return Object.keys(this.fieldLabelMap)\r\n    },\r\n\r\n    // 日期类型字段列表\r\n    dateFields() {\r\n      return this.availableFields.filter(field => {\r\n        // 检查 fieldLabelMap 中的 display 属性\r\n        return this.fieldLabelMap[field] && this.fieldLabelMap[field].display === \"date\"\r\n      })\r\n    },\r\n\r\n    // 分组字段名称\r\n    groupFieldName() {\r\n      if (this.config.primaryField && this.config.dateField) {\r\n        return `${this.getFieldLabel(this.config.dateField)}+${this.getFieldLabel(this.config.primaryField)}`\r\n      } else if (this.config.primaryField) {\r\n        return this.getFieldLabel(this.config.primaryField)\r\n      } else if (this.config.dateField) {\r\n        return this.getFieldLabel(this.config.dateField)\r\n      }\r\n      return \"分组\"\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * 根据字段键获取字段标签\r\n     * @param {String} field - 字段的键\r\n     * @returns {String} 字段的标签\r\n     */\r\n    getFieldLabel(field) {\r\n      return this.fieldLabelMap[field]?.name || field\r\n    },\r\n\r\n    /**\r\n     * 根据操作符代码获取操作符标签\r\n     * @param {String} op - 操作符代码\r\n     * @returns {String} 操作符标签\r\n     */\r\n    getOperatorLabel(op) {\r\n      const operator = this.operatorOptions.find(item => item.value === op)\r\n      return operator ? operator.label : op\r\n    },\r\n\r\n    /**\r\n     * 打开筛选条件对话框\r\n     */\r\n    openFilterDialog() {\r\n      this.currentFilter = {\r\n        field: \"\",\r\n        operator: \"eq\",\r\n        value: \"\"\r\n      }\r\n      this.filterDialogVisible = true\r\n    },\r\n\r\n    /**\r\n     * 添加筛选条件\r\n     */\r\n    addFilter() {\r\n      if (!this.currentFilter.field) {\r\n        this.$message.warning(\"请选择筛选字段\")\r\n        return\r\n      }\r\n\r\n      if (!this.currentFilter.value && this.currentFilter.value !== 0) {\r\n        this.$message.warning(\"请输入筛选值\")\r\n        return\r\n      }\r\n\r\n      // 初始化filters数组（如果不存在）\r\n      if (!this.config.filters) {\r\n        this.$set(this.config, 'filters', [])\r\n      }\r\n\r\n      // 添加新的筛选条件\r\n      this.config.filters.push({...this.currentFilter})\r\n\r\n      // 重置当前筛选条件\r\n      this.currentFilter = {\r\n        field: \"\",\r\n        operator: \"eq\",\r\n        value: \"\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 移除筛选条件\r\n     * @param {Number} index - 要移除的筛选条件索引\r\n     */\r\n    removeFilter(index) {\r\n      this.config.filters.splice(index, 1)\r\n    },\r\n\r\n    /**\r\n     * 获取字段的显示类型\r\n     * @param {string} fieldKey - 字段标识\r\n     * @returns {string} 字段显示类型（text/number/date/boolean/custom）\r\n     */\r\n    getFieldDisplay(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      if (!fieldConfig) return \"text\"\r\n\r\n      // 检查是否是自定义方法\r\n      if (fieldConfig.display && typeof this[fieldConfig.display] === \"function\") {\r\n        return \"custom\"\r\n      }\r\n\r\n      return fieldConfig.display || \"text\"\r\n    },\r\n\r\n    /**\r\n     * 判断字段是否可以进行汇总计算\r\n     * @param {string} fieldKey - 字段标识\r\n     * @returns {boolean} 是否可汇总\r\n     */\r\n    isAggregatable(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      return fieldConfig?.aggregated || false\r\n    },\r\n\r\n    /**\r\n     * 添加新的字段配置行\r\n     */\r\n    addField() {\r\n      this.config.fields.push({\r\n        fieldKey: \"\",\r\n        aggregation: \"none\",\r\n        format: \"none\",\r\n        sort: \"none\",\r\n        hideZeroValues: false\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 删除指定索引的字段配置行\r\n     * @param {number} index - 要删除的字段索引\r\n     */\r\n    removeField(index) {\r\n      this.config.fields.splice(index, 1)\r\n    },\r\n\r\n    /**\r\n     * 移动字段配置行的位置\r\n     * @param {number} index - 当前字段的索引\r\n     * @param {string} direction - 移动方向，'up' 或 'down'\r\n     */\r\n    moveField(index, direction) {\r\n      const fields = [...this.config.fields] // 创建数组副本\r\n\r\n      if (direction === \"up\" && index > 0) {\r\n        // 向上移动，与上一个元素交换位置\r\n        [fields[index], fields[index - 1]] = [fields[index - 1], fields[index]]\r\n      } else if (direction === \"down\" && index < fields.length - 1) {\r\n        // 向下移动，与下一个元素交换位置\r\n        [fields[index], fields[index + 1]] = [fields[index + 1], fields[index]]\r\n      }\r\n\r\n      // 使用整个新数组替换，确保响应式更新\r\n      this.$set(this.config, \"fields\", fields)\r\n    },\r\n\r\n    /**\r\n     * 处理字段选择变更事件\r\n     * @param {number} index - 变更的字段索引\r\n     */\r\n    handleFieldSelect(index) {\r\n      const field = this.config.fields[index]\r\n      const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n      if (fieldConfig) {\r\n        // 根据字段配置设置默认值\r\n        field.format = this.getDefaultFormat(fieldConfig.display)\r\n        field.aggregation = fieldConfig.aggregated ? \"sum\" : \"none\"\r\n        field.sort = \"none\" // 默认不排序\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 根据显示类型获取默认的格式化方式\r\n     * @param {string} displayType - 显示类型\r\n     * @returns {string} 默认格式\r\n     */\r\n    getDefaultFormat(displayType) {\r\n      switch (displayType) {\r\n        case \"date\":\r\n          return \"YYYY-MM-DD\"\r\n        case \"number\":\r\n          return \"decimal\"\r\n        default:\r\n          return \"none\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 重置配置到初始状态\r\n     */\r\n    resetConfig() {\r\n      this.config = {\r\n        name: \"\",\r\n        primaryField: \"\",\r\n        matchOptions: {\r\n          exact: true,\r\n          caseSensitive: false\r\n        },\r\n        dateField: \"\",\r\n        dateOptions: {\r\n          convertToNumber: false,\r\n          formatType: \"day\"\r\n        },\r\n        showDetails: false,\r\n        fields: [],\r\n        filters: []\r\n      }\r\n      this.showResult = false\r\n    },\r\n\r\n    /**\r\n     * 获取日期格式化模式\r\n     * @returns {String} 日期格式\r\n     */\r\n    getDateFormat() {\r\n      switch (this.config.dateOptions.formatType) {\r\n        case \"year\":\r\n          return \"YYYY\"\r\n        case \"month\":\r\n          return \"YYYY-MM\"\r\n        case \"day\":\r\n        default:\r\n          return \"YYYY-MM-DD\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 调用后端服务进行数据汇总\r\n     */\r\n    async handleServerAggregate() {\r\n      // 验证分组依据和分组日期至少填写一个\r\n      if (!this.config.primaryField && !this.config.dateField) {\r\n        this.$message.warning(\"请至少选择分组依据或分组日期其中之一\");\r\n        return;\r\n      }\r\n\r\n      // 验证是否有字段配置\r\n      if (!this.config.fields.length) {\r\n        this.$message.warning(\"请添加至少一个字段\");\r\n        return;\r\n      }\r\n\r\n      // 验证字段配置是否完整\r\n      const incompleteField = this.config.fields.find(field => !field.fieldKey);\r\n      if (incompleteField) {\r\n        this.$message.warning(\"请完成所有字段的配置\");\r\n        return;\r\n      }\r\n\r\n      // 确保汇总函数已经传入\r\n      if (!this.aggregateFunction) {\r\n        this.$message.error(\"汇总函数未定义\")\r\n        return\r\n      }\r\n\r\n      try {\r\n        this.loading = true\r\n\r\n        // 准备请求参数\r\n        const params = {\r\n          dataSourceType: this.dataSourceType,\r\n          config: {\r\n            primaryField: this.config.primaryField,\r\n            matchOptions: this.config.matchOptions,\r\n            dateField: this.config.dateField,\r\n            dateOptions: this.config.dateOptions,\r\n            showDetails: this.config.showDetails,\r\n            fields: this.config.fields,\r\n            filters: this.config.filters || []\r\n          }\r\n        }\r\n\r\n        // 调用从父组件传入的汇总函数\r\n        const response = await this.aggregateFunction(params)\r\n\r\n        if (response.code === 200) {\r\n          // 过滤零值记录\r\n          this.processedData = this.filterZeroValueRecords(response.data)\r\n          this.showResult = true\r\n        } else {\r\n          this.$message.error(response.msg || \"汇总数据失败\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"数据汇总失败:\", error)\r\n        this.$message.error(\"汇总处理失败：\" + (error.message || \"未知错误\"))\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 过滤零值记录\r\n     * @param {Array} data - 原始数据\r\n     * @returns {Array} 过滤后的数据\r\n     */\r\n    filterZeroValueRecords(data) {\r\n      // 找出设置了hideZeroValues为true的字段\r\n      const zeroFilterFields = this.config.fields\r\n        .filter(field => field.hideZeroValues === true)\r\n        .map(field => ({\r\n          key: field.fieldKey,\r\n          aggProp: this.getResultProp(field)\r\n        }));\r\n\r\n      // 如果没有需要过滤的字段，直接返回原始数据\r\n      if (zeroFilterFields.length === 0) {\r\n        return data;\r\n      }\r\n\r\n      // 过滤数据\r\n      return data.filter(record => {\r\n        // 检查每个需要过滤零值的字段\r\n        for (const field of zeroFilterFields) {\r\n          const value = record[field.aggProp];\r\n          // 如果字段值为0，过滤掉这条记录\r\n          if (value === 0 || value === \"0\" || value === \"0.00\") {\r\n            return false;\r\n          }\r\n        }\r\n        // 所有字段都不为零，保留这条记录\r\n        return true;\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 获取结果数据的属性名\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 属性名\r\n     */\r\n    getResultProp(field) {\r\n      // 如果有汇总方式，属性名为 fieldKey_aggregation\r\n      if (field.aggregation && field.aggregation !== \"none\") {\r\n        return `${field.fieldKey}_${field.aggregation}`\r\n      }\r\n      return field.fieldKey\r\n    },\r\n\r\n    /**\r\n     * 获取结果表格的列标题\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 列标题\r\n     */\r\n    getResultLabel(field) {\r\n      const baseLabel = this.getFieldLabel(field.fieldKey)\r\n\r\n      // 如果有汇总方式，在标签中添加汇总方式信息\r\n      if (field.aggregation && field.aggregation !== \"none\") {\r\n        // 获取汇总方式的中文名称\r\n        const aggregationLabel = this.aggregationOptions.find(opt => opt.value === field.aggregation)?.label || field.aggregation\r\n        return `${baseLabel}(${aggregationLabel})`\r\n      }\r\n\r\n      return baseLabel\r\n    },\r\n\r\n    /**\r\n     * 格式化单元格的值\r\n     * @param {*} value - 原始值\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 格式化后的值\r\n     */\r\n    formatCellValue(value, field) {\r\n      if (value == null) return \"-\"\r\n\r\n      const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n      if (!fieldConfig) return value\r\n\r\n      // 处理自定义 display 方法\r\n      if (fieldConfig.display && typeof this[fieldConfig.display] === \"function\") {\r\n        // 调用组件中定义的方法\r\n        return this[fieldConfig.display](value)\r\n      }\r\n\r\n      // 根据字段类型进行格式化\r\n      switch (fieldConfig.display) {\r\n        case \"number\":\r\n          const numValue = Number(value)\r\n          if (isNaN(numValue)) return \"-\"\r\n\r\n          switch (field.format) {\r\n            case \"decimal\":\r\n              return numValue.toFixed(2)\r\n            case \"percent\":\r\n              return (numValue * 100).toFixed(2) + \"%\"\r\n            case \"currency\":\r\n              return \"¥\" + numValue.toFixed(2)\r\n            case \"usd\":\r\n              return \"$\" + numValue.toFixed(2)\r\n            case \"hideZero\":\r\n              return numValue === 0 ? \"-\" : numValue.toFixed(2)\r\n            default:\r\n              return numValue.toFixed(2)\r\n          }\r\n\r\n        case \"date\":\r\n          return moment(value).format(field.format || \"YYYY-MM-DD\")\r\n\r\n        case \"boolean\":\r\n          if (field.aggregation === \"avg\") {\r\n            return (Number(value) * 100).toFixed(2) + \"%\"\r\n          }\r\n          return value ? \"是\" : \"否\"\r\n\r\n        default:\r\n          return value\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 格式化分组键\r\n     * @param {Object|string} groupKey - 分组键\r\n     * @returns {string} 格式化后的分组键\r\n     */\r\n    formatGroupKey(groupKey) {\r\n      if (typeof groupKey === \"object\" && groupKey !== null) {\r\n        if (groupKey.primary !== undefined && groupKey.date !== undefined) {\r\n          // 获取主分组字段的配置\r\n          const primaryFieldConfig = this.fieldLabelMap[this.config.primaryField]\r\n          let primaryValue = groupKey.primary\r\n\r\n          // 如果主分组字段有自定义 display 方法，应用它\r\n          if (primaryFieldConfig && primaryFieldConfig.display &&\r\n            typeof this[primaryFieldConfig.display] === \"function\") {\r\n            primaryValue = this[primaryFieldConfig.display](primaryValue)\r\n          }\r\n\r\n          // 日期值在前，主值在后\r\n          return `${groupKey.date} ${primaryValue}`\r\n        }\r\n      }\r\n\r\n      // 如果是简单值，检查是否需要应用自定义 display 方法\r\n      if (this.config.primaryField) {\r\n        const fieldConfig = this.fieldLabelMap[this.config.primaryField]\r\n        if (fieldConfig && fieldConfig.display &&\r\n          typeof this[fieldConfig.display] === \"function\") {\r\n          return this[fieldConfig.display](groupKey)\r\n        }\r\n      }\r\n\r\n      return String(groupKey || \"\")\r\n    },\r\n\r\n    /**\r\n     * 计算表格合计行\r\n     * @param {Object} param0 - 包含列信息和数据的对象\r\n     * @returns {Array} 合计行数据\r\n     */\r\n    getSummary({columns, data}) {\r\n      const sums = []\r\n\r\n      columns.forEach((column, index) => {\r\n        // 第一列显示\"合计\"文本\r\n        if (index === 0) {\r\n          sums[index] = \"合计\"\r\n          return\r\n        }\r\n\r\n        // 使用索引获取当前列对应的字段配置\r\n        const fieldIndex = index - 1\r\n        const field = this.config.fields[fieldIndex]\r\n\r\n        if (!field || !field.fieldKey) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 检查字段是否配置了汇总方式\r\n        if (!field.aggregation || field.aggregation === \"none\") {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 获取字段配置\r\n        const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n\r\n        // 对于不是数字类型的字段但有percentage显示方法的特殊处理\r\n        if (!fieldConfig) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        if (fieldConfig.display !== \"number\" &&\r\n          fieldConfig.display !== \"percentage\" &&\r\n          typeof this[fieldConfig.display] !== \"function\") {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 获取列数据并转换为数字\r\n        const values = data.map(item => {\r\n          const prop = this.getResultProp(field)\r\n          const val = Number(item[prop])\r\n          return isNaN(val) ? 0 : val // 处理非数字值\r\n        }).filter(val => !isNaN(val))\r\n\r\n        if (values.length === 0) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 根据汇总方式计算结果\r\n        let sum = 0\r\n        switch (field.aggregation) {\r\n          case \"sum\":\r\n            sum = values.reduce((a, b) => a + b, 0)\r\n            break\r\n          case \"avg\":\r\n            sum = values.reduce((a, b) => a + b, 0) / values.length\r\n            break\r\n          case \"max\":\r\n            sum = Math.max(...values)\r\n            break\r\n          case \"min\":\r\n            sum = Math.min(...values)\r\n            break\r\n          case \"variance\":\r\n            const mean = values.reduce((a, b) => a + b, 0) / values.length\r\n            sum = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length\r\n            break\r\n          default:\r\n            sum = values.reduce((a, b) => a + b, 0)\r\n        }\r\n\r\n        // 根据字段显示类型和格式设置来格式化结果\r\n        if (fieldConfig.display === \"percentage\" || fieldConfig.display === \"percent\") {\r\n          // 使用percentage方法格式化\r\n          sums[index] = this.percentage(sum)\r\n        } else if (field.format === \"decimal\") {\r\n          sums[index] = sum.toFixed(2)\r\n        } else if (field.format === \"percent\") {\r\n          sums[index] = (sum * 100).toFixed(2) + \"%\"\r\n        } else if (field.format === \"currency\") {\r\n          sums[index] = \"¥\" + sum.toFixed(2)\r\n        } else if (field.format === \"usd\") {\r\n          sums[index] = \"$\" + sum.toFixed(2)\r\n        } else {\r\n          sums[index] = sum.toFixed(2)\r\n        }\r\n      })\r\n\r\n      return sums\r\n    },\r\n\r\n    /**\r\n     * 保存当前配置\r\n     */\r\n    async saveConfig() {\r\n      try {\r\n        // 验证配置名称\r\n        if (!this.config.name) {\r\n          this.$message.warning(\"请输入速查名称\")\r\n          return\r\n        }\r\n\r\n        // 验证分组依据和分组日期至少填写一个\r\n        if (!this.config.primaryField && !this.config.dateField) {\r\n          this.$message.warning(\"请至少选择分组依据或分组日期其中之一\")\r\n          return\r\n        }\r\n\r\n        if (!this.config.fields.length) {\r\n          this.$message.warning(\"请添加至少一个字段\")\r\n          return\r\n        }\r\n\r\n        // 验证字段配置是否完整\r\n        const incompleteField = this.config.fields.find(field => !field.fieldKey)\r\n        if (incompleteField) {\r\n          this.$message.warning(\"请完成所有字段的配置\")\r\n          return\r\n        }\r\n\r\n        // 构造符合 AggregatorConfigDTO 的数据结构\r\n        const configToSave = {\r\n          name: this.config.name,\r\n          type: this.configType,\r\n          config: this.config\r\n        }\r\n\r\n        // 发送请求\r\n        await saveAggregatorConfig(configToSave)\r\n\r\n        this.$message.success(\"配置保存成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"保存配置失败：\" + (err.message || \"未知错误\"))\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 加载保存的配置列表\r\n     */\r\n    async loadConfigs() {\r\n      this.configLoading = true\r\n      this.configDialogVisible = true\r\n      try {\r\n        const result = await loadAggregatorConfigs({configType: this.configType})\r\n        this.savedConfigs = result.rows\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\r\n          err.response?.data?.message ||\r\n          err.message ||\r\n          \"加载配置列表失败，请稍后重试\"\r\n        )\r\n      } finally {\r\n        this.configLoading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 选择并加载配置\r\n     * @param {Object} row - 选中的配置行\r\n     */\r\n    async handleConfigSelect(row) {\r\n      try {\r\n        // 解析配置JSON\r\n        var config = JSON.parse(row.config)\r\n        config.name = row.name\r\n        this.config = config\r\n        // this.config.name = row.name\r\n\r\n        this.configDialogVisible = false\r\n        this.$message.success(\"配置加载成功\")\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\"加载配置失败：\" + err.message)\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 删除配置\r\n     * @param {Object} row - 要删除的配置行\r\n     */\r\n    async deleteConfig(row) {\r\n      try {\r\n        await this.$confirm(\"确认删除该配置？\", \"提示\", {\r\n          type: \"warning\"\r\n        })\r\n\r\n        await deleteAggregatorConfig(row.id)\r\n        this.savedConfigs = this.savedConfigs.filter(config => config.id !== row.id)\r\n        this.$message.success(\"配置删除成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"删除配置失败：\" + err.message)\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 打印表格\r\n     */\r\n    printTable() {\r\n      // 实现与原组件相同的打印功能\r\n      const printWindow = window.open(\"\", \"_blank\")\r\n      const table = this.$refs.resultTable.$el.cloneNode(true)\r\n      const title = \"汇总数据\"\r\n      const date = new Date().toLocaleDateString()\r\n\r\n      // 公司标志和标题的HTML模板\r\n      const headerTemplate = `\r\n        <div class=\"company-header\">\r\n          <div class=\"company-logo\">\r\n            <img src=\"/logo.png\" alt=\"Rich Shipping Logo\" />\r\n            <div class=\"company-name\">\r\n              <div class=\"company-name-cn\">广州瑞旗国际货运代理有限公司</div>\r\n              <div class=\"company-name-en\">GUANGZHOU RICH SHIPPING INT'L CO.,LTD.</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"document-title\">\r\n            <div class=\"title-cn\"></div>\r\n            <div class=\"title-en\"></div>\r\n          </div>\r\n        </div>\r\n      `\r\n      printWindow.document.write(`\r\n        <html lang=\"\">\r\n          <head>\r\n            <title>${title}</title>\r\n            <style>\r\n              /* 基础样式 */\r\n              body {\r\n                margin: 0;\r\n                padding: 0;\r\n                font-family: Arial, sans-serif;\r\n              }\r\n\r\n              /* 打印样式 - 必须放在这里才能生效 */\r\n              @media print {\r\n                @page {\r\n                  size: ${this.isLandscape ? \"landscape\" : \"portrait\"};\r\n                  margin: 1.5cm 1cm 1cm 1cm;\r\n                }\r\n\r\n                /* 重要：使用重复表头技术 */\r\n                thead {\r\n                  display: table-header-group;\r\n                }\r\n\r\n                /* 页眉作为表格的一部分，放在thead中 */\r\n                .page-header {\r\n                  display: table-header-group;\r\n                }\r\n\r\n                /* 内容部分 */\r\n                .page-content {\r\n                  display: table-row-group;\r\n                }\r\n\r\n                /* 避免元素内部分页 */\r\n                .company-header, .header-content {\r\n                  page-break-inside: avoid;\r\n                }\r\n\r\n                /* 表格样式 */\r\n                table.main-table {\r\n                  width: 100%;\r\n                  border-collapse: collapse;\r\n                  border: none;\r\n                }\r\n\r\n                /* 确保表头在每页都显示 */\r\n                table.data-table thead {\r\n                  display: table-header-group;\r\n                }\r\n\r\n                /* 避免行内分页 */\r\n                table.data-table tr {\r\n                  page-break-inside: avoid;\r\n                }\r\n              }\r\n\r\n              /* 表格样式 */\r\n              table.data-table {\r\n                border-collapse: collapse;\r\n                width: 100%;\r\n                margin-top: 20px;\r\n                table-layout: fixed;\r\n              }\r\n\r\n              table.data-table th, table.data-table td {\r\n                border: 1px solid #ddd;\r\n                padding: 8px;\r\n                text-align: left;\r\n                font-size: 12px;\r\n                word-wrap: break-word;\r\n                word-break: break-all;\r\n                white-space: normal;\r\n              }\r\n\r\n              table.data-table th {\r\n                background-color: #f2f2f2;\r\n              }\r\n\r\n              /* Element UI 表格样式模拟 */\r\n              .el-table {\r\n                border-collapse: collapse;\r\n                width: 100%;\r\n                table-layout: fixed;\r\n              }\r\n\r\n              .el-table th, .el-table td {\r\n                border: 1px solid #ddd;\r\n                padding: 8px;\r\n                text-align: left;\r\n                font-size: 12px;\r\n                word-wrap: break-word;\r\n                word-break: break-all;\r\n                white-space: normal;\r\n              }\r\n\r\n              .el-table th {\r\n                background-color: #f2f2f2;\r\n                font-weight: bold;\r\n              }\r\n\r\n              .el-table__footer {\r\n                background-color: #f8f8f9;\r\n                font-weight: bold;\r\n              }\r\n\r\n              .el-table__footer td {\r\n                border: 1px solid #ddd;\r\n                padding: 8px;\r\n              }\r\n\r\n              /* 公司标题和标志样式 */\r\n              .company-header {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                border-bottom: 2px solid #000;\r\n                padding-bottom: 10px;\r\n                width: 100%;\r\n              }\r\n\r\n              .company-logo {\r\n                display: flex;\r\n                align-items: center;\r\n              }\r\n\r\n              .company-logo img {\r\n                height: 50px;\r\n                margin-right: 10px;\r\n              }\r\n\r\n              .company-name {\r\n                display: flex;\r\n                flex-direction: column;\r\n              }\r\n\r\n              .company-name-cn {\r\n                font-size: 18px;\r\n                font-weight: bold;\r\n                color: #ff0000;\r\n              }\r\n\r\n              .company-name-en {\r\n                font-size: 14px;\r\n              }\r\n\r\n              .document-title {\r\n                text-align: right;\r\n              }\r\n\r\n              .title-cn {\r\n                font-size: 18px;\r\n                font-weight: bold;\r\n              }\r\n\r\n              .title-en {\r\n                font-size: 16px;\r\n                font-weight: bold;\r\n              }\r\n\r\n              /* 清除表格边框 */\r\n              table.main-table, table.main-table td {\r\n                border: none;\r\n              }\r\n\r\n              /* 页眉容器 */\r\n              .header-container {\r\n                width: 100%;\r\n                margin-bottom: 20px;\r\n              }\r\n            </style>\r\n          </head>\r\n          <body>\r\n            <!-- 使用表格布局确保页眉在每页重复 -->\r\n            <table class=\"main-table\">\r\n              <thead class=\"page-header\">\r\n                <tr>\r\n                  <td>\r\n                    <div class=\"header-container\">\r\n                      ${headerTemplate}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </thead>\r\n              <tbody class=\"page-content\">\r\n                <tr>\r\n                  <td>\r\n                    <!-- 保留原始表格的类名并添加data-table类 -->\r\n                    ${table.outerHTML.replace('<table', '<table class=\"el-table data-table\"')}\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </body>\r\n        </html>\r\n      `)\r\n\r\n      printWindow.document.close()\r\n\r\n      setTimeout(() => {\r\n        try {\r\n          printWindow.focus();\r\n          printWindow.print();\r\n        } catch (e) {\r\n          console.error(\"打印过程中发生错误:\", e);\r\n        }\r\n      }, 1000)\r\n    },\r\n\r\n    /**\r\n     * 导出PDF\r\n     */\r\n    async exportToPDF() {\r\n      try {\r\n        this.loading = true\r\n        const element = this.$refs.resultTable.$el\r\n        const opt = {\r\n          margin: [0.8, 0.8, 0.8, 0.8],\r\n          filename: \"汇总数据.pdf\",\r\n          image: {type: \"jpeg\", quality: 0.98},\r\n          html2canvas: {scale: 2},\r\n          jsPDF: {\r\n            unit: \"in\",\r\n            format: \"a3\",\r\n            orientation: this.isLandscape ? \"landscape\" : \"portrait\"\r\n          },\r\n          pagebreak: {mode: [\"avoid-all\", \"css\", \"legacy\"]},\r\n          header: [\r\n            {text: \"汇总数据\", style: \"headerStyle\"},\r\n            {text: new Date().toLocaleDateString(), style: \"headerStyle\", alignment: \"right\"}\r\n          ],\r\n          footer: {\r\n            height: \"20px\",\r\n            contents: {\r\n              default: \"<span style=\\\"float:right\\\">{{page}}/{{pages}}</span>\"\r\n            }\r\n          }\r\n        }\r\n\r\n        await html2pdf().set(opt).from(element).save()\r\n        this.$message.success(\"PDF导出成功\")\r\n      } catch (error) {\r\n        this.$message.error(\"PDF导出失败：\" + error.message)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 获取人员姓名\r\n     * @param {Number} id - 人员ID\r\n     * @returns {String} 人员姓名\r\n     */\r\n    getName(id) {\r\n      if (id !== null) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff && staff !== undefined) {\r\n          return staff.staffShortName + staff.staffFamilyEnName\r\n        }\r\n      }\r\n      return \"\"\r\n    },\r\n\r\n    /**\r\n     * 格式化百分比值\r\n     * @param {*} value - 要格式化的值\r\n     * @returns {string} 格式化后的百分比\r\n     */\r\n    percentage(value) {\r\n      if (value == null || value === '') {\r\n        return '-';\r\n      }\r\n\r\n      // 处理已经带有%的情况\r\n      if (typeof value === 'string' && value.includes('%')) {\r\n        return value;\r\n      }\r\n\r\n      // 将数值转换为百分比格式\r\n      const numValue = Number(value);\r\n      if (isNaN(numValue)) {\r\n        return '-';\r\n      }\r\n\r\n      // 如果值已经是百分比形式(例如0.25表示25%)，则直接乘以100\r\n      // 如果值已经是整数形式(例如25表示25%)，则不需要乘以100\r\n      const isDecimal = numValue > 0 && numValue <= 1;\r\n      const percentValue = isDecimal ? numValue * 100 : numValue;\r\n\r\n      // 格式化为2位小数的百分比\r\n      return percentValue.toFixed(2) + '%';\r\n    },\r\n\r\n    /**\r\n     * 获取列对齐方式\r\n     * @param {string} fieldKey - 字段键\r\n     * @returns {string} 对齐方式\r\n     */\r\n    getColumnAlign(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      return fieldConfig && fieldConfig.align ? fieldConfig.align : 'left'\r\n    },\r\n\r\n    /**\r\n     * 获取列宽度\r\n     * @param {string} fieldKey - 字段键\r\n     * @returns {string|number} 列宽度\r\n     */\r\n    getColumnWidth(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      return fieldConfig && fieldConfig.width ? fieldConfig.width : ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.data-aggregator {\r\n  padding: 20px;\r\n}\r\n\r\n.config-card, .result-card {\r\n  height: 100%;\r\n  overflow: auto;\r\n}\r\n\r\n.result-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-with-operations {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.operations {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.filter-tags {\r\n  margin-top: 10px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 5px;\r\n}\r\n\r\n.el-tag {\r\n  margin-right: 5px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.field-config-table {\r\n  border: 1px solid #EBEEF5;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.table-header,\r\n.table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px;\r\n  border-bottom: 1px solid #EBEEF5;\r\n}\r\n\r\n.table-header {\r\n  background-color: #F5F7FA;\r\n  font-weight: bold;\r\n}\r\n\r\n.col {\r\n  flex: 1;\r\n  padding: 0 5px;\r\n  min-width: 120px;\r\n}\r\n\r\n.col:first-child {\r\n  flex: 0 0 60px;\r\n  min-width: 60px;\r\n}\r\n\r\n.col-operation {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-input-number {\r\n  width: 100%;\r\n}\r\n</style>\r\n\r\n"]}]}