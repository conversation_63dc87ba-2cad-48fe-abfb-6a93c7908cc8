<template>
  <div id="app">
    <el-row :gutter="20" style="margin: 0;padding: 0;">
      <!--搜索条件-->
      <el-col :span="showLeft">
        <el-form :model="queryParams" class="query" ref="queryForm" size="mini" :inline="true" v-show="showSearch">
          <el-form-item label="单号" prop="rctNo">
            <el-input v-model="queryParams.rctNo" placeholder="操作单号" @keydown.enter.native="handleQuery"/>
          </el-form-item>
          <el-form-item label="操作" prop="rctOpDate">
            <el-date-picker v-model="queryParams.rctOpDate" clearable
                            placeholder="操作日期"
                            style="width:100%"
                            type="daterange"
                            :default-time="['00:00:00', '23:59:59']"
                            value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="ATD" prop="rctOpDate">
            <el-date-picker v-model="queryParams.ATDDate" :default-time="['00:00:00', '23:59:59']"
                            clearable
                            placeholder="ATD"
                            style="width:100%"
                            type="daterange"
                            value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="ETD" prop="rctOpDate">
            <el-date-picker v-model="queryParams.ETDDate" clearable
                            placeholder="ETD"
                            style="width:100%"
                            type="daterange"
                            :default-time="['00:00:00', '23:59:59']"
                            value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="紧急" prop="urgencyDegree">
            <el-input v-model="queryParams.urgencyDegree" placeholder="紧急程度"/>
          </el-form-item>
          <el-form-item label="审核" prop="pasVerifyTime">
            <el-date-picker v-model="queryParams.pasVerifyTime" clearable
                            placeholder="商务审核时间"
                            style="width:100%"
                            :default-time="['00:00:00', '23:59:59']"
                            value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="标记" prop="isOpAllotted">
            <el-select v-model="queryParams.isOpAllotted" clearable placeholder="操作分配标记" style="width: 100%">
              <el-option label="未分配" value="0">未分配</el-option>
              <el-option label="已分配" value="1">已分配</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客户" prop="clientId">
            <company-select :multiple="false" :no-parent="true"
                            :pass="queryParams.clientId" :placeholder="'客户'" :role-client="'1'"
                            :role-control="true" :roleTypeId="1" @return="queryParams.clientId=$event"
            />
          </el-form-item>
          <el-form-item label="放货" prop="releaseTypeId">
            <tree-select :d-load="true" :flat="false"
                         :multiple="false" :pass="queryParams.releaseTypeId"
                         :placeholder="'放货方式'" :type="'releaseType'"
                         @return="queryParams.releaseTypeId=$event"
            />
          </el-form-item>
          <el-form-item label="进度" prop="processStatusId">
            <tree-select :d-load="true" :flat="false" :multiple="false"
                         :pass="queryParams.processStatusId" :placeholder="'进度状态'"
                         :type="'processStatus'" @return="queryParams.processStatusId=$event"
            />
          </el-form-item>
          <el-form-item label="物流" prop="logisticsTypeId">
            <tree-select :d-load="true" :flat="false" :multiple="false"
                         :pass="queryParams.logisticsTypeId" :placeholder="'物流类型'"
                         :type="'serviceType'" @return="queryParams.logisticsTypeId=$event"
            />
          </el-form-item>
          <el-form-item label="启运" prop="polIds">
            <location-select :multiple="true" :no-parent="true"
                             :pass="queryParams.polIds" :placeholder="'启运港'" @return="queryParams.polIds=$event"
            />
          </el-form-item>
          <el-form-item label="目的" prop="destinationPortIds">
            <location-select :en="true" :multiple="true"
                             :pass="queryParams.destinationPortIds" :placeholder="'目的港'"
                             @return="queryParams.destinationPortIds=$event"
            />
          </el-form-item>
          <el-form-item label="货量" prop="revenueTons">
            <el-input v-model="queryParams.revenueTons" placeholder="计费货量" style="width: 100%"/>
          </el-form-item>
          <el-form-item label="业务" prop="salesId">
            <treeselect v-model="salesId" :disable-branch-nodes="true" :disabled-fuzzy-matching="true"
                        :flatten-search-results="true" :normalizer="staffNormalizer"
                        :options="belongList" :show-count="true" placeholder="业务员"
                        @open="loadSales" @select="queryParams.salesId = $event.staffId"
                        @input="$event==undefined?queryParams.salesId = null:null"
            >
              <div slot="value-label" slot-scope="{node}">
                {{
                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + " " + node.raw.staff.staffGivingEnName : ""
                }}
              </div>
              <label slot="option-label"
                     slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                     :class="labelClassName"
              >
                {{ node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label }}
                <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
              </label>
            </treeselect>
          </el-form-item>
          <el-form-item label="助理" prop="salesAssistantId">
            <treeselect v-model="salesAssistantId" :disable-branch-nodes="true" :disabled-fuzzy-matching="true"
                        :flatten-search-results="true" :normalizer="staffNormalizer"
                        :options="belongList" :show-count="true" placeholder="业务助理"
                        @open="loadSales" @select="queryParams.salesAssistantId = $event.staffId"
                        @input="$event==undefined?queryParams.salesAssistantId=null:null"
            >
              <div slot="value-label" slot-scope="{node}">
                {{
                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + " " + node.raw.staff.staffGivingEnName : ""
                }}
              </div>
              <label slot="option-label"
                     slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                     :class="labelClassName"
              >
                {{ node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label }}
                <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
              </label>
            </treeselect>
          </el-form-item>
          <el-form-item label="标记" prop="isPsaVerified">
            <el-select v-model="queryParams.isPsaVerified" clearable placeholder="商务审核标记" style="width: 100%">
              <el-option label="已审" value="0">已审</el-option>
              <el-option label="未审" value="1">未审</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="商务" prop="verifyPsaId">
            <treeselect v-model="verifyPsaId" :disable-branch-nodes="true" :disabled-fuzzy-matching="true"
                        :flatten-search-results="true" :normalizer="staffNormalizer" :options="businessList"
                        :show-count="true" placeholder="商务"
                        @open="loadBusinesses" @select="queryParams.verifyPsaId = $event.staffId"
                        @input="$event==undefined?queryParams.verifyPsaId=null:null"
            >
              <div slot="value-label" slot-scope="{node}">
                {{
                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + " " + node.raw.staff.staffGivingEnName : ""
                }}
              </div>
              <label slot="option-label"
                     slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                     :class="labelClassName"
              >
                {{ node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label }}
                <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
              </label>
            </treeselect>
          </el-form-item>
          <el-form-item label="操作" prop="opId">
            <treeselect v-model="opId" :disable-branch-nodes="true" :disabled-fuzzy-matching="true"
                        :flatten-search-results="true" :normalizer="staffNormalizer"
                        :options="opList.filter(v => {return v.role.roleLocalName=='操作员'})"
                        :show-count="true" placeholder="操作员"
                        @open="loadOp" @select="queryParams.opId = $event.staffId"
                        @input="$event==undefined?queryParams.opId = null:null"
            >
              <div slot="value-label" slot-scope="{node}">
                {{
                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + " " + node.raw.staff.staffGivingEnName : ""
                }}
              </div>
              <label slot="option-label"
                     slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                     :class="labelClassName"
              >
                {{ node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label }}
                <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
              </label>
            </treeselect>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="showRight">
        <!--顶部操作按钮-->
        <el-row :gutter="10" class="mb8">
          <!--<el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:booking:add']"
            >新增
            </el-button>
          </el-col>-->
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:booking:add']"
              icon="el-icon-plus"
              plain
              size="mini"
              type="primary"
              @click="handleExport"
            >导出
            </el-button>
          </el-col>
          <!--<el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:booking:remove']"
            >删除
            </el-button>
          </el-col>-->

          <el-col :span="1.5">
            <el-col :span="2">
              <el-popover
                placement="right"
                trigger="click"
                width="400"
              >
                <el-table :data="statisticsOp">
                  <el-table-column label="操作" property="opName" width="100"></el-table-column>
                  <el-table-column label="已派单" property="number" width="300"></el-table-column>
                </el-table>
                <el-button v-if="type==='psa'" slot="reference" @click="handleStatistics">派单统计</el-button>
              </el-popover>
            </el-col>
          </el-col>
          <el-col :span="1.5">
            <el-button @click="handleOpenAggregator">数据汇总</el-button>
            <el-dialog v-dialogDrag v-dialogDragWidth
                       :visible.sync="openAggregator" append-to-body width="80%"
            >
              <data-aggregator-back-ground :aggregate-function="listAggregatorBooking"
                                           :config-type="'booking-agg'" :data-source-type="'rct'"
                                           :field-label-map="fieldLabelMap"/>
            </el-dialog>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!--表格-->
        <el-table v-loading="loading" :data="rctList"
                  stripe @selection-change="handleSelectionChange"
                  @row-dblclick="dbclick"
        >
          <el-table-column align="left" label="序号" type="index" width="50">
            <template slot-scope="scope">
              <el-badge
                :value="getBadge(scope.row)"
                class="item"
              >
                <div style="width: 15px">{{ scope.$index + 1 }}</div>
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column align="left" label="操作单号" prop="clientId" show-overflow-tooltip width="120">
            <template slot-scope="scope">
              <div class="column-text highlight-text" style="font-size: 18px;height: 23px"
              >{{ scope.row.rctNo }}
              </div>
              <div class="unHighlight-text" style="width: 100px;">{{
                  parseTime(scope.row.rctCreateTime, "{y}.{m}.{d}") + " " + emergencyLevel(scope.row.emergencyLevel)
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="委托单位" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <div class="column-text highlight-text">
                {{ scope.row.clientSummary ? scope.row.clientSummary.split("/")[1] : null }}
              </div>
              <div class="unHighlight-text" style="height: 23px">
                {{
                  (scope.row.orderBelongsTo ? scope.row.orderBelongsTo : "") + " " + (scope.row.releaseType ? getReleaseType(scope.row.releaseType) : "") + " " + scope.row.paymentNode
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="物流类型" width="50">
            <template slot-scope="scope">
              <div class="flex-box">
                <p class="column-text top-box" style=" font-weight: 600;">{{ scope.row.logisticsTypeEnName }}</p>
                <p class="column-text bottom-box" style=" color: #b7bbc2;height: 23px">
                  {{ scope.row.impExpType === "1" ? "出口" : "" }}
                  {{ scope.row.impExpType === "2" ? "进口" : "" }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="启运港" show-overflow-tooltip width="60">
            <template slot-scope="scope">
              <div style="width: 55px;overflow: hidden">
                <p class="column-text top-box " style=" font-size: 15px">
                  {{ scope.row.pol ? scope.row.pol.split("(")[0] : scope.row.pol }}
                </p>
                <p class="unHighlight-text">
                  {{ scope.row.pol ? "(" + scope.row.pol.split("(")[1] : "" }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="目的港" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <div style="width: 95px;overflow: hidden">
                <p class="column-text bottom-box highlight-text" style=" ">
                  {{ scope.row.destinationPort ? scope.row.destinationPort.split("(")[0] : scope.row.destinationPort }}
                </p>
                <p class="unHighlight-text">
                  {{ scope.row.destinationPort ? "(" + scope.row.destinationPort.split("(")[1] : "" }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="计费货量" width="140">
            <template slot-scope="scope">
              <div class="flex-box">
                <p class="column-text top-box highlight-text" style=" ">{{ scope.row.revenueTon }}</p>
                <p class="column-text bottom-box unHighlight-text" style=" ">{{ scope.row.goodsNameSummary }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="提单" width="60">
            <template slot-scope="scope">
              <div class="flex-box">
                <p class="column-text top-box" style=" ">
                  {{
                    (scope.row.blTypeCode ? scope.row.blTypeCode : "")
                  }}
                </p>
                <p class="column-text bottom-box unHighlight-text" style=" height: 23px">
                  {{ (scope.row.blFormCode ? scope.row.blFormCode : "") }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="订舱" show-overflow-tooltip width="90">
            <template slot-scope="scope">
              <div class="flex-box">
                <p class="column-text bottom-box"
                   style="text-overflow: ellipsis; white-space: nowrap;font-weight: 600;  font-size: 13px"
                >{{
                    scope.row.carrierEnName
                  }} <span class="column-text unHighlight-text" style=" font-size: 12px">{{
                    "(" + scope.row.agreementTypeCode + ")"
                    }}</span></p>
                <p class="column-text top-box" style="height: 23px ">{{ scope.row.supplierName }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="入仓与SO号">
            <template slot-scope="scope">
              <div class="flex-box">
                <p class="column-text top-box" style=" ">{{ scope.row.warehousingNo }}</p>
                <p class="column-text bottom-box" style=" ">{{ scope.row.soNo }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="提单与柜号" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="flex-box">
                <p class="column-text top-box" style=" ">{{ scope.row.blNo }}</p>
                <p class="column-text bottom-box" style=" height: 23px">{{ scope.row.sqdContainersSealsSum }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="订单状态" show-overflow-tooltip width="50">
            <template slot-scope="scope">
              <div class="flex-box">
                <p class="column-text top-box" style=" ">
                  {{ processStatus(scope.row.processStatusId) }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="物流进度" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="flex-box">
                <p class="column-text top-box" style=" ">{{
                    (scope.row.podEta ? ("ATD: " + parseTime(scope.row.podEta, "{m}-{d}")) : ("ETD: " + parseTime(scope.row.etd, "{m}-{d}")))
                  }}</p>
                <p class="column-text bottom-box" style="height: 23px ">{{
                    (scope.row.destinationPortEta ? ("ATA: " + parseTime(scope.row.destinationPortEta, "{m}-{d}")) : ("ETA: " + parseTime(scope.row.eta, "{m}-{d}")))
                  }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="文件进度">
            <template slot-scope="scope">
              <div class="flex-box">
                <p class="column-text top-box" style=" ">{{
                    (scope.row.docStatusA ? (scope.row.docStatusA.split(":")[0] + ": " + moment(scope.row.docStatusA).format("MM.DD")) : "")
                  }}</p>
                <p class="column-text bottom-box" style=" ">
                  {{
                    (scope.row.docStatusB ? (scope.row.docStatusB.split(":")[0] + ": " + moment(scope.row.docStatusB).format("MM.DD")) : "")
                  }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="right" label="收款" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <div class="flex-box">
                <p class="column-text top-box" style=" ">
                  {{
                    currency(scope.row.dnInRmb, {
                      separator: ",",
                      symbol: "¥",
                      precision: 2
                    }).format()
                  }}
                </p>
                <p class="column-text bottom-box unHighlight-text" style="height: 23px ">{{
                    currency(currency(scope.row.dnUsdBalance).value, {
                      separator: ",",
                      symbol: "$",
                      precision: 2
                    }).format() + " / " + currency(currency(scope.row.dnRmbBalance).value, {
                      separator: ",",
                      symbol: "¥",
                      precision: 2
                    }).format()
                  }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="right" label="主服务付款" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <div class="flex-box ">
                <p class="column-text top-box" style=" ">
                  {{
                    currency(scope.row.cnInRmb, {
                      separator: ",",
                      symbol: "¥",
                      precision: 2
                    }).format()
                  }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="right" label="业绩" width="100">
            <template slot="header" slot-scope="scope">
              <div style="margin-right: 5px">
                业绩
              </div>
            </template>
            <template slot-scope="scope">
              <div :class="currency(scope.row.sqdProfitRmbSumVat).divide(scope.row.sqdDnRmbSumVat).value<0?'warning':''"
                   class="flex-box"
                   style="margin-right: 5px"
              >
                <p class="column-text top-box" style="height: 23px ">{{
                    currency(scope.row.profitInRmb, {separator: ",", symbol: "¥", precision: 2}).format()
                  }}</p>
                <p class="column-text bottom-box unHighlight-text" style=" height: 23px">{{
                    currency(scope.row.profitInRmb).divide(scope.row.cnInRmb).multiply(100).value + "%"
                  }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="业务/助理" show-overflow-tooltip width="60">
            <template slot-scope="scope">
              <div class="flex-box" style="width: 55px;overflow: hidden">
                <p class="column-text top-box" style="overflow: hidden">{{
                    (getName(scope.row.salesId) + (scope.row.salesId ? ("/" + getName(scope.row.salesAssistantId)) : getName(scope.row.salesAssistantId))) ? getName(scope.row.salesId) + (scope.row.salesId ? ("/" + getName(scope.row.salesAssistantId)) : getName(scope.row.salesAssistantId)) : null
                  }}</p>
                <p class="column-text bottom-box unHighlight-text" style="height: 23px ">
                  {{ parseTime(scope.row.newBookingTime, "{m}.{d}") }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="商务审核" show-overflow-tooltip width="60">
            <template slot-scope="scope">
              <div class="flex-box" style="width: 55px;overflow: hidden">
                <p class="column-text top-box" style=" width: 55px;overflow: hidden ">{{
                    getName(scope.row.verifyPsaId)
                  }}</p>
                <p class="column-text bottom-box unHighlight-text" style=" ">
                  {{
                    parseTime(scope.row.psaVerifyTime, "{m}.{d}") + " " + processStatus(scope.row.psaVerifyStatusId)
                  }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="left" label="操作员" show-overflow-tooltip width="60">
            <template slot-scope="scope">
              <div class="flex-box">
                <p class="column-text top-box" style=" ">{{ getName(scope.row.opId) }}</p>
                <p class="column-text bottom-box unHighlight-text" style=" ">
                  {{
                    parseTime(scope.row.rctCreateTime, "{m}.{d}") + " " + processStatus(scope.row.processStatusId)
                  }}</p>
              </div>
            </template>
          </el-table-column>

          <el-table-column v-if="type==='booking'" align="left" class-name="small-padding fixed-width" label="操作"
                           width="50"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="danger"
                style="margin-right: -8px"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:booking:remove']"
                v-if="hiddenDelete(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>


        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import booking from "@/views/system/booking/index"
import Treeselect from "@riophae/vue-treeselect"
import store from "@/store"
import {
  delRct,
  listAggregatorBooking,
  listAggregatorRct,
  listRct,
  listVerifyAggregatorList,
  listVerifyList,
  op
} from "@/api/system/rct"
import pinyin from "js-pinyin"
import currency from "currency.js"
import {parseTime} from "../../../utils/rich"
import moment from "moment/moment"
import CompanySelect from "@/components/CompanySelect/index.vue"
import DataAggregator from "@/views/system/DataAggregator/index.vue"
import {rctFieldLabelMap} from "@/config/rctFieldLabelMap"
import DataAggregatorBackGround from "@/views/system/DataAggregatorBackGround/index.vue"

export default {
  name: "bookingList",
  components: {DataAggregatorBackGround, DataAggregator, CompanySelect, Treeselect},
  data() {
    return {
      showLeft: 3,
      showRight: 24,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 操作单列表表格数据
      salesId: null,
      verifyPsaId: null,
      salesAssistantId: null,
      opId: null,
      belongList: [],
      opList: [],
      businessList: [],
      rctList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20
      },
      // 表单参数
      form: {},
      statisticsOp: [],
      // 表单校验
      rules: {},
      openAggregator: false,
      fieldLabelMap: rctFieldLabelMap,
      aggregatorRctList: []
    }
  },
  watch: {
    showSearch(n) {
      if (n === true) {
        this.showRight = 21
        this.showLeft = 3
      } else {
        this.showRight = 24
        this.showLeft = 0
      }
    }
  },
  mounted() {
    let load = false
    if (this.$route.query.no) {
      this.queryParams.newBookingNo = this.$route.query.no
      this.getList().then(() => {
        load = true
      })
    } else {
      this.getList().then(() => {
        load = true
      })
    }
    if (load) {
      this.loadSales()
      this.loadOp()
      this.loadBusinesses()
    }
    this.loadStaffList()
  },
  computed: {
    moment() {
      return moment
    }
  },
  props: ["type"],
  methods: {
    listAggregatorBooking(params) {
      params.config = JSON.stringify(params.config)
      this.queryParams.params = params;
      return listAggregatorBooking(this.queryParams)
    },
    handleOpenAggregator() {
      this.openAggregator = true
    },

    handleStatistics() {
      op().then(response => {
        this.statisticsOp = response.data
      })
    },
    parseTime,
    getBadge(row) {
      if (((this.type === "booking" && row.sqdShippingBookingStatus == "0") || (this.type === "psa" && row.sqdShippingBookingStatus == "1" && row.psaVerify == "0"))) {
        return "new"
      } else {
        return ""
      }
    },
    hiddenDelete(row) {
      if (this.type === "booking" && row.sqdShippingBookingStatus == 0) {
        return true
      }
      if (this.type === "psa") {
        return false
      }
    },
    currency,
    getReleaseType(id) {
      if (id == 1) return "月结"
      if (id == 2) return "押放"
      if (id == 3) return "票结"
      if (id == 4) return "签放"
      if (id == 5) return "订金"
      if (id == 6) return "预付"
      if (id == 7) return "扣货"
      if (id == 9) return "居间"
      return ""
    },
    handleVerify(row) {
      this.$tab.openPage("操作单", "/opprocess/opdetail", {rId: this.ids[0], psaVerify: true})
    },
    getName(id) {
      if (id) {
        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]
        if (staff) {
          return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName
        }
      }
    },
    sqdDocDeliveryWay(type) {
      if (type == 1) return " 境外快递"
      if (type == 2) return " 境内快递"
      if (type == 3) return " 跑腿"
      if (type == 4) return " 业务送达"
      if (type == 5) return " 客户自取"
      if (type == 6) return " QQ"
      if (type == 7) return " 微信"
      if (type == 8) return " 电邮"
      if (type == 9) return " 公众号"
      if (type == 10) return " 承运人系统"
      if (type == 11) return " 订舱口系统"
      if (type == 12) return " 第三方系统"
    },
    logisticsPaymentTerms(v) {
      if (v == 1) return "月结"
      if (v == 2) return "押单"
      if (v == 3) return "此票结清"
      if (v == 4) return "经理签单"
      if (v == 5) return "预收订金"
      if (v == 6) return "全额预付"
      if (v == 7) return "扣货"
      if (v == 8) return "背靠背"
    },
    emergencyLevel(v) {
      if (v == 0) return "预定"
      if (v == 1) return "当天"
      if (v == 2) return "常规"
      if (v == 3) return "紧急"
      if (v == 4) return "立即"
    },
    difficultyLevel(v) {
      if (v == 0) return "简易"
      if (v == 1) return "标准"
      if (v == 2) return "高级"
      if (v == 3) return "特别"
    },
    processStatus(v) {
      if (v == 1) return "等待"
      if (v == 2) return "进行"
      if (v == 3) return "变更"
      if (v == 4) return "异常"
      if (v == 5) return "质押"
      if (v == 6) return "确认"
      if (v == 7) return "完成"
      if (v == 8) return "取消"
      if (v == 9) return "驳回"
      if (v == 10) return "回收"
    },
    loadSales() {
      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {
        store.dispatch("getSalesList").then(() => {
          this.belongList = this.$store.state.data.salesList
        })
      } else {
        this.belongList = this.$store.state.data.salesList
      }
    },
    loadBusinesses() {
      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {
        store.dispatch("getBusinessesList").then(() => {
          this.businessList = this.$store.state.data.businessesList
        })
      } else {
        this.businessList = this.$store.state.data.businessesList
      }
    },
    loadOp() {
      if (this.$store.state.data.opList.length == 0 || this.$store.state.data.redisList.opList) {
        store.dispatch("getOpList").then(() => {
          this.opList = this.$store.state.data.opList
        })
      } else {
        this.opList = this.$store.state.data.opList
      }
    },
    loadStaffList() {
      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {
        store.dispatch("getAllRsStaffList").then(() => {
          this.staffList = this.$store.state.data.allRsStaffList
        })
      } else {
        this.staffList = this.$store.state.data.allRsStaffList
      }
    },
    /** 查询操作单列表列表 */
    async getList() {
      this.loading = true
      if (this.type === "psa") {
        this.queryParams.sqdShippingBookingStatus = 1
      }
      /* if (this.type === 'booking')  {
        this.queryParams.sqdShippingBookingStatus = 0
      } */

      this.queryParams.permissionLevel = this.$store.state.user.permissionLevelList.C
      await listVerifyList(this.queryParams).then(response => {
        this.rctList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.queryParams.searchValue = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.queryParams.searchValue = null
      this.getList()
    },
    // handleStatusChange(row) {
    //   let text = row.status === "0" ? "启用" : "停用";
    //   this.$modal.confirm('确认要"' + text + '吗？').then(function () {
    //     return changeStatus(row.rctId, row.status);
    //   }).then(() => {
    //     this.$modal.msgSuccess(text + "成功");
    //   }).catch(function () {
    //     row.status = row.status === "0" ? "1" : "0";
    //   });
    // },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.rctId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$tab.openPage("操作单", "/opprocess/opdetail", {})
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$tab.openPage("操作单", "/opprocess/opdetail", {rId: row.rctId})
    },
    dbclick(row, column, event) {
      // 已经订舱了的不可以修改
      /* if (this.type === 'booking' && row.sqdShippingBookingStatus == 1) {
        return
      } */
      /* if (this.type === 'psa' && row.psaVerifyStatusId == 1) {
        return
      } */
      if (this.type === "booking") {
        this.$tab.openPage("订舱单明细", "/salesquotation/bookingDetail", {rId: row.rctId, booking: true})
      }
      if (this.type === "psa") {
        this.$tab.openPage("商务审核明细", "/psaVerify/psaDetail", {rId: row.rctId, psaVerify: true})
      }
    },
    tableRowClassName({row, rowIndex}) {
      if (this.type === "booking" && row.sqdShippingBookingStatus == 0) {
        return "unconfirmed"
      }
      if (this.type === "psa" && row.psaVerify != 1) {
        return "unconfirmed"
      }
      return ""
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const rctIds = row.rctId || this.ids
      this.$confirm("是否确认删除操作单列表编号为\"" + rctIds + "\"的数据项？", "提示", {customClass: "modal-confirm"}).then(function () {
        return delRct(rctIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download("system/rct/bookingExport", {
        ...this.queryParams
      }, `rct_${new Date().getTime()}.xlsx`)
    },
    staffNormalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      let l
      if (node.staff) {
        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {
          if (node.role.roleLocalName != null) {
            l = node.role.roleLocalName + "," + pinyin.getFullChars(node.role.roleLocalName)
          } else {
            l = node.dept.deptLocalName + "," + pinyin.getFullChars(node.dept.deptLocalName)
          }
        } else {
          l = node.staff.staffCode + " " + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + " " + node.staff.staffGivingEnName + "," + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)
        }
      }
      if (node.roleId) {
        return {
          id: node.roleId,
          label: l,
          children: node.children,
          isDisabled: node.staffId == null && node.children == undefined
        }
      } else {
        return {
          id: node.deptId,
          label: l,
          children: node.children,
          isDisabled: node.staffId == null && node.children == undefined
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.column-text {
  margin: 0;
  padding: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.highlight-text {
  font-weight: 600;
  font-size: 15px
}

.unHighlight-text {
  color: #b7bbc2;
  margin: 0;
}

::v-deep .el-table .success-row {
  background: #f8f8f9;
}

.red {
  color: rgb(103, 194, 58);
}

.item {
  margin-top: 10px;
  margin-right: 40px;
}

::v-deep .el-badge__content.is-fixed {
  font-size: 12px;
  top: 0px;
  right: 2px;
}
</style>
