<template>
  <el-col :span="21.5" :style="{'display':openChargeList?'':'none'}" style="margin: 0;padding: 0;">
    <div :class="{'inactive':openChargeList==false,'active':openChargeList}">
      <el-table ref="chargeTable" :data="chargeData" :row-class-name="rowIndex" border class="pd0"
                @selection-change="handleSelectionChange"
      >
        <el-table-column v-if="isReceivable" label="应收明细" width="20px">
          <template slot="header" slot-scope="scope">
            <el-row>
              <el-col :span="11">
                <el-row>
                  <el-col :span="4">
                    不含税小计：
                  </el-col>
                  <el-col :span="6"> USD {{
                      currency(rsClientMessageReceivableUSD, {
                        separator: ",",
                        symbol: "$"
                      }).format()
                    }}
                  </el-col>
                  <el-col :offset="2" :span="6"> RMB {{
                      currency(rsClientMessageReceivableRMB, {
                        separator: ",",
                        symbol: "￥"
                      }).format()
                    }}
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="11">
                <el-row class="unHighlight-text">
                  <el-col :span="4">
                    含税小计：
                  </el-col>
                  <el-col :span="6"> USD {{
                      currency(rsClientMessageReceivableTaxUSD, {
                        separator: ",",
                        symbol: "$"
                      }).format()
                    }}
                  </el-col>
                  <el-col :offset="2" :span="6"> RMB {{
                      currency(rsClientMessageReceivableTaxRMB, {
                        separator: ",",
                        symbol: "￥"
                      }).format()
                    }}
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="2">
                <el-button type="primary" @click.native="profitOpen=true">利润</el-button>
                <el-dialog
                  :visible.sync="profitOpen"
                  title="单票利润"
                  width="30%"
                  @open="openProfit"
                >
                  <el-table
                    :data="profitTableData"
                    border
                    style="width: 100%"
                  >
                    <el-table-column
                      label="货币"
                      prop="currencyCode"
                    >
                    </el-table-column>
                    <el-table-column
                      label="应收"
                      prop="receivable"
                    >
                    </el-table-column>
                    <el-table-column
                      label="应付"
                      prop="payable"
                    >
                    </el-table-column>
                    <el-table-column
                      label="不含税利润" prop="profit"
                      style="color: #0d0dfd"
                    >
                    </el-table-column>
                    <el-table-column
                      label="含税应收"
                      prop="receivableTax"
                    >
                    </el-table-column>
                    <el-table-column
                      label="含税应付"
                      prop="payableTax"
                    >
                    </el-table-column>
                    <el-table-column
                      label="含税利润"
                      prop="profitTax"
                    >
                    </el-table-column>
                  </el-table>

                  <el-row>
                    <el-col :span="5">
                      <el-form-item label="折合币种" prop="rctOpDate">
                        <el-select v-model="currencyCode" @change="profitCount(currencyCode)">
                          <el-option label="RMB" value="RMB"/>
                          <el-option label="USD" value="USD"/>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="7">
                      <el-row>
                        <el-col :span="12">
                          <div style="color: #0d0dfd">不含税利润</div>
                        </el-col>
                        <el-col :span="12">
                          <el-input v-model="profit" placeholder="不含税利润"/>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="7">
                      <el-row>
                        <el-col :span="12">
                          <div>含税利润</div>
                        </el-col>
                        <el-col :span="12">
                          <el-input v-model="profitTax" placeholder="含税利润"/>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="5">
                      <el-row>
                        <el-col :span="12">
                          <div>折算汇率</div>
                        </el-col>
                        <el-col :span="12">
                          <el-input v-model="exchangeRate" placeholder="含税利润"/>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-dialog>
              </el-col>
            </el-row>
          </template>
          <el-table-column
            align="center"
            type="selection"
          >
          </el-table-column>
          <el-table-column align="center" label="客户">
            <template slot-scope="scope">
              <div v-if="!scope.row.showClient" style="width: 50px;height: 20px;"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showClient = true"
              >
                {{ scope.row.companyName }}
              </div>
              <tree-select v-if="(companyList&&companyList.length>0)&&scope.row.showClient"
                           :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                           :multiple="false" :pass="scope.row.clearingCompanyId" :placeholder="'客户'"
                           :custom-options="companyList" :flat="false"
                           :type="'clientCustom'" @return="scope.row.clearingCompanyId=$event"
                           @close=" showClientName==scope.row.companyName ? scope.row.showClient = false:null"
                           @returnData="showClientName=(($event.companyShortName&&$event.companyShortName!=='')?$event.companyShortName:$event.companyEnShortName)"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="费用" prop="quotationChargeId" width="80px">
            <template slot-scope="scope">
              <div v-if="!scope.row.showQuotationCharge"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCharge = true"
              >
                {{ scope.row.chargeName }}
              </div>
              <tree-select v-show="scope.row.showQuotationCharge" :dbn="true" :flat="false" :multiple="false"
                           :pass="scope.row.dnChargeNameId" :placeholder="'运费'" :type="'charge'"
                           :disabled="disabled|| scope.row.isAccountConfirmed == '1'"
                           @return="scope.row.dnChargeNameId = $event"
                           @returnData="handleChargeSelect(scope.row,$event)"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="货币" prop="quotationCurrencyId" width="70">
            <template slot-scope="scope">
              <div v-if="!scope.row.showQuotationCurrency" style="width: 69px ;height: 23px"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true"
              >
                {{ scope.row.dnCurrencyCode }}
              </div>
              <tree-select v-show="scope.row.showQuotationCurrency" :pass="scope.row.dnCurrencyCode"
                           :disabled="disabled || scope.row.isAccountConfirmed == '1'" :type="'currency'"
                           @return="changeCurrency(scope.row,$event)"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="单价" prop="quotationRate" width="80px">
            <template slot-scope="scope">
              <div v-if="!scope.row.showUnitRate"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true"
              >
                {{
                  scope.row.dnCurrencyCode === "RMB" ? currency(scope.row.dnUnitRate, {
                    separator: ",",
                    precision: 2,
                    symbol: "¥"
                  }).format() : (scope.row.dnCurrencyCode === "USD" ? currency(scope.row.dnUnitRate, {
                    separator: ",",
                    precision: 2,
                    symbol: "$"
                  }).format() : scope.row.dnUnitRate)
                }}
              </div>
              <el-input-number v-show="scope.row.showUnitRate" v-model="scope.row.dnUnitRate" :controls="false"
                               :min="0.0001"
                               :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                               :precision="4" style="display:flex;width: 100%"
                               @blur="scope.row.showUnitRate=false"
                               @change="countProfit(scope.row,'unitRate')"
                               @input="countProfit(scope.row,'unitRate')"
                               @focusout.native="scope.row.showUnitRate=false"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="单位" prop="quotationUnitId" width="50">
            <template slot-scope="scope">
              <div v-if="!scope.row.showQuotationUnit"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationUnit = true"
              >
                {{ scope.row.dnUnitCode }}
              </div>
              <tree-select v-show="scope.row.showQuotationUnit"
                           :disabled="disabled|| scope.row.isAccountConfirmed == '1'" :pass="scope.row.dnUnitCode"
                           :type="'unit'" @return="changeUnit(scope.row,$event)"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="数量" prop="quotationAmount" width="48px">
            <template slot-scope="scope">
              <div v-if="!scope.row.showAmount"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true"
              >
                {{ scope.row.dnAmount }}
              </div>
              <el-input-number v-if="scope.row.showAmount" v-model="scope.row.dnAmount" :controls="false"
                               :disabled="disabled|| scope.row.isAccountConfirmed == '1'"
                               placeholder="数量" style="display:flex;width: 100%"
                               :min="0.00" @blur="scope.row.showAmount=false"
                               @change="countProfit(scope.row,'amount')"
                               @input="countProfit(scope.row,'amount')"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="汇率" prop="quotationExchangeRate" width="60">
            <template slot-scope="scope" style="display:flex;">
              <div v-if="!scope.row.showCurrencyRate"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true"
              >
                {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}
              </div>
              <el-input-number v-show="scope.row.showCurrencyRate" v-model="scope.row.basicCurrencyRate"
                               :controls="false"
                               :disabled="disabled|| scope.row.isAccountConfirmed == '1'"
                               :precision="4" :step="0.0001" style="width: 100%"
                               :min="0.0001" @blur="scope.row.showCurrencyRate=false"
                               @change="countProfit(scope.row,'currencyRate')"
                               @input="countProfit(scope.row,'currencyRate')"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="税率" prop="quotationTaxRate" width="50px">
            <template slot-scope="scope">
              <div style="display: flex;justify-content: center">
                <div v-if="!scope.row.showDutyRate"
                     @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true"
                >
                  {{ scope.row.dutyRate }}
                </div>
                <el-input-number v-if="scope.row.showDutyRate" v-model="scope.row.dutyRate" :controls="false"
                                 :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                                 :min="0" style="width: 75%"
                                 @blur="scope.row.showDutyRate=false"
                                 @change="countProfit(scope.row,'dutyRate')"
                                 @input="countProfit(scope.row,'dutyRate')"
                />
                <div>%</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="小计" prop="subtotal" width="75">
            <template slot-scope="scope">
              <div>
                {{
                  currency(scope.row.subtotal, {
                    separator: ",",
                    precision: 2,
                    symbol: (scope.row.dnCurrencyCode === "RMB" ? "¥" : "$")
                  }).format()
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="费用备注">
            <template slot-scope="scope">
              <input v-model="scope.row.chargeRemark"
                     :disabled="disabled|| scope.row.isAccountConfirmed == '1'"
                     style="border: none;width: 100%;height: 100%;"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="审核状态">
            <template slot-scope="scope">
              {{ auditStatus(scope.row.isAccountConfirmed) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="已收金额">
            <template slot-scope="scope">
              {{
                scope.row.sqdDnCurrencyPaid
              }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="未收余额">
            <template slot-scope="scope">
              {{ scope.row.sqdDnCurrencyBalance }}
            </template>
          </el-table-column>
          <el-table-column label="所属服务">
            <template slot-scope="scope">
              <div>
                <!--{{ getServiceName(scope.row.sqd_service_type_id) }}-->
                {{ scope.row.serviceName ? scope.row.serviceName : getServiceName(scope.row.sqdServiceTypeId) }}
              </div>
              <!-- <el-select v-else v-model="scope.row.sqdServiceTypeId" filterable placeholder="所属服务">
                 <el-option
                   v-for="item in services"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                 >
                 </el-option>
               </el-select>-->
            </template>
          </el-table-column>
        </el-table-column>

        <!--应付-->
        <el-table-column v-else label="应付明细" width="20px">
          <template slot="header" slot-scope="scope">
            <el-row>
              <el-col :span="4">
                <el-row>
                  <el-col :span="12">{{ currency(payTotalUSD, {separator: ",", symbol: "$"}).format() }}</el-col>
                  <el-col :span="12">{{ currency(payTotalRMB, {separator: ",", symbol: "￥"}).format() }}</el-col>
                </el-row>
              </el-col>
              <el-col :span="10">
                <el-row>
                  <el-col :span="5">
                    不含税小计：
                  </el-col>
                  <el-col :span="6"> USD {{ currency(payDetailUSD, {separator: ",", symbol: "$"}).format() }}</el-col>
                  <el-col :offset="2" :span="6"> RMB {{
                      currency(payDetailRMB, {separator: ",", symbol: "￥"}).format()
                    }}
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="10">
                <el-row class="unHighlight-text">
                  <el-col :span="4">
                    含税小计：
                  </el-col>
                  <el-col :span="6"> USD {{
                      currency(payDetailUSDTax, {separator: ",", symbol: "$"}).format()
                    }}
                  </el-col>
                  <el-col :offset="2" :span="6"> RMB {{
                      currency(payDetailRMBTax, {
                        separator: ",",
                        symbol: "￥"
                      }).format()
                    }}
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </template>
          <el-table-column
            align="center"
            type="selection"
          >
          </el-table-column>
          <el-table-column v-if="!hiddenSupplier" align="center" label="供应商" width="90">
            <template slot-scope="scope">
              <div v-if="!scope.row.showSupplier"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showSupplier = true"
              >
                {{ scope.row.companyName }}
              </div>
              <company-select v-if="(companyList && companyList.length>0)&&scope.row.showSupplier"
                              :class="disabled || scope.row.isAccountConfirmed == '1'?'disable-form':''"
                              :disabled="disabled || scope.row.isAccountConfirmed == '1'" :load-options="companyList"
                              :multiple="false"
                              :no-parent="true"
                              :pass="scope.row.clearingCompanyId"
                              :placeholder="'供应商'"
                              @return="scope.row.clearingCompanyId=$event"
                              @returnData="$event.companyShortName==scope.row.companyName?scope.row.showSupplier = false:null"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="费用" prop="costChargeId" width="80px">
            <template slot-scope="scope">
              <div v-if="!scope.row.showCostCharge"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCostCharge = true"
              >
                {{ scope.row.chargeName }}
              </div>
              <tree-select v-if="scope.row.showCostCharge" :dbn="true" :flat="false" :multiple="false"
                           :pass="scope.row.dnChargeNameId" :placeholder="'运费'" :type="'charge'"
                           :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                           @return="scope.row.dnChargeNameId = $event"
                           @returnData="$event.chargeLocalName == scope.row.chargeName ? scope.row.showCostCharge=false:null"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="货币" prop="costCurrencyId" width="70px">
            <template slot-scope="scope">
              <div v-if="!scope.row.showQuotationCurrency" style="width: 69px ;height: 23px"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true"
              >
                {{ scope.row.dnCurrencyCode }}
              </div>
              <tree-select v-show="scope.row.showQuotationCurrency" :pass="scope.row.dnCurrencyCode"
                           :disabled="disabled || scope.row.isAccountConfirmed == '1'" :type="'currency'"
                           @return="changeCurrency(scope.row,$event)"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="单价" prop="inquiryRate" width="80px">
            <template slot-scope="scope">
              <div v-if="!scope.row.showUnitRate"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true"
              >
                {{
                  scope.row.dnCurrencyCode === "RMB" ? currency(scope.row.dnUnitRate, {
                    separator: ",",
                    precision: 2,
                    symbol: "¥"
                  }).format() : scope.row.dnCurrencyCode === "USD" ? currency(scope.row.dnUnitRate, {
                    separator: ",",
                    precision: 2,
                    symbol: "$"
                  }).format() : scope.row.dnUnitRate
                }}
              </div>
              <el-input-number v-if="scope.row.showUnitRate" v-model="scope.row.dnUnitRate" :controls="false" :min="0"
                               style="display:flex;width: 100%" @blur="scope.row.showUnitRate=false"
                               :precision="4"
                               @change="countProfit(scope.row,'unitRate')"
                               @input="countProfit(scope.row,'unitRate')"
                               :disabled="disabled || scope.row.isAccountConfirmed == '1'"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="单位" prop="costUnitId" width="50px">
            <template slot-scope="scope">
              <div v-if="!scope.row.showCostUnit"
                   @click="(disabled || scope.row.isAccountConfirmed == '1')?null:scope.row.showCostUnit = true"
              >
                {{ scope.row.dnUnitCode }}
              </div>
              <tree-select v-show="scope.row.showCostUnit" :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                           :pass="scope.row.dnUnitCode"
                           :type="'unit'" @return="changeUnitCost(scope.row,$event)"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="数量" prop="costAmount" width="48px">
            <template slot-scope="scope">
              <div v-if="!scope.row.showAmount"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true"
              >
                {{ scope.row.dnAmount }}
              </div>
              <el-input-number v-if="scope.row.showAmount" v-model="scope.row.dnAmount" :controls="false"
                               :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                               placeholder="数量" style="display:flex;width: 100%"
                               :min="0.00" @blur="scope.row.showAmount=false"
                               @change="countProfit(scope.row,'amount')"
                               @input="countProfit(scope.row,'amount')"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="汇率" prop="costExchangeRate" width="60px">
            <template slot-scope="scope" style="display:flex;">
              <div v-if="!scope.row.showCurrencyRate"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true"
              >
                {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}
              </div>
              <el-input-number v-if="scope.row.showCurrencyRate" v-model="scope.row.basicCurrencyRate" :controls="false"
                               :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                               :precision="4" :step="0.0001" style="width: 100%"
                               :min="0.0001" @blur="scope.row.showCurrencyRate=false"
                               @change="countProfit(scope.row,'currencyRate')"
                               @input="countProfit(scope.row,'currencyRate')"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="税率" prop="costTaxRate" width="68px">
            <template slot-scope="scope">
              <div style="display: flex;justify-content: center">
                <div v-if="!scope.row.showDutyRate"
                     @click="(disabled || scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true"
                >
                  {{ scope.row.dutyRate }}
                </div>
                <el-input-number v-if="scope.row.showDutyRate" v-model="scope.row.dutyRate" :controls="false"
                                 :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                                 :min="0" style="width: 75%"
                                 @blur="scope.row.showDutyRate=false"
                                 @change="countProfit(scope.row,'dutyRate')"
                                 @input="countProfit(scope.row,'dutyRate')"
                />
                <div>%</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="小计" prop="costTotal" width="65">
            <template slot-scope="scope">
              <div>
                {{
                  currency(scope.row.subtotal, {
                    separator: ",",
                    precision: 2,
                    symbol: (scope.row.dnCurrencyCode === "RMB" ? "¥" : "$")
                  }).format()
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="费用备注">
            <template slot-scope="scope">
              <input v-model="scope.row.chargeRemark" :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                     style="border: none"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="审核状态">
            <template slot-scope="scope">
              {{ auditStatus(scope.row.isAccountConfirmed) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="已付金额">
            <template slot-scope="scope">
              {{
                scope.row.sqdDnCurrencyPaid
              }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="未付余额">
            <template slot-scope="scope">
              {{ scope.row.sqdDnCurrencyBalance }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="生成应收" prop="costTotal" width="65">
            <template slot="header" slot-scope="scope">
              <el-button
                :disabled="disabled"
                size="mini"
                type="text"
                @click="copyAllFreight()"
              >生成应收
              </el-button>
            </template>
            <template slot-scope="scope">
              <el-button
                :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                size="mini"
                type="text"
                @click="copyFreight(scope.row)"
              >复制到应收
              </el-button>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="50">
          <template slot="header" slot-scope="scope">
            <el-button
              :disabled="disabled || hasConfirmRow"
              size="mini"
              type="text"
              @click="deleteAllItem()"
              style="color: red"
            >全部删除
            </el-button>
          </template>
          <template slot-scope="scope">
            <el-button
              icon="el-icon-delete"
              size="mini"
              type="danger"
              @click="deleteItem(scope.row)"
              :disabled="disabled || scope.row.isAccountConfirmed == '1'"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-button style="padding: 0" type="text"
               @click="addReceivablePayable"
               :disabled="disabled"
    >[＋]
    </el-button>
  </el-col>
</template>

<script>
import currency from "currency.js"
import Treeselect from "@riophae/vue-treeselect"
import pinyin from "js-pinyin"
import CompanySelect from "@/components/CompanySelect/index.vue"
import {parseTime} from "@/utils/rich"

export default {
  name: "charges",
  components: {CompanySelect, Treeselect},
  props: ["chargeData", "companyList", "openChargeList", "isReceivable", "disabled",
    "serviceTypeId", "serviceId", "hiddenSupplier", "rsClientMessageReceivableTaxUSD",
    "rsClientMessageReceivableTaxRMB", "rsClientMessagePayableTaxUSD", "rsClientMessagePayableTaxRMB",
    "rsClientMessageReceivableRMB", "rsClientMessageReceivableUSD", "rsClientMessagePayableRMB",
    "rsClientMessagePayableUSD", "rsClientMessageProfit", "rsClientMessageProfitNoTax", "payDetailRMB",
    "payDetailUSD", "payDetailRMBTax", "payDetailUSDTax", "rsClientMessageProfitUSD", "rsClientMessageProfitRMB",
    "rsClientMessageProfitTaxRMB", "rsClientMessageProfitTaxUSD", "ATD"],
  watch: {
    chargeData: {
      handler: function (newVal, oldVal) {
        if (!oldVal) {
          this.$emit("return", newVal)
          return
        }

        // 遍历费用列表，检查币种变化
        newVal ? newVal.forEach((item, index) => {
          const oldItem = oldVal[index]

          // 检查币种变化并计算小计
          if (item.currency && item.amount) {
            // 如果从 RMB 换成 USD，使用 1/汇率 计算
            if (oldItem && oldItem.currency === "RMB" && item.currency === "USD") {
              if (item.exchangeRate && item.exchangeRate !== 0) {
                try {
                  // 计算 1/汇率，保留4位小数
                  const inverseRate = currency(1, {precision: 4}).divide(item.exchangeRate).value

                  // 计算小计: 单价 * 数量 * 汇率 * (1 + 税率/100)
                  item.subtotal = currency(item.dnUnitRate || 0, {precision: 4})
                    .multiply(item.amount)
                    .multiply(inverseRate)
                    .multiply(currency(1).add(currency(item.dutyRate || 0).divide(100)))
                    .value
                } catch (error) {
                  console.error("计算小计出错:", error)
                  item.subtotal = 0
                }
              }
            }
          }
        }) : null

        this.$emit("return", newVal ? newVal : [])
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => this.$refs.chargeTable.toggleRowSelection(item, true)) : null

    this.profitCount("RMB")
  },
  computed: {

    hasConfirmRow() {
      let result = false;
      (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => {
        if (item.isAccountConfirmed === "1") {
          result = true
        }
      }) : null
      return result
    }
  },
  data() {
    return {
      payTotalRMB: 0,
      payTotalUSD: 0,
      showClientName: null,
      currencyCode: "RMB",
      profit: 0,
      profitTax: 0,
      exchangeRate: 0,
      services: [{
        value: 1,
        label: "海运"
      }, {
        value: 10,
        label: "空运"
      }, {
        value: 20,
        label: "铁路"
      }, {
        value: 40,
        label: "快递"
      }, {
        value: 50,
        label: "拖车"
      }, {
        value: 60,
        label: "报关"
      }, {
        value: 70,
        label: "清关派送"
      }, {
        value: 80,
        label: "码头仓储"
      }, {
        value: 90,
        label: "检验证书"
      }, {
        value: 100,
        label: "保险"
      }, {
        value: 101,
        label: "扩展服务"
      }],
      service: [{
        value: 1,
        label: "基础服务"
      }, {
        value: 4,
        label: "前程运输"
      }, {
        value: 5,
        label: "出口报关"
      }, {
        value: 6,
        label: "进口清关"
      }, {value: 2, label: "海运"}
        , {value: 3, label: "陆运"}
        , {value: 4, label: "铁路"}
        , {value: 5, label: "空运"}
        , {value: 6, label: "快递"}
        , {value: 21, label: "整柜海运"}
        , {value: 22, label: "拼柜海运"}
        , {value: 23, label: "散杂船"}
        , {value: 24, label: "滚装船"}
        , {value: 41, label: "整柜铁路"}
        , {value: 42, label: "拼柜铁路"}
        , {value: 43, label: "铁路车皮"}
        , {value: 51, label: "空运普舱"}
        , {value: 52, label: "空运包板"}
        , {value: 53, label: "空运包机"}
        , {value: 54, label: "空运行李"}
        , {value: 961, label: "前程运输"}
        , {value: 964, label: "进口清关"}
        , {value: 7, label: "出口报关"}
      ],
      chargeRemark: null,
      profitOpen: false,
      profitTableData: []
    }
  },
  methods: {
    openProfit() {
      this.profitTableData = []

      let RMB = {}
      RMB.currencyCode = "RMB"
      RMB.receivable = this.rsClientMessageReceivableRMB
      RMB.payable = this.rsClientMessagePayableRMB
      // 不含税利润
      RMB.profit = this.rsClientMessageProfitRMB
      // 含税应收
      RMB.receivableTax = this.rsClientMessageReceivableTaxRMB
      // 含税应付
      RMB.payableTax = this.rsClientMessagePayableTaxRMB
      // 含税利润
      RMB.profitTax = this.rsClientMessageProfitTaxRMB

      let USD = {}
      USD.currencyCode = "USD"
      USD.receivable = this.rsClientMessageReceivableUSD
      USD.payable = this.rsClientMessagePayableUSD
      USD.profit = this.rsClientMessageProfitUSD
      USD.receivableTax = this.rsClientMessageReceivableTaxUSD
      USD.payableTax = this.rsClientMessagePayableTaxUSD
      USD.profitTax = this.rsClientMessageProfitTaxUSD

      this.profitTableData.push(RMB)
      this.profitTableData.push(USD)

      this.profitCount("RMB")
    },
    profitCount(type) {
      let exchangeRate
      for (const a of this.$store.state.data.exchangeRateList) {
        if (this.ATD) {
          if (a.localCurrency === "RMB"
            && "USD" == a.overseaCurrency
            && parseTime(a.validFrom) <= parseTime(this.ATD)
            && parseTime(this.ATD) <= parseTime(a.validTo)
          ) {
            exchangeRate = currency(a.settleRate).divide(a.base).value
          }
        }
        if (!exchangeRate) {
          if (a.localCurrency === "RMB"
            && "USD" == a.overseaCurrency
            && parseTime(a.validFrom) <= parseTime(new Date())
            && parseTime(new Date()) <= parseTime(a.validTo)) {
            exchangeRate = currency(a.settleRate).divide(a.base).value
          }
        }
      }
      this.exchangeRate = exchangeRate

      if (type === "RMB") {
        // 都折算成人民币
        this.profit = currency(this.rsClientMessageProfitUSD).multiply(exchangeRate).add(this.rsClientMessageProfitRMB).value
        this.profitTax = currency(this.rsClientMessageProfitTaxUSD).multiply(exchangeRate).add(this.rsClientMessageProfitTaxRMB).value
      } else {
        this.profit = currency(this.rsClientMessageProfitRMB).divide(exchangeRate).add(this.rsClientMessageProfitUSD).value
        this.profitTax = currency(this.rsClientMessageProfitTaxRMB).divide(exchangeRate).add(this.rsClientMessageProfitTaxUSD).value
      }
    },
    auditStatus(status) {
      return status == 1 ? "已审核" : "未审核"
    },
    selectCharge(target, row) {
      row.dnChargeNameId = target.chargeId
      row.chargeName = target.chargeLocalName
    },
    handleSelectionChange(val) {
      this.$emit("selectRow", val)

      this.payTotalUSD = 0
      this.payTotalRMB = 0
      val ? val.map(item => {
        if (item.isRecievingOrPaying == 1) {
          if (item.dnCurrencyCode === "USD") {
            this.payTotalUSD = currency(this.payTotalUSD).add(item.subtotal)
          } else {
            this.payTotalRMB = currency(this.payTotalRMB).add(item.subtotal)
          }

        }
      }) : null

    },
    currency,
    getServiceName(id) {
      let serviceName = ""
      this.services.map(obj => {
        obj.value === id ? serviceName = obj.label : null
      })
      return serviceName
    },
    copyFreight(row) {
      if (this.companyList.length > 0) {
        row.payClearingCompanyId = this.companyList[0].companyId
        row.payCompanyName = this.companyList[0].companyShortName
      }
      row.isAccountConfirmed = 0
      // 报价列表跳转订舱时没有公司列表,复制到应收没有客户信息
      let data = this._.cloneDeep(row)

      this.$emit("copyFreight", {...data, chargeId: null})
    },
    copyAllFreight() {
      if (!this.companyList.length > 0) {
        this.$modal.alertWarning("请先选择委托单位或关联单位")
        return
      }

      this.chargeData.map(charge => {
        charge.payClearingCompanyId = this.companyList[0].companyId
        charge.payCompanyName = this.companyList[0].companyShortName
        charge.isRecievingOrPaying = 0
        charge.isAccountConfirmed = 0
        charge.chargeId = null
        this.$emit("copyFreight", this._.cloneDeep(charge))
      })
    },
    changeUnitCost(row, unit) {
      row.dnUnitCode = unit
      this.$nextTick(() => {
        row.showCostUnit = false
      })
    },
    changeUnit(row, unit) {
      row.dnUnitCode = unit
      this.$nextTick(() => {
        row.showQuotationUnit = false
      })
    },
    handleChargeSelect(row, data) {
      if (row.chargeLocalName === data.chargeName) {
        row.chargeName = data.chargeLocalName
        row.showQuotationCharge = false
      }
      if (row.currencyCode == null && data.currencyCode) {
        row.dnCurrencyCode = data.currencyCode
      }
    },
    changeCurrency(row, currencyCode) {
      row.dnCurrencyCode = currencyCode
      /* let exchangeRate
      if (currencyCode === "USD") {
        for (const a of this.$store.state.data.exchangeRateList) {
          if (a.localCurrency === "RMB"
            && row.dnCurrencyCode == a.overseaCurrency
          ) {
            exchangeRate = currency(a.settleRate).divide(a.base).value
          }
        }
      } */

      this.$nextTick(() => {
        // row.basicCurrencyRate = exchangeRate ? exchangeRate : 1
        row.showQuotationCurrency = false
      })
    },
    /** 序号 */
    rowIndex({ row, rowIndex }) {
      row.id = rowIndex + 1
    },
    addReceivablePayable() {
      let obj = {
        showClient: true,
        showSupplier: true,
        showQuotationCharge: true,
        showCostCharge: true,
        showQuotationCurrency: true,
        showCostCurrency: true,
        showQuotationUnit: true,
        showCostUnit: true,
        showStrategy: true,
        showUnitRate: true,
        showAmount: true,
        showCurrencyRate: true,
        showDutyRate: true,
        basicCurrencyRate: 1,
        dutyRate: 0,
        dnAmount: 1,
        // 应收还是应付
        isRecievingOrPaying: this.isReceivable ? 0 : 1,
        clearingCompanyId: this.chargeData.length > 0 ? this.chargeData[this.chargeData.length - 1].clearingCompanyId : null
      }
      if (this.serviceTypeId === 1) obj.sqdServiceTypeId = 1
      if (this.serviceTypeId === 10) obj.sqdServiceTypeId = 10
      if (this.serviceTypeId === 20) obj.sqdServiceTypeId = 20
      if (this.serviceTypeId === 40) obj.sqdServiceTypeId = 40
      if (this.serviceTypeId === 50) obj.sqdServiceTypeId = 50
      if (this.serviceTypeId === 60) obj.sqdServiceTypeId = 60
      if (this.serviceTypeId === 70) obj.sqdServiceTypeId = 70
      if (this.serviceTypeId === 80) obj.sqdServiceTypeId = 80
      if (this.serviceTypeId === 90) obj.sqdServiceTypeId = 90
      if (this.serviceTypeId === 100) obj.sqdServiceTypeId = 100
      if (this.serviceTypeId === 101) obj.sqdServiceTypeId = 101
      this.chargeData.push(obj)
    },
    countProfit(row, category) {
      // 确保所有必要的值都存在且有效
      if (!row) return

      // 使用currency.js来处理数值,避免精度损失
      const unitRate = row.dnUnitRate || 0
      const amount = row.dnAmount || 0
      const currencyRate = currency(row.basicCurrencyRate || 1, {precision: 4}).value
      const dutyRate = currency(row.dutyRate || 0).value

      try {
        // 计算小计
        const subtotal = currency(unitRate, {precision: 4})
          .multiply(amount)
          .multiply(currencyRate)
          .multiply(currency(1).add(currency(dutyRate).divide(100)))
          .value

        // 更新行数据
        row.subtotal = currency(subtotal, {precision: 2}).value
        row.sqdDnCurrencyBalance = row.isAccountConfirmed === "0" ? currency(subtotal, {precision: 2}).value : row.sqdDnCurrencyBalance

        // 根据不同的输入类型关闭对应的编辑状态
        switch (category) {
          case "strategy":
            row.showStrategy = false
            break
          case "unitRate":
            // 不在这里关闭编辑状态,改用@blur事件
            break
          case "amount":
            // 不在这里关闭编辑状态,改用@blur事件
            break
          case "currencyRate":
            // 不在这里关闭编辑状态,改用@blur事件
            break
          case "dutyRate":
            // 不在这里关闭编辑状态,改用@blur事件
            break
        }

        // 触发数据更新
        this.$emit("return", this.chargeData)

      } catch (error) {
        console.error("计算小计时出错:", error)
        this.$message.error("计算小计时出错,请检查输入值是否正确")
      }
    },
    deleteItem(row) {
      this.$emit("deleteItem", row)
    },
    deleteAllItem(row) {
      this.$emit("deleteAll")
    },
    companyNormalizer(node) {
      return {
        id: node.companyId,
        label: (node.companyShortName != null ? node.companyShortName : "") + " " + (node.companyLocalName != null ? node.companyLocalName : "") + "," + pinyin.getFullChars((node.companyShortName != null ? node.companyShortName : "") + " " + (node.companyLocalName != null ? node.companyLocalName : ""))
      }
    }
  }
}
</script>
<style lang="scss" scoped>
input:focus {
  outline: none;
}

.unHighlight-text {
  color: #b7bbc2;
  margin: 0;
}
</style>
