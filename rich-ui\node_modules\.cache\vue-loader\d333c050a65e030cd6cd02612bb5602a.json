{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\opBill.vue?vue&type=style&index=0&id=51086db6&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\opBill.vue", "mtime": 1754646305894}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICdAL2Fzc2V0cy9zdHlsZXMvb3AtZG9jdW1lbnQuc2Nzcyc7DQo="}, {"version": 3, "sources": ["opBill.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0jPA", "file": "opBill.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <div class=\"container\" style=\"margin: 15px;width: auto\">\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"63px\">\r\n      <el-row>\r\n        <el-col>\r\n          <!--主表信息-->\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"3\">\r\n              <div class=\"rs-application-title\">\r\n                {{ op ? \"操作单\" : \"订舱单\" }}\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"3\">\r\n              <input v-model=\"form.rctNo\" class=\"rct-no\" disabled\r\n                     placeholder=\"操作单号(RCT)\" size=\"large\" @focus=\"generateRct(false)\"\r\n              />\r\n              <el-form-item label=\"操作日期\" prop=\"rctOpDate\">\r\n                <el-date-picker v-model=\"form.rctCreateTime\" clearable\r\n                                placeholder=\"操作日期\"\r\n                                style=\"width:100%\"\r\n                                type=\"datetime\"\r\n                                :class=\"'disable-form'\" disabled\r\n                                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"3\">\r\n              <el-form-item label=\"紧急程度\" prop=\"urgencyDegree\">\r\n                <tree-select :class=\"psaVerify|| disabled?'disable-form':''\" :flat=\"false\" :multiple=\"false\"\r\n                             :disabled=\"psaVerify || disabled\" :pass=\"form.emergencyLevel\" :placeholder=\"'紧急程度'\"\r\n                             :type=\"'emergencyLevel'\" @return=\"form.emergencyLevel=$event\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"简易程度\" prop=\"orderDifficulty\">\r\n                <tree-select :class=\"psaVerify|| disabled?'disable-form':''\" :flat=\"false\" :multiple=\"false\"\r\n                             :disabled=\"psaVerify || disabled\" :pass=\"form.difficultyLevel\" :placeholder=\"'简易程度'\"\r\n                             :type=\"'difficultyLevel'\" @return=\"form.difficultyLevel=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"3\">\r\n              <el-form-item label=\"订单所属\" prop=\"orderBelongsTo\">\r\n                <tree-select :class=\"psaVerify|| disabled?'disable-form':''\" :disabled=\"psaVerify || disabled\"\r\n                             :flat=\"false\" :multiple=\"false\" :pass=\"form.orderBelongsTo\" :placeholder=\"'收付路径'\"\r\n                             :type=\"'rsPaymentTitle'\" @return=\"form.orderBelongsTo=$event\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"放货方式\">\r\n                <tree-select :d-load=\"false\" :disabled=\"psaVerify || disabled\" :flat=\"false\"\r\n                             :multiple=\"false\" :pass=\"form.releaseType\"\r\n                             :placeholder=\"'放货方式'\" :type=\"'releaseType'\"\r\n                             :class=\"psaVerify || disabled?'disable-form':''\" @return=\"form.releaseType=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"3\">\r\n              <div class=\"form-box\">\r\n                <div class=\"form-item\">\r\n                  物流文件:\r\n                </div>\r\n                <div class=\"form-content\">\r\n                  {{ form.transportStatusA }}\r\n                </div>\r\n              </div>\r\n              <div class=\"form-box\">\r\n                <div class=\"form-item\">\r\n                  付款节点:\r\n                </div>\r\n                <div class=\"form-content\">\r\n                  {{ form.paymentNode }}\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"3\">\r\n              <div class=\"form-box\">\r\n                <div class=\"form-item\">\r\n                  付款进度:\r\n                </div>\r\n                <div class=\"form-content\">\r\n                  {{ }}\r\n                </div>\r\n              </div>\r\n              <!--<div class=\"form-box\">\r\n                <div class=\"form-item\">\r\n                  状态日期:\r\n                </div>\r\n                <div class=\"form-content\">\r\n                  {{ form.processStatusTime + \" \" + (form.processStatusId ? getProcess(form.processStatusId) : \"\") }}\r\n                </div>\r\n              </div>-->\r\n              <div class=\"form-box\">\r\n                <div class=\"form-item\">\r\n                  收款进度:\r\n                </div>\r\n                <div class=\"form-content\">\r\n                  {{ }}\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <div class=\"parting-bar\"/>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"委托单位\" prop=\"clientId\">\r\n                <el-input v-model=\"form.clientName\" :disabled=\"psaVerify || disabled\" placeholder=\"委托单位\"\r\n                          :class=\"psaVerify || disabled?'disable-form':''\" @focus=\"selectCompany\"\r\n                />\r\n              </el-form-item>\r\n              <el-col :span=\"12\" style=\"padding-left: 0;\">\r\n                <el-form-item label=\"客户单号\" prop=\"clientJobNo\">\r\n                  <el-input v-model=\"form.clientJobNo\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                            :disabled=\"psaVerify || disabled\"\r\n                            placeholder=\"客户单号\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\" style=\"padding-right: 0;\">\r\n                <el-form-item label=\"合同号\" prop=\"clientInvoiceNo\">\r\n                  <el-input v-model=\"form.clientContractNo\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                            :disabled=\"psaVerify || disabled\"\r\n                            placeholder=\"合同号\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-col :span=\"12\" style=\"padding: 0;\">\r\n                <el-form-item label=\"联系人\" prop=\"clientContact\">\r\n                  <el-input v-model=\"form.clientContact\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                            :disabled=\"psaVerify || disabled\"\r\n                            placeholder=\"联系人\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"发票号\" prop=\"clientInvoiceNo\">\r\n                  <el-input v-model=\"form.clientInvoiceNo\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                            :disabled=\"psaVerify || disabled\"\r\n                            placeholder=\"(委托单位)发票号\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"电话\" prop=\"clientContactTel\">\r\n                  <el-input v-model=\"form.clientContactTel\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                            :disabled=\"psaVerify || disabled\"\r\n                            placeholder=\"电话\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"客户角色\" prop=\"clientContactTel\">\r\n                  <!--<el-input v-model=\"form.clientRole\" :disabled=\"psaVerify || disabled\" placeholder=\"电话\"/>-->\r\n                  <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.roleIds\" :type=\"'companyRole'\"\r\n                               :disabled=\"psaVerify || disabled\"\r\n                               :class=\"psaVerify || disabled?'disable-form':''\" class=\"sss\" @return=\"getCompanyRoleIds\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"邮箱\" prop=\"clientContactEmail\">\r\n                <el-input v-model=\"form.clientContactEmail\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                          :disabled=\"psaVerify || disabled\"\r\n                          placeholder=\"邮箱\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"关联工厂\" prop=\"relationClientIdList\">\r\n                <company-select :class=\"psaVerify || disabled?'disable-form':''\" :load-options=\"companyList\"\r\n                                :disabled=\"psaVerify || disabled\" :multiple=\"true\" :no-parent=\"true\"\r\n                                :pass=\"RelationClientIdList\"\r\n                                :role-control=\"!checkRole(['Operator'])\"\r\n                                @deselect=\"handleDeselectCompanyIds\"\r\n                                @return=\"selectRelationClient($event)\"\r\n                                @returnData=\"handleSelectCompanyIds($event)\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <div class=\"parting-bar\"/>\r\n          <el-row>\r\n            <el-row :gutter=\"10\">\r\n              <el-col :span=\"18\">\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"货名概要\" prop=\"goodsNameSummary\">\r\n                    <el-input v-model=\"form.goodsNameSummary\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                              :disabled=\"psaVerify || disabled\"\r\n                              placeholder=\"货名概要\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"货物特征\" prop=\"cargoTypeIds\">\r\n                    <tree-select :class=\"psaVerify || disabled?'disable-form':''\" :disabled=\"psaVerify || disabled\"\r\n                                 :flat=\"false\" :multiple=\"true\" :pass=\"cargoTypeCodes\"\r\n                                 :placeholder=\"'货物特征'\" :type=\"'cargoTypeCode'\"\r\n                                 @return=\"cargoTypeCodes=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"件数\" prop=\"packageQuantity\">\r\n                    <div style=\"display: flex\">\r\n                      <el-input-number v-model=\"form.packageQuantity\" :controls=\"false\"\r\n                                       :disabled=\"psaVerify || disabled\"\r\n                                       placeholder=\"总件数\" style=\"width: 100%;flex: 2\"\r\n                                       :class=\"psaVerify || disabled?'disable-form':''\"\r\n                      />\r\n                      <el-input class=\"disable-form\" disabled style=\"flex: 1\" value=\"PKG\"/>\r\n                    </div>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"毛重\" prop=\"grossWeight\">\r\n                    <div style=\"display: flex\">\r\n                      <el-input v-model=\"grossWeight\" :disabled=\"psaVerify || disabled\" placeholder=\"总毛重\"\r\n                                style=\"width: 64%;flex: 2\" @change.native=\"autoCompletion('grossWeight')\"\r\n                                :class=\"psaVerify || disabled?'disable-form':''\"\r\n                      />\r\n                      <el-input class=\"disable-form\" disabled style=\"flex: 1\" value=\"KGS\"/>\r\n                    </div>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"体积\" prop=\"volume\">\r\n                    <div style=\"display: flex\">\r\n                      <el-input-number v-model=\"form.goodsVolume\" :controls=\"false\" :disabled=\"psaVerify || disabled\"\r\n                                       :precision=\"2\"\r\n                                       :step=\"0.01\" placeholder=\"体积\" style=\"width: 64%;flex: 2\"\r\n                                       :class=\"psaVerify || disabled?'disable-form':''\"\r\n                      />\r\n                      <el-input class=\"disable-form\" disabled style=\"flex: 1\" value=\"CBM\"/>\r\n                    </div>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"货值\" prop=\"goodsValue\">\r\n                    <div style=\"display: flex\">\r\n                      <el-input v-model=\"goodsValue\" :disabled=\"psaVerify || disabled\" placeholder=\"总货值\"\r\n                                style=\"width: 64%\" @change.native=\"autoCompletion('goodsValue')\"\r\n                                :class=\"psaVerify || disabled?'disable-form':''\"\r\n                      />\r\n                      <tree-select :disabled=\"psaVerify || disabled\" :pass=\"form.goodsCurrencyCode\"\r\n                                   :placeholder=\"'货值币种'\" :type=\"'currency'\" style=\"width: 36%\"\r\n                                   :class=\"psaVerify || disabled?'disable-form':''\"\r\n                                   @return=\"form.goodsCurrencyCode=$event\"\r\n                      />\r\n                    </div>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row :gutter=\"10\">\r\n              <el-col :span=\"18\">\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"物流类型\" prop=\"logisticsTypeId\">\r\n                    <tree-select :class=\"psaVerify || disabled?'disable-form':''\" :dbn=\"true\"\r\n                                 :disabled=\"psaVerify || disabled\" :flat=\"false\"\r\n                                 :main=\"true\" :multiple=\"false\" :pass=\"form.logisticsTypeId\"\r\n                                 :placeholder=\"'物流类型'\" :type=\"'mainServiceType'\"\r\n                                 @return=\"form.logisticsTypeId=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"进出口\" prop=\"impExpType\">\r\n                    <el-select v-model=\"form.impExpType\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                               :disabled=\"psaVerify || disabled\" clearable\r\n                               filterable\r\n                               placeholder=\"进出口\" style=\"width: 100%\"\r\n                    >\r\n                      <el-option label=\"出口\" value=\"1\">出口</el-option>\r\n                      <el-option label=\"进口\" value=\"2\">进口</el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"运输条款\" prop=\"logisticsTermsId\">\r\n                    <tree-select :class=\"psaVerify || disabled?'disable-form':''\" :disabled=\"psaVerify || disabled\"\r\n                                 :flat=\"false\" :multiple=\"false\"\r\n                                 :pass=\"form.logisticsTerms\" :placeholder=\"'运输条款'\"\r\n                                 :type=\"'transportationTerms'\" @return=\"form.logisticsTerms=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"贸易条款\" prop=\"tradingTermsId\">\r\n                    <tree-select :class=\"psaVerify || disabled?'disable-form':''\" :disabled=\"psaVerify || disabled\"\r\n                                 :flat=\"false\" :multiple=\"false\"\r\n                                 :pass=\"form.tradingTerms\" :placeholder=\"'贸易条款'\"\r\n                                 :type=\"'tradingTerms'\" @return=\"form.tradingTerms=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"收汇方式\" prop=\"tradingPaymentChannelId\">\r\n                    <tree-select :class=\"psaVerify || disabled?'disable-form':''\" :disabled=\"psaVerify || disabled\"\r\n                                 :flat=\"false\" :multiple=\"false\"\r\n                                 :pass=\"form.tradingPaymentChannel\" :placeholder=\"'贸易付款方式'\"\r\n                                 :type=\"'paymentChannels'\" @return=\"form.tradingPaymentChannel=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"运费付于\" prop=\"tradingPaymentChannelId\">\r\n                    <tree-select :disabled=\"psaVerify || disabled\" :flat=\"false\"\r\n                                 :multiple=\"false\" :pass=\"form.freightPaidWayCode\"\r\n                                 :class=\"psaVerify || disabled?'disable-form':''\" :placeholder=\"'运费付于'\"\r\n                                 :type=\"'freightPaidWay'\" @return=\"form.freightPaidWayCode=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row :gutter=\"10\">\r\n              <el-col :span=\"18\">\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"启运港\" prop=\"polId\">\r\n                    <location-select :check-port=\"logisticsType\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                                     :disabled=\"psaVerify || disabled\" :en=\"true\"\r\n                                     :load-options=\"locationOptions\" :multiple=\"false\" :pass=\"form.polId\"\r\n                                     :placeholder=\"'启运港'\" @return=\"form.polId=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"中转港\" prop=\"transitPortId\">\r\n                    <location-select :check-port=\"logisticsType\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                                     :disabled=\"psaVerify || disabled\" :en=\"true\" :load-options=\"locationOptions\"\r\n                                     :multiple=\"false\" :pass=\"form.transitPortId\"\r\n                                     :placeholder=\"'中转港'\"\r\n                                     @return=\"form.transitPortId=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"卸货港\" prop=\"podId\" style=\"margin: 0;padding: 0;\">\r\n                    <location-select :check-port=\"logisticsType\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                                     :disabled=\"psaVerify || disabled\" :en=\"true\"\r\n                                     :load-options=\"locationOptions\" :multiple=\"false\" :pass=\"form.podId\"\r\n                                     :placeholder=\"'卸货港'\" @return=\"form.podId=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"目的港\" prop=\"destinationPortId\">\r\n                    <location-select :check-port=\"logisticsType\" :disabled=\"psaVerify || disabled\" :en=\"true\"\r\n                                     :load-options=\"locationOptions\" :multiple=\"false\"\r\n                                     :pass=\"form.destinationPortId\" :placeholder=\"'目的港'\"\r\n                                     :class=\"psaVerify || disabled?'disable-form':''\"\r\n                                     @return=\"form.destinationPortId=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row :gutter=\"10\">\r\n              <el-col :span=\"18\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label=\"计费货量\" prop=\"revenueTon\">\r\n                    <el-input v-model=\"form.revenueTon\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                              :disabled=\"psaVerify || disabled\"\r\n                              placeholder=\"计费货量\" style=\"width: 100%\"\r\n                              @focus=\"editRevenueTon\"\r\n                    />\r\n                    <el-dialog\r\n                      v-dialogDrag\r\n                      v-dialogDragWidth\r\n                      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n                      :visible.sync=\"openGenerateRevenueTons\" append-to-body\r\n                      title=\"修改计费货量\" width=\"350px\"\r\n                    >\r\n                      <el-row>\r\n                        <el-col :span=\"23\">\r\n                          <el-col :span=\"12\">\r\n                            <el-input-number v-model=\"form.countA\" :controls=\"false\" :min=\"1\"\r\n                                             placeholder=\"数量\" style=\"width: 100%;\"\r\n                            />\r\n                            <el-input-number v-model=\"form.countB\" :controls=\"false\" :min=\"1\"\r\n                                             placeholder=\"数量\" style=\"width: 100%;\"\r\n                            />\r\n                            <el-input-number v-model=\"form.countC\" :controls=\"false\" :min=\"1\"\r\n                                             placeholder=\"数量\" style=\"width: 100%;\"\r\n                            />\r\n                          </el-col>\r\n                          <el-col :span=\"12\">\r\n                            <tree-select :pass=\"form.unitCodeA\" :type=\"'unit'\" placeholder=\"选择柜型\"\r\n                                         @returnData=\"form.unitCodeA = $event.unitCode\"\r\n                            />\r\n                            <tree-select :pass=\"form.unitCodeB\" :type=\"'unit'\" placeholder=\"选择柜型\"\r\n                                         @returnData=\"form.unitCodeB = $event.unitCode\"\r\n                            />\r\n                            <tree-select :pass=\"form.unitCodeC\" :type=\"'unit'\" placeholder=\"选择柜型\"\r\n                                         @returnData=\"form.unitCodeC = $event.unitCode\"\r\n                            />\r\n                          </el-col>\r\n                        </el-col>\r\n                      </el-row>\r\n                      <span slot=\"footer\" class=\"dialog-footer\">\r\n                        <el-button type=\"primary\" @click=\"revenueTonConfirm\">确 定</el-button>\r\n                      </span>\r\n                    </el-dialog>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label=\"箱型特征\">\r\n                    <tree-select :class=\"psaVerify || disabled?'disable-form':''\" :dbn=\"true\"\r\n                                 :disabled=\"psaVerify || disabled\"\r\n                                 :disable-branch-nodes=\"true\" :flat=\"false\" :multiple=\"true\"\r\n                                 :pass=\"ctnrTypeCodeIds\" :placeholder=\"'服务类型'\" :type=\"'ctnrType'\"\r\n                                 @return=\"ctnrTypeCodeIds=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"服务列表\">\r\n                    <tree-select :class=\"psaVerify || disabled?'disable-form':''\" :dbn=\"true\"\r\n                                 :disabled=\"psaVerify || disabled\"\r\n                                 :flat=\"false\" :multiple=\"true\"\r\n                                 :pass=\"form.serviceTypeIds\" :placeholder=\"'服务类型'\" :type=\"'serviceType'\"\r\n                                 @return=\"getServiceTypeList\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-col>\r\n            </el-row>\r\n          </el-row>\r\n          <div class=\"parting-bar\"/>\r\n          <el-row :gutter=\"10\">\r\n            <el-row :gutter=\"10\">\r\n              <el-col :span=\"3\">\r\n                <el-form-item label=\"提单类型\">\r\n                  <tree-select :class=\"psaVerify || disabled?'disable-form':''\" :disabled=\"psaVerify || disabled\"\r\n                               :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"form.blTypeCode\"\r\n                               :placeholder=\"'提单类型'\" :type=\"'blType'\" @return=\"form.blTypeCode=$event\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"3\">\r\n                <el-form-item label=\"出单方式\" prop=\"blFormCode\">\r\n                  <tree-select :class=\"psaVerify || disabled?'disable-form':''\" :disabled=\"psaVerify || disabled\"\r\n                               :flat=\"false\" :multiple=\"false\" :pass=\"form.blFormCode\"\r\n                               :placeholder=\"'出单方式'\" :type=\"'blForm'\" @return=\"form.blFormCode=$event\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"3\">\r\n                <el-form-item label=\"交单方式\" prop=\"goodsNameSummary\">\r\n                  <tree-select :class=\"psaVerify || disabled?'disable-form':''\" :disabled=\"psaVerify || disabled\"\r\n                               :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"form.sqdDocDeliveryWay\"\r\n                               :placeholder=\"'货代单交单方式'\"\r\n                               :type=\"'docReleaseWay'\" @return=\"form.sqdDocDeliveryWay=$event\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"4\">\r\n                <div style=\"height: 32px;width: 50%; padding: 0 0 4px;display: flex\">\r\n                  <el-checkbox-button v-model=\"form.noTransferAllowed\" :disabled=\"psaVerify || disabled\"\r\n                                      label=\"基础信息\"\r\n                                      style=\"width: 100px\"\r\n                  >\r\n                    <i\r\n                      :class=\"form.noTransferAllowed?'el-icon-check':'el-icon-minus'\"\r\n                    /> {{ form.noTransferAllowed ? \"不可中转\" : \"接受中转\" }}\r\n                  </el-checkbox-button>\r\n                  <el-checkbox-button v-model=\"form.noDividedAllowed\" :disabled=\"psaVerify || disabled\"\r\n                                      label=\"订单信息\"\r\n                                      style=\"width: 100px\"\r\n                  >\r\n                    <i\r\n                      :class=\"form.noDividedAllowed?'el-icon-check':'el-icon-minus'\"\r\n                    /> {{ form.noDividedAllowed ? \"不可分批\" : \"接受分批\" }}\r\n                  </el-checkbox-button>\r\n                  <el-checkbox-button v-model=\"form.noAgreementShowed\" :disabled=\"psaVerify || disabled\"\r\n                                      label=\"分支信息\"\r\n                                      style=\"width: 100px\"\r\n                  ><i\r\n                    :class=\"form.noAgreementShowed?'el-icon-check':'el-icon-minus'\"\r\n                  /> {{ form.noAgreementShowed ? \"不可套约\" : \"接受套约\" }}\r\n                  </el-checkbox-button>\r\n                  <el-checkbox-button v-model=\"form.isCustomsIntransitShowed\" :disabled=\"psaVerify || disabled\"\r\n                                      label=\"分支信息\"\r\n                                      style=\"width: 100px\"\r\n                  ><i\r\n                    :class=\"form.isCustomsIntransitShowed?'el-icon-check':'el-icon-minus'\"\r\n                  /> 属地清关\r\n                  </el-checkbox-button>\r\n                </div>\r\n              </el-col>\r\n              <el-col :span=\"5\">\r\n                <el-form-item label=\"唛头\" prop=\"goodsNameSummary\">\r\n                  <el-input v-model=\"form.shippingMark\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                            :disabled=\"psaVerify || disabled\"\r\n                            show-word-limit style=\"padding: 0;margin: 0;\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row style=\"margin-bottom: 3px\">\r\n              <el-col :span=\"4\">\r\n                <div class=\"select-label\">发货人\r\n                  <div class=\"select-btn-group\">\r\n                    <el-button style=\"color: blue\" type=\"text\" @click=\"handleAddCommon('common')\">[↗]</el-button>\r\n                    <el-button style=\"color: blue\" type=\"text\" @click=\"openCommonUsed\">[...]</el-button>\r\n                  </div>\r\n                </div>\r\n                <el-input v-model=\"form.bookingShipper\" :autosize=\"{ minRows: 5, maxRows: 8}\"\r\n                          :class=\"psaVerify || disabled?'disable-form':''\"\r\n                          :disabled=\"psaVerify || disabled\" maxlength=\"500\" show-word-limit\r\n                          style=\"padding: 0;margin: 0;\"\r\n                          type=\"textarea\"\r\n                />\r\n              </el-col>\r\n              <el-col :span=\"4\">\r\n                <div class=\"custom-form-label\">收货人</div>\r\n                <el-input v-model=\"form.bookingConsignee\" :autosize=\"{ minRows: 5, maxRows: 8}\"\r\n                          :class=\"psaVerify || disabled?'disable-form':''\"\r\n                          :disabled=\"psaVerify || disabled\" maxlength=\"500\" show-word-limit\r\n                          style=\"padding: 0;margin: 0;\"\r\n                          type=\"textarea\"\r\n                />\r\n              </el-col>\r\n              <el-col :span=\"4\">\r\n                <div class=\"custom-form-label\">通知人</div>\r\n                <el-input v-model=\"form.bookingNotifyParty\" :autosize=\"{ minRows: 5, maxRows: 8}\"\r\n                          :class=\"psaVerify || disabled?'disable-form':''\" :disabled=\"psaVerify || disabled\"\r\n                          maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\"\r\n                          type=\"textarea\"\r\n                />\r\n              </el-col>\r\n              <el-col :span=\"3\">\r\n                <div class=\"custom-form-label\" style=\"border-right: solid 1px #DCDFE6;\">第二通知人</div>\r\n                <el-input v-model=\"form.secondBookingNotifyParty\"\r\n                          :autosize=\"{ minRows: 5, maxRows: 8}\" :class=\"psaVerify || disabled?'disable-form':''\"\r\n                          :disabled=\"psaVerify || disabled\" maxlength=\"500\"\r\n                          show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                />\r\n              </el-col>\r\n              <el-col :span=\"3\">\r\n                <div class=\"custom-form-label\" style=\"border-right: solid 1px #DCDFE6;\">代理</div>\r\n                <el-input v-model=\"form.bookingAgent\"\r\n                          :class=\"psaVerify || disabled?'disable-form':''\" :disabled=\"psaVerify || disabled\"\r\n                          :autosize=\"{ minRows: 5, maxRows: 8}\" maxlength=\"500\"\r\n                          show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                />\r\n              </el-col>\r\n            </el-row>\r\n          </el-row>\r\n          <div class=\"parting-bar\"/>\r\n          <div style=\"margin-top: 10px;margin-bottom: 10px\">\r\n            <!--<el-checkbox-button v-if=\"!booking\" v-model=\"serviceInfo\" label=\"分支信息\" style=\"width: 100px\"><i\r\n                :class=\"serviceInfo?'el-icon-check':'el-icon-minus'\"\r\n            /> 服务信息\r\n            </el-checkbox-button>-->\r\n            <el-checkbox-button v-model=\"branchInfo\" label=\"分支信息\" style=\"width: 100px\"><i\r\n              :class=\"branchInfo?'el-icon-check':'el-icon-minus'\"\r\n            /> 基础信息\r\n            </el-checkbox-button>\r\n            <el-checkbox-button v-model=\"logisticsInfo\" label=\"物流进度\" style=\"width: 100px\"><i\r\n              :class=\"logisticsInfo?'el-icon-check':'el-icon-minus'\"\r\n            /> 物流进度\r\n            </el-checkbox-button>\r\n            <!--<el-checkbox-button v-model=\"docInfo\" label=\"文件列表\" style=\"width: 100px\"><i\r\n                :class=\"docInfo?'el-icon-check':'el-icon-minus'\"\r\n            /> 文件列表\r\n            </el-checkbox-button>-->\r\n            <el-checkbox-button v-model=\"chargeInfo\" label=\"费用列表\" style=\"width: 100px\"><i\r\n              :class=\"chargeInfo?'el-icon-check':'el-icon-minus'\"\r\n            /> 费用列表\r\n            </el-checkbox-button>\r\n            <el-checkbox-button v-model=\"auditInfo\" label=\"审核信息\" style=\"width: 100px\"><i\r\n              :class=\"auditInfo?'el-icon-check':'el-icon-minus'\"\r\n            /> 审核信息\r\n            </el-checkbox-button>\r\n          </div>\r\n          <div class=\"parting-bar\"/>\r\n          <!--文件进度-->\r\n          <el-row>\r\n            <el-form ref=\"fileProcessForm\" :model=\"form\" class=\"file-process\" label-position=\"top\" label-width=\"80px\">\r\n              <el-row>\r\n                <el-col :span=\"1\">\r\n                  <el-form-item label=\"期望赎单\">\r\n                    <el-input\r\n                      v-model=\"form.opAskingBlGetTime\"\r\n                      style=\"width: 100%\"\r\n                    >\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"1\">\r\n                  <el-form-item label=\"期望放单\">\r\n                    <el-input\r\n                      v-model=\"form.opAskingBlReleaseTime\"\r\n                      style=\"width: 100%\"\r\n                    >\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"1\">\r\n                  <el-form-item label=\"预计赎单\">\r\n                    <el-input\r\n                      v-model=\"form.accPromissBlGetTime\"\r\n                      style=\"width: 100%\"\r\n                    >\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"1\">\r\n                  <el-form-item label=\"提单赎回\">\r\n                    <el-input\r\n                      v-model=\"form.actualBlGotTime\"\r\n                      style=\"width: 100%\"\r\n                    >\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"1\">\r\n                  <el-form-item label=\"预计放单\" label-width=\"100%\">\r\n                    <el-input\r\n                      v-model=\"form.accPromissBlReleaseTime\"\r\n                      style=\"width: 100%\"\r\n                    >\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"1\">\r\n                  <el-form-item label=\"已放提单\">\r\n                    <el-input\r\n                      v-model=\"form.actualBlReleaseTime\"\r\n                      style=\"width: 100%\"\r\n                    >\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"1\">\r\n                  <el-form-item label=\"已发代理\">\r\n                    <el-input\r\n                      v-model=\"form.agentNoticeTime\"\r\n                      style=\"width: 100%\"\r\n                    >\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :offset=\"2\" :span=\"4\">\r\n                  <el-form-item label=\"ATD\" prop=\"revenueTons\">\r\n                    <el-date-picker v-model=\"form.podEta\"\r\n                                    clearable\r\n                                    placeholder=\"ATA\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"ATA\" prop=\"revenueTons\">\r\n                    <el-date-picker v-model=\"form.destinationPortEta\"\r\n                                    clearable\r\n                                    placeholder=\"ATA\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n              </el-row>\r\n            </el-form>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!--提单信息-->\r\n      <bill-of-lading-info\r\n        :bookingMessageForm=\"bookingMessageForm\"\r\n        :bookingMessageList=\"bookingMessageList\"\r\n        :bookingMessageStatus=\"bookingMessageStatus\"\r\n        :openBookingMessage=\"openBookingMessage\"\r\n        :auditInfo=\"auditInfo\"\r\n        :branchInfo=\"branchInfo\"\r\n        :chargeInfo=\"chargeInfo\"\r\n        :companyList=\"companyList\"\r\n        :disabled=\"disabled\"\r\n        :form=\"form\"\r\n        :logisticsInfo=\"logisticsInfo\"\r\n        :psaVerify=\"psaVerify\"\r\n        :rsClientMessage=\"rsClientMessage\"\r\n        :rsClientMessagePayableRMB=\"rsClientMessagePayableRMB\"\r\n        :rsClientMessagePayableTaxRMB=\"rsClientMessagePayableTaxRMB\"\r\n        :rsClientMessagePayableTaxUSD=\"rsClientMessagePayableTaxUSD\"\r\n        :rsClientMessagePayableUSD=\"rsClientMessagePayableUSD\"\r\n        :rsClientMessageProfitRMB=\"rsClientMessageProfitRMB\"\r\n        :rsClientMessageProfitTaxRMB=\"rsClientMessageProfitTaxRMB\"\r\n        :rsClientMessageProfitTaxUSD=\"rsClientMessageProfitTaxUSD\"\r\n        :rsClientMessageProfitUSD=\"rsClientMessageProfitUSD\"\r\n        :rsClientMessageReceivableRMB=\"rsClientMessageReceivableRMB\"\r\n        :rsClientMessageReceivableTaxRMB=\"rsClientMessageReceivableTaxRMB\"\r\n        :rsClientMessageReceivableTaxUSD=\"rsClientMessageReceivableTaxUSD\"\r\n        :rsClientMessageReceivableUSD=\"rsClientMessageReceivableUSD\"\r\n        @confirmed=\"confirmed\"\r\n        @copyFreight=\"copyFreight\"\r\n        @getBillOfLading=\"getBillOfLading\"\r\n        @getChargeListBill=\"getChargeListBill\"\r\n        @getOpBill=\"getOpBill\"\r\n        @handleAddCommon=\"handleAddCommon\"\r\n        @handleProfit=\"handleProfit\"\r\n        @handleReceiveSelected=\"handleReceiveSelected\"\r\n        @handleSelectionChange=\"handleSelectionChange\"\r\n        @openChargeSelect=\"openChargeSelect\"\r\n        @openReleaseUsed=\"openReleaseUsed\"\r\n        @rsClientMessageCharge=\"rsClientMessageCharge\"\r\n        @bookingMessageConfirm=\"bookingMessageConfirm\"\r\n        @closeBookingMessage=\"closeBookingMessage\"\r\n        @deleteBookingMessage=\"deleteBookingMessage\"\r\n        @handleAddBookingMessage=\"addBookingMessage\"\r\n        @handleBookingMessageUpdate=\"handleBookingMessageUpdate\"\r\n      />\r\n\r\n      <!--子服务-->\r\n      <!--整柜海运-->\r\n      <sea-fcl-component\r\n        :audit-info=\"auditInfo\"\r\n        :booking=\"booking\"\r\n        :branch-info=\"branchInfo\"\r\n        :carrier-list=\"carrierList\"\r\n        :carrier-normalizer=\"carrierNormalizer\"\r\n        :charge-info=\"chargeInfo\"\r\n        :company-list=\"companyList\"\r\n        :disabled=\"disabled\"\r\n        :form=\"form\"\r\n        :logistics-info=\"logisticsInfo\"\r\n        :psa-verify=\"psaVerify\"\r\n        :sea-fcl-list=\"form.rsOpSeaFclList\"\r\n        :supplier-list=\"supplierList\"\r\n        @addProgress=\"addProgress\"\r\n        @addSeaFCL=\"addSeaFCL\"\r\n        @auditCharge=\"auditCharge\"\r\n        @calculateCharge=\"calculateCharge\"\r\n        @changeServiceFold=\"changeServiceFold\"\r\n        @copyFreight=\"copyFreight\"\r\n        @deleteRsOpFclSea=\"deleteRsOpFclSea\"\r\n        @generateFreight=\"generateFreight\"\r\n        @getBookingBill=\"getBookingBill\"\r\n        @getBookingStatus=\"getBookingStatus\"\r\n        @getPayable=\"getPayable\"\r\n        @getServiceInstanceDisable=\"getServiceInstanceDisable\"\r\n        @getServiceObject=\"getServiceObject\"\r\n        @handleSettledRate=\"handleSettledRate\"\r\n        @openChargeSelect=\"openChargeSelect\"\r\n        @psaBookingCancel=\"psaBookingCancel\"\r\n        @selectCarrier=\"selectCarrier\"\r\n        @selectPsaBookingOpen=\"selectPsaBookingOpen\"\r\n      />\r\n\r\n      <!--拼柜海运-->\r\n      <sea-lcl-component\r\n        :audit-info=\"auditInfo\"\r\n        :booking=\"booking\"\r\n        :branch-info=\"branchInfo\"\r\n        :carrier-list=\"carrierList\"\r\n        :carrier-normalizer=\"carrierNormalizer\"\r\n        :charge-info=\"chargeInfo\"\r\n        :company-list=\"companyList\"\r\n        :disabled=\"disabled\"\r\n        :form=\"form\"\r\n        :logistics-info=\"logisticsInfo\"\r\n        :psa-verify=\"psaVerify\"\r\n        :sea-lcl-list=\"form.rsOpSeaLclList\"\r\n        :supplier-list=\"supplierList\"\r\n        @addProgress=\"addProgress\"\r\n        @addSeaLCL=\"addSeaLCL\"\r\n        @auditCharge=\"auditCharge\"\r\n        @calculateCharge=\"calculateCharge\"\r\n        @changeServiceFold=\"changeServiceFold\"\r\n        @copyFreight=\"copyFreight\"\r\n        @deleteRsOpLclSea=\"deleteRsOpLclSea\"\r\n        @generateFreight=\"generateFreight\"\r\n        @getBookingBill=\"getBookingBill\"\r\n        @getBookingStatus=\"getBookingStatus\"\r\n        @getFormDisable=\"getFormDisable\"\r\n        @getPayable=\"getPayable\"\r\n        @getServiceInstanceDisable=\"getServiceInstanceDisable\"\r\n        @getServiceObject=\"getServiceObject\"\r\n        @handleSettledRate=\"handleSettledRate\"\r\n        @openChargeSelect=\"openChargeSelect\"\r\n        @psaBookingCancel=\"psaBookingCancel\"\r\n        @selectCarrier=\"selectCarrier\"\r\n        @selectPsaBookingOpen=\"selectPsaBookingOpen\"\r\n      />\r\n\r\n      <!--空运-->\r\n      <air-component\r\n        :air-list=\"form.rsOpAirList\"\r\n        :form=\"form\"\r\n        :branch-info=\"branchInfo\"\r\n        :logistics-info=\"logisticsInfo\"\r\n        :charge-info=\"chargeInfo\"\r\n        :audit-info=\"auditInfo\"\r\n        :disabled=\"disabled\"\r\n        :booking=\"booking\"\r\n        :psa-verify=\"psaVerify\"\r\n        :supplier-list=\"supplierList\"\r\n        :carrier-list=\"carrierList\"\r\n        :company-list=\"companyList\"\r\n        :carrier-normalizer=\"carrierNormalizer\"\r\n        @changeServiceFold=\"changeServiceFold\"\r\n        @addAir=\"addAir\"\r\n        @deleteRsOpAir=\"deleteRsOpAir\"\r\n        @openChargeSelect=\"openChargeSelect\"\r\n        @auditCharge=\"auditCharge\"\r\n        @getBookingBill=\"getBookingBill\"\r\n        @generateFreight=\"generateFreight\"\r\n        @selectPsaBookingOpen=\"selectPsaBookingOpen\"\r\n        @selectCarrier=\"selectCarrier\"\r\n        @addProgress=\"addProgress\"\r\n        @psaBookingCancel=\"psaBookingCancel\"\r\n        @copyFreight=\"copyFreight\"\r\n        @calculateCharge=\"calculateCharge\"\r\n      />\r\n\r\n      <!--铁路-->\r\n      <railway-component\r\n        v-for=\"item in RAIL\"\r\n        :key=\"item.serviceTypeId\"\r\n        :fold-state=\"getFold(item.serviceTypeId)\"\r\n        :form=\"form\"\r\n        :branch-info=\"branchInfo\"\r\n        :logistics-info=\"logisticsInfo\"\r\n        :charge-info=\"chargeInfo\"\r\n        :audit-info=\"auditInfo\"\r\n        :disabled=\"disabled\"\r\n        :booking=\"booking\"\r\n        :psa-verify=\"psaVerify\"\r\n        :supplier-list=\"supplierList\"\r\n        :company-list=\"companyList\"\r\n        :form-disable=\"getFormDisable(item.serviceTypeId)\"\r\n        :payable=\"getPayable(item.serviceTypeId)\"\r\n        :service-instance=\"getServiceInstance(item.serviceTypeId)\"\r\n        :service-item=\"item\"\r\n        :service-object=\"getServiceObject(item.serviceTypeId)\"\r\n        @changeFold=\"changeFold\"\r\n        @changeServiceObject=\"changeServiceObject\"\r\n        @auditCharge=\"auditCharge\"\r\n        @generateFreight=\"generateFreight\"\r\n        @psaBookingCancel=\"psaBookingCancel\"\r\n        @copyFreight=\"copyFreight\"\r\n        @calculateCharge=\"calculateCharge\"\r\n      />\r\n\r\n      <!--快递-->\r\n      <express-component\r\n        v-for=\"item in EXPRESS\"\r\n        :key=\"item.serviceTypeId\"\r\n        :fold-state=\"getFold(item.serviceTypeId)\"\r\n        :form=\"form\"\r\n        :form-disable=\"getFormDisable(item.serviceTypeId)\"\r\n        :payable=\"getPayable(item.serviceTypeId)\"\r\n        :service-instance=\"getServiceInstance(item.serviceTypeId)\"\r\n        :service-item=\"item\"\r\n        :service-object=\"getServiceObject(item.serviceTypeId)\"\r\n        :branch-info=\"branchInfo\"\r\n        :service-info=\"serviceInfo\"\r\n        :logistics-info=\"logisticsInfo\"\r\n        :charge-info=\"chargeInfo\"\r\n        :audit-info=\"auditInfo\"\r\n        :disabled=\"disabled\"\r\n        :booking=\"booking\"\r\n        :psa-verify=\"psaVerify\"\r\n        :supplier-list=\"supplierList\"\r\n        :company-list=\"companyList\"\r\n        @changeFold=\"changeFold\"\r\n        @changeServiceObject=\"changeServiceObject\"\r\n        @auditCharge=\"auditCharge\"\r\n        @generateFreight=\"generateFreight\"\r\n        @psaBookingCancel=\"psaBookingCancel\"\r\n        @copyFreight=\"copyFreight\"\r\n        @calculateCharge=\"calculateCharge\"\r\n      />\r\n\r\n      <!--整柜拖车-->\r\n      <ctnr-truck-component\r\n        :ctnr-truck-list=\"form.rsOpCtnrTruckList\"\r\n        :form=\"form\"\r\n        :branch-info=\"branchInfo\"\r\n        :logistics-info=\"logisticsInfo\"\r\n        :charge-info=\"chargeInfo\"\r\n        :audit-info=\"auditInfo\"\r\n        :disabled=\"disabled\"\r\n        :booking=\"booking\"\r\n        :psa-verify=\"psaVerify\"\r\n        :supplier-list=\"supplierList\"\r\n        :company-list=\"companyList\"\r\n        :location-options=\"locationOptions\"\r\n        @changeServiceFold=\"changeServiceFold\"\r\n        @addCtnrTruck=\"addCtnrTruck\"\r\n        @deleteRsOpCtnrTruck=\"deleteRsOpCtnrTruck\"\r\n        @openChargeSelect=\"openChargeSelect\"\r\n        @auditCharge=\"auditCharge\"\r\n        @getDispatchingBill=\"getDispatchingBill\"\r\n        @generateFreight=\"generateFreight\"\r\n        @handleAddCommon=\"handleAddCommon\"\r\n        @openDispatchCommon=\"openDispatchCommon\"\r\n        @copyFreight=\"copyFreight\"\r\n        @calculateCharge=\"calculateCharge\"\r\n      />\r\n\r\n      <!--散货拖车-->\r\n      <bulk-truck-component\r\n        :bulk-truck-list=\"form.rsOpBulkTruckList\"\r\n        :form=\"form\"\r\n        :branch-info=\"branchInfo\"\r\n        :logistics-info=\"logisticsInfo\"\r\n        :charge-info=\"chargeInfo\"\r\n        :audit-info=\"auditInfo\"\r\n        :disabled=\"disabled\"\r\n        :booking=\"booking\"\r\n        :psa-verify=\"psaVerify\"\r\n        :supplier-list=\"supplierList\"\r\n        :company-list=\"companyList\"\r\n        :location-options=\"locationOptions\"\r\n        @changeServiceFold=\"changeServiceFold\"\r\n        @addBulkTruck=\"addBulkTruck\"\r\n        @deleteRsOpBulkTruck=\"deleteRsOpBulkTruck\"\r\n        @openChargeSelect=\"openChargeSelect\"\r\n        @auditCharge=\"auditCharge\"\r\n        @getDispatchingBill=\"getDispatchingBill\"\r\n        @generateFreight=\"generateFreight\"\r\n        @handleAddCommon=\"handleAddCommon\"\r\n        @openDispatchCommon=\"openDispatchCommon\"\r\n        @copyFreight=\"copyFreight\"\r\n        @calculateCharge=\"calculateCharge\"\r\n      />\r\n\r\n      <!--单证报关-->\r\n      <doc-declare-component\r\n        :doc-declare-list=\"form.rsOpDocDeclareList\"\r\n        :form=\"form\"\r\n        :branch-info=\"branchInfo\"\r\n        :logistics-info=\"logisticsInfo\"\r\n        :charge-info=\"chargeInfo\"\r\n        :audit-info=\"auditInfo\"\r\n        :disabled=\"disabled\"\r\n        :booking=\"booking\"\r\n        :psa-verify=\"psaVerify\"\r\n        :supplier-list=\"supplierList\"\r\n        :company-list=\"companyList\"\r\n        @changeServiceFold=\"changeServiceFold\"\r\n        @addDocDeclare=\"addDocDeclare\"\r\n        @deleteRsOpDocDeclare=\"deleteRsOpDocDeclare\"\r\n        @openChargeSelect=\"openChargeSelect\"\r\n        @auditCharge=\"auditCharge\"\r\n        @getBookingBill=\"getBookingBill\"\r\n        @generateFreight=\"generateFreight\"\r\n        @selectPsaBookingOpen=\"selectPsaBookingOpen\"\r\n        @psaBookingCancel=\"psaBookingCancel\"\r\n        @copyFreight=\"copyFreight\"\r\n        @calculateCharge=\"calculateCharge\"\r\n      />\r\n\r\n      <!--全包报关-->\r\n      <free-declare-component\r\n        :free-declare-list=\"form.rsOpFreeDeclareList\"\r\n        :form=\"form\"\r\n        :branch-info=\"branchInfo\"\r\n        :logistics-info=\"logisticsInfo\"\r\n        :charge-info=\"chargeInfo\"\r\n        :audit-info=\"auditInfo\"\r\n        :disabled=\"disabled\"\r\n        :booking=\"booking\"\r\n        :psa-verify=\"psaVerify\"\r\n        :supplier-list=\"supplierList\"\r\n        :company-list=\"companyList\"\r\n        @changeServiceFold=\"changeServiceFold\"\r\n        @addFreeDeclare=\"addFreeDeclare\"\r\n        @deleteRsOpFreeDeclare=\"deleteRsOpFreeDeclare\"\r\n        @openChargeSelect=\"openChargeSelect\"\r\n        @auditCharge=\"auditCharge\"\r\n        @getBookingBill=\"getBookingBill\"\r\n        @generateFreight=\"generateFreight\"\r\n        @copyFreight=\"copyFreight\"\r\n        @calculateCharge=\"calculateCharge\"\r\n      />\r\n\r\n      <!--清关-->\r\n      <clearance-component\r\n        v-for=\"item in CLEAR\"\r\n        :key=\"item.serviceTypeId\"\r\n        :fold-state=\"getFold(item.serviceTypeId)\"\r\n        :form=\"form\"\r\n        :branch-info=\"branchInfo\"\r\n        :logistics-info=\"logisticsInfo\"\r\n        :charge-info=\"chargeInfo\"\r\n        :audit-info=\"auditInfo\"\r\n        :disabled=\"disabled\"\r\n        :booking=\"booking\"\r\n        :psa-verify=\"psaVerify\"\r\n        :supplier-list=\"supplierList\"\r\n        :company-list=\"companyList\"\r\n        :form-disable=\"getFormDisable(item.serviceTypeId)\"\r\n        :payable=\"getPayable(item.serviceTypeId)\"\r\n        :service-instance=\"getServiceInstance(item.serviceTypeId)\"\r\n        :service-item=\"item\"\r\n        :service-object=\"getServiceObject(item.serviceTypeId)\"\r\n        @changeFold=\"changeFold\"\r\n        @changeServiceObject=\"changeServiceObject\"\r\n        @auditCharge=\"auditCharge\"\r\n        @getBookingBill=\"getBookingBill\"\r\n        @generateFreight=\"generateFreight\"\r\n        @psaBookingCancel=\"psaBookingCancel\"\r\n        @copyFreight=\"copyFreight\"\r\n        @calculateCharge=\"calculateCharge\"\r\n      />\r\n\r\n      <!--仓储-->\r\n      <whs-component\r\n        v-for=\"item in WHS\"\r\n        :key=\"item.serviceTypeId\"\r\n        :fold-state=\"getFold(item.serviceTypeId)\"\r\n        :audit-info=\"auditInfo\"\r\n        :booking=\"booking\"\r\n        :branch-info=\"branchInfo\"\r\n        :charge-info=\"chargeInfo\"\r\n        :company-list=\"companyList\"\r\n        :disabled=\"disabled\"\r\n        :form=\"form\"\r\n        :logistics-info=\"logisticsInfo\"\r\n        :psa-verify=\"psaVerify\"\r\n        :rs-op-warehouse-payable=\"rsOpWarehousePayable\"\r\n        :supplier-list=\"supplierList\"\r\n        :whs-services=\"WHS\"\r\n        :form-disable=\"getFormDisable(item.serviceTypeId)\"\r\n        :payable=\"getPayable(item.serviceTypeId)\"\r\n        :service-instance=\"getServiceInstance(item.serviceTypeId)\"\r\n        :service-item=\"item\"\r\n        :service-object=\"getServiceObject(item.serviceTypeId)\"\r\n        @auditCharge=\"auditCharge\"\r\n        @calculateCharge=\"calculateCharge\"\r\n        @changeFold=\"changeFold\"\r\n        @changeServiceObject=\"changeServiceObject\"\r\n        @copyFreight=\"copyFreight\"\r\n        @generateFreight=\"generateFreight\"\r\n        @getBookingBill=\"getBookingBill\"\r\n        @outboundPlan=\"outboundPlan\"\r\n        @psaBookingCancel=\"psaBookingCancel\"\r\n      />\r\n\r\n      <!--拓展服务-->\r\n      <extend-service-component\r\n        v-for=\"item in EXTEND\"\r\n        :key=\"item.serviceTypeId\"\r\n        :fold-state=\"getFold(item.serviceTypeId)\"\r\n        :audit-info=\"auditInfo\"\r\n        :booking=\"booking\"\r\n        :branch-info=\"branchInfo\"\r\n        :charge-info=\"chargeInfo\"\r\n        :company-list=\"companyList\"\r\n        :disabled=\"disabled\"\r\n        :extend-service-list=\"extendServiceList\"\r\n        :form=\"form\"\r\n        :logistics-info=\"logisticsInfo\"\r\n        :psa-verify=\"psaVerify\"\r\n        :supplier-list=\"supplierList\"\r\n        :form-disable=\"getFormDisable(item.serviceTypeId)\"\r\n        :payable=\"getPayable(item.serviceTypeId)\"\r\n        :service-instance=\"getServiceInstance(item.serviceTypeId)\"\r\n        :service-item=\"item\"\r\n        :service-object=\"getServiceObject(item.serviceTypeId)\"\r\n        @changeFold=\"changeFold\"\r\n        @changeServiceObject=\"changeServiceObject\"\r\n        @auditCharge=\"auditCharge\"\r\n        @calculateCharge=\"calculateCharge\"\r\n        @changeServiceFold=\"changeExtendServiceFold\"\r\n        @copyFreight=\"copyFreight\"\r\n        @generateFreight=\"generateFreight\"\r\n        @psaBookingCancel=\"psaBookingCancel\"\r\n      />\r\n\r\n      <!--备注-->\r\n      <div>\r\n        <el-row :gutter=\"10\" class=\"spc\" style=\"margin-bottom:15px;display: -webkit-box\">\r\n          <!--报价-->\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"报价单号\">\r\n              <el-input v-model=\"form.qoutationNo\" :disabled=\"true\" placeholder=\"报价单号\"\r\n                        class=\"disable-form\" style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n            <el-input v-model=\"form.qoutationSketch\" :autosize=\"{ minRows: 10, maxRows: 20}\"\r\n                      :class=\"!booking ||disabled ?'disable-form':''\"\r\n                      placeholder=\"内容\" style=\"padding-bottom: 2px;\" type=\"textarea\"\r\n                      :disabled=\"!booking ||disabled\"\r\n            />\r\n            <el-form-item label=\"业务员\" prop=\"salesId\">\r\n              <treeselect v-model=\"salesId\" :class=\"form.sqdShippingBookingStatus==1?'disable-form':''\"\r\n                          :disabled=\"form.sqdShippingBookingStatus==1\"\r\n                          :disabled-fuzzy-matching=\"true\" :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                          :options=\"belongList\" :show-count=\"true\" placeholder=\"业务员\"\r\n                          @input=\"$event==undefined?form.salesId = null:null\"\r\n                          :disabled-branch-nodes=\"true\"\r\n                          @select=\"form.salesId = $event.staffId\"\r\n              >\r\n                <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                  {{\r\n                    node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                  }}\r\n                </div>\r\n                <label slot=\"option-label\"\r\n                       slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                       :class=\"labelClassName\"\r\n                >\r\n                  {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                  <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                </label>\r\n              </treeselect>\r\n            </el-form-item>\r\n            <el-form-item label=\"报价日期\" prop=\"quotationTime\">\r\n              <el-date-picker v-model=\"form.qoutationTime\"\r\n                              :class=\" 'disable-form'\" disabled\r\n                              placeholder=\"报价日期\" style=\"width:100%\"\r\n                              clearable type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <!--订舱-->\r\n          <el-col :span=\"5\">\r\n            <el-row>\r\n              <el-col :span=\"16\">\r\n                <el-form-item label=\"订舱单号\">\r\n                  <el-input v-model=\"form.newBookingNo\" :disabled=\"true\" class=\"disable-form\"\r\n                            placeholder=\"订舱单号\" style=\"width: 100%\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"8\" align=\"center\">\r\n                <el-button :disabled=\" !booking || form.sqdShippingBookingStatus==1 \" type=\"success\"\r\n                           @click=\"submitForm('booking')\"\r\n                >\r\n                  <i\r\n                    :class=\"form.sqdShippingBookingStatus !=null && form.sqdShippingBookingStatus != 0?'el-icon-check':''\"\r\n                  />\r\n                  {{\r\n                    form.sqdShippingBookingStatus == null || form.sqdShippingBookingStatus == 0 ? \"提交订舱\" : \"已订舱\"\r\n                  }}\r\n                </el-button>\r\n              </el-col>\r\n            </el-row>\r\n\r\n            <el-input v-model=\"form.newBookingRemark\" :autosize=\"{ minRows: 10, maxRows: 20}\"\r\n                      :class=\"!booking||disabled?'disable-form':''\"\r\n                      placeholder=\"业务订舱备注\" type=\"textarea\"\r\n                      :disabled=\"!booking||disabled\"\r\n            />\r\n            <el-col :span=\"16\">\r\n              <el-form-item label=\"业务助理\" prop=\"salesAssistantId\">\r\n                <treeselect v-model=\"salesAssistantId\" :class=\"!booking|| disabled?'disable-form':''\"\r\n                            :disabled-branch-nodes=\"true\"\r\n                            :disabled-fuzzy-matching=\"true\" :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                            :disabled=\"!booking\" :options=\"belongList\" :show-count=\"true\"\r\n                            placeholder=\"业务助理\"\r\n                            @input=\"$event==undefined?form.salesAssistantId=null:null\"\r\n                            @select=\"form.salesAssistantId = $event.staffId\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"16\">\r\n              <el-form-item label=\"订舱日期\" prop=\"newBookingTime\">\r\n                <el-date-picker v-model=\"form.newBookingTime\" :class=\"'disable-form'\"\r\n                                clearable\r\n                                placeholder=\"订舱申请单日期\" style=\"width:100%\"\r\n                                disabled type=\"datetime\"\r\n                                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-col>\r\n          <!--商务审核-->\r\n          <el-col :span=\"4\">\r\n            <el-row>\r\n              <el-col :span=\"16\">\r\n                <el-form-item label=\"审核意见\">\r\n                  <tree-select :class=\"!psaVerify|| form.psaVerify == 1?'disable-form':''\"\r\n                               :disabled=\"!psaVerify|| form.psaVerify == 1\" :flat=\"false\" :multiple=\"false\"\r\n                               :pass=\"form.psaVerifyStatusId\" :placeholder=\"'审核意见'\"\r\n                               :type=\"'processStatus'\"\r\n                               :type-id=\"1\" @return=\"form.psaVerifyStatusId=$event\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"8\" align=\"center\">\r\n                <el-button :disabled=\"!psaVerify || form.psaVerify == 1\" type=\"success\"\r\n                           @click=\"submitForm('psa')\"\r\n                >\r\n                  <i :class=\"form.psaVerify == 1?'el-icon-check':''\"/>{{\r\n                    form.psaVerify != 1 ? \"提交审核\" : \"已审核\"\r\n                  }}\r\n                </el-button>\r\n              </el-col>\r\n            </el-row>\r\n\r\n            <el-form-item label=\"\" label-width=\"100\" prop=\"inquiryInnerRemarkSum\">\r\n              <el-input v-model=\"form.inquiryInnerRemarkSum\" :autosize=\"{ minRows: 10, maxRows: 20}\" maxlength=\"150\"\r\n                        :class=\"!psaVerify || form.psaVerify == 1?'disable-form':''\"\r\n                        :disabled=\"!psaVerify || form.psaVerify == 1\" placeholder=\"内容\"\r\n                        show-word-limit\r\n                        type=\"textarea\"\r\n              />\r\n            </el-form-item>\r\n            <el-col :span=\"16\">\r\n              <el-form-item label=\"商务审核\" prop=\"verifyPsaId\">\r\n                <treeselect v-model=\"verifyPsaId\" :class=\"!booking ||disabled?'disable-form':''\"\r\n                            :disabled-branch-nodes=\"true\"\r\n                            :disabled=\"!booking ||disabled\" :disabled-fuzzy-matching=\"true\"\r\n                            :flatten-search-results=\"true\"\r\n                            :normalizer=\"businessesNormalizer\" :options=\"businessList\" :show-count=\"true\"\r\n                            placeholder=\"商务\" @input=\"$event==undefined?form.verifyPsaId=null:null\"\r\n                            @select=\"form.verifyPsaId = $event.staffId\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"16\">\r\n              <el-form-item label=\"审核时间\" prop=\"psaVerifyTime\">\r\n                <el-date-picker v-model=\"form.psaVerifyTime\" class=\"disable-form\"\r\n                                clearable\r\n                                disabled placeholder=\"商务审核时间\"\r\n                                style=\"width:100%\" type=\"datetime\"\r\n                                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-col>\r\n          <!--<el-col :span=\"4\">\r\n            <el-form-item label=\"派单意见\">\r\n              <el-input v-model=\"form.newBookingNo\" :disabled=\"psaVerify || disabled\" placeholder=\"订舱单号\"\r\n                        style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"\" label-width=\"100\" prop=\"opLeaderNotice\">\r\n              <el-input v-model=\"form.opLeaderNotice\" :autosize=\"{ minRows: 10, maxRows: 20}\" :disabled=\"psaVerify || disabled\"\r\n                        maxlength=\"150\" placeholder=\"内容\" show-word-limit type=\"textarea\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"派单操作\" prop=\"opId\">\r\n              <treeselect v-model=\"opId\" :disabled-branch-nodes=\"true\" :disabled=\"psaVerify || disabled\"\r\n                          :disabled-fuzzy-matching=\"true\" :flatten-search-results=\"true\"\r\n                          :normalizer=\"staffNormalizer\"\r\n                          :options=\"opList.filter(v => {return v.role.roleLocalName=='操作员'})\" :show-count=\"true\"\r\n                          placeholder=\"操作员\"\r\n                          @input=\"$event==undefined?form.opId = null:null\"\r\n                          @select=\"form.opId = $event.staffId\"\r\n              >\r\n                <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                  {{\r\n                    node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                  }}\r\n                </div>\r\n                <label slot=\"option-label\"\r\n                       slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                       :class=\"labelClassName\"\r\n                >\r\n                  {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                  <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                </label>\r\n              </treeselect>\r\n            </el-form-item>\r\n            <el-form-item label=\"派单日期\" prop=\"processStatusTime\">\r\n              <el-date-picker  clearable\r\n                              placeholder=\"状态日期\"\r\n                              style=\"width:100%\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>-->\r\n          <!--操作-->\r\n          <el-col :span=\"5\">\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"订单状态\">\r\n                  <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.processStatusId\" :placeholder=\"'订单状态'\"\r\n                               :disabled=\"!op || disabled\" :type=\"'processStatus'\" :type-id=\"2\"\r\n                               :class=\"!op || disabled?'disable-form':''\" @return=\"form.processStatusId=$event\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\" align=\"center\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"!op || form.opAccept == 1 || finance\" type=\"success\"\r\n                               @click=\"submitForm('opConfirm')\"\r\n                    ><i :class=\"form.opAccept == 1?'el-icon-check':''\"/>{{\r\n                        form.opAccept != 1 ? \"确认接单\" : \"已接单\"\r\n                      }}\r\n                    </el-button>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-button v-if=\"op && form.opAccept == 0\" type=\"warning\" @click=\"turnDown\"\r\n                    >{{\r\n                        \"驳回\"\r\n                      }}\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-row>\r\n            <el-form-item label=\"\" label-width=\"100\" prop=\"opInnerRemark\">\r\n              <el-input v-model=\"form.opInnerRemark\" :autosize=\"{ minRows: 10, maxRows: 20}\"\r\n                        :class=\"!op|| disabled?'disable-form':''\"\r\n                        :disabled=\"!op\" placeholder=\"内容\"\r\n                        show-word-limit type=\"textarea\"\r\n              />\r\n            </el-form-item>\r\n            <el-col :span=\"16\">\r\n              <el-form-item label=\"操作员\" prop=\"opId\">\r\n                <treeselect v-model=\"opId\" :disabled-branch-nodes=\"true\"\r\n                            :disabled-fuzzy-matching=\"true\" :flatten-search-results=\"true\"\r\n                            :normalizer=\"businessesNormalizer\"\r\n                            :options=\"opList.filter(v => {return v.role.roleLocalName=='操作员'})\" :show-count=\"true\"\r\n                            :class=\"!psaVerify || disabled || form.psaVerify == 1?'disable-form':''\"\r\n                            :disabled=\"!psaVerify || form.psaVerify == 1 || disabled\"\r\n                            placeholder=\"操作员\"\r\n                            @input=\"$event==undefined?form.opId = null:null\"\r\n                            @select=\"form.opId = $event.staffId\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"16\">\r\n              <el-form-item label=\"状态日期\" prop=\"statusUpdateTime\">\r\n                <el-date-picker v-model=\"form.statusUpdateTime\" class=\"disable-form\"\r\n                                clearable disabled\r\n                                placeholder=\"状态日期\"\r\n                                style=\"width:100%\" type=\"datetime\"\r\n                                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!--费用选择弹出层-->\r\n      <el-dialog\r\n        v-dialogDrag\r\n        v-dialogDragWidth\r\n        :close-on-click-modal=\"false\"\r\n        :destroy-on-close=\"true\" :modal-append-to-body=\"false\"\r\n        :visible.sync=\"openFreightSelect\" append-to-body center\r\n        custom-class=\"dialog\"\r\n        width=\"90%\"\r\n      >\r\n        <FreightSelect :freight-select-data=\"freightSelectData\" :type-id=\"freightSelectData.typeId\"\r\n                       @returnFreight=\"handleFreightSelect\"\r\n        />\r\n      </el-dialog>\r\n\r\n      <!--费用复制选择弹出层-->\r\n      <el-dialog\r\n        v-dialogDrag\r\n        v-dialogDragWidth\r\n        :close-on-click-modal=\"false\"\r\n        :destroy-on-close=\"true\" :modal-append-to-body=\"false\"\r\n        :visible.sync=\"chargeOpen\" append-to-body center\r\n        custom-class=\"dialog\"\r\n        width=\"70%\"\r\n      >\r\n        <charge-select :service-type-id=\"chargeSelectItem?chargeSelectItem.sqdServiceTypeId:null\"\r\n                       :client-id=\"form.clientId\" :search-data=\"chargeSearchData\" @returnCharge=\"handleChargeSelect\"\r\n        />\r\n      </el-dialog>\r\n\r\n      <!--委托单位选择弹出层-->\r\n      <el-dialog\r\n        v-dialogDrag\r\n        v-dialogDragWidth :append-to-body=\"true\"\r\n        :close-on-click-modal=\"false\"\r\n        :destroy-on-close=\"true\" :modal-append-to-body=\"false\"\r\n        :visible.sync=\"openCompanySelect\" center\r\n        custom-class=\"dialog\"\r\n        width=\"90%\"\r\n      >\r\n        <select-company :roleTypeId=\"'1'\" @return=\"selectCompanyData\"/>\r\n      </el-dialog>\r\n\r\n      <!--商务订舱选择弹出层-->\r\n      <el-dialog\r\n        v-dialogDrag\r\n        v-dialogDragWidth :append-to-body=\"true\"\r\n        :close-on-click-modal=\"false\"\r\n        :destroy-on-close=\"true\" :modal-append-to-body=\"false\"\r\n        :visible.sync=\"openPsaBookingSelect\" center\r\n        custom-class=\"dialog\"\r\n        width=\"90%\"\r\n      >\r\n        <psa-booking-list-select :psaBookingSelectData=\"psaBookingSelectData\" @return=\"selectPsaBooking\"/>\r\n      </el-dialog>\r\n\r\n      <!--通用信息选择弹出层-->\r\n      <el-dialog\r\n        v-dialogDrag\r\n        v-dialogDragWidth\r\n        :append-to-body=\"true\"\r\n        :close-on-click-modal=\"false\"\r\n        :destroy-on-close=\"true\" :modal-append-to-body=\"false\"\r\n        :visible.sync=\"openCommonUsedSelect\" center\r\n        custom-class=\"dialog\"\r\n        width=\"90%\"\r\n      >\r\n        <common-used-select :common-used-select-data=\"commonUsedSelectData\" @return=\"selectCommonUsed\"/>\r\n      </el-dialog>\r\n\r\n      <!--出仓计划-->\r\n      <outbound-plan :open-outbound=\"openOutbound\" :outbound-form-prop=\"outboundForm\" :outboundData=\"outboundData\"\r\n                     @closeOutbound=\"openOutbound=false\"\r\n      ></outbound-plan>\r\n\r\n    </el-form>\r\n\r\n    <div ref=\"dragArea\" class=\"drag-area\">\r\n      <div>{{ this.form.rctNo }}</div>\r\n      <el-button\r\n        v-if=\"(psaVerify&&form.psaVerify != 1) || (form.opAccept==1 &&this.$route.query.type==='op') || form.sqdShippingBookingStatus!=1\"\r\n        type=\"primary\" @click=\"submitForm()\"\r\n      >{{ \"保存更改\" }}\r\n      </el-button>\r\n      <el-button\r\n        v-if=\"booking && form.rctId && form.sqdShippingBookingStatus==='1'\" type=\"primary\" @click=\"saveAs()\"\r\n      >{{ \"另存为\" }}\r\n      </el-button>\r\n    </div>\r\n    <!-- 预览 -->\r\n    <print-preview ref=\"preView\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport document from \"@/views/system/document/index\"\r\nimport OrderDifficultySelect from \"@/components/OrderDifficultySelect/index.vue\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport store from \"@/store\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport DocList from \"@/views/system/document/docList.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport LogisticsNoInfo from \"@/views/system/document/logisticsNoInfo.vue\"\r\nimport opHistory from \"@/views/system/document/opHistory.vue\"\r\nimport receivablePayable from \"@/views/system/document/receivablePayable.vue\"\r\nimport {\r\n  addRct,\r\n  getRct,\r\n  getRctCFMon,\r\n  getRctMon,\r\n  getRctRSWHMon,\r\n  saveAllService,\r\n  saveAsAllService,\r\n  saveAsRct,\r\n  updateRct\r\n} from \"@/api/system/rct\"\r\nimport UrgencyDegreeSelect from \"@/components/UrgencyDegreeSelect/index.vue\"\r\nimport ProgressStatus from \"@/components/ProgressStatus/index.vue\"\r\nimport {checkRole} from \"@/utils/permission\"\r\nimport currency from \"currency.js\"\r\nimport _ from \"lodash\"\r\nimport FreightSelect from \"@/views/system/freight/freightSelect.vue\"\r\nimport {getQuotation, queryLocal} from \"@/api/system/quotation\"\r\nimport SelectCompany from \"@/views/system/company/selectCompany.vue\"\r\nimport {getBooking} from \"@/api/system/booking\"\r\nimport ProgressName from \"@/components/ProgressName/index.vue\"\r\nimport PrintTemplate from \"@/views/system/print/PrintTemplate.vue\"\r\nimport {defaultElementTypeProvider, hiprint} from \"../../../index\"\r\nimport printPreview from \"@/views/print/demo/design/preview.vue\"\r\nimport dispatchBill from \"../../../print-template/dispatchBill\"\r\nimport booking from \"../../../print-template/booking\"\r\nimport CFLBooking from \"../../../print-template/CFLBbooking\"\r\nimport debitNode from \"../../../print-template/debitNode\"\r\nimport debitNodeEn from \"../../../print-template/debitNodeEn\"\r\nimport debitNodeEnHKRMBToUSD from \"../../../print-template/debitNodeEnHKRMBToUSD\"\r\nimport debitNodeUSDToRMB from \"../../../print-template/debitNodeUSDToRMB\"\r\nimport debitNodeZSUSD from \"../../../print-template/debitNodeZSUSD\"\r\nimport debitNodeCFL from \"../../../print-template/debitNodeCFL\"\r\nimport debitNodeCFLToRMB from \"../../../print-template/debitNodeCFLToRMB\"\r\nimport FCLBill from \"../../../print-template/FCLBill\"\r\nimport AirBill from \"../../../print-template/AirBill\"\r\nimport moment from \"moment\"\r\nimport {addPsarct, updatePsarct} from \"@/api/system/psarct\"\r\nimport PsaBookingListSelect from \"@/views/system/booking/psaBookingListSelect.vue\"\r\nimport {addClientsinfo} from \"@/api/system/clientsinfo\"\r\nimport CommonUsedSelect from \"@/views/system/commonused/commonUsedSelect.vue\"\r\nimport {locationOptions} from \"@/api/system/location\"\r\nimport billOfLading from \"@/print-template/billOfLading\"\r\nimport billOfLadingRelease from \"@/print-template/billOfLadingRelease\"\r\nimport {updateCharge} from \"@/api/system/rsCharge\"\r\nimport {updateServiceinstances} from \"@/api/system/serviceinstances\"\r\nimport toWords from \"num-words\"\r\nimport ChargeSelect from \"@/views/system/document/chargeSelect.vue\"\r\nimport DatePickerItem from \"@/views/system/DatePickerItem/index.vue\"\r\nimport OutboundPlan from \"@/views/system/document/outboundPlan.vue\"\r\nimport SeaFclComponent from \"@/views/system/document/serviceComponents/SeaFclComponent.vue\"\r\nimport SeaLclComponent from \"@/views/system/document/serviceComponents/SeaLclComponent.vue\"\r\nimport AirComponent from \"@/views/system/document/serviceComponents/AirComponent.vue\"\r\nimport RailwayComponent from \"@/views/system/document/serviceComponents/RailwayComponent.vue\"\r\nimport ExpressComponent from \"@/views/system/document/serviceComponents/ExpressComponent.vue\"\r\nimport CtnrTruckComponent from \"@/views/system/document/serviceComponents/CtnrTruckComponent.vue\"\r\nimport BulkTruckComponent from \"@/views/system/document/serviceComponents/BulkTruckComponent.vue\"\r\nimport DocDeclareComponent from \"@/views/system/document/serviceComponents/DocDeclareComponent.vue\"\r\nimport FreeDeclareComponent from \"@/views/system/document/serviceComponents/FreeDeclareComponent.vue\"\r\nimport ClearanceComponent from \"@/views/system/document/serviceComponents/ClearanceComponent.vue\"\r\nimport WhsComponent from \"@/views/system/document/serviceComponents/WhsComponent.vue\"\r\nimport ExtendServiceComponent from \"@/views/system/document/serviceComponents/ExtendServiceComponent.vue\"\r\nimport BillOfLadingInfo from \"@/views/system/document/serviceComponents/BillOfLadingInfo.vue\"\r\n\r\n// 导入提取的mixins和utils\r\nimport chargeCalculatorMixin from \"@/views/system/document/mixins/chargeCalculator.js\"\r\nimport computedPropsMixin from \"@/views/system/document/mixins/computedProps.js\"\r\nimport formValidatorMixin from \"@/views/system/document/mixins/formValidator.js\"\r\nimport opDataHandlerMixin from \"@/views/system/document/mixins/opDataHandler.js\"\r\nimport serviceManagerMixin from \"@/views/system/document/mixins/serviceManager.js\"\r\nimport {createServiceInstance, createServiceObject} from \"@/views/system/document/utils/serviceFactory.js\"\r\n\r\nlet hiprintTemplate\r\n\r\n// 添加防抖函数工具\r\nconst debounce = function (func, wait = 500) {\r\n  let timeout\r\n  return function (...args) {\r\n    const context = this\r\n    clearTimeout(timeout)\r\n    timeout = setTimeout(() => {\r\n      func.apply(context, args)\r\n    }, wait)\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: \"op\",\r\n  props: [\"type\"],\r\n  components: {\r\n    OutboundPlan,\r\n    DatePickerItem,\r\n    ChargeSelect,\r\n    CommonUsedSelect,\r\n    PsaBookingListSelect,\r\n    printPreview,\r\n    PrintTemplate,\r\n    ProgressName,\r\n    SelectCompany,\r\n    FreightSelect,\r\n    ProgressStatus,\r\n    UrgencyDegreeSelect,\r\n    receivablePayable, opHistory, LogisticsNoInfo,\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    DocList,\r\n    Treeselect,\r\n    CompanySelect,\r\n    OrderDifficultySelect,\r\n    document,\r\n    SeaFclComponent,\r\n    SeaLclComponent,\r\n    AirComponent,\r\n    RailwayComponent,\r\n    ExpressComponent,\r\n    CtnrTruckComponent,\r\n    BulkTruckComponent,\r\n    DocDeclareComponent,\r\n    FreeDeclareComponent,\r\n    ClearanceComponent,\r\n    WhsComponent,\r\n    ExtendServiceComponent,\r\n    BillOfLadingInfo\r\n  },\r\n  mixins: [\r\n    chargeCalculatorMixin,\r\n    computedPropsMixin,\r\n    formValidatorMixin,\r\n    opDataHandlerMixin,\r\n    serviceManagerMixin\r\n  ],\r\n  data() {\r\n    // 验证函数和服务实例创建函数已移动到mixins中\r\n\r\n    // 常量配置\r\n    const CONFIG = {\r\n      RCT_SETTINGS: {\r\n        leadingCharacter: \"RCT\",\r\n        GZCFLeadingCharacter: \"CFL\",\r\n        RSWHLeadingCharacter: \"RSH\",\r\n        month: 1,\r\n        noNum: 1,\r\n        rctNo: null\r\n      },\r\n      SERVICE_SETS: {\r\n        SEA: new Set(),\r\n        SEAFCL: new Set(),\r\n        SEALCL: new Set(),\r\n        AIR: new Set(),\r\n        RAIL: new Set(),\r\n        EXPRESS: new Set(),\r\n        TRUCK: new Set(),\r\n        CUSTOM: new Set(),\r\n        CLEAR: new Set(),\r\n        WHS: new Set(),\r\n        EXTEND: new Set()\r\n      }\r\n    }\r\n\r\n    return {\r\n      isSubmitting: false, // 添加提交状态控制变量\r\n      form: {\r\n        noTransferAllowed: false,\r\n        noDividedAllowed: false,\r\n        noAgreementShowed: false,\r\n        isCustomsIntransitShowed: false,\r\n        rsOpSeaFclList: [],\r\n        rsOpSeaLclList: [],\r\n        rsOpAirList: [],\r\n        rsOpCtnrTruckList: [],\r\n        rsOpBulkTruckList: [],\r\n        rsOpDocDeclareList: [],\r\n        rsOpFreeDeclareList: []\r\n      },\r\n      statusForm: {},\r\n      // rules已移动到formValidatorMixin中\r\n      rct: {...CONFIG.RCT_SETTINGS},\r\n\r\n      // 为了兼容模板中的直接引用，保持根级别的属性\r\n      psaVerify: false,\r\n      op: false,\r\n      finance: false,\r\n      booking: false,\r\n      openDocList: false,\r\n      openGenerateRevenueTons: false,\r\n      openBookingMessage: false,\r\n      openFreightSelect: false,\r\n      openCompanySelect: false,\r\n      openCommonUsedSelect: false,\r\n      openPsaBookingSelect: false,\r\n      openGenerateRct: false,\r\n\r\n      // UI面板显示状态（兼容模板直接引用）\r\n      basicInfo: true,\r\n      orderInfo: true,\r\n      branchInfo: true,\r\n      serviceInfo: false,\r\n      logisticsInfo: false,\r\n      docInfo: false,\r\n      chargeInfo: false,\r\n      auditInfo: false,\r\n      clientMessage: true,\r\n      basicLogistics: false,\r\n      preCarriage: false,\r\n      exportDeclaration: false,\r\n      importClearance: false,\r\n\r\n      // 服务折叠状态（兼容模板直接引用）\r\n      rsOpSealFclFold: false,\r\n      otherFold: true,\r\n      rsOpSealLclFold: false,\r\n      rsOpBulkShipFold: false,\r\n      rsOpRoroShipFold: false,\r\n      rsOpAirFold: false,\r\n      rsOpRailFold: false,\r\n      rsOpRailFclFold: false,\r\n      rsOpRailLclFold: false,\r\n      rsOpExpressFold: false,\r\n      rsOpPortServiceFold: false,\r\n      rsOpExportTruckFold: false,\r\n      rsOpCtnrTruckFold: false,\r\n      rsOpBulkTruckFold: false,\r\n      rsOpExportCustomsClearanceFold: false,\r\n      rsOpDocDeclareFold: false,\r\n      rsOpDOAgentFold: false,\r\n      rsOpClearAgentFold: false,\r\n      rsOpFreeDeclareFold: false,\r\n      rsOpImportCustomsClearanceFold: false,\r\n      rsOpImportDispatchTruckFold: false,\r\n      rsOpWarehouseFold: false,\r\n      rsOpInspectionAndCertificateFold: false,\r\n      rsOpLandFold: false,\r\n      rsOpInsuranceFold: false,\r\n      rsOpExpandServiceFold: false,\r\n      rsOpWHSFold: false,\r\n      rsOp3rdCertFold: false,\r\n      rsOpINSFold: false,\r\n      rsOpTradingFold: false,\r\n      rsOpFumigationFold: false,\r\n      rsOpCOFold: false,\r\n      rsOpOtherFold: false,\r\n\r\n      // 财务数据变量（兼容模板直接引用）\r\n      rsClientMessageReceivable: 0,\r\n      rsClientMessageReceivableRMB: 0,\r\n      rsClientMessageReceivableUSD: 0,\r\n      rsClientMessageReceivableTaxRMB: 0,\r\n      rsClientMessageReceivableTaxUSD: 0,\r\n      rsClientMessagePayable: 0,\r\n      rsClientMessageChargeData: {},\r\n      rsClientMessagePayableRMB: 0,\r\n      rsClientMessagePayableUSD: 0,\r\n      rsClientMessagePayableTaxRMB: 0,\r\n      rsClientMessagePayableTaxUSD: 0,\r\n      sqdUnreceivedRmbSum: 0,\r\n      sqdUnreceivedUsdSum: 0,\r\n      sqdUnpaidRmbSum: 0,\r\n      sqdUnpaidUsdSum: 0,\r\n      rsClientMessageProfitRMB: 0,\r\n      rsClientMessageProfitUSD: 0,\r\n      rsBasicLogisticsPayable: 0,\r\n      rsPrecarriagePayable: 0,\r\n      rsExportCustomsPayable: 0,\r\n      rsImportCustomsPayable: 0,\r\n      rsClientMessageProfitTaxRMB: 0,\r\n      rsClientMessageProfitTaxUSD: 0,\r\n\r\n      // 各项服务应付变量（兼容模板直接引用）\r\n      rsOpSeaFclPayable: 0,\r\n      rsOpSeaLclPayable: 0,\r\n      rsOpOtherPayableUSD: 0,\r\n      rsOpOtherPayableRMB: 0,\r\n      rsOpBulkShipPayable: 0,\r\n      rsOpRoroShipPayable: 0,\r\n      rsOpAirPayableRMB: 0,\r\n      rsOpAirPayableUSD: 0,\r\n      rsOpRailPayable: 0,\r\n      rsOpRailFclPayableRMB: 0,\r\n      rsOpRailFclPayableUSD: 0,\r\n      rsOpRailLclPayableRMB: 0,\r\n      rsOpRailLclPayableUSD: 0,\r\n      rsOpExpressPayableRMB: 0,\r\n      rsOpExpressPayableUSD: 0,\r\n      rsOpPortServicePayable: 0,\r\n      rsOpExportTruckPayable: 0,\r\n      rsOpExportCustomsClearancePayable: 0,\r\n      rsOpImportCustomsClearancePayable: 0,\r\n      rsOpImportDispatchTruckPayable: 0,\r\n      rsOpWarehousePayable: 0,\r\n      rsOpInspectionAndCertificatePayable: 0,\r\n      rsOpLandPayable: 0,\r\n      rsOpInsurancePayable: 0,\r\n      rsOpCtnrTruckPayableRMB: 0,\r\n      rsOpCtnrTruckPayableUSD: 0,\r\n      rsOpBulkTruckPayableRMB: 0,\r\n      rsOpBulkTruckPayableUSD: 0,\r\n      rsOpDocDeclarePayable: 0,\r\n      rsOpFreeDeclarePayable: 0,\r\n      rsOpDOAgentPayableRMB: 0,\r\n      rsOpDOAgentPayableUSD: 0,\r\n      rsOpClearAgentPayableRMB: 0,\r\n      rsOpClearAgentPayableUSD: 0,\r\n      rsOpWHSPayableRMB: 0,\r\n      rsOpWHSPayableUSD: 0,\r\n      rsOp3rdCertPayableRMB: 0,\r\n      rsOp3rdCertPayableUSD: 0,\r\n      rsOpINSPayableRMB: 0,\r\n      rsOpINSPayableUSD: 0,\r\n      rsOpTradingPayableRMB: 0,\r\n      rsOpTradingPayableUSD: 0,\r\n      rsOpFumigationPayableRMB: 0,\r\n      rsOpFumigationPayableUSD: 0,\r\n      rsOpCOPayableRMB: 0,\r\n      rsOpCOPayableUSD: 0,\r\n\r\n      // 表单状态变量（兼容模板直接引用）\r\n      outboundForm: null,\r\n      openOutbound: false,\r\n      outboundData: {},\r\n      formType: null,\r\n      serviceList: new Set(),\r\n      companyList: [],\r\n      grossWeight: null,\r\n      goodsValue: null,\r\n      locationOptions: [],\r\n      logisticsType: null,\r\n      carrierIds: [],\r\n      carrierList: [],\r\n      clientDocList: [],\r\n      logisticsProgressList: [],\r\n      belongList: [],\r\n      businessList: [],\r\n      opList: [],\r\n      // 客户信息数据\r\n      rsClientMessage: {\r\n        rsChargeList: [],\r\n        rsOpLogList: [],\r\n        rsDocList: []\r\n      },\r\n      // new 子服务数据\r\n      rsOpSeaFcl: createServiceObject(),\r\n      rsOpSeaLcl: createServiceObject(),\r\n      rsOpAir: createServiceObject(),\r\n      rsOpRailFCL: createServiceObject(false),\r\n      rsOpRailLCL: createServiceObject(false),\r\n      rsOpRail: createServiceObject(false),\r\n      rsOpExpress: createServiceObject(false),\r\n      rsOpTruck: createServiceObject(false),\r\n      rsOpCtnrTruck: createServiceObject(true, {rsOpTruckList: []}),\r\n      rsOpBulkTruck: createServiceObject(true, {rsOpTruckList: []}),\r\n      // 正单报关\r\n      rsOpDocDeclare: createServiceObject(),\r\n      // 全包报关\r\n      rsOpFreeDeclare: createServiceObject(),\r\n      rsOpImportDispatchTruck: createServiceObject(false),\r\n      // 代理放单\r\n      rsOpDOAgent: createServiceObject(false),\r\n      rsOpClearAgent: createServiceObject(),\r\n      rsOpWHS: createServiceObject(),\r\n      rsOpWarehouse: createServiceObject(),\r\n      rsOpInsurance: createServiceObject(),\r\n      rsOpExpandService: createServiceObject(),\r\n      rsOp3rdCert: createServiceObject(),\r\n      rsOpINS: createServiceObject(),\r\n      rsOpTrading: createServiceObject(),\r\n      rsOpFumigation: createServiceObject(),\r\n      rsOpCO: createServiceObject(),\r\n      rsOpOther: createServiceObject(),\r\n      // new 服务实例\r\n      rsClientServiceInstance: createServiceInstance(),\r\n      rsOpSeaFclServiceInstance: createServiceInstance(),\r\n      rsOpSeaLclServiceInstance: createServiceInstance(),\r\n      rsOpAirServiceInstance: createServiceInstance(),\r\n      rsOpRailFclServiceInstance: createServiceInstance(),\r\n      rsOpRailLclServiceInstance: createServiceInstance(),\r\n      rsOpExpressServiceInstance: createServiceInstance(),\r\n      rsOpCtnrTruckServiceInstance: createServiceInstance(),\r\n      rsOpBulkTruckServiceInstance: createServiceInstance(),\r\n      rsOpDocDeclareServiceInstance: createServiceInstance(),\r\n      rsOpFreeDeclareServiceInstance: createServiceInstance(),\r\n      rsOpDOAgentServiceInstance: createServiceInstance(),\r\n      rsOpClearAgentServiceInstance: createServiceInstance(),\r\n      rsOpWHSServiceInstance: createServiceInstance(),\r\n      rsOp3rdCertServiceInstance: createServiceInstance(),\r\n      rsOpINSServiceInstance: createServiceInstance(),\r\n      rsOpTradingServiceInstance: createServiceInstance(),\r\n      rsOpFumigationServiceInstance: createServiceInstance(),\r\n      rsOpCOServiceInstance: createServiceInstance(),\r\n      rsOpOtherServiceInstance: createServiceInstance(),\r\n      list: new Set(),\r\n\r\n      chargeOpen: false,\r\n      chargeSearchData: {},\r\n      chargeSelectItem: null,\r\n\r\n      // 客户信息中的审核信息\r\n      opConfirmedName: null,\r\n      opConfirmedDate: null,\r\n      accountConfirmedName: null,\r\n      accountConfirmedDate: null,\r\n      psaConfirmedName: null,\r\n      psaConfirmedDate: null,\r\n      salesConfirmedName: null,\r\n      salesConfirmedDate: null,\r\n      clientConfirmedName: null,\r\n      clientConfirmedDate: null,\r\n      RelationClientIdList: [],\r\n      PaymentTitleCode: null,\r\n      salesId: null,\r\n      salesAssistantId: null,\r\n      salesObserverId: null,\r\n      verifyPsaId: null,\r\n      bookingOpId: null,\r\n      opId: null,\r\n      docOpId: null,\r\n      opObserverId: null,\r\n      carrierId: null,\r\n      basicServiceName: [],\r\n      RelationClientList: [],\r\n      relationClientLists: [],\r\n      basicServiceId: [],\r\n      freightSelectData: {},\r\n      curFreightSelectRow: {},\r\n      psaBookingSelectData: {},\r\n      commonUsedSelectData: {},\r\n      commonUsedType: null,\r\n      ...CONFIG.SERVICE_SETS,\r\n      bookingMessageTitle: null,\r\n      supplierList: [],\r\n      bookingMessageList: [],\r\n      bookingMessageForm: {},\r\n      bookingBillPrintRow: null,\r\n      bookingMessageStatus: null,\r\n      selectedPrintCharges: [],\r\n      ctnrTypeCodeIds: [],\r\n      cargoTypeCodes: [],\r\n      showPsaRct: false,\r\n      rsServiceInstances: {\r\n        accountConfirmTime: null,\r\n        agreementNo: \"\",\r\n        agreementTypeCode: null,\r\n        clientConfirmedTime: null,\r\n        confirmAccountId: null,\r\n        createBy: null,\r\n        createByName: null,\r\n        createTime: null,\r\n        deleteBy: null,\r\n        deleteByName: null,\r\n        deleteStatus: null,\r\n        deleteTime: null,\r\n        inquiryInnerRemark: \"\",\r\n        inquiryLeatestUpdatedTime: null,\r\n        inquiryNo: null,\r\n        inquiryNotice: null,\r\n        inquiryPsaId: null,\r\n        isAccountConfirmed: null,\r\n        isDnClientConfirmed: null,\r\n        isDnOpConfirmed: null,\r\n        isDnPsaConfirmed: null,\r\n        isDnSalesConfirmed: null,\r\n        isDnSupplierConfirmed: null,\r\n        logisticsPaymentTermsCode: null,\r\n        maxWeight: null,\r\n        opConfirmedTime: null,\r\n        paymentTitleCode: null,\r\n        permissionLevel: null,\r\n        psaConfirmedTime: null,\r\n        rctId: null,\r\n        rctNo: null,\r\n        remark: null,\r\n        salesConfirmedTime: null,\r\n        serviceBelongTo: null,\r\n        serviceId: null,\r\n        serviceTypeId: null,\r\n        supplierConfirmedTime: null,\r\n        supplierContact: null,\r\n        supplierId: null,\r\n        supplierName: null,\r\n        supplierSummary: null,\r\n        supplierTel: null,\r\n        updateBy: null,\r\n        updateByName: null,\r\n        updateTime: null,\r\n        serviceFold: false\r\n      },\r\n      curPsaRow: null\r\n    }\r\n  },\r\n  watch: {\r\n    \"form.logisticsTypeId\"(n) {\r\n      if (this.$store.state.data.serviceTypeList.length == 0 || this.$store.state.data.redisList.serviceType) {\r\n        store.dispatch(\"getServiceTypeList\").then(() => {\r\n          this.getType(n)\r\n        })\r\n      } else {\r\n        this.getType(n)\r\n      }\r\n    },\r\n    \"rsClientMessage.rsChargeList\": {\r\n      handler(n, rsOpService) {\r\n        this._debouncedCalculateFinancials(n)\r\n      },\r\n      immediate: false\r\n    },\r\n    \"form.rsOpSeaFclList\"(n) {\r\n      this._updateServicePayables(n)\r\n    },\r\n    \"form.rsOpSeaLclList\"(n) {\r\n      this._updateServicePayables(n)\r\n    },\r\n    \"form.rsOpOther.rsChargeList\"(n) {\r\n      const result = this._calculateChargeAmount(n)\r\n      this.rsOpOtherPayableRMB = result.rmbTaxTotal\r\n      this.rsOpOtherPayableUSD = result.usdTaxTotal\r\n    },\r\n    \"form.rsOpAirList\"(n) {\r\n      this._updateServicePayables(n)\r\n    },\r\n    \"rsOpRailFCL.rsChargeList\"(n) {\r\n      const result = this._calculateChargeAmount(n)\r\n      this.rsOpRailFclPayableRMB = result.rmbTaxTotal\r\n      this.rsOpRailFclPayableUSD = result.usdTaxTotal\r\n    },\r\n    \"rsOpRailLCL.rsChargeList\"(n) {\r\n      const result = this._calculateChargeAmount(n)\r\n      this.rsOpRailLclPayableRMB = result.rmbTaxTotal\r\n      this.rsOpRailLclPayableUSD = result.usdTaxTotal\r\n    },\r\n    \"rsOpExpress.rsChargeList\"(n) {\r\n      const result = this._calculateChargeAmount(n)\r\n      this.rsOpExpressPayableRMB = result.rmbTaxTotal\r\n      this.rsOpExpressPayableUSD = result.usdTaxTotal\r\n    },\r\n    \"form.rsOpCtnrTruckList\"(n) {\r\n      this._updateServicePayables(n)\r\n    },\r\n    \"form.rsOpBulkTruckList\"(n) {\r\n      this._updateServicePayables(n)\r\n    },\r\n    \"form.rsOpDocDeclareList\"(n) {\r\n      this._updateServicePayables(n)\r\n    },\r\n    \"form.rsOpFreeDeclareList\"(n) {\r\n      this._updateServicePayables(n)\r\n    },\r\n    \"rsOpDOAgent.rsChargeList\"(n) {\r\n      const result = this._calculateChargeAmount(n)\r\n      this.rsOpDOAgentPayableRMB = result.rmbTaxTotal\r\n      this.rsOpDOAgentPayableUSD = result.usdTaxTotal\r\n    },\r\n    \"rsOpClearAgent.rsChargeList\"(n) {\r\n      const result = this._calculateChargeAmount(n)\r\n      this.rsOpClearAgentPayableRMB = result.rmbTaxTotal\r\n      this.rsOpClearAgentPayableUSD = result.usdTaxTotal\r\n    },\r\n    \"rsOpWHS.rsChargeList\"(n) {\r\n      const result = this._calculateChargeAmount(n)\r\n      this.rsOpWHSPayableRMB = result.rmbTaxTotal\r\n      this.rsOpWHSPayableUSD = result.usdTaxTotal\r\n    },\r\n    \"rsOp3rdCert.rsChargeList\"(n) {\r\n      const result = this._calculateChargeAmount(n)\r\n      this.rsOp3rdCertPayableRMB = result.rmbTaxTotal\r\n      this.rsOp3rdCertPayableUSD = result.usdTaxTotal\r\n    },\r\n    \"rsOpINS.rsChargeList\"(n) {\r\n      const result = this._calculateChargeAmount(n)\r\n      this.rsOpINSPayableRMB = result.rmbTaxTotal\r\n      this.rsOpINSPayableUSD = result.usdTaxTotal\r\n    },\r\n    \"rsOpTrading.rsChargeList\"(n) {\r\n      const result = this._calculateChargeAmount(n)\r\n      this.rsOpTradingPayableRMB = result.rmbTaxTotal\r\n      this.rsOpTradingPayableUSD = result.usdTaxTotal\r\n    },\r\n    \"rsOpFumigation.rsChargeList\"(n) {\r\n      const result = this._calculateChargeAmount(n)\r\n      this.rsOpFumigationPayableRMB = result.rmbTaxTotal\r\n      this.rsOpFumigationPayableUSD = result.usdTaxTotal\r\n    },\r\n    \"rsOpCO.rsChargeList\"(n) {\r\n      const result = this._calculateChargeAmount(n)\r\n      this.rsOpCOPayableRMB = result.rmbTaxTotal\r\n      this.rsOpCOPayableUSD = result.usdTaxTotal\r\n    }\r\n  },\r\n  computed: {\r\n    // 通用表单禁用逻辑检查\r\n    ...(() => {\r\n      const createFormDisableComputed = (serviceInstanceName) => {\r\n        return function () {\r\n          const instance = this[serviceInstanceName]\r\n          if (!instance) return false\r\n\r\n          return !!(\r\n            instance.isDnOpConfirmed ||\r\n            instance.isDnPsaConfirmed ||\r\n            instance.isDnSupplierConfirmed ||\r\n            instance.isAccountConfirmed ||\r\n            instance.isDnClientConfirmed ||\r\n            instance.isDnSalesConfirmed\r\n          )\r\n        }\r\n      }\r\n\r\n      // 服务实例映射表\r\n      const serviceInstanceMap = {\r\n        rsOpSealFclFormDisable: \"rsOpSeaFclServiceInstance\",\r\n        rsOpSealLclFormDisable: \"rsOpSeaLclServiceInstance\",\r\n        rsOpAirFormDisable: \"rsOpAirServiceInstance\",\r\n        rsOpRailFclFormDisable: \"rsOpRailFclServiceInstance\",\r\n        rsOpRailLclFormDisable: \"rsOpRailLclServiceInstance\",\r\n        rsOpExpressFormDisable: \"rsOpExpressServiceInstance\",\r\n        rsOpCtnrTruckFormDisable: \"rsOpCtnrTruckServiceInstance\",\r\n        rsOpBulkTruckFormDisable: \"rsOpBulkTruckServiceInstance\",\r\n        rsOpDocDeclareFormDisable: \"rsOpImportCustomsClearanceServiceInstance\",\r\n        rsOpFreeDeclareFormDisable: \"rsOpExportCustomsClearanceServiceInstance\",\r\n        rsOpDOAgentFormDisable: \"rsOpImportCustomsClearanceServiceInstance\",\r\n        rsOpClearAgentFormDisable: \"rsOpClearAgentServiceInstance\",\r\n        rsOpWHSFormDisable: \"rsOpWHSServiceInstance\",\r\n        rsClientMessageFormDisable: \"rsClientServiceInstance\",\r\n        rsOp3rdCertFormDisable: \"rsOp3rdCertServiceInstance\",\r\n        rsOpINSFormDisable: \"rsOpINSServiceInstance\",\r\n        rsOpTradingFormDisable: \"rsOpTradingServiceInstance\",\r\n        rsOpCOFormDisable: \"rsOpCOServiceInstance\"\r\n      }\r\n\r\n      // 动态生成所有FormDisable计算属性\r\n      const result = {}\r\n      Object.keys(serviceInstanceMap).forEach(computedName => {\r\n        result[computedName] = createFormDisableComputed(serviceInstanceMap[computedName])\r\n      })\r\n\r\n      return result\r\n    })(),\r\n\r\n    // 操作单号禁用逻辑（特殊逻辑，保持独立）\r\n    rctNoDisable() {\r\n      if (!this.form.rctNo || !this.form.createTime) return false\r\n\r\n      const today = new Date()\r\n      const currentDateStr = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, \"0\")}-${today.getDate().toString().padStart(2, \"0\")}`\r\n      const formDateStr = this.form.createTime.split(\" \")[0]\r\n\r\n      return formDateStr !== currentDateStr\r\n    },\r\n    disabled() {\r\n      if (this.form.opAccept == 0 && this.op) {\r\n        return true\r\n      }\r\n      if (this.booking && this.form.sqdShippingBookingStatus == 1) {\r\n        return true\r\n      }\r\n      return false\r\n    },\r\n    // 拓展服务列表\r\n    extendServiceList() {\r\n      const extendServices = []\r\n\r\n      // 根据serviceTypeId映射拓展服务数据\r\n      const extendServiceMap = {\r\n        90: { // 3rdCert\r\n          data: this.rsOp3rdCert,\r\n          serviceShortName: \"第三方认证\",\r\n          serviceFold: this.rsOp3rdCertFold\r\n        },\r\n        100: { // Insurance\r\n          data: this.rsOpINS,\r\n          serviceShortName: \"保险\",\r\n          serviceFold: this.rsOpINSFold\r\n        },\r\n        101: { // Trading\r\n          data: this.rsOpTrading,\r\n          serviceShortName: \"贸易\",\r\n          serviceFold: this.rsOpTradingFold\r\n        },\r\n        102: { // Fumigation\r\n          data: this.rsOpFumigation,\r\n          serviceShortName: \"熏蒸\",\r\n          serviceFold: this.rsOpFumigationFold\r\n        },\r\n        103: { // CO\r\n          data: this.rsOpCO,\r\n          serviceShortName: \"产地证\",\r\n          serviceFold: this.rsOpCOFold\r\n        },\r\n        104: { // Other\r\n          data: this.rsOpOther,\r\n          serviceShortName: \"其他\",\r\n          serviceFold: this.rsOpOtherFold\r\n        }\r\n      }\r\n\r\n      // 遍历EXTEND集合，构建拓展服务列表\r\n      Array.from(this.EXTEND).forEach(item => {\r\n        const serviceConfig = extendServiceMap[item.serviceTypeId]\r\n        if (serviceConfig && serviceConfig.data) {\r\n          extendServices.push({\r\n            serviceTypeId: item.serviceTypeId,\r\n            serviceShortName: serviceConfig.serviceShortName,\r\n            serviceFold: serviceConfig.serviceFold,\r\n            rsServiceInstances: serviceConfig.data.rsServiceInstances || {},\r\n            rsChargeList: serviceConfig.data.rsChargeList || [],\r\n            rsOpLogList: serviceConfig.data.rsOpLogList || [],\r\n            rsDocList: serviceConfig.data.rsDocList || [],\r\n            payableRMB: serviceConfig.data.payableRMB || 0,\r\n            payableUSD: serviceConfig.data.payableUSD || 0,\r\n            payableRMBTax: serviceConfig.data.payableRMBTax || 0,\r\n            payableUSDTax: serviceConfig.data.payableUSDTax || 0\r\n          })\r\n        }\r\n      })\r\n\r\n      return extendServices\r\n    }\r\n  },\r\n  beforeMount() {\r\n    if (this.$store.state.data.exchangeRateList.length == 0 || this.$store.state.data.redisList.exchangeRateList) {\r\n      store.dispatch(\"getExchangeRate\")\r\n    }\r\n\r\n    if (this.$route.query.type === \"op\") {\r\n      this.op = true\r\n    }\r\n    if (this.$store.state.user.deptNum.has(4)) {\r\n      this.finance = true\r\n    }\r\n    if (this.$route.query.psaVerify) {\r\n      this.psaVerify = true\r\n    }\r\n    if (this.$route.query.booking) {\r\n      this.booking = true\r\n    }\r\n\r\n    this.loadSelection()\r\n    this.reset()\r\n\r\n    this.formType = \"rct\"\r\n    // 如果是来自于操作单列表修改,则会通过路由传递操作单id--rId\r\n    if (this.$route.query.rId) {\r\n      this.formType = \"rct\"\r\n      this.getRctDetail(this.$route.query.rId).then(() => {\r\n      })\r\n    } else if (this.$route.query.bId) {\r\n      // 订舱单\r\n      this.formType = \"booking\"\r\n      this.getBookingDetail(this.$route.query.bId).then(() => {\r\n      })\r\n    } else if (this.$route.query.id) {\r\n      // 如果是来自报价列表的订舱申请\r\n      this.formType = \"booking\"\r\n      this.getQuotation(this.$route.query.id)\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化防抖函数\r\n    this._debouncedCalculateFinancials = _.debounce(this._calculateFinancials, 300)\r\n\r\n    // 初始化服务管理器\r\n    this.serviceManager = this._createServiceManager()\r\n\r\n    /**\r\n     * 监听悬浮拖拽区域\r\n     */\r\n    this.$nextTick(() => {\r\n      // 获取DOM元素\r\n      let dragArea = this.$refs.dragArea\r\n      // 缓存 clientX clientY 的对象: 用于判断是点击事件还是移动事件\r\n      let clientOffset = {}\r\n      // 绑定鼠标按下事件\r\n      dragArea.addEventListener(\"mousedown\", (event) => {\r\n        let offsetX = dragArea.getBoundingClientRect().left // 获取当前的x轴距离\r\n        let offsetY = dragArea.getBoundingClientRect().top // 获取当前的y轴距离\r\n        let innerX = event.clientX - offsetX // 获取鼠标在方块内的x轴距\r\n        let innerY = event.clientY - offsetY // 获取鼠标在方块内的y轴距\r\n        // console.log(offsetX, offsetY, innerX, innerY);\r\n        // 缓存 clientX clientY\r\n        clientOffset.clientX = event.clientX\r\n        clientOffset.clientY = event.clientY\r\n        // 鼠标移动的时候不停的修改div的left和top值\r\n        document.onmousemove = function (event) {\r\n          dragArea.style.left = event.clientX - innerX + \"px\"\r\n          dragArea.style.top = event.clientY - innerY + \"px\"\r\n          // dragArea 距离顶部的距离\r\n          let dragAreaTop = window.innerHeight - dragArea.getBoundingClientRect().height\r\n          // dragArea 距离左部的距离\r\n          let dragAreaLeft = window.innerWidth - dragArea.getBoundingClientRect().width\r\n          // 边界判断处理\r\n          // 1、设置左右不能动\r\n          // dragArea.style.left = dragAreaLeft + \"px\";\r\n\r\n          // 1.设置左侧边界\r\n          if (dragArea.getBoundingClientRect().left <= 0) {\r\n            dragArea.style.left = \"0px\"\r\n          }\r\n          // 2.设置右侧边界\r\n          if (dragArea.getBoundingClientRect().left >= dragAreaLeft) {\r\n            dragArea.style.left = dragAreaLeft + \"px\"\r\n          }\r\n          // 3、设置顶部边界\r\n          if (dragArea.getBoundingClientRect().top <= 0) {\r\n            dragArea.style.top = \"0px\"\r\n          }\r\n          // 4、设置底部边界\r\n          if (dragArea.getBoundingClientRect().top >= dragAreaTop) {\r\n            dragArea.style.top = dragAreaTop + \"px\"\r\n          }\r\n        }\r\n        // 鼠标抬起时，清除绑定在文档上的mousemove和mouseup事件；否则鼠标抬起后还可以继续拖拽方块\r\n        document.onmouseup = function () {\r\n          document.onmousemove = null\r\n          document.onmouseup = null\r\n        }\r\n      }, false)\r\n      // 绑定鼠标松开事件\r\n      dragArea.addEventListener(\"mouseup\", (event) => {\r\n        let clientX = event.clientX\r\n        let clientY = event.clientY\r\n        /* if (clientX === clientOffset.clientX && clientY === clientOffset.clientY) {\r\n          console.log('click 事件');\r\n        } else {\r\n          console.log('drag 事件');\r\n        } */\r\n      })\r\n    })\r\n\r\n    // 初始化打印\r\n    this.initPrint()\r\n  },\r\n  methods: {\r\n    // 服务管理器 - 统一管理所有服务类型相关操作\r\n    _createServiceManager() {\r\n      return {\r\n        // 服务类型配置映射表\r\n        serviceTypeMap: {\r\n          1: {\r\n            property: \"rsOpSeaFcl\",\r\n            listProperty: \"rsOpSeaFclList\",\r\n            template: \"rsOpSeaFcl\",\r\n            instance: \"rsOpSeaFclServiceInstance\",\r\n            fold: \"rsOpSealFclFold\",\r\n            payable: \"rsOpSeaFclPayable\"\r\n          },\r\n          2: {\r\n            property: \"rsOpSeaLcl\",\r\n            listProperty: \"rsOpSeaLclList\",\r\n            template: \"rsOpSeaLcl\",\r\n            instance: \"rsOpSeaLclServiceInstance\",\r\n            fold: \"rsOpSealLclFold\",\r\n            payable: \"rsOpSeaLclPayable\"\r\n          },\r\n          10: {\r\n            property: \"rsOpAir\",\r\n            listProperty: \"rsOpAirList\",\r\n            template: \"rsOpAir\",\r\n            instance: \"rsOpAirServiceInstance\",\r\n            fold: \"rsOpAirFold\",\r\n            payable: \"rsOpAirPayable\"\r\n          },\r\n          20: {\r\n            property: \"rsOpRailFCL\",\r\n            listProperty: null,\r\n            template: \"rsOpRailFCL\",\r\n            instance: \"rsOpRailFclServiceInstance\",\r\n            fold: \"rsOpRailFclFold\",\r\n            payable: \"rsOpRailFclPayable\"\r\n          },\r\n          21: {\r\n            property: \"rsOpRailLCL\",\r\n            listProperty: null,\r\n            template: \"rsOpRailLCL\",\r\n            instance: \"rsOpRailLclServiceInstance\",\r\n            fold: \"rsOpRailLclFold\",\r\n            payable: \"rsOpRailLclPayable\"\r\n          },\r\n          40: {\r\n            property: \"rsOpExpress\",\r\n            listProperty: null,\r\n            template: \"rsOpExpress\",\r\n            instance: \"rsOpExpressServiceInstance\",\r\n            fold: \"rsOpExpressFold\",\r\n            payable: \"rsOpExpressPayable\"\r\n          },\r\n          50: {\r\n            property: \"rsOpCtnrTruck\",\r\n            listProperty: \"rsOpCtnrTruckList\",\r\n            template: \"rsOpCtnrTruck\",\r\n            instance: \"rsOpCtnrTruckServiceInstance\",\r\n            fold: \"rsOpCtnrTruckFold\",\r\n            payable: \"rsOpCtnrTruckPayable\"\r\n          },\r\n          51: {\r\n            property: \"rsOpBulkTruck\",\r\n            listProperty: \"rsOpBulkTruckList\",\r\n            template: \"rsOpBulkTruck\",\r\n            instance: \"rsOpBulkTruckServiceInstance\",\r\n            fold: \"rsOpBulkTruckFold\",\r\n            payable: \"rsOpBulkTruckPayable\"\r\n          },\r\n          60: {\r\n            property: \"rsOpDocDeclare\",\r\n            listProperty: \"rsOpDocDeclareList\",\r\n            template: \"rsOpDocDeclare\",\r\n            instance: \"rsOpImportCustomsClearanceServiceInstance\",\r\n            fold: \"rsOpDocDeclareFold\",\r\n            payable: \"rsOpDocDeclarePayable\"\r\n          },\r\n          61: {\r\n            property: \"rsOpFreeDeclare\",\r\n            listProperty: \"rsOpFreeDeclareList\",\r\n            template: \"rsOpFreeDeclare\",\r\n            instance: \"rsOpExportCustomsClearanceServiceInstance\",\r\n            fold: \"rsOpFreeDeclareFold\",\r\n            payable: \"rsOpFreeDeclarePayable\"\r\n          },\r\n          70: {\r\n            property: \"rsOpDOAgent\",\r\n            listProperty: null,\r\n            template: \"rsOpDOAgent\",\r\n            instance: \"rsOpImportCustomsClearanceServiceInstance\",\r\n            fold: \"rsOpDOAgentFold\",\r\n            payable: \"rsOpDOAgentServiceInstance\"\r\n          },\r\n          71: {\r\n            property: \"rsOpClearAgent\",\r\n            listProperty: null,\r\n            template: \"rsOpClearAgent\",\r\n            instance: \"rsOpClearAgentServiceInstance\",\r\n            fold: \"rsOpClearAgentFold\",\r\n            payable: \"rsOpClearAgentPayable\"\r\n          },\r\n          80: {\r\n            property: \"rsOpWHS\",\r\n            listProperty: null,\r\n            template: \"rsOpWHS\",\r\n            instance: \"rsOpWHSServiceInstance\",\r\n            fold: \"rsOpWHSFold\",\r\n            payable: \"rsOpWHSPayable\"\r\n          },\r\n          90: {\r\n            property: \"rsOp3rdCert\",\r\n            listProperty: null,\r\n            template: \"rsOp3rdCert\",\r\n            instance: \"rsOp3rdCertServiceInstance\",\r\n            fold: \"rsOp3rdCertFold\",\r\n            payable: \"rsOp3rdCertPayable\"\r\n          },\r\n          100: {\r\n            property: \"rsOpINS\",\r\n            listProperty: null,\r\n            template: \"rsOpINS\",\r\n            instance: \"rsOpINSServiceInstance\",\r\n            fold: \"rsOpINSFold\",\r\n            payable: \"rsOpINSPayable\"\r\n          },\r\n          101: {\r\n            property: \"rsOpTrading\",\r\n            listProperty: null,\r\n            template: \"rsOpTrading\",\r\n            instance: \"rsOpTradingServiceInstance\",\r\n            fold: \"rsOpTradingFold\",\r\n            payable: \"rsOpTradingPayable\"\r\n          },\r\n          102: {\r\n            property: \"rsOpFumigation\",\r\n            listProperty: null,\r\n            template: \"rsOpFumigation\",\r\n            instance: \"rsOpFumigationServiceInstance\",\r\n            fold: \"rsOpFumigationFold\",\r\n            payable: \"rsOpFumigationPayable\"\r\n          },\r\n          103: {\r\n            property: \"rsOpCO\",\r\n            listProperty: null,\r\n            template: \"rsOpCO\",\r\n            instance: \"rsOpCOServiceInstance\",\r\n            fold: \"rsOpCOFold\",\r\n            payable: \"rsOpCOPayable\"\r\n          },\r\n          104: {\r\n            property: \"rsOpOther\",\r\n            listProperty: null,\r\n            template: \"rsOpOther\",\r\n            instance: \"rsOpOtherServiceInstance\",\r\n            fold: \"rsOpOtherFold\",\r\n            payable: \"rsOpOtherPayable\"\r\n          }\r\n        },\r\n\r\n        // 获取服务配置\r\n        getConfig(serviceTypeId) {\r\n          return this.serviceTypeMap[serviceTypeId] || null\r\n        },\r\n\r\n        // 获取服务对象\r\n        getServiceObject(serviceTypeId, context) {\r\n          const config = this.getConfig(serviceTypeId)\r\n          return config ? context[config.property] : null\r\n        },\r\n\r\n        // 获取服务实例\r\n        getServiceInstance(serviceTypeId, context) {\r\n          const config = this.getConfig(serviceTypeId)\r\n          return config ? context[config.instance] : null\r\n        },\r\n\r\n        // 获取应付对象\r\n        getPayable(serviceTypeId, context) {\r\n          const config = this.getConfig(serviceTypeId)\r\n          return config && config.payable ? context[config.payable] : null\r\n        },\r\n\r\n        // 切换折叠状态\r\n        toggleFold(serviceTypeId, context) {\r\n          const config = this.getConfig(serviceTypeId)\r\n          if (config && config.fold) {\r\n            context[config.fold] = !context[config.fold]\r\n          }\r\n        },\r\n\r\n        // 获取折叠状态\r\n        getFoldState(serviceTypeId, context) {\r\n          const config = this.getConfig(serviceTypeId)\r\n          return config && config.fold ? context[config.fold] : false\r\n        },\r\n\r\n        // 批量处理服务实例\r\n        processServiceInstances(serviceList, context) {\r\n          serviceList.forEach(serviceTypeId => {\r\n            const config = this.getConfig(serviceTypeId)\r\n            if (config && context.serviceList.has(serviceTypeId)) {\r\n              if (context[config.property] && context[config.instance]) {\r\n                context[config.property].rsServiceInstances = context[config.instance]\r\n                context.form[config.property] = context[config.property]\r\n              }\r\n            }\r\n          })\r\n        },\r\n\r\n        // 获取所有配置的服务类型ID\r\n        getAllServiceTypeIds() {\r\n          return Object.keys(this.serviceTypeMap).map(id => parseInt(id))\r\n        }\r\n      }\r\n    },\r\n\r\n    // 通用服务删除函数\r\n    _createServiceDeleteHandler(listProperty, extraValidation = null, warningMessage = \"请先删除相关费用\") {\r\n      return (item) => {\r\n        // 通用验证：检查费用列表\r\n        if (item.rsChargeList && item.rsChargeList.length > 0) {\r\n          this.$message.warning(warningMessage)\r\n          return\r\n        }\r\n\r\n        // 额外验证（如检查PSA订舱号）\r\n        if (extraValidation && extraValidation(item)) {\r\n          this.$message.warning(\"请先删除相关费用或取消订舱\")\r\n          return\r\n        }\r\n\r\n        // 执行删除\r\n        this.form[listProperty] = this.form[listProperty].filter(v => v !== item)\r\n      }\r\n    },\r\n\r\n    // 通用服务添加函数\r\n    _createServiceAddHandler(serviceTemplateProperty, listProperty) {\r\n      return () => {\r\n        const serviceTemplate = this._.cloneDeep(this[serviceTemplateProperty])\r\n        serviceTemplate.rsChargeList = []\r\n        serviceTemplate.rsOpLogList = []\r\n        serviceTemplate.rsDocList = []\r\n        serviceTemplate.rsServiceInstances = this.rsServiceInstances\r\n        this.form[listProperty].push(serviceTemplate)\r\n      }\r\n    },\r\n\r\n    // 服务配置映射表\r\n    _getServiceConfig() {\r\n      return {\r\n        freeDeclare: {\r\n          listProperty: \"rsOpFreeDeclareList\",\r\n          templateProperty: \"rsOpFreeDeclare\",\r\n          deleteValidation: null\r\n        },\r\n        docDeclare: {\r\n          listProperty: \"rsOpDocDeclareList\",\r\n          templateProperty: \"rsOpDocDeclare\",\r\n          deleteValidation: null\r\n        },\r\n        bulkTruck: {\r\n          listProperty: \"rsOpBulkTruckList\",\r\n          templateProperty: \"rsOpBulkTruck\",\r\n          deleteValidation: null\r\n        },\r\n        ctnrTruck: {\r\n          listProperty: \"rsOpCtnrTruckList\",\r\n          templateProperty: \"rsOpCtnrTruck\",\r\n          deleteValidation: null\r\n        },\r\n        air: {\r\n          listProperty: \"rsOpAirList\",\r\n          templateProperty: \"rsOpAir\",\r\n          deleteValidation: null\r\n        },\r\n        seaLcl: {\r\n          listProperty: \"rsOpSeaLclList\",\r\n          templateProperty: \"rsOpSeaLcl\",\r\n          deleteValidation: (item) => item.sqdPsaNo\r\n        },\r\n        seaFcl: {\r\n          listProperty: \"rsOpSeaFclList\",\r\n          templateProperty: \"rsOpSeaFcl\",\r\n          deleteValidation: (item) => item.sqdPsaNo\r\n        }\r\n      }\r\n    },\r\n\r\n    // ===================== 费用计算函数 =====================\r\n\r\n    // 通用费用计算函数\r\n    _calculateChargeAmount(chargeList) {\r\n      if (!chargeList || !Array.isArray(chargeList)) {\r\n        return {\r\n          rmbTotal: 0,\r\n          usdTotal: 0,\r\n          rmbTaxTotal: 0,\r\n          usdTaxTotal: 0,\r\n          rmbUnpaid: 0,\r\n          usdUnpaid: 0\r\n        }\r\n      }\r\n\r\n      let rmbTotal = 0, usdTotal = 0, rmbTaxTotal = 0, usdTaxTotal = 0, rmbUnpaid = 0, usdUnpaid = 0\r\n\r\n      chargeList.forEach(charge => {\r\n        if (charge.subtotal) {\r\n          const amount = parseFloat(charge.subtotal) || 0\r\n          const taxRate = parseFloat(charge.dutyRate) || 0\r\n          const amountWithoutTax = amount / (1 + taxRate / 100)\r\n          const unpaidBalance = parseFloat(charge.sqdDnCurrencyBalance) || 0\r\n\r\n          if (charge.dnCurrencyCode === \"USD\") {\r\n            usdTotal += amountWithoutTax\r\n            usdTaxTotal += amount\r\n            usdUnpaid += unpaidBalance\r\n          } else {\r\n            rmbTotal += amountWithoutTax\r\n            rmbTaxTotal += amount\r\n            rmbUnpaid += unpaidBalance\r\n          }\r\n        }\r\n      })\r\n\r\n      return {rmbTotal, usdTotal, rmbTaxTotal, usdTaxTotal, rmbUnpaid, usdUnpaid}\r\n    },\r\n\r\n    // 计算服务列表的费用汇总\r\n    _calculateServiceListAmount(serviceList) {\r\n      if (!serviceList || !Array.isArray(serviceList)) {\r\n        return {\r\n          rmbTotal: 0,\r\n          usdTotal: 0,\r\n          rmbUnpaid: 0,\r\n          usdUnpaid: 0,\r\n          rmbTaxTotal: 0,\r\n          usdTaxTotal: 0\r\n        }\r\n      }\r\n\r\n      let rmbTotal = 0, usdTotal = 0, rmbTaxTotal = 0, usdTaxTotal = 0, rmbUnpaid = 0, usdUnpaid = 0\r\n\r\n      serviceList.forEach(service => {\r\n        if (service && service.rsChargeList) {\r\n          const result = this._calculateChargeAmount(service.rsChargeList)\r\n          rmbTotal += result.rmbTotal\r\n          usdTotal += result.usdTotal\r\n          rmbTaxTotal += result.rmbTaxTotal\r\n          usdTaxTotal += result.usdTaxTotal\r\n          rmbUnpaid += result.rmbUnpaid\r\n          usdUnpaid += result.usdUnpaid\r\n        }\r\n      })\r\n\r\n      return {rmbTotal, usdTotal, rmbUnpaid, usdUnpaid, rmbTaxTotal, usdTaxTotal}\r\n    },\r\n\r\n    // 更新服务项目的应付金额\r\n    _updateServicePayables(serviceList) {\r\n      if (!serviceList || !Array.isArray(serviceList)) return\r\n\r\n      serviceList.forEach(service => {\r\n        const result = this._calculateChargeAmount(service.rsChargeList)\r\n        service.payableRMB = result.rmbTaxTotal\r\n        service.payableUSD = result.usdTaxTotal\r\n      })\r\n    },\r\n\r\n    // 财务数据计算主函数\r\n    _calculateFinancials(n) {\r\n      // 使用通用函数计算客户信息应收\r\n      const receivableResult = this._calculateChargeAmount(n)\r\n\r\n      // 更新应收相关数据\r\n      this.rsClientMessageReceivableRMB = receivableResult.rmbTotal\r\n      this.rsClientMessageReceivableUSD = receivableResult.usdTotal\r\n      this.rsClientMessageReceivableTaxRMB = receivableResult.rmbTaxTotal\r\n      this.rsClientMessageReceivableTaxUSD = receivableResult.usdTaxTotal\r\n      this.sqdUnreceivedRmbSum = receivableResult.rmbUnpaid\r\n      this.sqdUnreceivedUsdSum = receivableResult.usdUnpaid\r\n\r\n      // 计算所有服务的应付费用\r\n      const allServiceLists = [\r\n        this.form.rsOpSeaFclList,\r\n        this.form.rsOpSeaLclList,\r\n        this.form.rsOpAirList,\r\n        this.form.rsOpCtnrTruckList,\r\n        this.form.rsOpBulkTruckList,\r\n        this.form.rsOpDocDeclareList,\r\n        this.form.rsOpFreeDeclareList\r\n      ]\r\n\r\n      const singleServices = [\r\n        this.rsOpRailFCL,\r\n        this.rsOpRailLCL,\r\n        this.rsOpExpress,\r\n        this.rsOpDOAgent,\r\n        this.rsOpClearAgent,\r\n        this.rsOpWHS,\r\n        this.rsOp3rdCert,\r\n        this.rsOpINS,\r\n        this.rsOpTrading,\r\n        this.rsOpFumigation,\r\n        this.rsOpCO,\r\n        this.rsOpOther\r\n      ]\r\n\r\n      // 计算服务列表总应付\r\n      let totalPayable = {rmbTotal: 0, usdTotal: 0, rmbTaxTotal: 0, usdTaxTotal: 0, rmbUnpaid: 0, usdUnpaid: 0}\r\n\r\n      allServiceLists.forEach(serviceList => {\r\n        if (serviceList) {\r\n          const result = this._calculateServiceListAmount(serviceList)\r\n          totalPayable.rmbTotal += result.rmbTotal\r\n          totalPayable.usdTotal += result.usdTotal\r\n          totalPayable.rmbTaxTotal += result.rmbTaxTotal\r\n          totalPayable.usdTaxTotal += result.usdTaxTotal\r\n          totalPayable.rmbUnpaid += result.rmbUnpaid\r\n          totalPayable.usdUnpaid += result.usdUnpaid\r\n        }\r\n      })\r\n\r\n      // 计算单个服务应付\r\n      singleServices.forEach(service => {\r\n        if (service && service.rsChargeList) {\r\n          const result = this._calculateChargeAmount(service.rsChargeList)\r\n          totalPayable.rmbTotal += result.rmbTotal\r\n          totalPayable.usdTotal += result.usdTotal\r\n          totalPayable.rmbTaxTotal += result.rmbTaxTotal\r\n          totalPayable.usdTaxTotal += result.usdTaxTotal\r\n          totalPayable.rmbUnpaid += result.rmbUnpaid\r\n          totalPayable.usdUnpaid += result.usdUnpaid\r\n        }\r\n      })\r\n\r\n      // 更新应付相关数据\r\n      this.rsClientMessagePayableRMB = totalPayable.rmbTotal\r\n      this.rsClientMessagePayableUSD = totalPayable.usdTotal\r\n      this.sqdUnpaidRmbSum = totalPayable.rmbUnpaid\r\n      this.sqdUnpaidUsdSum = totalPayable.usdUnpaid\r\n\r\n      // 计算利润（不含税）\r\n      this.rsClientMessageProfitRMB = receivableResult.rmbTotal - totalPayable.rmbTotal\r\n      this.rsClientMessageProfitUSD = receivableResult.usdTotal - totalPayable.usdTotal\r\n\r\n      // 计算利润（含税）\r\n      if (n.length > 0) {\r\n        this.rsClientMessageProfitTaxRMB = receivableResult.rmbTaxTotal - totalPayable.rmbTaxTotal\r\n        this.rsClientMessageProfitTaxUSD = receivableResult.usdTaxTotal - totalPayable.usdTaxTotal\r\n      } else {\r\n        this.rsClientMessageProfitTaxRMB = receivableResult.rmbTotal - totalPayable.rmbTaxTotal\r\n        this.rsClientMessageProfitTaxUSD = receivableResult.usdTotal - totalPayable.usdTaxTotal\r\n      }\r\n      // console.log(receivableResult.rmbTaxTotal,totalPayable)\r\n    },\r\n\r\n    outboundPlan() {\r\n      this.outboundData.rctId = this.form.rctId\r\n      this.outboundData.clientName = this.form.clientSummary.split(\"/\")[1]\r\n      this.outboundData.customerOrderNo = this.form.rctNo\r\n      this.outboundData.outboundType = \"整柜\"\r\n      this.outboundData.operator = this.$store.state.data.allRsStaffList.find(item => item.staffId === this.form.opId).staffGivingEnName || \"\"\r\n      this.outboundData.containerNo = this.form.sqdContainersSealsSum || \"\"\r\n      this.outboundData.orderDate = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n      this.openOutbound = true\r\n    },\r\n    handleStatusChange() {\r\n      // 将进度设置为点击的最新日期\r\n\r\n    },\r\n    handleProfit() {\r\n\r\n    },\r\n    // 将对象属性值转换为大写（保持属性名不变）\r\n    convertObjectKeysToUpperCase(obj) {\r\n      if (!obj || typeof obj !== \"object\") return obj\r\n\r\n      return Object.fromEntries(\r\n        Object.entries(obj).map(([key, value]) => [\r\n          key,\r\n          typeof value === \"string\" ? value.toUpperCase() : value\r\n        ])\r\n      )\r\n    },\r\n    openChargeSelect(serviceObject) {\r\n      this.chargeSelectItem = serviceObject\r\n\r\n      this.chargeSearchData = {\r\n        locationOptions: this.locationOptions,\r\n        polId: this.form.polId,\r\n        destinationPortId: this.form.destinationPortId,\r\n        carrierId: this.form.carrierId,\r\n        supplierId: serviceObject.supplierId,\r\n        supplierList: this.supplierList\r\n      }\r\n\r\n      this.chargeOpen = true\r\n    },\r\n    auditCharge(serviceObject, rsChargeList) {\r\n      this.$set(serviceObject, \"rsChargeList\", rsChargeList)\r\n    },\r\n    // 检查服务实例是否禁用（与computed中的逻辑一致）\r\n    getServiceInstanceDisable(serviceInstance) {\r\n      if (!serviceInstance) return false\r\n\r\n      return !!(\r\n        serviceInstance.isDnOpConfirmed ||\r\n        serviceInstance.isDnPsaConfirmed ||\r\n        serviceInstance.isDnSupplierConfirmed ||\r\n        serviceInstance.isDnClientConfirmed ||\r\n        serviceInstance.isDnSalesConfirmed\r\n      )\r\n    },\r\n    // ===================== 服务删除函数（使用通用工具函数） =====================\r\n    deleteRsOpFreeDeclare(item) {\r\n      const config = this._getServiceConfig().freeDeclare\r\n      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)\r\n    },\r\n    deleteRsOpDocDeclare(item) {\r\n      const config = this._getServiceConfig().docDeclare\r\n      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)\r\n    },\r\n    deleteRsOpBulkTruck(item) {\r\n      const config = this._getServiceConfig().bulkTruck\r\n      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)\r\n    },\r\n    deleteRsOpCtnrTruck(item) {\r\n      const config = this._getServiceConfig().ctnrTruck\r\n      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)\r\n    },\r\n    deleteRsOpLclSea(item) {\r\n      const config = this._getServiceConfig().seaLcl\r\n      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)\r\n    },\r\n    deleteRsOpFclSea(item) {\r\n      const config = this._getServiceConfig().seaFcl\r\n      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)\r\n    },\r\n    deleteRsOpAir(item) {\r\n      const config = this._getServiceConfig().air\r\n      return this._createServiceDeleteHandler(config.listProperty, config.deleteValidation)(item)\r\n    },\r\n    updateServiceInstance(serviceInstance) {\r\n      updateServiceinstances(serviceInstance)\r\n    },\r\n    // 获取订舱状态文本(草稿箱-0/已订舱-1/已放舱-2/已使用-3/已取消--1)\r\n    getBookingStatus(status) {\r\n      const statusMap = {\r\n        \"0\": \"草稿箱\",\r\n        \"1\": \"已订舱\",\r\n        \"2\": \"已放舱\",\r\n        \"3\": \"已使用\",\r\n        \"-1\": \"已取消\"\r\n      }\r\n      return statusMap[status] || \"未知状态\"\r\n    },\r\n    // ===================== 服务添加函数（使用通用工具函数） =====================\r\n    addFreeDeclare() {\r\n      const config = this._getServiceConfig().freeDeclare\r\n      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()\r\n    },\r\n    addDocDeclare() {\r\n      const config = this._getServiceConfig().docDeclare\r\n      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()\r\n    },\r\n    addBulkTruck() {\r\n      const config = this._getServiceConfig().bulkTruck\r\n      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()\r\n    },\r\n    addCtnrTruck() {\r\n      const config = this._getServiceConfig().ctnrTruck\r\n      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()\r\n    },\r\n    addAir() {\r\n      const config = this._getServiceConfig().air\r\n      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()\r\n    },\r\n    addSeaLCL() {\r\n      const config = this._getServiceConfig().seaLcl\r\n      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()\r\n    },\r\n    addSeaFCL() {\r\n      const config = this._getServiceConfig().seaFcl\r\n      return this._createServiceAddHandler(config.templateProperty, config.listProperty)()\r\n    },\r\n    psaBookingCancel(serviceObject) {\r\n      if (this.form.seaId === null) {\r\n        this.$message.warning(\"当前商务舱位未选择\")\r\n        return\r\n      }\r\n      this.$confirm(\"确认取消？\", \"\", {confirmButtonText: \"确认取消\"})\r\n        .then(_ => {\r\n          // serviceObject.seaId = this.form.seaId\r\n          serviceObject.rctNo = null\r\n          serviceObject.sqdPsaNo = null\r\n          serviceObject.clientShortName = null\r\n          serviceObject.salesId = null\r\n          serviceObject.salesAssistantId = null\r\n          serviceObject.distributionStatus = \"0\"\r\n          serviceObject.bookingStatus = \"-1\"\r\n          // serviceObject.opId = this.$store.state.user.sid\r\n          serviceObject.bookingId = this.$store.state.user.sid\r\n          serviceObject.newBookingTime = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n\r\n          // TODO\r\n          this.submitForm()\r\n          this.showPsaRct = false\r\n        })\r\n\r\n    },\r\n    selectCommonUsed(row) {\r\n      if (this.commonUsedType === \"common\") {\r\n        this.form.bookingShipper = row.bookingShipper\r\n        this.form.bookingConsignee = row.bookingConsignee\r\n        this.form.bookingNotifyParty = row.bookingNotifyParty\r\n        this.form.bookingAgent = row.bookingAgent\r\n      }\r\n      if (this.commonUsedType === \"release\") {\r\n        this.bookingMessageForm.bookingShipper = row.bookingShipper\r\n        this.bookingMessageForm.bookingConsignee = row.bookingConsignee\r\n        this.bookingMessageForm.bookingNotifyParty = row.bookingNotifyParty\r\n        this.bookingMessageForm.bookingAgent = row.bookingAgent\r\n      }\r\n      if (this.commonUsedType === \"dispatch\") {\r\n        this.form.precarriageAddress = row.precarriageAddress\r\n        this.form.precarriageTel = row.precarriageTel\r\n        this.form.precarriageContact = row.precarriageContact\r\n        this.form.precarriageRemark = row.precarriageRemark\r\n      }\r\n\r\n      this.openCommonUsedSelect = false\r\n    },\r\n    openDispatchCommon() {\r\n      this.commonUsedSelectData.destinationPortId = this.form.destinationPortId\r\n      this.commonUsedSelectData.polId = this.form.polId\r\n      this.commonUsedSelectData.clientId = this.form.clientId\r\n      this.commonUsedSelectData.type = \"dispatch\"\r\n\r\n      this.commonUsedType = \"dispatch\"\r\n\r\n      this.openCommonUsedSelect = true\r\n    },\r\n    openCommonUsed() {\r\n      this.commonUsedSelectData.destinationPortId = this.form.destinationPortId\r\n      this.commonUsedSelectData.polId = this.form.polId\r\n      this.commonUsedSelectData.clientId = this.form.clientId\r\n      this.commonUsedSelectData.type = \"common\"\r\n\r\n      this.commonUsedType = \"common\"\r\n\r\n      this.openCommonUsedSelect = true\r\n    },\r\n    openReleaseUsed() {\r\n      this.commonUsedSelectData.destinationPortId = this.form.destinationPortId\r\n      this.commonUsedSelectData.polId = this.form.polId\r\n      this.commonUsedSelectData.clientId = this.form.clientId\r\n      this.commonUsedSelectData.type = \"release\"\r\n\r\n      this.commonUsedType = \"release\"\r\n\r\n      this.openCommonUsedSelect = true\r\n    },\r\n    handleAddCommon(type) {\r\n      let data = {}\r\n      data.polId = this.form.polId\r\n      data.destinationPortId = this.form.destinationPortId\r\n      data.clientId = this.form.clientId\r\n      if (type === \"common\") {\r\n        data.bookingShipper = this.form.bookingShipper\r\n        data.bookingConsignee = this.form.bookingConsignee\r\n        data.bookingNotifyParty = this.form.bookingNotifyParty\r\n        data.bookingAgent = this.form.bookingAgent\r\n        data.serviceTypeId = this.form.logisticsTypeId\r\n      }\r\n      if (type === \"release\") {\r\n        data.bookingShipper = this.bookingMessageForm.bookingShipper\r\n        data.bookingConsignee = this.bookingMessageForm.bookingConsignee\r\n        data.bookingNotifyParty = this.bookingMessageForm.bookingNotifyParty\r\n        data.bookingAgent = this.bookingMessageForm.bookingAgent\r\n        data.serviceTypeId = this.form.logisticsTypeId\r\n      }\r\n      if (type === \"dispatch\") {\r\n        data.precarriageAddress = this.form.precarriageAddress\r\n        data.precarriageContact = this.form.precarriageContact\r\n        data.precarriageTel = this.form.precarriageTel\r\n        data.serviceTypeId = 5\r\n        data.dispatchAddress\r\n        data.dispatchContact\r\n        data.dispatchTel\r\n        data.dispatchRemark\r\n      }\r\n\r\n      addClientsinfo(data).then(response => {\r\n        this.$modal.msgSuccess(\"信息新增成功\")\r\n      })\r\n    },\r\n    customMerge(obj1, obj2) {\r\n      return this._.mergeWith({}, obj1, obj2, (objValue, srcValue) => {\r\n        if (this._.isNull(srcValue)) {\r\n          return objValue\r\n        }\r\n        if (this._.isBoolean(objValue)) {\r\n          return objValue\r\n        }\r\n        return undefined  // 由merge处理\r\n      })\r\n    },\r\n    /**\r\n     * 返回选择得商务订舱数据\r\n     * @param row\r\n     */\r\n    selectPsaBooking(row) {\r\n      this.$confirm(\"确认选择？选中信息会覆盖当前数据\", \"\", {confirmButtonText: \"确认覆盖\"})\r\n        .then(_ => {\r\n          this.$refs[\"form\"].validate(valid => {\r\n            if (valid) {\r\n              // 如果当前已有商务单号(说明已经选择),要先取消再选择\r\n              if (this.form.sqdPsaNo !== null) {\r\n                this.$message.warning(\"请先取消当前订舱\")\r\n                return\r\n              }\r\n\r\n              let mergeRow = this.customMerge(this.form.rsOpSeaFclList.filter(item => item.seaId === this.curPsaRow.seaId)[0], row)\r\n\r\n              if (row.carrierId) {\r\n                mergeRow.carrierId = row.carrierId\r\n              }\r\n\r\n              // 将选定的数据覆盖当前数据\r\n              this.form.rsOpSeaFclList = this.form.rsOpSeaFclList.map(item => {\r\n                if (item.seaId === this.curPsaRow.seaId) {\r\n                  item = mergeRow\r\n                }\r\n                return item\r\n              })\r\n              // selectRow.sqdPsaNo = row.psaNo\r\n              let locationIds = [row.polId, row.destinationPortId]\r\n              locationOptions({locationSelectList: locationIds}).then(response => {\r\n                this.locationOptions ? this.locationOptions = Array.from(new Map(this.locationOptions.concat(response.data).map(item => [item.locationId, item])).values()) : this.locationOptions = response.data\r\n                // 更新商务订舱表\r\n                let data = {}\r\n                data.seaId = row.seaId\r\n                data.rctNo = this.form.rctNo\r\n                data.clientShortName = this.form.clientName.split(\"/\")[1]\r\n                data.salesId = this.form.salesId\r\n                data.salesAssistantId = this.form.salesAssistantId\r\n                data.distributionStatus = \"1\"\r\n                data.opId = this.$store.state.user.sid\r\n                data.newBookingTime = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n\r\n                updatePsarct(data).then(_ => {\r\n                  this.submitForm()\r\n                })\r\n                this.showPsaRct = true\r\n\r\n                this.openPsaBookingSelect = false\r\n              })\r\n            } else {\r\n              this.$message.warning(\"请先完整填写操作单\")\r\n            }\r\n          })\r\n\r\n        })\r\n        .catch(_ => {\r\n        })\r\n    },\r\n    selectPsaBookingOpen(item) {\r\n      this.curPsaRow = item\r\n      this.psaBookingSelectData.destinationPortId = this.form.destinationPortId\r\n      this.psaBookingSelectData.polId = this.form.polId\r\n      this.psaBookingSelectData.locationOptions = this.locationOptions\r\n\r\n      this.openPsaBookingSelect = true\r\n    },\r\n    addProgress(rsOpLogList, processId) {\r\n      let data = {}\r\n      if (processId === 15) {\r\n        data.processId = processId\r\n        data.processStatusId = 7\r\n        data.processStatusTime = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n        data.opId = this.$store.state.user.sid\r\n        data.basProcess = {processShortName: \"装船\"}\r\n        data.basProcessStatus = {processStatusShortName: \"完成\"}\r\n      }\r\n      if (processId === 18) {\r\n        data.processId = processId\r\n        data.processStatusId = 7\r\n        data.processStatusTime = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n        data.opId = this.$store.state.user.sid\r\n        data.basProcess = {processShortName: \"到港\"}\r\n        data.basProcessStatus = {processStatusShortName: \"完成\"}\r\n      }\r\n\r\n      rsOpLogList.push(data)\r\n    },\r\n    // 应收勾选\r\n    handleReceiveSelected(rows) {\r\n      this.selectedPrintCharges = rows\r\n    },\r\n    // 提单打印\r\n    getBillOfLading(type) {\r\n      hiprintTemplate = null\r\n      let data = {}\r\n\r\n      let printRow = this.bookingBillPrintRow ? this.bookingBillPrintRow[0] : null\r\n      if (!printRow) {\r\n        this.$message.warning(\"请先勾选一条提单信息\")\r\n        return\r\n      }\r\n\r\n      let prefix = \"<div style=\\\"word-break: keep-all !important; \\n\" +\r\n        \"            word-wrap: break-word !important; \\n\" +\r\n        \"            white-space: pre-wrap !important;\\\">\"\r\n      let suffix = \"</div>\"\r\n\r\n      data.bookingShipper = printRow.bookingShipper ? printRow.bookingShipper.replace(/\\n/g, \"</br>\") : null\r\n      data.bookingShipper = prefix + data.bookingShipper + suffix\r\n      data.bookingConsignee = printRow.bookingConsignee ? printRow.bookingConsignee.replace(/\\n/g, \"</br>\") : null\r\n      data.bookingConsignee = prefix + data.bookingConsignee + suffix\r\n      data.bookingNotifyParty = printRow.bookingNotifyParty ? printRow.bookingNotifyParty.replace(/\\n/g, \"</br>\") : null\r\n      data.bookingNotifyParty = prefix + data.bookingNotifyParty + suffix\r\n      data.bookingAgent = printRow.bookingAgent ? printRow.bookingAgent.replace(/\\n/g, \"</br>\") : null\r\n      data.bookingAgent = prefix + data.bookingAgent + suffix\r\n      data.containerNo = printRow.containerNo\r\n      data.rctNo = this.form.rctNo\r\n      data.containerType = printRow.containerType\r\n      data.contractNo = printRow.contractNo\r\n      data.sealNo = printRow.sealNo\r\n      data.shippingMark = printRow.shippingMark\r\n      let packageQuantitySum = 0\r\n      if (printRow.packageQuantity) {\r\n        printRow.packageQuantity.split(/\\n/g).map(item => {\r\n          // 提取packageQuantity中的数字\r\n          let num = item.match(/\\d+/g)\r\n          packageQuantitySum += Number(num)\r\n        })\r\n        // 最后加上原始的单位\r\n        // 提取数字和单位，例如从\"2 CARTONS\"中提取出数字2和单位CARTONS\r\n        const firstPackage = printRow.packageQuantity.split(/\\n/g)[0]\r\n        const unit = firstPackage.replace(/\\d+/g, \"\").trim() // 去除数字，保留单位\r\n        data.packageQuantity = packageQuantitySum + \" \" + unit\r\n      }\r\n\r\n      data.recelp = printRow.city ? printRow.city : \"GUANG ZHOU\"\r\n      data.goodsDescription = printRow.goodsDescription ? printRow.goodsDescription.replace(/\\n/g, \"</br>\") : null\r\n      data.goodsDescription = prefix + data.goodsDescription + suffix\r\n      let goodsVolumeSum = 0\r\n      printRow.goodsVolume ? printRow.goodsVolume.split(/\\n/g).map(item => {\r\n        goodsVolumeSum += Number(item.trim())\r\n      }) : null\r\n      data.goodsVolume = goodsVolumeSum + \"CBM\"\r\n      let grossWeightSum = 0\r\n      printRow.grossWeight ? printRow.grossWeight.split(/\\n/g).map(item => {\r\n        grossWeightSum += Number(item.trim())\r\n      }) : null\r\n      data.grossWeight = grossWeightSum + \"KGS\"\r\n      data.blTypeCode = printRow.blTypeCode\r\n      data.blFormCode = printRow.blFormCode\r\n      data.sqdDocDeliveryWay = printRow.sqdDocDeliveryWay\r\n      data.polName = printRow.polName\r\n      data.podName = printRow.destinationPort\r\n      data.destinationPort = printRow.destinationPort\r\n      // 货名\r\n      data.goodsNameSummary = this.form.goodsNameSummary\r\n      // so号\r\n      data.soNo = this.form.soNo\r\n\r\n      data.blNumbers = printRow.blNumbers\r\n      data.issueDate = printRow.issueDate\r\n      data.mblNo = printRow.mBlNo\r\n      data.issuePlace = printRow.issuePlace\r\n      const options = {\r\n        day: \"2-digit\",\r\n        month: \"short\",\r\n        year: \"numeric\"\r\n      }\r\n      data.onBoardDate = printRow.onBoardDate ? new Date(printRow.onBoardDate).toLocaleDateString(\"en-GB\", options).toUpperCase() : null\r\n      data.declaredValue = printRow.declaredValue\r\n      data.logisticsTerms = this.form.logisticsTerms\r\n      data.firstVessel = this.form.firstVessel\r\n      data.payWay = printRow.payWay\r\n      data.forwardingAgent = printRow.bookingAgent\r\n      data.blRemark = printRow.blRemark ? printRow.blRemark.replace(/\\n/g, \"</br>\") : null\r\n      let bookingNo = \"\"\r\n      this.form.soNo ? this.form.soNo.split(\"/\").map(item => {\r\n        bookingNo += item + \"</br>\"\r\n      }) : null\r\n      data.bookingNo = bookingNo\r\n      if (printRow.containerNo) {\r\n        let sealNoArr = printRow.sealNo ? printRow.sealNo.split(/\\n/g) : null\r\n        let packageQuantityArr = printRow.packageQuantity ? printRow.packageQuantity.split(/\\n/g) : null\r\n        let goodsVolumeArr = printRow.goodsVolume ? printRow.goodsVolume.split(/\\n/g) : null\r\n        let containerTypeArr = printRow.containerType ? printRow.containerType.split(/\\n/g) : null\r\n        let grossWeightArr = printRow.grossWeight ? printRow.grossWeight.split(/\\n/g) : null\r\n        let containers = 0\r\n        containerTypeArr ? containerTypeArr.map(item => {\r\n          let revenue = item.split(\"x\")\r\n          containers = Number(revenue[0]) + Number(containers)\r\n        }) : null\r\n        data.containerQuantity = toWords(containers) + \"(\" + this.form.logisticsTypeEnName + \")\"\r\n        printRow.goodsSummary = \"\"\r\n        printRow.containerNo.split(/\\n/g).map((item, index) => {\r\n          printRow.goodsSummary += item + \"/\" + ((sealNoArr && sealNoArr.length > index) ? sealNoArr[index] : null) + \"/\" + ((containerTypeArr && containerTypeArr.length > index) ? containerTypeArr[index] : null) + \"/\" + ((packageQuantityArr && packageQuantityArr.length > index) ? packageQuantityArr[index] : null) + \"/\" + ((grossWeightArr && grossWeightArr.length > index) ? grossWeightArr[index] : null) + \"KGS\" + \"/\" + ((goodsVolumeArr && goodsVolumeArr.length > index) ? goodsVolumeArr[index] : null) + \"CBM\" + \"</br>\"\r\n        })\r\n      }\r\n      // 出单地\r\n      data.issuePlace = \"\"\r\n      data.carrier = printRow.containerType ? printRow.containerType.split(/\\n/g).map(item => {\r\n        return item + \"</br>\"\r\n      }) : null\r\n      data.goodsSummary = printRow.goodsSummary\r\n\r\n      data = this.convertObjectKeysToUpperCase(data)\r\n      if (type === \"套打提单\") {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: billOfLading})\r\n      } else {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: billOfLadingRelease})\r\n      }\r\n\r\n      // 打开预览组件\r\n      this.$refs.preView.print(hiprintTemplate, data)\r\n    },\r\n    closeBookingMessage() {\r\n      this.openBookingMessage = false\r\n      this.bookingMessageForm = {}\r\n    },\r\n    deleteBookingMessage(row) {\r\n      this.bookingMessageList = this.bookingMessageList.filter(item => item.rsBookingMessageId !== row.rsBookingMessageId)\r\n    },\r\n    handleBookingMessageUpdate(row) {\r\n      this.bookingMessageStatus = \"edit\"\r\n      this.bookingMessageForm = row\r\n      this.openBookingMessage = true\r\n    },\r\n    handleSelectionChange(val) {\r\n      // 勾选要打印的提单,只能勾选一项\r\n      if (val.length > 1) {\r\n        this.$message.warning(\"只能勾选一条提单信息\")\r\n        return\r\n      }\r\n\r\n      this.bookingBillPrintRow = val\r\n    },\r\n    addBookingMessage(bookingForm) {\r\n      // 合并bookingForm和this.bookingMessageForm\r\n      this.bookingMessageForm = {\r\n        rctId: this.form.rctId,\r\n        polName: this.form.polName,\r\n        podName: this.form.podName,\r\n        destinationPort: this.form.destinationPort\r\n      }\r\n      Object.assign(this.bookingMessageForm, bookingForm)\r\n      this.bookingMessageStatus = \"add\"\r\n      this.openBookingMessage = true\r\n    },\r\n    bookingMessageConfirm(bookingForm) {\r\n      if (this.bookingMessageStatus === \"add\") {\r\n        this.bookingMessageList.push(bookingForm)\r\n      }\r\n\r\n      this.openBookingMessage = false\r\n    },\r\n    // 订舱单打印\r\n    getBookingBill(serviceObject) {\r\n      let data = {}\r\n      data.bookingShipper = this.form.bookingShipper\r\n      data.bookingConsignee = this.form.bookingConsignee\r\n      data.bookingNotifyParty = this.form.bookingNotifyParty\r\n      data.logisticsTerms = this.form.logisticsTerms\r\n      data.tradingTerms = this.form.tradingTerms\r\n      data.clientName = serviceObject.supplierName\r\n      data.clientContact = this.form.clientContact\r\n      let str = this.form.destinationPort ? this.form.destinationPort.split(\"(\") : []\r\n      data.destinationPort1 = str[0]\r\n      data.destinationPort2 = str[1] ? (\"(\" + str[1]) : \"\"\r\n      let polSplitArr = this.form.polName ? this.form.polName.split(\"(\") : []\r\n      data.polName = polSplitArr[0]\r\n      data.polName1 = polSplitArr[1] ? (\"(\" + polSplitArr[1]) : \"\"\r\n      let podSplitArr = this.form.podName ? this.form.podName.split(\"(\") : []\r\n      data.podName = podSplitArr[0]\r\n      data.podName1 = podSplitArr[1] ? (\"(\" + podSplitArr[1]) : \"\"\r\n      data.rctNo = this.form.rctNo\r\n      data.packageQuantity = this.form.packageQuantity + \"PKG\"\r\n      data.grossWeight = this.form.grossWeight + \" KGS\"\r\n      data.goodsVolume = this.form.goodsVolume + \" CBM\"\r\n      data.noDividedAllowedYes = this.form.noDividedAllowed ? \"√\" : \"\"\r\n      data.noDividedAllowedNo = this.form.noDividedAllowed ? \"\" : \"√\"\r\n      data.noTransferAllowedYes = this.form.noTransferAllowed ? \"√\" : \"\"\r\n      data.noTransferAllowedNo = this.form.noTransferAllowed ? \"\" : \"√\"\r\n      data.oceanVessel = (serviceObject.firstVessel ? serviceObject.firstVessel : \"\") + \" \" + (serviceObject.inquiryScheduleSummary ? serviceObject.inquiryScheduleSummary : \"\")\r\n      data.printDate = moment().format(\"YYYY/MM/DD\")\r\n      data.clientContact = this.form.clientContact\r\n      data.freightPaidWayCode = this.form.freightPaidWayCode\r\n      data.logisticsTypeEnName = this.form.logisticsTypeEnName\r\n      data.clientContactTel = this.form.clientContactTel\r\n      data.bookingAgentRemark = serviceObject.bookingAgentRemark\r\n      let chargeListStr = \"\"\r\n      serviceObject.bookingChargeRemark ? serviceObject.bookingChargeRemark.split(\"\\n\").filter(item => item !== \"\").map(item => chargeListStr += item + \"<br>\") : \"\"\r\n      data.bookingChargeRemark = chargeListStr\r\n      data.shippingMark = this.form.shippingMark\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\")\r\n      }\r\n\r\n      let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.$store.state.user.sid)[0]\r\n      if (staff) {\r\n        data.curTel = staff.staffPhoneNum\r\n        data.curName = staff.staffShortName\r\n        data.curEmail = staff.staffEmailEnterprise\r\n      }\r\n\r\n      data.goodsNameSummary = this.form.goodsNameSummary\r\n      data.vesselSummary = \"by \" + (this.form.carrierEnName ? this.form.carrierEnName : \"\") + \"</br> \"\r\n      data.vesselSummary += \"ETD: \" + (serviceObject.etd ? serviceObject.etd : \"\")\r\n\r\n      data.revenueTon = this.form.revenueTon\r\n      data.curDate = moment().format(\"YYYYMMDD\")\r\n      data.companyName = \"广州瑞旗国际货运代理有限公司\"\r\n      if (this.form.orderBelongsTo === \"GZRS\") {\r\n        data.companyName = \"广州瑞旗国际货运代理有限公司\"\r\n      } else if (this.form.orderBelongsTo === \"HKRS\") {\r\n        data.companyName = \"瑞旗国际（中国）有限公司\"\r\n      } else if (this.form.orderBelongsTo === \"CFL\") {\r\n        data.companyName = \"广州正泽国际货运代理有限公司\"\r\n      } else if (this.form.orderBelongsTo === \"GZVS\") {\r\n        data.companyName = \"广州外海国际供应链有限公司\"\r\n      } else if (this.form.orderBelongsTo === \"CASH\") {\r\n        data.companyName = \"公司现金账户\"\r\n      }\r\n\r\n      if (this.form.rctNo && this.form.rctNo.startsWith(\"CFL\")) {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: CFLBooking})\r\n      } else {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: booking})\r\n      }\r\n      // 打开预览组件\r\n      this.$refs.preView.print(hiprintTemplate, data)\r\n    },\r\n    getDispatchingBill(serviceObject) {\r\n      hiprintTemplate = null\r\n      let data = {}\r\n      data.truckList = serviceObject.rsOpTruckList\r\n      // data.company = (this.form.clientName.split(\"/\")[2] === \"null\" || this.form.clientName.split(\"/\")[2] === \"\") ? this.form.clientName.split(\"/\")[1] : this.form.clientName.split(\"/\")[2]\r\n      data.company = serviceObject.supplierName\r\n      data.clientContact = this.form.clientContact\r\n      // 货名\r\n      data.goodsNameSummary = this.form.goodsNameSummary\r\n      // so号\r\n      data.soNo = this.form.soNo\r\n      data.packageQuantity = this.form.packageQuantity + \" PACKAGES\"\r\n      data.grossWeight = this.form.grossWeight + \" KGS\"\r\n      data.goodsVolume = this.form.goodsVolume + \" CBM\"\r\n      data.revenueTon = this.form.revenueTon\r\n      data.carrierEnName = this.form.sqdCarrier ? this.form.sqdCarrier : this.form.carrierEnName\r\n      data.pol = this.form.pol\r\n      data.pod = this.form.destinationPort\r\n      data.podName = this.form.podName\r\n      data.precarriageTime = moment(serviceObject.precarriageTime).format(\"yyyy-MM-DD HH:mm:ss\")\r\n      data.precarriageAddress = serviceObject.precarriageAddress\r\n      data.precarriageContact = serviceObject.precarriageContact\r\n      data.precarriageRemark = serviceObject.precarriageRemark\r\n      data.backLocation\r\n      data.REF = this.form.rctNo\r\n      data.subtotal = 0\r\n      serviceObject.rsChargeList.map(item => {\r\n        item.subtotal ? data.subtotal = currency(item.subtotal).add(data.subtotal).value : null\r\n      })\r\n\r\n      // 用于组装pdf文件名\r\n      data.pdfName = \"[派车单]\" + \"-\" + this.form.rctNo + \"-\" + this.form.revenueTon + \"-\" + serviceObject.rsServiceInstances.supplierSummary + \"-\" + moment().format(\"YYYYMMDD\")\r\n\r\n      hiprintTemplate = new hiprint.PrintTemplate({template: dispatchBill})\r\n      // 打开预览组件\r\n      this.$refs.preView.print(hiprintTemplate, data)\r\n    },\r\n    // 操作单打印\r\n    getOpBill(type) {\r\n      hiprintTemplate = null\r\n      let data = {}\r\n      data.title = this.form.logisticsTypeEnName\r\n      data.chargeList = this.selectedPrintCharges\r\n      data.rctNo = this.form.rctNo\r\n      data.revenueTon = this.form.revenueTon\r\n      data.printDate = moment().format(\"YYYY/MM/DD\")\r\n      data.company = this.form.clientName.split(\"/\")[2] === \"null\" || this.form.clientName.split(\"/\")[2] === \"\" ? this.form.clientName.split(\"/\")[1] : this.form.clientName.split(\"/\")[2]\r\n      data.clientContact = this.form.clientContact\r\n      let qoutationSketch = \"\"\r\n      let qoutationSketchArr = this.form.qoutationSketch ? this.form.qoutationSketch.split(\"\\n\") : null\r\n      qoutationSketchArr ? qoutationSketchArr.map(item => {\r\n        qoutationSketch += item + \"</br>\"\r\n      }) : null\r\n      data.qoutationSketch = qoutationSketch\r\n      data.contract = this.form.contract\r\n      data.clientContactTel = this.form.clientContactTel\r\n      data.revenueTon = this.form.revenueTon\r\n      data.newBookingRemark = this.form.newBookingRemark\r\n      data.carrierEnName = this.form.carrierEnName\r\n      data.grossWeight = this.form.grossWeight + \"KGS\"\r\n      data.tradingTerms = this.getTradingTerms(this.form.tradingTerms)\r\n      data.goodsNameSummary = this.form.goodsNameSummary\r\n      data.vessel = this.form.cvClosingTime ? this.form.cvClosingTime : \"**\"\r\n      data.truk = this.form.truk\r\n      data.pol = this.form.pol\r\n      const index = this.form.pol ? this.form.pol.indexOf(\"(\") : -1\r\n      data.pol1 = index !== -1 ? this.form.pol.slice(0, index) : \"\"\r\n      data.pol2 = index !== -1 ? this.form.pol.slice(index) : \"\"\r\n      data.pod = this.form.destinationPort\r\n      const index2 = this.form.destinationPort ? this.form.destinationPort.indexOf(\"(\") : -1\r\n      data.pod1 = index2 !== -1 ? this.form.destinationPort.slice(0, index2) : \"\"\r\n      data.pod2 = index2 !== -1 ? this.form.destinationPort.slice(index2) : \"\"\r\n      data.podName = this.form.podName\r\n      const index3 = this.form.podName ? this.form.podName.indexOf(\"(\") : -1\r\n      data.podName1 = index3 !== -1 ? this.form.podName.slice(0, index3) : \"\"\r\n      data.podName2 = index3 !== -1 ? this.form.podName.slice(index3) : \"\"\r\n      data.soNo = this.form.soNo\r\n      //放货方式\r\n      data.releaseType = this.getReleaseType(this.form.releaseType)\r\n      // 出单方式\r\n      data.ORG = this.form.blFormCode === \"ORG\" ? \"√\" : \"\"\r\n      data.TLX = this.form.blFormCode === \"TLX\" ? \"√\" : \"\"\r\n      data.SWB = this.form.blFormCode === \"SWB\" ? \"√\" : \"\"\r\n      data.sign = this.getName(this.form.salesId)\r\n      data.signDate = this.form.rctCreateTime\r\n      data.orderBelongsTo = this.form.orderBelongsTo\r\n      data.packageQuantity = this.form.packageQuantity\r\n      data.goodsVolume = this.form.goodsVolume + \"CBM\"\r\n      data.firstVessel = this.form.firstVessel\r\n      data.transType\r\n      data.trcukAddr = \"\"\r\n      this.form.rsOpCtnrTruckList ? this.form.rsOpCtnrTruckList.map(item => {\r\n        item.precarriageAddress ? data.trcukAddr += (item.precarriageAddress + \"</br>\") : null\r\n        return item\r\n      }) : null\r\n      this.form.rsOpBulkTruckList ? this.form.rsOpBulkTruckList.map(item => {\r\n        item.precarriageAddress ? data.trcukAddr += (item.precarriageAddress + \"</br>\") : null\r\n        return item\r\n      }) : null\r\n      let total = 0\r\n      let USD = 0\r\n      let RMB = 0\r\n      this.selectedPrintCharges.map(item => {\r\n        // total = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).add(total).value\r\n        if (item.dnCurrencyCode === \"USD\") {\r\n          USD = currency(USD).add(currency(item.dnUnitRate).multiply(item.dnAmount)).value\r\n        }\r\n        if (item.dnCurrencyCode === \"RMB\") {\r\n          RMB = currency(RMB).add(currency(item.dnUnitRate).multiply(item.dnAmount)).value\r\n        }\r\n      })\r\n      data.total = (USD ? (\"USD: \" + currency(USD).value + \" \") : \" \") + (RMB ? (\"RMB: \" + currency(RMB).value) : \" \")\r\n\r\n      data.pdfName = \"[操作单]\" + \"-\" + this.form.rctNo + moment().format(\"YYYY-MM-DD\")\r\n      data.bookingRemark = this.form.newBookingRemark\r\n      data.secondVessel = this.form.secondVessel ? this.form.secondVessel : \"**\"\r\n      data.includes\r\n\r\n      data.tradingPaymentChannel = this.form.freightPaidWayCode\r\n      data.clearType = this.form.serviceTypeIds.includes(60) ? \"单证报关\" : \"无单报关\"\r\n      data.tradingTerms = this.form.tradingTerms\r\n      data.supplierName = \"**\"\r\n      if (this.form.rsOpSeaFclList && this.form.rsOpSeaFclList[0]) {\r\n        data.supplierName = this.form.rsOpSeaFclList[0].supplierName ? this.form.rsOpSeaFclList[0].supplierName : \"**\"\r\n      }\r\n      if (this.form.rsOpSeaLclList && this.form.rsOpSeaLclList[0]) {\r\n        data.supplierName = this.form.rsOpSeaLclList[0].supplierName ? this.form.rsOpSeaLclList[0].supplierName : \"**\"\r\n      }\r\n\r\n      data.carrierEnName = this.form.carrierEnName\r\n      // 应收\r\n      data.receive = \"\"\r\n      this.rsClientMessage.rsChargeList ? this.rsClientMessage.rsChargeList.map(v => {\r\n        data.receive = data.receive + v.chargeName + \"/\" + v.dnUnitCode + \"  \" + v.dnUnitRate + \"/\" + v.dnCurrencyCode + \"</br>\"\r\n      }) : null\r\n      // 应付\r\n      data.pay = \"\"\r\n      this.form.rsOpSeaFclList ? this.form.rsOpSeaFclList.map(item => {\r\n        item.rsChargeList ? item.rsChargeList.map(v => {\r\n          data.pay = data.pay + v.chargeName + \"/\" + v.dnUnitCode + \"  \" + v.dnUnitRate + \"/\" + v.dnCurrencyCode + \"</br>\"\r\n        }) : null\r\n      }) : null\r\n      this.form.rsOpSeaLclList ? this.form.rsOpSeaLclList.map(item => {\r\n        item.rsChargeList ? item.rsChargeList.map(v => {\r\n          data.pay = data.pay + v.chargeName + \"/\" + v.dnUnitCode + \"  \" + v.dnUnitRate + \"/\" + v.dnCurrencyCode + \"</br>\"\r\n        }) : null\r\n      }) : null\r\n      this.form.rsOpAirList ? this.form.rsOpAirList.map(item => {\r\n        item.rsChargeList ? item.rsChargeList.map(v => {\r\n          data.pay = data.pay + v.chargeName + \"/\" + v.dnUnitCode + \"  \" + v.dnUnitRate + \"/\" + v.dnCurrencyCode + \"</br>\"\r\n        }) : null\r\n      }) : null\r\n      this.form.rsOpFreeDeclareList ? this.form.rsOpFreeDeclareList.map(item => {\r\n        item.rsChargeList ? item.rsChargeList.map(v => {\r\n          data.pay = data.pay + v.chargeName + \"/\" + v.dnUnitCode + \"  \" + v.dnUnitRate + \"/\" + v.dnCurrencyCode + \"</br>\"\r\n        }) : null\r\n      }) : null\r\n      this.form.rsOpDocDeclareList ? this.form.rsOpDocDeclareList.map(item => {\r\n        item.rsChargeList ? item.rsChargeList.map(v => {\r\n          data.pay = data.pay + v.chargeName + \"/\" + v.dnUnitCode + \"  \" + v.dnUnitRate + \"/\" + v.dnCurrencyCode + \"</br>\"\r\n        }) : null\r\n      }) : null\r\n      this.form.rsOpBulkTruckList ? this.form.rsOpBulkTruckList.map(item => {\r\n        item.rsChargeList ? item.rsChargeList.map(v => {\r\n          data.pay = data.pay + v.chargeName + \"/\" + v.dnUnitCode + \"  \" + v.dnUnitRate + \"/\" + v.dnCurrencyCode + \"</br>\"\r\n        }) : null\r\n      }) : null\r\n      this.form.rsOpCtnrTruckList ? this.form.rsOpCtnrTruckList.map(item => {\r\n        item.rsChargeList ? item.rsChargeList.map(v => {\r\n          data.pay = data.pay + v.chargeName + \"/\" + v.dnUnitCode + \"  \" + v.dnUnitRate + \"/\" + v.dnCurrencyCode + \"</br>\"\r\n        }) : null\r\n      }) : null\r\n\r\n      this.form.serviceTypeIds ? this.form.serviceTypeIds.map(v => {\r\n        this.getServiceObject(v) ? this.getServiceObject(v).rsChargeList.map(v => {\r\n          data.pay = data.pay + v.chargeName + \"/\" + v.dnUnitCode + \"  \" + v.dnUnitRate + \"/\" + v.dnCurrencyCode + \"</br>\"\r\n        }) : null\r\n      }) : null\r\n\r\n      if (type === \"整柜\") {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: FCLBill})\r\n      } else if (type === \"散货\") {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: FCLBill})\r\n      } else if (type === \"空运\") {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: AirBill})\r\n      } else {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: FCLBill})\r\n      }\r\n\r\n      // 打开预览组件\r\n      this.$refs.preView.print(hiprintTemplate, data)\r\n    },\r\n    getReleaseType(id) {\r\n      if (id == 1) return \"月结\"\r\n      if (id == 2) return \"押放\"\r\n      if (id == 3) return \"票结\"\r\n      if (id == 4) return \"签放\"\r\n      if (id == 5) return \"订金\"\r\n      if (id == 6) return \"预付\"\r\n      if (id == 7) return \"扣货\"\r\n      if (id == 9) return \"居间\"\r\n      return \"\"\r\n    },\r\n    getTradingTerms(id) {\r\n      if (id == 1) return \"EXW\"\r\n      if (id == 2) return \"FCA\"\r\n      if (id == 3) return \"FOB\"\r\n      if (id == 4) return \"CNF\"\r\n      if (id == 5) return \"CIF\"\r\n      if (id == 6) return \"DDU\"\r\n      if (id == 7) return \"DDP\"\r\n      return \"\"\r\n    },\r\n    // 费用清单打印\r\n    getChargeListBill(type) {\r\n      // 如果用户勾选了不同结算单位的费用,给出提醒\r\n      let continuePrint = true\r\n      this.selectedPrintCharges.map(item => {\r\n        if (item.clearingCompanyId !== this.selectedPrintCharges[0].clearingCompanyId) {\r\n          continuePrint = false\r\n          return\r\n        }\r\n      })\r\n      if (!continuePrint) {\r\n        this.$message.warning(\"请选择同一结算单位的费用\")\r\n        return\r\n      }\r\n\r\n      hiprintTemplate = null\r\n      let data = {}\r\n\r\n      let chargeList\r\n      if (this.$store.state.data.chargeList.length == 0 || this.$store.state.data.redisList.charge) {\r\n        store.dispatch(\"getChargeList\").then(() => {\r\n          chargeList = this.$store.state.data.chargeList\r\n        })\r\n      } else {\r\n        chargeList = this.$store.state.data.chargeList\r\n      }\r\n\r\n      data.rctNo = this.form.rctNo\r\n      data.containerNo = this.form.rsOpSeaFclList ? this.form.rsOpSeaFclList[0].sqdContainersSealsSum : (this.form.rsOpSeaLclList ? this.form.rsOpSeaLclList[0].sqdContainersSealsSum : null)\r\n      data.revenueTon = this.form.revenueTon\r\n      data.contractNo = this.form.clientJobNo\r\n      data.printDate = moment().format(\"YYYY/MM/DD\")\r\n      let searchV = this.companyList.filter(item => item.companyId === this.selectedPrintCharges[0].clearingCompanyId)[0]\r\n      data.company = searchV ? searchV.companyLocalName : \"\"\r\n      data.clientContact = this.form.clientContact\r\n      data.carrierEnName = this.form.carrierEnName\r\n      data.pol = this.form.pol\r\n      data.pod = this.form.destinationPort\r\n      data.soNo = (this.form.rsOpAirList && this.form.rsOpAirList.length > 0) ? this.form.rsOpAirList[0].soNo : this.form.soNo\r\n      data.transType = this.form.serviceTypeIds.includes(1) ? \"FCL\" : \"LCL\"\r\n\r\n      data.chargeList = this.selectedPrintCharges.map(item => {\r\n        return {\r\n          ...item,\r\n          subtotal: (type === \"CN-广州正泽[USD->RMB]\" || type === \"CN-广州瑞旗[USD->RMB]\") ? currency(item.subtotal).multiply(item.basicCurrencyRate).value : ((type === \"EN- 瑞旗香港账户[HSBC RMB->USD]\" || type === \"EN-广州瑞旗[RMB->USD]\") ? currency(item.subtotal, {precision: 2}).divide(item.basicCurrencyRate).value : item.subtotal),\r\n          dutyRate: currency(item.dutyRate, {symbol: \"\"}).format() + \"%\",\r\n          chargeName: (type === \"EN-广州瑞旗[RMB->USD]\" || type === \"EN-广州瑞旗[招行USD]\" || type === \"EN- 瑞旗香港账户[HSBC RMB->USD]\" || type === \"EN- 香港瑞旗[HSBC]\") ? chargeList.filter(v => v.chargeId === item.dnChargeNameId)[0].chargeEnName.toUpperCase() : item.chargeName\r\n        }\r\n      })\r\n      let total = 0\r\n      let USD = 0\r\n      let RMB = 0\r\n      data.chargeList.forEach(item => {\r\n        if (type === \"EN-广州瑞旗[RMB->USD]\" || type === \"EN- 瑞旗香港账户[HSBC RMB->USD]\") {\r\n          if (item.dnCurrencyCode === \"USD\") {\r\n            USD = currency(USD).add(item.subtotal).value\r\n          }\r\n          if (item.dnCurrencyCode === \"RMB\") {\r\n            USD = currency(USD).add(item.subtotal).value\r\n          }\r\n        } else if (type === \"CN-广州瑞旗[USD->RMB]\" || type === \"CN-广州正泽[USD->RMB]\") {\r\n          if (item.dnCurrencyCode === \"USD\") {\r\n            RMB = currency(RMB).add(item.subtotal).value\r\n          }\r\n          if (item.dnCurrencyCode === \"RMB\") {\r\n            RMB = currency(RMB).add(item.subtotal).value\r\n          }\r\n        } else {\r\n          // total = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).add(total).value\r\n          if (item.dnCurrencyCode === \"USD\") {\r\n            USD = currency(USD).add(item.subtotal).value\r\n          }\r\n          if (item.dnCurrencyCode === \"RMB\") {\r\n            RMB = currency(RMB).add(item.subtotal).value\r\n          }\r\n        }\r\n      })\r\n      data.USD = currency(USD).value\r\n      data.RMB = currency(RMB).value\r\n      // data.total = (USD ? (\"USD: \" + currency(USD).value + \"</br>\") : \" \") + (RMB ? (\"RMB: \" + currency(RMB).value) : \" \")\r\n\r\n      data.pdfName = \"[费用清单]\" + \"-\" + this.form.rctNo + moment().format(\"YYYY-MM-DD\")\r\n\r\n      data.pol = (type === \"EN- 瑞旗香港账户[HSBC RMB->USD]\" || type === \"EN-广州瑞旗[招行USD]\" || \"EN-广州瑞旗[RMB->USD]\" === type || type === \"EN- 香港瑞旗[HSBC]\") ? pinyin.getFullChars(this.form.pol.slice(0, this.form.pol.indexOf(\"(\"))).toUpperCase() : data.pol\r\n\r\n      if (type === \"CN-广州瑞旗[招行USD+工行RMB]\") {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: debitNode})\r\n      } else if (type === \"CN-广州瑞旗[USD->RMB]\") {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeUSDToRMB})\r\n      } else if (type === \"EN- 香港瑞旗[HSBC]\") {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeEn})\r\n      } else if (type === \"EN-广州瑞旗[招行USD]\") {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeZSUSD})\r\n      } else if (type === \"CN-广州正泽[招行USD+RMB]\") {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeCFL})\r\n      } else if (type === \"EN-广州瑞旗[RMB->USD]\") {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeZSUSD})\r\n      } else if (type === \"CN-广州正泽[USD->RMB]\") {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeCFLToRMB})\r\n      } else if (type === \"EN- 瑞旗香港账户[HSBC RMB->USD]\") {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: debitNodeEnHKRMBToUSD})\r\n      } else {\r\n        hiprintTemplate = new hiprint.PrintTemplate({template: debitNode})\r\n      }\r\n      // 打开预览组件\r\n      this.$refs.preView.print(hiprintTemplate, data)\r\n    },\r\n    // 费用清单打印\r\n    checkRole,\r\n    initPrint() {\r\n      hiprint.init({\r\n        providers: [new defaultElementTypeProvider()]\r\n      })\r\n    },\r\n    editRevenueTon() {\r\n      if (this.form.revenueTon && this.form.revenueTon.split(\"+\").length > 0) {\r\n        this.form.revenueTon.split(\"+\").map((revenueTon, i) => {\r\n          if (revenueTon.split(\"x\").length > 0 && i === 0) {\r\n            this.form.countA = Number(revenueTon.split(\"x\")[0])\r\n            this.form.unitCodeA = revenueTon.split(\"x\")[1]\r\n          }\r\n          if (revenueTon.split(\"x\").length > 0 && i === 1) {\r\n            this.form.countB = Number(revenueTon.split(\"x\")[0])\r\n            this.form.unitCodeB = revenueTon.split(\"x\")[1]\r\n          }\r\n          if (revenueTon.split(\"x\").length > 0 && i === 2) {\r\n            this.form.countC = Number(revenueTon.split(\"x\")[0])\r\n            this.form.unitCodeC = revenueTon.split(\"x\")[1]\r\n          }\r\n        })\r\n      }\r\n      this.openGenerateRevenueTons = true\r\n    },\r\n    revenueTonConfirm() {\r\n      let revenueString = []\r\n      if (this.form.unitCodeA) {\r\n        this.form.countA ? revenueString.push(this.form.countA + \"x\" + this.form.unitCodeA) : revenueString.push(1 + \"x\" + this.form.unitCodeA)\r\n      }\r\n      if (this.form.unitCodeB && this.form.countB != null) {\r\n        this.form.countB ? revenueString.push(this.form.countB + \"x\" + this.form.unitCodeB) : revenueString.push(1 + \"x\" + this.form.unitCodeB)\r\n      }\r\n      if (this.form.unitCodeC) {\r\n        this.form.countC ? revenueString.push(this.form.countC + \"x\" + this.form.unitCodeC) : revenueString.push(1 + \"x\" + this.form.unitCodeC)\r\n      }\r\n      this.form.revenueTon = revenueString.join(\"+\")\r\n      this.form.unitCodeA = null\r\n      this.form.unitCodeB = null\r\n      this.form.unitCodeC = null\r\n      this.form.conutA = null\r\n      this.form.conutB = null\r\n      this.form.conutC = null\r\n      this.openGenerateRevenueTons = false\r\n    },\r\n    changeServiceObject(serviceTypeId, serviceObject) {\r\n      const serviceTypeMap = {\r\n        1: \"rsOpSeaFcl\",\r\n        2: \"rsOpSeaLcl\",\r\n        10: \"rsOpAir\",\r\n        20: \"rsOpRailFCL\",\r\n        21: \"rsOpRailLCL\",\r\n        40: \"rsOpExpress\",\r\n        50: \"rsOpCtnrTruck\",\r\n        51: \"rsOpBulkTruck\",\r\n        60: \"rsOpDocDeclare\",\r\n        61: \"rsOpFreeDeclare\",\r\n        70: \"rsOpDOAgent\",\r\n        71: \"rsOpClearAgent\",\r\n        80: \"rsOpWHS\",\r\n        90: \"rsOp3rdCert\",\r\n        100: \"rsOpINS\",\r\n        101: \"rsOpTrading\",\r\n        102: \"rsOpFumigation\",\r\n        103: \"rsOpCO\",\r\n        104: \"rsOpOther\"\r\n      }\r\n\r\n      const targetProperty = serviceTypeMap[serviceTypeId]\r\n      if (targetProperty) {\r\n        this[targetProperty] = Object.assign({}, serviceObject, this[targetProperty] || {})\r\n      }\r\n    },\r\n    getFormDisable(serviceTypeId) {\r\n      const serviceTypeMap = {\r\n        1: \"rsOpSealFclFormDisable\",\r\n        2: \"rsOpSealLclFormDisable\",\r\n        10: \"rsOpAirFormDisable\",\r\n        20: \"rsOpRailFclFormDisable\",\r\n        21: \"rsOpRailLclFormDisable\",\r\n        40: \"rsOpExpressFormDisable\",\r\n        50: \"rsOpCtnrTruckFormDisable\",\r\n        51: \"rsOpBulkTruckFormDisable\",\r\n        60: \"rsOpDocDeclareFormDisable\",\r\n        61: \"rsOpFreeDeclareFormDisable\",\r\n        70: \"rsOpDOAgentFormDisable\",\r\n        71: \"rsOpClearAgentFormDisable\",\r\n        80: \"rsOpWHSFormDisable\",\r\n        90: \"rsOp3rdCertFormDisable\",\r\n        100: \"rsOpINSFormDisable\",\r\n        101: \"rsOpTradingFormDisable\",\r\n        102: \"rsOpFumigationFormDisable\",\r\n        103: \"rsOpCOFormDisable\"\r\n      }\r\n\r\n      const propertyName = serviceTypeMap[serviceTypeId]\r\n      return propertyName ? this[propertyName] : undefined\r\n    },\r\n    changeServiceFold(serviceInstance) {\r\n      serviceInstance.serviceFold = !serviceInstance.serviceFold\r\n    },\r\n    // 拓展服务折叠状态变更\r\n    changeExtendServiceFold(serviceData) {\r\n      const serviceTypeId = serviceData.serviceTypeId\r\n      // 根据serviceTypeId更新对应的折叠状态\r\n      switch (serviceTypeId) {\r\n        case 90: // 3rdCert\r\n          this.rsOp3rdCertFold = !this.rsOp3rdCertFold\r\n          break\r\n        case 100: // Insurance\r\n          this.rsOpINSFold = !this.rsOpINSFold\r\n          break\r\n        case 101: // Trading\r\n          this.rsOpTradingFold = !this.rsOpTradingFold\r\n          break\r\n        case 102: // Fumigation\r\n          this.rsOpFumigationFold = !this.rsOpFumigationFold\r\n          break\r\n        case 103: // CO\r\n          this.rsOpCOFold = !this.rsOpCOFold\r\n          break\r\n        case 104: // Other\r\n          this.rsOpOtherFold = !this.rsOpOtherFold\r\n          break\r\n      }\r\n    },\r\n    // 切换折叠状态（使用服务管理器）\r\n    changeFold(serviceTypeId) {\r\n      this.serviceManager.toggleFold(serviceTypeId, this)\r\n    },\r\n    // 获取折叠状态（使用服务管理器）\r\n    getFold(serviceTypeId) {\r\n      return this.serviceManager.getFoldState(serviceTypeId, this)\r\n    },\r\n    addTuck(rsOpTuckList) {\r\n      let obj = {}\r\n\r\n      rsOpTuckList.push(obj)\r\n    },\r\n    // 获取服务对象（使用服务管理器）\r\n    getServiceObject(serviceTypeId) {\r\n      return this.serviceManager.getServiceObject(serviceTypeId, this)\r\n    },\r\n    // 获取应付对象（使用服务管理器）\r\n    getPayable(serviceTypeId) {\r\n      return this.serviceManager.getPayable(serviceTypeId, this)\r\n    },\r\n    // 获取服务实例（使用服务管理器）\r\n    getServiceInstance(serviceTypeId) {\r\n      return this.serviceManager.getServiceInstance(serviceTypeId, this)\r\n    },\r\n    getName(id) {\r\n      if (id) {\r\n        if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n          store.dispatch(\"getAllRsStaffList\")\r\n        }\r\n\r\n        if (id) {\r\n          let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n          if (staff) {\r\n            return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName\r\n          } else {\r\n            return \"\"\r\n          }\r\n        }\r\n      } else {\r\n        return \"\"\r\n      }\r\n    },\r\n    async getBookingDetail(id) {\r\n      this.reset()\r\n      await getBooking(id).then(response => {\r\n        let rr = []\r\n        if (response.data.relationClientIds) {\r\n          response.data.relationClientIds.split(\",\").forEach(v => {\r\n            rr.push(Number(v))\r\n          })\r\n        }\r\n        this.relationClientIds = rr\r\n        this.grossWeight = response.data.grossWeight\r\n        this.goodsValue = response.data.goodsValue\r\n        this.form = response.data\r\n        this.form.relationClientIds = rr\r\n        let cIds = new Set()\r\n        if (cIds.size > 0) {\r\n          cIds.forEach(c => {\r\n            this.carrierIds.push(c)\r\n          })\r\n        }\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.salesId) {\r\n                      this.salesId = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.salesAssistantId) {\r\n                      this.salesAssistantId = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.salesObserverId) {\r\n                      this.salesObserverId = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.opList != undefined) {\r\n          for (const a of this.opList) {\r\n            if (a.staffId == response.data.opId) {\r\n              this.opId = a.roleId\r\n            }\r\n\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (a.role.roleLocalName == \"操作员\" && b.staffId == response.data.opId) {\r\n                  this.opId = b.roleId\r\n                }\r\n                if (a.role.roleLocalName == \"订舱员\" && b.staffId == response.data.bookingOpId) {\r\n                  this.bookingOpId = b.roleId\r\n                }\r\n                if (a.role.roleLocalName == \"单证员\" && b.staffId == response.data.docOpId) {\r\n                  this.docOpId = b.roleId\r\n                }\r\n                if (b.staffId == response.data.opObserverId) {\r\n                  this.opObserverId = b.roleId\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.businessList != undefined) {\r\n          for (const a of this.businessList) {\r\n            /* if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.staffId == response.data.verifyPsaId) {\r\n                  this.verifyPsaId = b.roleId\r\n                }\r\n              }\r\n            } */\r\n            if (a.staffId == response.data.verifyPsaId) {\r\n              this.verifyPsaId = a.staffId\r\n            }\r\n          }\r\n        }\r\n        // 设置基础物流信息的值\r\n        if (response.data.rsBookingLogisticsTypeBasicInfo != null) {\r\n          this.logisticsBasicInfo = response.data.rsBookingLogisticsTypeBasicInfo\r\n          this.logisticsReceivablePayableList = response.data.rsBookingLogisticsTypeBasicInfo.rsBookingReceivablePayableList\r\n          /* this.logisticsReceivablePayableList.map(logisticsReceivablePayable=>{\r\n            log\r\n          }) */\r\n        }\r\n        // 设置前程运输的值\r\n        if (response.data.rsBookingPreCarriageBasicInfo != null) {\r\n          this.preCarriageBasicInfo = response.data.rsBookingPreCarriageBasicInfo\r\n          this.preCarriageReceivablePayableList = response.data.rsBookingPreCarriageBasicInfo.rsBookingReceivablePayableList\r\n        }\r\n        // 设置出口报关的值\r\n        if (response.data.rsBookingExportDeclarationBasicInfo != null) {\r\n          this.exportDeclarationBasicInfo = response.data.rsBookingExportDeclarationBasicInfo\r\n          this.exportDeclarationReceivablePayableList = response.data.rsBookingExportDeclarationBasicInfo.rsBookingReceivablePayableList\r\n        }\r\n        // 设置进口清关的值\r\n        if (response.data.rsBookingImportClearanceBasicInfo != null) {\r\n          this.importClearanceBasicInfo = response.data.rsBookingImportClearanceBasicInfo\r\n          this.importClearanceReceivablePayableList = response.data.rsBookingImportClearanceBasicInfo.rsBookingReceivablePayableList\r\n        }\r\n        this.locationOptions = response.locationOptions\r\n      })\r\n    },\r\n    // 从报价表中过来\r\n    async getQuotation(id) {\r\n      this.reset()\r\n      await getQuotation(id).then(response => {\r\n        let revenueTonArr = []\r\n        response.midRevenueTonsList.map(item => {\r\n          revenueTonArr.push(item.count + \"x\" + item.unit)\r\n        })\r\n        this.companyList = response.company ? [response.company] : []\r\n        this.form.clientName = response.data.companyName\r\n        this.form.company = response.data.company\r\n        this.form.revenueTon = revenueTonArr.join(\"+\")\r\n        this.form.logisticsTypeId = response.data.logisticsTypeId\r\n        this.form.salesId = response.data.staffId\r\n        this.form.clientId = response.data.companyId\r\n        this.form.clientRoleId = response.data.companyRoleId\r\n        this.form.clientContactor = response.data.extStaffName\r\n        this.form.clientContactorTel = response.data.extStaffPhoneNum\r\n        this.form.clientContactorEmail = response.data.extStaffEmailEnterprise\r\n        this.form.quotationNo = response.data.richNo\r\n        this.form.quotationDate = new Date()\r\n        this.form.impExpTypeId = response.data.imExPort\r\n        this.form.goodsNameSummary = response.data.cargoName\r\n        this.form.goodsValue = response.data.cargoPrice\r\n        this.form.goodsCurrencyId = response.data.cargoCurrencyId\r\n        this.form.grossWeight = response.data.grossWeight\r\n        this.grossWeight = response.data.grossWeight\r\n        this.form.weightUnitId = response.data.cargoUnitId\r\n        this.form.polId = response.data.departureId\r\n        this.form.destinationPortId = response.data.destinationId\r\n        this.form.transitPortId = response.data.transportationTermsId\r\n        this.form.revenueTons = response.data.revenueTons\r\n        this.form.newBookingRemark = response.data.remark\r\n        this.form.inquiryNo = response.data.richNo\r\n        this.form.qoutationNo = response.data.richNo\r\n        this.form.qoutationSketch = response.data.quotationSketch\r\n        this.form.qoutationTime = new Date()\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.staffId) {\r\n                      this.salesId = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        let cIds = new Set()\r\n        for (const v of this.carrierList) {\r\n          if (v.children != undefined && v.children.length > 0) {\r\n            for (const a of v.children) {\r\n              if (a.carrier != null && a.carrier.carrierId != null && a.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                if (response.data.carrierIds.includes(a.carrier.carrierId)) {\r\n                  cIds.add(a.serviceTypeId)\r\n                }\r\n              }\r\n              if (a.children != undefined && a.children.length > 0) {\r\n                for (const b of a.children) {\r\n                  if (b.carrier != null && b.carrier.carrierId != null && b.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                    if (response.data.carrierIds.includes(b.carrier.carrierId)) {\r\n                      cIds.add(b.serviceTypeId)\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (cIds.size > 0) {\r\n          cIds.forEach(c => {\r\n            this.carrierIds.push(c)\r\n          })\r\n        }\r\n\r\n        // 注意事项\r\n        let characteristics = \"\"\r\n        if (response.characteristics) {\r\n          for (const c of response.characteristics) {\r\n            characteristics += (c.serviceType != null ? c.serviceType : \"\")\r\n              + (c.cargoType != null ? c.cargoType : \"\")\r\n              + (c.company != null ? c.company : \"\")\r\n              + (c.locationDeparture != null ? c.locationDeparture : \"\")\r\n              + (c.locationDestination != null ? c.locationDestination : \"\")\r\n              + (c.info != null ? c.info : \"\")\r\n              + (c.essentialDetail != null ? c.essentialDetail : \"\") + \"\\n\"\r\n          }\r\n        }\r\n        this.form.inquiryNotice = characteristics\r\n        this.form.serviceTypeIds = response.serviceTypeIds\r\n        this.form.cargoTypeIds = response.cargoTypeIds\r\n        this.cargoTypeCodes = response.cargoTypeCodeSum ? response.cargoTypeCodeSum.toString().split(\",\") : []\r\n        this.locationOptions = response.locationOptions\r\n        this.form.preCarriageRegionIds = response.locationLoadingIds\r\n        this.form.clientRoleId = response.roleIds[0]\r\n\r\n        for (const qf of response.quotationFreight) {\r\n          // 一条费用拆分成两条(应收和应付)\r\n          let chargePay = {}\r\n          chargePay.showClient = false\r\n          chargePay.showSupplier = false\r\n          chargePay.showQuotationCharge = false\r\n          chargePay.showCostCharge = false\r\n          chargePay.showQuotationCurrency = false\r\n          chargePay.showCostCurrency = false\r\n          chargePay.showQuotationUnit = false\r\n          chargePay.showCostUnit = false\r\n          chargePay.showStrategy = false\r\n          chargePay.showUnitRate = false\r\n          chargePay.showAmount = false\r\n          chargePay.showCurrencyRate = false\r\n          chargePay.showDutyRate = false\r\n\r\n          chargePay.companyName = qf.company\r\n          chargePay.clearingCompanyId = qf.companyId\r\n          chargePay.dnChargeNameId = qf.chargeId\r\n          chargePay.chargeName = qf.charge\r\n          chargePay.dnCurrencyCode = qf.quotationCurrencyCode\r\n          chargePay.dnUnitRate = qf.inquiryRate\r\n          chargePay.dnUnitCode = qf.unitCode\r\n          chargePay.dnAmount = qf.inquiryAmount\r\n          chargePay.basicCurrencyRate = qf.exchangeRate\r\n          chargePay.dutyRate = qf.taxRate\r\n          chargePay.subtotal = currency(chargePay.dnUnitRate).multiply(chargePay.dnAmount).multiply(chargePay.basicCurrencyRate).value\r\n\r\n          let chargeReceive = {}\r\n          chargeReceive.showClient = false\r\n          chargeReceive.showSupplier = false\r\n          chargeReceive.showQuotationCharge = false\r\n          chargeReceive.showCostCharge = false\r\n          chargeReceive.showQuotationCurrency = false\r\n          chargeReceive.showCostCurrency = false\r\n          chargeReceive.showQuotationUnit = false\r\n          chargeReceive.showCostUnit = false\r\n          chargeReceive.showStrategy = false\r\n          chargeReceive.showUnitRate = false\r\n          chargeReceive.showAmount = false\r\n          chargeReceive.showCurrencyRate = false\r\n          chargeReceive.showDutyRate = false\r\n\r\n          if (this.form.clientName) {\r\n            chargeReceive.clearingCompanyId = response.data.companyId\r\n            chargeReceive.companyName = response.data.company\r\n          }\r\n          chargeReceive.dnChargeNameId = qf.chargeId\r\n          chargeReceive.chargeName = qf.charge\r\n          chargeReceive.dnCurrencyCode = qf.quotationCurrencyCode\r\n          chargeReceive.dnUnitRate = qf.quotationRate\r\n          chargeReceive.dnUnitCode = qf.unitCode\r\n          chargeReceive.dnAmount = qf.quotationAmount\r\n          chargeReceive.basicCurrencyRate = qf.exchangeRate\r\n          chargeReceive.dutyRate = qf.taxRate\r\n          chargeReceive.subtotal = currency(chargeReceive.dnUnitRate).multiply(chargeReceive.dnAmount).multiply(chargeReceive.basicCurrencyRate).value\r\n\r\n          if (qf.serviceTypeId === 1) {\r\n            this.rsOpSeaFcl.rsChargeList.push(chargePay)\r\n            this.form.rsOpSeaFclList.map(item => {\r\n              item.rsChargeList.push(chargePay)\r\n            })\r\n          }\r\n          if (qf.serviceTypeId === 2) {\r\n            this.rsOpSeaLcl.rsChargeList.push(chargePay)\r\n            this.form.rsOpSeaLclList.map(item => {\r\n              item.rsChargeList.push(chargePay)\r\n            })\r\n          }\r\n          if (qf.serviceTypeId === 10) {\r\n            this.rsOpAir.rsChargeList.push(chargePay)\r\n            this.form.rsOpAirList.map(item => {\r\n              item.rsChargeList.push(chargePay)\r\n            })\r\n          }\r\n          if (qf.serviceTypeId === 20) {\r\n            this.rsOpRailFCL.rsChargeList.push(chargePay)\r\n          }\r\n          if (qf.serviceTypeId === 21) {\r\n            this.rsOpRailLCL.rsChargeList.push(chargePay)\r\n          }\r\n          if (qf.serviceTypeId === 40) {\r\n            this.rsOpExpress.rsChargeList.push(chargePay)\r\n          }\r\n          if (qf.serviceTypeId === 50) {\r\n            this.rsOpCtnrTruck.rsChargeList.push(chargePay)\r\n            this.form.rsOpCtnrTruckList.map(item => {\r\n              item.rsChargeList.push(chargePay)\r\n            })\r\n          }\r\n          if (qf.serviceTypeId === 51) {\r\n            this.rsOpBulkTruck.rsChargeList.push(chargePay)\r\n            this.form.rsOpBulkTruckList.map(item => {\r\n              item.rsChargeList.push(chargePay)\r\n            })\r\n          }\r\n          if (qf.serviceTypeId === 60) {\r\n            this.rsOpDocDeclare.rsChargeList.push(chargePay)\r\n            this.form.rsOpDocDeclareList.map(item => {\r\n              item.rsChargeList.push(chargePay)\r\n            })\r\n          }\r\n          if (qf.serviceTypeId === 61) {\r\n            this.rsOpFreeDeclare.rsChargeList.push(chargePay)\r\n            this.form.rsOpFreeDeclareList.map(item => {\r\n              item.rsChargeList.push(chargePay)\r\n            })\r\n          }\r\n          if (qf.serviceTypeId === 70) {\r\n            this.rsOpDOAgent.rsChargeList.push(chargePay)\r\n          }\r\n          if (qf.serviceTypeId === 71) {\r\n            this.rsOpClearAgent.rsChargeList.push(chargePay)\r\n          }\r\n          if (qf.serviceTypeId === 80) {\r\n            this.rsOpWHS.rsChargeList.push(chargePay)\r\n          }\r\n          if (qf.serviceTypeId === 90) {\r\n            this.rsOp3rdCert.rsChargeList.push(chargePay)\r\n          }\r\n          if (qf.serviceTypeId === 100) {\r\n            this.rsOpINS.rsChargeList.push(chargePay)\r\n          }\r\n          if (qf.serviceTypeId === 101) {\r\n            this.rsOpTrading.rsChargeList.push(chargePay)\r\n          }\r\n          if (qf.serviceTypeId === 102) {\r\n            this.rsOpFumigation.rsChargeList.push(chargePay)\r\n          }\r\n          if (qf.serviceTypeId === 103) {\r\n            this.rsOpCO.rsChargeList.push(chargePay)\r\n          }\r\n          if (qf.serviceTypeId === 104) {\r\n            this.rsOpOther.rsChargeList.push(chargePay)\r\n          }\r\n\r\n          this.rsClientMessage.rsChargeList.push(chargeReceive)\r\n        }\r\n      })\r\n    },\r\n    generateFreight(type, serviceTypeId, item) {\r\n      console.log(item)\r\n      if (!this.form.revenueTon) {\r\n        this.$modal.msgWarning(\"请先录入计费货量\")\r\n        return\r\n      }\r\n\r\n      this.curFreightSelectRow = item\r\n\r\n      this.freightSelectData.typeId = type\r\n      if (type != 5) {\r\n        this.freightSelectData.destinationPortId = this.form.destinationPortId\r\n      }\r\n      if (type == 5) {\r\n        this.freightSelectData.precarriageRegionId = this.form.precarriageRegionId\r\n      }\r\n      this.freightSelectData.polId = this.form.polId\r\n      this.freightSelectData.serviceTypeId = serviceTypeId\r\n      this.freightSelectData.revenueTonList = this.form.revenueTon.split(\"+\")\r\n      this.freightSelectData.locationOptions = this.locationOptions\r\n\r\n      this.openFreightSelect = true\r\n    },\r\n    currency,\r\n    async getRctDetail(id) {\r\n      this.reset()\r\n      await getRct(id).then(response => {\r\n        if (response.outboundRecord) {\r\n          this.outboundForm = response.outboundRecord\r\n        }\r\n\r\n        this.form = response.data\r\n\r\n        this.showPsaRct = response.data.psaRctId ? true : false\r\n\r\n        this.form.serviceTypeIds = response.data.serviceTypeIds ? response.data.serviceTypeIds : this.form.serviceTypeIdList.split(\",\")\r\n\r\n        this.bookingMessageList = response.data.bookingMessagesList ? response.data.bookingMessagesList : []\r\n\r\n        this.form.noTransferAllowed = response.data.noTransferAllowed === \"1\"\r\n        this.form.noDividedAllowed = response.data.noDividedAllowed === \"1\"\r\n        this.form.noAgreementShowed = response.data.noAgreementShowed === \"1\"\r\n        this.form.isCustomsIntransitShowed = response.data.isCustomsIntransitShowed === \"1\"\r\n        this.grossWeight = response.data.grossWeight\r\n        this.goodsValue = response.data.goodsValue\r\n        let cIds = new Set()\r\n        let rr = []\r\n        // 关联客户\r\n        if (response.data.relationClientIds) {\r\n          response.data.relationClientIds.split(\",\").forEach(v => {\r\n            rr.push(Number(v))\r\n          })\r\n        }\r\n        this.relationClientIds = rr\r\n        this.form.relationClientIds = rr\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.salesId) {\r\n                      this.salesId = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.salesAssistantId) {\r\n                      this.salesAssistantId = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.salesObserverId) {\r\n                      this.salesObserverId = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.opList != undefined) {\r\n          for (const a of this.opList) {\r\n            if (a.staffId == response.data.opId) {\r\n              this.opId = a.roleId\r\n            }\r\n\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (a.role.roleLocalName == \"操作员\" && b.staffId == response.data.opId) {\r\n                  this.opId = b.roleId\r\n                }\r\n                if (a.role.roleLocalName == \"订舱员\" && b.staffId == response.data.bookingOpId) {\r\n                  this.bookingOpId = b.roleId\r\n                }\r\n                if (a.role.roleLocalName == \"单证员\" && b.staffId == response.data.docOpId) {\r\n                  this.docOpId = b.roleId\r\n                }\r\n                if (b.staffId == response.data.opObserverId) {\r\n                  this.opObserverId = b.roleId\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        this.verifyPsaId = response.data.verifyPsaId\r\n        this.opId = response.data.opId\r\n\r\n        if (response.data.rsClientMessage != null) {\r\n          this.rsClientMessage = response.data.rsClientMessage\r\n          this.rsClientServiceInstance = response.data.rsClientMessage.rsServiceInstances\r\n        }\r\n        if (response.data.serviceTypeIds.indexOf(20) !== -1 && response.data.rsOpRailFCL !== null) {\r\n          this.rsOpRailFCL = response.data.rsOpRailFCL\r\n          this.rsOpRailFclServiceInstance = response.data.rsOpRailFCL.rsServiceInstances\r\n        }\r\n        if (response.data.serviceTypeIds.indexOf(21) !== -1 && response.data.rsOpRailLCL !== null) {\r\n          this.rsOpRailLCL = response.data.rsOpRailLCL\r\n          this.rsOpRailLclServiceInstance = response.data.rsOpRailLCL.rsServiceInstances\r\n        }\r\n        if (response.data.serviceTypeIds.indexOf(40) !== -1 && response.data.rsOpExpress !== null) {\r\n          this.rsOpExpress = response.data.rsOpExpress\r\n          this.rsOpExpressServiceInstance = response.data.rsOpExpress.rsServiceInstances\r\n        }\r\n        if (response.data.serviceTypeIds.indexOf(70) !== -1 && response.data.rsOpDOAgent !== null) {\r\n          this.rsOpDOAgent = response.data.rsOpDOAgent\r\n          this.rsOpDOAgentServiceInstance = response.data.rsOpDOAgent.rsServiceInstances\r\n        }\r\n        if (response.data.serviceTypeIds.indexOf(71) !== -1 && response.data.rsOpClearAgent !== null) {\r\n          this.rsOpClearAgent = response.data.rsOpClearAgent\r\n          this.rsOpClearAgentServiceInstance = response.data.rsOpClearAgent.rsServiceInstances\r\n        }\r\n        if (response.data.serviceTypeIds.indexOf(80) !== -1 && response.data.rsOpWHS !== null) {\r\n          this.rsOpWHS = response.data.rsOpWHS\r\n          this.rsOpWHSServiceInstance = response.data.rsOpWHS.rsServiceInstances\r\n        }\r\n        if (response.data.serviceTypeIds.indexOf(90) !== -1 && response.data.rsOp3rdCert !== null) {\r\n          this.rsOp3rdCert = response.data.rsOp3rdCert\r\n          this.rsOp3rdCertServiceInstance = response.data.rsOp3rdCert.rsServiceInstances\r\n        }\r\n\r\n        if (response.data.serviceTypeIds.indexOf(100) !== -1 && response.data.rsOpINS !== null) {\r\n          this.rsOpINS = response.data.rsOpINS\r\n          this.rsOpINSServiceInstance = response.data.rsOpINS.rsServiceInstances\r\n        }\r\n        if (response.data.serviceTypeIds.indexOf(101) !== -1 && response.data.rsOpTrading !== null) {\r\n          this.rsOpTrading = response.data.rsOpTrading\r\n          this.rsOpTradingServiceInstance = response.data.rsOpTrading.rsServiceInstances\r\n        }\r\n        if (response.data.serviceTypeIds.indexOf(102) !== -1 && response.data.rsOpFumigation !== null) {\r\n          this.rsOpFumigation = response.data.rsOpFumigation\r\n          this.rsOpFumigationServiceInstance = response.data.rsOpFumigation.rsServiceInstances\r\n        }\r\n        if (response.data.serviceTypeIds.indexOf(103) !== -1 && response.data.rsOpCO !== null) {\r\n          this.rsOpCO = response.data.rsOpCO\r\n          this.rsOpCOServiceInstance = response.data.rsOpCO.rsServiceInstances\r\n        }\r\n        if (response.data.serviceTypeIds.indexOf(104) !== -1 && response.data.rsOpOther !== null) {\r\n          this.rsOpOther = response.data.rsOpOther\r\n          this.rsOpOtherServiceInstance = response.data.rsOpOther.rsServiceInstances\r\n        }\r\n\r\n        // 客户信息审核\r\n        this.opConfirmedName = response.data.rsClientMessage.rsServiceInstances.isDnOpConfirmed ? this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isDnOpConfirmed)[0].staffFamilyLocalName + \"\" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isDnOpConfirmed)[0].staffGivingLocalName : null\r\n        this.opConfirmedDate = response.data.rsClientMessage.rsServiceInstances.opConfirmedTime ? response.data.rsClientMessage.rsServiceInstances.opConfirmedTime : null\r\n        this.accountConfirmedName = response.data.rsClientMessage.rsServiceInstances.isAccountConfirmed ? this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isAccountConfirmed)[0].staffFamilyLocalName + \"\" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isAccountConfirmed)[0].staffGivingLocalName : null\r\n        this.accountConfirmedDate = response.data.rsClientMessage.rsServiceInstances.accountConfirmTime ? response.data.rsClientMessage.rsServiceInstances.accountConfirmTime : null\r\n        this.clientConfirmedName = response.data.rsClientMessage.rsServiceInstances.isDnClientConfirmed ? this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isDnClientConfirmed)[0].staffFamilyLocalName + \"\" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isDnClientConfirmed)[0].staffGivingLocalName : null\r\n        this.clientConfirmedDate = response.data.rsClientMessage.rsServiceInstances.clientConfirmedTime ? response.data.rsClientMessage.rsServiceInstances.clientConfirmedTime : null\r\n        this.salesConfirmedName = response.data.rsClientMessage.rsServiceInstances.isDnSalesConfirmed ? this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isDnSalesConfirmed)[0].staffFamilyLocalName + \"\" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == response.data.rsClientMessage.rsServiceInstances.isDnSalesConfirmed)[0].staffGivingLocalName : null\r\n        this.salesConfirmedDate = response.data.rsClientMessage.rsServiceInstances.salesConfirmedTime ? response.data.rsClientMessage.rsServiceInstances.salesConfirmedTime : null\r\n\r\n        this.locationOptions = response.locationOptions\r\n        this.RelationClientIdList = []\r\n        response.data.relationClientIdList ? response.data.relationClientIdList.split(\",\").map(v => this.RelationClientIdList.push(Number(v))) : []\r\n\r\n        this.relationClientLists.push(response.companyList)\r\n        this.companyList = response.companyList.filter(v => v.companyId === response.data.clientId).concat(response.companyList.filter(v => v.companyId !== response.data.clientId))\r\n\r\n        this.supplierList = response.supplierList\r\n\r\n        /* let clientCompany = response.companyList.filter(v => v.companyId === response.data.clientId).length > 0 ? response.companyList.filter(v => v.companyId === response.data.clientId)[0] : null\r\n        if (clientCompany) {\r\n          this.form.clientName = clientCompany.companyTaxCode + \"/\" + clientCompany.companyShortName + \"/\" + clientCompany.companyLocalName\r\n        } */\r\n        this.form.clientName = response.data.clientSummary\r\n\r\n        // 箱型特征\r\n        this.ctnrTypeCodeIds = this.form.ctnrTypeCode ? this.form.ctnrTypeCode.split(\",\") : []\r\n        this.cargoTypeCodes = this.form.cargoTypeCodeSum ? this.form.cargoTypeCodeSum.split(\",\") : []\r\n\r\n        // 折叠信息以及显示信息\r\n        if (response.data.messageDisplay) {\r\n          response.data.messageDisplay.split(\",\").forEach((v, i) => {\r\n            if (i === 0) v == 1 ? this.serviceInfo = true : this.serviceInfo = false\r\n            if (i === 1) v == 1 ? this.orderInfo = true : this.orderInfo = false\r\n            if (i === 2) v == 1 ? this.branchInfo = true : this.branchInfo = false\r\n            if (i === 3) v == 1 ? this.logisticsInfo = true : this.logisticsInfo = false\r\n            if (i === 4) v == 1 ? this.docInfo = true : this.docInfo = false\r\n            if (i === 5) v == 1 ? this.chargeInfo = true : this.chargeInfo = false\r\n            if (i === 6) v == 1 ? this.auditInfo = true : this.auditInfo = false\r\n          })\r\n        }\r\n        if (response.data.serviceMessageFold) {\r\n          response.data.serviceMessageFold.split(\",\").forEach((v, i) => {\r\n            if (i === 0) v == 1 ? this.clientMessage = true : this.clientMessage = false\r\n            if (i === 1) v == 1 ? this.rsOpSealFclFold = true : this.rsOpSealFclFold = false\r\n            if (i === 2) v == 1 ? this.rsOpSealLclFold = true : this.rsOpSealLclFold = false\r\n            if (i === 3) v == 1 ? this.rsOpAirFold = true : this.rsOpAirFold = false\r\n            if (i === 4) v == 1 ? this.rsOpRailFclFold = true : this.rsOpRailFclFold = false\r\n            if (i === 5) v == 1 ? this.rsOpRailLclFold = true : this.rsOpRailLclFold = false\r\n            if (i === 6) v == 1 ? this.rsOpExpressFold = true : this.rsOpExpressFold = false\r\n            if (i === 7) v == 1 ? this.rsOpCtnrTruckFold = true : this.rsOpCtnrTruckFold = false\r\n            if (i === 8) v == 1 ? this.rsOpBulkTruckFold = true : this.rsOpBulkTruckFold = false\r\n            if (i === 9) v == 1 ? this.rsOpDocDeclareFold = true : this.rsOpDocDeclareFold = false\r\n            if (i === 10) v == 1 ? this.rsOpFreeDeclareFold = true : this.rsOpFreeDeclareFold = false\r\n            if (i === 11) v == 1 ? this.rsOpDOAgentFold = true : this.rsOpDOAgentFold = false\r\n            if (i === 12) v == 1 ? this.rsOpClearAgentFold = true : this.rsOpClearAgentFold = false\r\n            if (i === 13) v == 1 ? this.rsOpWHSFold = true : this.rsOpWHSFold = false\r\n            if (i === 14) v == 1 ? this.rsOp3rdCertFold = true : this.rsOp3rdCertFold = false\r\n            if (i === 15) v == 1 ? this.rsOpINSFold = true : this.rsOpINSFold = false\r\n            if (i === 16) v == 1 ? this.rsOpTradingFold = true : this.rsOpTradingFold = false\r\n            if (i === 17) v == 1 ? this.rsOpFumigationFold = true : this.rsOpFumigationFold = false\r\n            if (i === 18) v == 1 ? this.rsOpCOFold = true : this.rsOpCOFold = false\r\n            if (i === 19) v == 1 ? this.rsOpOtherFold = true : this.rsOpOtherFold = false\r\n          })\r\n        }\r\n\r\n        response.data.rsOpSeaFclList ? response.data.rsOpSeaFclList.map(item => {\r\n          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false\r\n          return item\r\n        }) : null\r\n        response.data.rsOpSeaLclList ? response.data.rsOpSeaLclList.map(item => {\r\n          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false\r\n          return item\r\n        }) : null\r\n        response.data.rsOpAirList ? response.data.rsOpAirList.map(item => {\r\n          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false\r\n          return item\r\n        }) : null\r\n        response.data.rsOpCtnrTruckList ? response.data.rsOpCtnrTruckList.map(item => {\r\n          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false\r\n          return item\r\n        }) : null\r\n        response.data.rsOpBulkTruckList ? response.data.rsOpBulkTruckList.map(item => {\r\n          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false\r\n          return item\r\n        }) : null\r\n        response.data.rsOpDocDeclareList ? response.data.rsOpDocDeclareList.map(item => {\r\n          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false\r\n          return item\r\n        }) : null\r\n        response.data.rsOpFreeDeclareList ? response.data.rsOpFreeDeclareList.map(item => {\r\n          item.rsServiceInstances.serviceFold == 1 ? item.rsServiceInstances.serviceFold = true : item.rsServiceInstances.serviceFold = false\r\n          return item\r\n        }) : null\r\n\r\n        // 默认收发通\r\n        // this.form.bookingShipper = response.data.bookingShipper ? response.data.bookingShipper : \"GUANGZHOU RICH SHIPPING INT'L CO., LTD.\"\r\n        this.form.bookingShipper = response.data.bookingShipper\r\n          ? response.data.bookingShipper\r\n          : (this.form.rctNo && this.form.rctNo.startsWith(\"CFL\")\r\n            ? \"GUANGZHOU CHERISH FREIGHT INT'L CO.,LTD.\"\r\n            : \"GUANGZHOU RICH SHIPPING INT'L CO., LTD.\")\r\n        this.form.bookingConsignee = response.data.bookingConsignee ? response.data.bookingConsignee : \"TO ORDER\"\r\n        this.form.bookingNotifyParty = response.data.bookingNotifyParty ? response.data.bookingNotifyParty : \"SAME AS CONSIGNEE\"\r\n      })\r\n    },\r\n    // 操作单驳回\r\n    turnDown() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        this.form.opId = null\r\n        this.form.psaVerifyStatusId = 0\r\n        this.form.psaVerify = 0\r\n        if (this.form.rctId != null) {\r\n          this.form.noTransferAllowed = this.form.noTransferAllowed ? 1 : 0\r\n          this.form.noDividedAllowed = this.form.noDividedAllowed ? 1 : 0\r\n          this.form.noAgreementShowed = this.form.noAgreementShowed ? 1 : 0\r\n          this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? 1 : 0\r\n          updateRct(this.form).then(response => {\r\n            this.form.noTransferAllowed = this.form.noTransferAllowed ? true : false\r\n            this.form.noDividedAllowed = this.form.noDividedAllowed ? true : false\r\n            this.form.noAgreementShowed = this.form.noAgreementShowed ? true : false\r\n            this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? true : false\r\n            this.$modal.msgSuccess(\"操作单已驳回\")\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 使用防抖包装实际的提交方法\r\n    submitForm: debounce(async function (type) {\r\n      if (this.isSubmitting) {\r\n        return // 防止重复提交\r\n      }\r\n      this.isSubmitting = true\r\n\r\n      this.$refs[\"form\"].validate(async valid => {\r\n        // 使用服务管理器批量处理服务实例\r\n        this.serviceManager.processServiceInstances(this.serviceManager.getAllServiceTypeIds(), this)\r\n\r\n        this.form.relationClientIdList = this.RelationClientIdList.toString()\r\n        this.form.serviceTypeIdList = this.serviceList.toString()\r\n        // TODO\r\n        if ((this.rsOpBulkTruck.rsOpTruckList && this.rsOpBulkTruck.rsOpTruckList) || (this.rsOpCtnrTruck.rsOpTruckList && this.rsOpCtnrTruck.rsOpTruckList.length > 0)) {\r\n          if (this.serviceList.has(50)) {\r\n            this.form.rsOpCtnrTruckList.map(item => {\r\n              item.sqdContainersSealsSum = item.rsOpTruckList.map(v => v.containerNo).join(\",\")\r\n            })\r\n          } else if (this.serviceList.has(51)) {\r\n            this.form.rsOpBulkTruckList.map(item => {\r\n              item.sqdContainersSealsSum = item.rsOpTruckList.map(v => v.containerNo).join(\",\")\r\n            })\r\n          }\r\n        }\r\n\r\n        // 提单信息列表\r\n        this.form.bookingMessagesList = this.bookingMessageList\r\n\r\n        // 优化函数：分割字符串并添加到Set集合\r\n        const addToSet = (text, set, separator = \"/\") => {\r\n          if (!text) return\r\n          text.split(separator)\r\n            .map(item => item.trim())\r\n            .filter(Boolean)\r\n            .forEach(item => set.add(item))\r\n        }\r\n\r\n        // 优化函数：从数组对象中提取字段并添加到Set集合\r\n        const addFromList = (list, fieldName, set, separator = /\\r\\n|\\r|\\n/) => {\r\n          if (!list?.length) return\r\n          list.forEach(item => {\r\n            if (item[fieldName]) {\r\n              addToSet(item[fieldName], set, separator)\r\n            }\r\n          })\r\n        }\r\n\r\n        // 获取FCL或LCL列表的第一项\r\n        const getFclOrLcl = (form) => {\r\n          return form.rsOpSeaFclList?.[0] || form.rsOpSeaLclList?.[0] || null\r\n        }\r\n\r\n        // 更新表单值\r\n        const updateFormValue = (form, fieldName, value) => {\r\n          if (form.rsOpSeaFclList?.[0]) {\r\n            form.rsOpSeaFclList[0][fieldName] = value\r\n          }\r\n          if (form.rsOpSeaLclList?.[0]) {\r\n            form.rsOpSeaLclList[0][fieldName] = value\r\n          }\r\n          form[fieldName] = value\r\n        }\r\n\r\n        // 处理集装箱号\r\n        const processContainers = () => {\r\n          const allContainers = new Set()\r\n\r\n          // 从表单中添加集装箱号\r\n          const firstItem = getFclOrLcl(this.form)\r\n          if (firstItem?.sqdContainersSealsSum) {\r\n            addToSet(firstItem.sqdContainersSealsSum, allContainers)\r\n          }\r\n\r\n          // 从bookingMessageList添加集装箱号\r\n          addFromList(this.bookingMessageList, \"containerNo\", allContainers)\r\n\r\n          // 从rsOpCtnrTruckList添加集装箱号\r\n          if (this.form.rsOpCtnrTruckList?.[0]?.rsOpTruckList) {\r\n            this.form.rsOpCtnrTruckList[0].rsOpTruckList\r\n              .map(item => item.containerNo?.trim())\r\n              .filter(Boolean)\r\n              .forEach(item => allContainers.add(item))\r\n          }\r\n\r\n          // 从rsOpBulkTruckList添加集装箱号\r\n          if (this.form.rsOpBulkTruckList?.[0]?.rsOpTruckList) {\r\n            this.form.rsOpBulkTruckList[0].rsOpTruckList\r\n              .map(item => item.containerNo?.trim())\r\n              .filter(Boolean)\r\n              .forEach(item => allContainers.add(item))\r\n          }\r\n\r\n          // 更新表单字段\r\n          const containersStr = Array.from(allContainers).join(\"/\")\r\n          updateFormValue(this.form, \"sqdContainersSealsSum\", containersStr)\r\n        }\r\n\r\n        // 处理提单号\r\n        const processBlNos = () => {\r\n          const allBlNos = new Set()\r\n\r\n          // 从表单中添加提单号\r\n          const firstItem = getFclOrLcl(this.form)\r\n          if (firstItem?.blNo) {\r\n            addToSet(firstItem.blNo, allBlNos)\r\n          }\r\n\r\n          // 从bookingMessageList添加提单号\r\n          addFromList(this.bookingMessageList, \"mBlNo\", allBlNos)\r\n\r\n          // 更新表单字段\r\n          const blNoStr = Array.from(allBlNos).join(\"/\")\r\n          updateFormValue(this.form, \"blNo\", blNoStr)\r\n        }\r\n\r\n        // 执行处理\r\n        processContainers()\r\n        processBlNos()\r\n\r\n        // 获取汇率\r\n        let exchangeRate\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (this.form.podEta) {\r\n            if (a.localCurrency === \"RMB\"\r\n              && \"USD\" == a.overseaCurrency\r\n              && parseTime(a.validFrom) <= parseTime(this.form.podEta)\r\n              && parseTime(this.form.podEta) <= parseTime(a.validTo)\r\n            ) {\r\n              exchangeRate = currency(a.settleRate).divide(a.base).value\r\n            }\r\n          } else {\r\n            if (a.localCurrency === \"RMB\"\r\n              && \"USD\" == a.overseaCurrency\r\n              && parseTime(a.validFrom) <= parseTime(new Date())\r\n              && parseTime(new Date()) <= parseTime(a.validTo)) {\r\n              exchangeRate = currency(a.settleRate).divide(a.base).value\r\n            }\r\n          }\r\n        }\r\n\r\n        this.rsClientMessageCharge(this.rsClientMessage.rsChargeList)\r\n\r\n        // 费用\r\n        this.form.dnRmb = this.rsClientMessageReceivableTaxRMB // rmb含税应收\r\n        this.form.dnUsd = this.rsClientMessageReceivableTaxUSD // usd含税应收\r\n        this.form.cnRmb = this.rsClientMessagePayableTaxRMB // rmb含税应付\r\n        this.form.cnUsd = this.rsClientMessagePayableTaxUSD // usd含税应付\r\n        // 折合费用\r\n        this.form.dnInRmb = currency(this.rsClientMessageReceivableTaxRMB).add(currency(this.rsClientMessageReceivableTaxUSD).multiply(exchangeRate)).value // 折合rmb含税应收\r\n        this.form.cnInRmb = currency(this.rsClientMessagePayableTaxRMB).add(currency(this.rsClientMessagePayableTaxUSD).multiply(exchangeRate)).value // 折合rmb含税应付\r\n\r\n        // 统计收款和付款进度\r\n        this.form.dnRmbBalance = this.sqdUnreceivedRmbSum // rmb含税未收\r\n        this.form.dnUsdBalance = this.sqdUnreceivedUsdSum // usd含税未收\r\n        this.form.cnRmbBalance = this.sqdUnpaidRmbSum // rmb含税未付\r\n        this.form.cnUsdBalance = this.sqdUnpaidUsdSum // usd含税未付\r\n        // 折合进度\r\n        this.form.dnInRmbBalance = currency(this.sqdUnreceivedRmbSum).add(currency(this.sqdUnreceivedUsdSum).multiply(exchangeRate)).value // 折合rmb未收\r\n        this.form.cnInRmbBalance = currency(this.sqdUnpaidRmbSum).add(currency(this.sqdUnpaidUsdSum).multiply(exchangeRate)).value // 折合rmb未付\r\n\r\n        // 利润\r\n        this.form.profitUsd = currency(this.form.dnUsd).subtract(this.form.cnUsd).value // 美元部分含税利润\r\n        this.form.profitRmb = currency(this.form.dnRmb).subtract(this.form.cnRmb).value // 人民币部分含税利润\r\n        this.form.profitInRmb = currency(this.form.profitUsd).multiply(exchangeRate).add(this.form.profitRmb).value // 折合人民币利润\r\n\r\n        this.form.noTransferAllowed = this.form.noTransferAllowed ? 1 : 0\r\n        this.form.noDividedAllowed = this.form.noDividedAllowed ? 1 : 0\r\n        this.form.noAgreementShowed = this.form.noAgreementShowed ? 1 : 0\r\n        this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? 1 : 0\r\n\r\n        this.form.messageDisplay = [Number(this.serviceInfo), Number(this.orderInfo), Number(this.branchInfo), Number(this.logisticsInfo), Number(this.docInfo), Number(this.chargeInfo), Number(this.auditInfo)].toString()\r\n        this.form.serviceMessageFold = [Number(this.clientMessage), Number(this.rsOpSealFclFold), Number(this.rsOpSealLclFold), Number(this.rsOpAirFold), Number(this.rsOpRailFclFold), Number(this.rsOpRailLclFold), Number(this.rsOpExpressFold), Number(this.rsOpCtnrTruckFold), Number(this.rsOpBulkTruckFold), Number(this.rsOpDocDeclareFold), Number(this.rsOpFreeDeclareFold), Number(this.rsOpDOAgentFold), Number(this.rsOpClearAgentFold), Number(this.rsOpWHSFold), Number(this.rsOp3rdCertFold), Number(this.rsOpINSFold), Number(this.rsOpTradingFold), Number(this.rsOpFumigationFold), Number(this.rsOpCOFold), Number(this.rsOpOtherFold)].toString()\r\n\r\n        this.form.rsOpSeaFclList ? this.form.rsOpSeaFclList.map(item => {\r\n          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n          return item\r\n        }) : null\r\n        this.form.rsOpSeaLclList ? this.form.rsOpSeaLclList.map(item => {\r\n          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n          return item\r\n        }) : null\r\n        this.form.rsOpAirList ? this.form.rsOpAirList.map(item => {\r\n          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n          return item\r\n        }) : null\r\n        this.form.rsOpCtnrTruckList ? this.form.rsOpCtnrTruckList.map(item => {\r\n          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n          return item\r\n        }) : null\r\n        this.form.rsOpBulkTruckList ? this.form.rsOpBulkTruckList.map(item => {\r\n          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n          return item\r\n        }) : null\r\n        this.form.rsOpDocDeclareList ? this.form.rsOpDocDeclareList.map(item => {\r\n          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n          return item\r\n        }) : null\r\n        this.form.rsOpFreeDeclareList ? this.form.rsOpFreeDeclareList.map(item => {\r\n          item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n          return item\r\n        }) : null\r\n\r\n        // 海运子服务合并主表信息\r\n        this.mergeSeaServiceWithMainService()\r\n\r\n        // 当目前海运订舱只有一个的时候\r\n        this.chooseWhenOnlyOne()\r\n\r\n        // 确保在提交前清除千分位分隔符\r\n        if (this.form.grossWeight) {\r\n          this.form.grossWeight = this.form.grossWeight.toString().replace(/,/g, \"\")\r\n        }\r\n\r\n        if (valid) {\r\n          let message = \"修改成功\"\r\n          // 商务审核\r\n          if (this.psaVerify && type === \"psa\") {\r\n            if (!this.form.psaVerifyStatusId) {\r\n              this.$message.warning(\"请选择审核状态\")\r\n            }\r\n            // TODO 驳回不需要选择操作\r\n            if (this.opId === null && this.form.psaVerifyStatusId == 1) {\r\n              this.$message.warning(\"请指派操作员\")\r\n              return\r\n            }\r\n\r\n            // 审核驳回\r\n            if (this.psaVerify && this.form.psaVerifyStatusId == 9) {\r\n              // if 商务驳回 then 商务日期=null，业务订舱日期=null，商务审核状态=未审核，业务订舱状态=未订舱\r\n              this.form.psaVerifyTime = null\r\n              this.form.newBookingTime = null\r\n              message = \"已驳回\"\r\n              this.form.sqdShippingBookingStatus = 0\r\n              this.form.psaVerify = 0\r\n            }\r\n            // 审核通过\r\n            if (this.psaVerify && this.form.psaVerifyStatusId == 1) {\r\n              message = \"审核通过\"\r\n              this.form.psaVerify = 1\r\n              this.form.psaVerifyTime = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n            }\r\n          }\r\n          // 确认订舱\r\n          if (type === \"booking\") {\r\n            if (this.form.verifyPsaId == null) {\r\n              this.$message.warning(\"请指定商务\")\r\n              return\r\n            }\r\n            this.form.sqdShippingBookingStatus = 1\r\n            // 折合含税报价\r\n            this.form.quotationInRmb = this.rsClientMessageReceivable\r\n            // 折合含税询价\r\n            this.form.inquiryInRmb = this.rsClientMessagePayable\r\n            // 预期不含税利润\r\n            // this.form.estimatedProfitInRmb = this.rsClientMessageProfitNoTax\r\n            // 预期含税利润\r\n            this.form.estimatedProfitInRmb = this.rsClientMessageProfit\r\n            // 报价备注,自动填写\r\n            if (this.form.qoutationSketch === \"\") {\r\n              this.form.rsOpSeaFclList && this.form.rsOpSeaFclList[0].rsChargeList ?\r\n                this.form.rsOpSeaFclList[0].rsChargeList.map(rsCharge => {\r\n                  this.form.qoutationSketch += rsCharge.chargeName + \":\\t\" + rsCharge.dnCurrencyCode + \" \" + rsCharge.dnUnitRate + \"/\" + rsCharge.dnUnitCode + \"\\n\"\r\n                })\r\n                : null\r\n              this.form.rsOpSeaLclList && this.form.rsOpSeaLclList[0].rsChargeList ?\r\n                this.form.rsOpSeaFclList[0].rsChargeList.map(rsCharge => {\r\n                  this.form.qoutationSketch += rsCharge.chargeName + \":\\t\" + rsCharge.dnCurrencyCode + \" \" + rsCharge.dnUnitRate + \"/\" + rsCharge.dnUnitCode + \"\\n\"\r\n                })\r\n                : null\r\n            }\r\n\r\n            this.form.newBookingTime = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n            this.form.psaVerify = 0\r\n            message = \"确认订舱\"\r\n          }\r\n          // 操作确认\r\n          if (this.$route.query.type === \"op\" && type === \"opConfirm\") {\r\n            if (this.form.rctNo === null) {\r\n              // this.generateRct(true)\r\n              // 生成rctNo\r\n              if (this.form.orderBelongsTo && this.form.orderBelongsTo === \"GZCF\") {\r\n                await getRctCFMon().then(v => {\r\n                  let num = v.data + 1\r\n                  if (num.toString().length < 3) {\r\n                    const j = 3 - (num.toString().length)\r\n                    for (let i = 0; i < j; i++) {\r\n                      num = \"0\" + num\r\n                    }\r\n                  }\r\n                  let date = new Date()\r\n                  let month = (date.getMonth() + Number(this.rct.month)).toString()\r\n                  let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)\r\n                  this.form.rctNo = this.rct.GZCFLeadingCharacter + year + (month.length == 1 ? \"0\" + month : month) + num.toString()\r\n                })\r\n              } else if (this.form.orderBelongsTo && this.form.orderBelongsTo === \"FSWH\") {\r\n                await getRctRSWHMon().then(v => {\r\n                  let num = v.data + 1\r\n                  if (num.toString().length < 3) {\r\n                    const j = 3 - (num.toString().length)\r\n                    for (let i = 0; i < j; i++) {\r\n                      num = \"0\" + num\r\n                    }\r\n                  }\r\n                  let date = new Date()\r\n                  let month = (date.getMonth() + Number(this.rct.month)).toString()\r\n                  let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)\r\n                  this.form.rctNo = this.rct.RSWHLeadingCharacter + year + (month.length == 1 ? \"0\" + month : month) + num.toString()\r\n                })\r\n              } else {\r\n                await getRctMon().then(v => {\r\n                  let num = v.data + 1\r\n                  if (num.toString().length < 3) {\r\n                    const j = 3 - (num.toString().length)\r\n                    for (let i = 0; i < j; i++) {\r\n                      num = \"0\" + num\r\n                    }\r\n                  }\r\n                  let date = new Date()\r\n                  let month = (date.getMonth() + Number(this.rct.month)).toString()\r\n                  let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)\r\n                  this.form.rctNo = this.rct.leadingCharacter + year + (month.length == 1 ? \"0\" + month : month) + num.toString()\r\n                })\r\n              }\r\n            }\r\n\r\n            // 接单日期TODO\r\n            this.form.rctCreateTime = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n            this.form.opAccept = 1\r\n            this.form.processStatusId = 6\r\n            message = \"操作接单成功\"\r\n          }\r\n\r\n          // 箱型特征\r\n          this.form.ctnrTypeCode = this.ctnrTypeCodeIds.toString()\r\n          // 货物特征\r\n          this.form.cargoTypeCodeSum = this.cargoTypeCodes.toString()\r\n\r\n          //  TODO 海运子服务剩一个时更新到主表\r\n\r\n          this.form.salesId = this.form.salesId ? this.form.salesId : this.$store.state.user.sid\r\n\r\n          // 每次保存更新状态时间\r\n          this.form.statusUpdateTime = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n          if (this.form.rctId != null) {\r\n            updateRct(this.form).then(response => {\r\n              this.saveAll(this.form.rctId)\r\n              this.$modal.msgSuccess(message)\r\n              /* this.open = false\r\n              this.getRctList() */\r\n              this.form.noTransferAllowed = this.form.noTransferAllowed ? true : false\r\n              this.form.noDividedAllowed = this.form.noDividedAllowed ? true : false\r\n              this.form.noAgreementShowed = this.form.noAgreementShowed ? true : false\r\n              this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? true : false\r\n\r\n              if (this.form.opAccept == 1 && (this.form.serviceTypeIds.indexOf(1) !== -1 || this.form.serviceTypeIds.indexOf(2) !== -1)) {\r\n                // TODO\r\n                // 更新商务订舱信息\r\n                updatePsarct({\r\n                  ...this.form, noTransferAllowed: null,\r\n                  noDividedAllowed: null,\r\n                  noAgreementShowed: null,\r\n                  isCustomsIntransitShowed: null\r\n                })\r\n              }\r\n              this.isSubmitting = false\r\n            }).catch(() => {\r\n              this.isSubmitting = false\r\n            })\r\n          } else {\r\n            // 操作订舱的时候在商务订舱信息中也添加一条记录\r\n            // 操作订舱就不需要操作接单,直接出现在操作单列表\r\n            if (this.$route.path === \"/opprocess/opdetail\" && this.formType === \"rct\") {\r\n              if (this.form.rctNo === null) {\r\n                // this.generateRct(true)\r\n                // 生成rctNo\r\n                if (this.form.orderBelongsTo && this.form.orderBelongsTo === \"GZCF\") {\r\n                  await getRctCFMon().then(v => {\r\n                    let num = v.data + 1\r\n                    if (num.toString().length < 3) {\r\n                      const j = 3 - (num.toString().length)\r\n                      for (let i = 0; i < j; i++) {\r\n                        num = \"0\" + num\r\n                      }\r\n                    }\r\n                    let date = new Date()\r\n                    let month = (date.getMonth() + Number(this.rct.month)).toString()\r\n                    let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)\r\n                    this.form.rctNo = this.rct.GZCFLeadingCharacter + year + (month.length == 1 ? \"0\" + month : month) + num.toString()\r\n                  })\r\n                } else if (this.form.orderBelongsTo && this.form.orderBelongsTo === \"RSWH\") {\r\n                  await getRctRSWHMon().then(v => {\r\n                    let num = v.data + 1\r\n                    if (num.toString().length < 3) {\r\n                      const j = 3 - (num.toString().length)\r\n                      for (let i = 0; i < j; i++) {\r\n                        num = \"0\" + num\r\n                      }\r\n                    }\r\n                    let date = new Date()\r\n                    let month = (date.getMonth() + Number(this.rct.month)).toString()\r\n                    let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)\r\n                    this.form.rctNo = this.rct.RSWHLeadingCharacter + year + (month.length == 1 ? \"0\" + month : month) + num.toString()\r\n                  })\r\n                } else {\r\n                  await getRctMon().then(v => {\r\n                    let num = v.data + 1\r\n                    if (num.toString().length < 3) {\r\n                      const j = 3 - (num.toString().length)\r\n                      for (let i = 0; i < j; i++) {\r\n                        num = \"0\" + num\r\n                      }\r\n                    }\r\n                    let date = new Date()\r\n                    let month = (date.getMonth() + Number(this.rct.month)).toString()\r\n                    let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)\r\n                    this.form.rctNo = this.rct.leadingCharacter + year + (month.length == 1 ? \"0\" + month : month) + num.toString()\r\n                  })\r\n                }\r\n              }\r\n              this.form.rctCreateTime = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n              this.form.opAccept = 1\r\n              this.form.processStatusId = 6\r\n              // 操作自定舱\r\n              this.form.sqdShippingBookingStatus = 1\r\n              this.form.psaVerifyStatusId = 1\r\n              this.form.psaVerify = 1\r\n\r\n              let data = this.form\r\n              data.bookingId = this.$store.state.user.sid\r\n              data.bookingStatus = \"1\" // 已订舱\r\n              data.bookingTime = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n              data.distributionStatus = \"2\" // 操作自订舱\r\n              // 主费用币种、单价、单位 = 例：usd8600/40HQ\r\n              data.rctNo = this.form.rctNo\r\n              data.clientShortName = this.form.clientName.split(\"/\")[1]\r\n              data.salesId = this.form.salesId\r\n              data.salesAssistantId = this.form.salesAssistantId\r\n              data.opId = this.$store.state.user.sid\r\n\r\n              addPsarct(data).then(response => {\r\n                // Psarct添加成功\r\n              }).catch(() => {\r\n                this.isSubmitting = false\r\n              })\r\n            }\r\n            addRct(this.form).then(response => {\r\n              this.form.rctId = response.data\r\n              this.saveAll(response.data)\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              /*  this.open = false\r\n              this.getRctList() */\r\n              this.form.noTransferAllowed = this.form.noTransferAllowed ? true : false\r\n              this.form.noDividedAllowed = this.form.noDividedAllowed ? true : false\r\n              this.form.noAgreementShowed = this.form.noAgreementShowed ? true : false\r\n              this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? true : false\r\n\r\n              // 新增的表是rct不是booking,所以地址栏变化\r\n              // this.$tab.openPage(\"订舱单明细\", \"/salesquotation/bookingDetail\", {rId: response.data, booking: true})\r\n              this.isSubmitting = false\r\n            }).catch(() => {\r\n              this.isSubmitting = false\r\n            })\r\n          }\r\n        } else {\r\n          this.$message.warning(\"请完整填写操作单\")\r\n          this.isSubmitting = false\r\n        }\r\n      })\r\n    }, 500),\r\n    saveAs() {\r\n      if (this.serviceList.has(20)) {\r\n        this.rsOpRailFCL.rsServiceInstances = this.rsOpRailFclServiceInstance\r\n        this.form.rsOpRailFCL = this.rsOpRailFCL\r\n      }\r\n      if (this.serviceList.has(21)) {\r\n        this.rsOpRailLCL.rsServiceInstances = this.rsOpRailLclServiceInstance\r\n        this.form.rsOpRailLCL = this.rsOpRailLCL\r\n      }\r\n      if (this.serviceList.has(40)) {\r\n        this.rsOpExpress.rsServiceInstances = this.rsOpExpressServiceInstance\r\n        this.form.rsOpExpress = this.rsOpExpress\r\n      }\r\n      if (this.serviceList.has(70)) {\r\n        this.rsOpDOAgent.rsServiceInstances = this.rsOpDOAgentServiceInstance\r\n        this.form.rsOpDOAgent = this.rsOpDOAgent\r\n      }\r\n      if (this.serviceList.has(71)) {\r\n        this.rsOpClearAgent.rsServiceInstances = this.rsOpClearAgentServiceInstance\r\n        this.form.rsOpClearAgent = this.rsOpClearAgent\r\n      }\r\n      if (this.serviceList.has(80)) {\r\n        this.rsOpWHS.rsServiceInstances = this.rsOpWHSServiceInstance\r\n        this.form.rsOpWHS = this.rsOpWHS\r\n      }\r\n      if (this.serviceList.has(90)) {\r\n        this.rsOp3rdCert.rsServiceInstances = this.rsOp3rdCertServiceInstance\r\n        this.form.rsOp3rdCert = this.rsOp3rdCert\r\n      }\r\n      if (this.serviceList.has(100)) {\r\n        this.rsOpINS.rsServiceInstances = this.rsOpINSServiceInstance\r\n        this.form.rsOpINS = this.rsOpINS\r\n      }\r\n      if (this.serviceList.has(101)) {\r\n        this.rsOpTrading.rsServiceInstances = this.rsOpTradingServiceInstance\r\n        this.form.rsOpTrading = this.rsOpTrading\r\n      }\r\n      if (this.serviceList.has(102)) {\r\n        this.rsOpFumigation.rsServiceInstances = this.rsOpFumigationServiceInstance\r\n        this.form.rsOpFumigation = this.rsOpFumigation\r\n      }\r\n      if (this.serviceList.has(103)) {\r\n        this.rsOpCO.rsServiceInstances = this.rsOpCOServiceInstance\r\n        this.form.rsOpCO = this.rsOpCO\r\n      }\r\n      if (this.serviceList.has(104)) {\r\n        this.rsOpOther.rsServiceInstances = this.rsOpOtherServiceInstance\r\n        this.form.rsOpOther = this.rsOpOther\r\n      }\r\n\r\n      this.form.relationClientIdList = this.RelationClientIdList.toString()\r\n      this.form.serviceTypeIdList = this.serviceList.toString()\r\n      // TODO\r\n      if ((this.rsOpBulkTruck.rsOpTruckList && this.rsOpBulkTruck.rsOpTruckList) || (this.rsOpCtnrTruck.rsOpTruckList && this.rsOpCtnrTruck.rsOpTruckList.length > 0)) {\r\n        if (this.serviceList.has(50)) {\r\n          this.form.rsOpCtnrTruckList.map(item => {\r\n            item.sqdContainersSealsSum = item.rsOpTruckList.map(v => v.containerNo).join(\",\")\r\n          })\r\n        } else if (this.serviceList.has(51)) {\r\n          this.form.rsOpBulkTruckList.map(item => {\r\n            item.sqdContainersSealsSum = item.rsOpTruckList.map(v => v.containerNo).join(\",\")\r\n          })\r\n        }\r\n      }\r\n\r\n      // 提单信息列表\r\n      this.form.bookingMessagesList = this.bookingMessageList\r\n\r\n      // 创建一个Set来存储所有不重复的集装箱号\r\n      let allContainers = new Set()\r\n\r\n      // 首先添加表单中已有的集装箱号\r\n      if (this.form.sqdContainersSealsSum) {\r\n        this.form.sqdContainersSealsSum.split(\"/\")\r\n          .map(container => container.trim())\r\n          .filter(container => container)\r\n          .forEach(container => allContainers.add(container))\r\n      }\r\n\r\n      // 然后添加bookingMessageList中的集装箱号\r\n      if (this.bookingMessageList && this.bookingMessageList.length > 0) {\r\n        this.bookingMessageList.forEach(item => {\r\n          if (item.containerNo) {\r\n            item.containerNo.split(\"/\")\r\n              .map(container => container.trim())\r\n              .filter(container => container)\r\n              .forEach(container => allContainers.add(container))\r\n          }\r\n        })\r\n      }\r\n\r\n      // 最后，将Set转换为逗号分隔的字符串\r\n      this.form.sqdContainersSealsSum = Array.from(allContainers).join(\"/\")\r\n\r\n      // 同样处理blNo\r\n      let allBlNos = new Set()\r\n\r\n      // 添加表单中已有的提单号\r\n      if (this.form.blNo) {\r\n        allBlNos.add(this.form.blNo.trim())\r\n      }\r\n\r\n      // 添加bookingMessageList中的提单号\r\n      if (this.bookingMessageList && this.bookingMessageList.length > 0) {\r\n        this.bookingMessageList.forEach(item => {\r\n          if (item.mBlNo) {\r\n            allBlNos.add(item.mBlNo.trim())\r\n          }\r\n        })\r\n      }\r\n\r\n      // 将Set转换为逗号分隔的字符串\r\n      this.form.blNo = Array.from(allBlNos).filter(blNo => blNo).join(\"/\")\r\n\r\n      let exchangeRate\r\n      for (const a of this.$store.state.data.exchangeRateList) {\r\n        if (this.form.podEta) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && \"USD\" == a.overseaCurrency\r\n            && parseTime(a.validFrom) <= parseTime(this.form.podEta)\r\n            && parseTime(this.form.podEta) <= parseTime(a.validTo)\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        } else {\r\n          if (a.localCurrency === \"RMB\"\r\n            && \"USD\" == a.overseaCurrency\r\n            && parseTime(a.validFrom) <= parseTime(new Date())\r\n            && parseTime(new Date()) <= parseTime(a.validTo)) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      }\r\n\r\n      // 费用\r\n      this.form.dnRmb = this.rsClientMessageReceivableTaxRMB // rmb含税应收\r\n      this.form.dnUsd = this.rsClientMessageReceivableTaxUSD // usd含税应收\r\n      this.form.cnRmb = this.rsClientMessagePayableTaxRMB // rmb含税应付\r\n      this.form.cnUsd = this.rsClientMessagePayableTaxUSD // usd含税应付\r\n      // 折合费用\r\n      this.form.dnInRmb = currency(this.rsClientMessageReceivableTaxRMB).add(currency(this.rsClientMessageReceivableTaxUSD).multiply(exchangeRate)).value // 折合rmb含税应收\r\n      this.form.cnInRmb = currency(this.rsClientMessagePayableTaxRMB).add(currency(this.rsClientMessagePayableTaxUSD).multiply(exchangeRate)).value // 折合rmb含税应付\r\n\r\n      // 统计收款和付款进度\r\n      this.form.dnRmbBalance = this.sqdUnreceivedRmbSum // rmb含税未收\r\n      this.form.dnUsdBalance = this.sqdUnreceivedUsdSum // usd含税未收\r\n      this.form.cnRmbBalance = this.sqdUnpaidRmbSum // rmb含税未付\r\n      this.form.cnUsdBalance = this.sqdUnpaidUsdSum // usd含税未付\r\n      // 折合进度\r\n      this.form.dnInRmbBalance = currency(this.sqdUnreceivedRmbSum).add(currency(this.sqdUnreceivedUsdSum).multiply(exchangeRate)).value // 折合rmb未收\r\n      this.form.cnInRmbBalance = currency(this.sqdUnpaidRmbSum).add(currency(this.sqdUnpaidUsdSum).multiply(exchangeRate)).value // 折合rmb未付\r\n\r\n      // 利润\r\n      this.form.profitUsd = currency(this.form.dnUsd).subtract(this.form.cnUsd).value // 美元部分含税利润\r\n      this.form.profitRmb = currency(this.form.dnRmb).subtract(this.form.cnRmb).value // 人民币部分含税利润\r\n      this.form.profitInRmb = currency(this.form.profitUsd).multiply(exchangeRate).add(this.form.profitRmb).value // 折合人民币利润\r\n\r\n      this.form.noTransferAllowed = this.form.noTransferAllowed ? 1 : 0\r\n      this.form.noDividedAllowed = this.form.noDividedAllowed ? 1 : 0\r\n      this.form.noAgreementShowed = this.form.noAgreementShowed ? 1 : 0\r\n      this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? 1 : 0\r\n\r\n      this.form.messageDisplay = [Number(this.serviceInfo), Number(this.orderInfo), Number(this.branchInfo), Number(this.logisticsInfo), Number(this.docInfo), Number(this.chargeInfo), Number(this.auditInfo)].toString()\r\n      this.form.serviceMessageFold = [Number(this.clientMessage), Number(this.rsOpSealFclFold), Number(this.rsOpSealLclFold), Number(this.rsOpAirFold), Number(this.rsOpRailFclFold), Number(this.rsOpRailLclFold), Number(this.rsOpExpressFold), Number(this.rsOpCtnrTruckFold), Number(this.rsOpBulkTruckFold), Number(this.rsOpDocDeclareFold), Number(this.rsOpFreeDeclareFold), Number(this.rsOpDOAgentFold), Number(this.rsOpClearAgentFold), Number(this.rsOpWHSFold), Number(this.rsOp3rdCertFold), Number(this.rsOpINSFold), Number(this.rsOpTradingFold), Number(this.rsOpFumigationFold), Number(this.rsOpCOFold), Number(this.rsOpOtherFold)].toString()\r\n\r\n      this.form.rsOpSeaFclList ? this.form.rsOpSeaFclList.map(item => {\r\n        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n        return item\r\n      }) : null\r\n      this.form.rsOpSeaLclList ? this.form.rsOpSeaLclList.map(item => {\r\n        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n        return item\r\n      }) : null\r\n      this.form.rsOpAirList ? this.form.rsOpAirList.map(item => {\r\n        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n        return item\r\n      }) : null\r\n      this.form.rsOpCtnrTruckList ? this.form.rsOpCtnrTruckList.map(item => {\r\n        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n        return item\r\n      }) : null\r\n      this.form.rsOpBulkTruckList ? this.form.rsOpBulkTruckList.map(item => {\r\n        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n        return item\r\n      }) : null\r\n      this.form.rsOpDocDeclareList ? this.form.rsOpDocDeclareList.map(item => {\r\n        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n        return item\r\n      }) : null\r\n      this.form.rsOpFreeDeclareList ? this.form.rsOpFreeDeclareList.map(item => {\r\n        item.rsServiceInstances.serviceFold = Number(item.rsServiceInstances.serviceFold)\r\n        return item\r\n      }) : null\r\n\r\n      // copy一份操作单\r\n      saveAsRct(this.form).then(response => {\r\n        this.form.rctId = response.data\r\n        this.saveAsAll(response.data)\r\n        this.$modal.msgSuccess(\"另存成功\")\r\n        /*  this.open = false\r\n        this.getRctList() */\r\n        this.form.noTransferAllowed = this.form.noTransferAllowed ? true : false\r\n        this.form.noDividedAllowed = this.form.noDividedAllowed ? true : false\r\n        this.form.noAgreementShowed = this.form.noAgreementShowed ? true : false\r\n        this.form.isCustomsIntransitShowed = this.form.isCustomsIntransitShowed ? true : false\r\n\r\n        // 新增的表是rct不是booking,所以地址栏变化\r\n        // this.$tab.openPage(\"订舱单明细\", \"/salesquotation/bookingDetail\", {rId: response.data, booking: true})\r\n      })\r\n\r\n    },\r\n    handleSettledRate(serviceObject) {\r\n      if (serviceObject.settledRate) {\r\n        if (this.form.revenueTon && this.form.revenueTon.split(\"+\").length > 0) {\r\n          this.form.revenueTon.split(\"+\").map((revenueTon, i) => {\r\n            if (revenueTon.split(\"x\").length > 0 && i === 0) {\r\n              this.form.countA = Number(revenueTon.split(\"x\")[0])\r\n              this.form.unitCodeA = revenueTon.split(\"x\")[1]\r\n            }\r\n            if (revenueTon.split(\"x\").length > 0 && i === 1) {\r\n              this.form.countB = Number(revenueTon.split(\"x\")[0])\r\n              this.form.unitCodeB = revenueTon.split(\"x\")[1]\r\n            }\r\n            if (revenueTon.split(\"x\").length > 0 && i === 2) {\r\n              this.form.countC = Number(revenueTon.split(\"x\")[0])\r\n              this.form.unitCodeC = revenueTon.split(\"x\")[1]\r\n            }\r\n          })\r\n        }\r\n\r\n        let priceArr = serviceObject.settledRate.split(\"/\")\r\n        if (priceArr[0]) {\r\n          // 根据结算价更改应付明细\r\n          serviceObject.rsChargeList.map(item => {\r\n            if (item.dnAmount === this.form.countA && item.dnUnitCode === this.form.unitCodeA) {\r\n              item.dnUnitRate = priceArr[0]\r\n            }\r\n          })\r\n        }\r\n        if (priceArr[1]) {\r\n          // 根据结算价更改应付明细\r\n          serviceObject.rsChargeList.map(item => {\r\n            if (item.dnAmount === this.form.countB && item.dnUnitCode === this.form.unitCodeB) {\r\n              item.dnUnitRate = priceArr[1]\r\n            }\r\n          })\r\n        }\r\n        if (priceArr[2]) {\r\n          // 根据结算价更改应付明细\r\n          serviceObject.rsChargeList.map(item => {\r\n            if (item.dnAmount === this.form.countC && item.dnUnitCode === this.form.unitCodeC) {\r\n              item.dnUnitRate = priceArr[2]\r\n            }\r\n          })\r\n        }\r\n      }\r\n    },\r\n    mergeSeaServiceWithMainService() {\r\n      this.form.rsOpSeaFclList ? this.form.rsOpSeaFclList = this.form.rsOpSeaFclList.map(item => {\r\n        let data = this.customMerge(this.form, item)\r\n        // 合并后填充其他属性\r\n        data.sqdPsaNo = item.sqdPsaNo\r\n        data.soNo = item.soNo\r\n        data.blNo = item.blNo\r\n        data.sqdContainersSealsSum = item.sqdContainersSealsSum\r\n        data.carrierId = item.carrierId\r\n        data.firstVessel = item.firstVessel\r\n        data.secondVessel = item.secondVessel\r\n        data.inquiryScheduleSummary = item.inquiryScheduleSummary\r\n        data.firstCyOpenTime = item.firstCyOpenTime\r\n        data.firstCyClosingTime = item.firstCyClosingTime\r\n        data.cvClosingTime = item.cvClosingTime\r\n        data.etd = item.etd\r\n        data.eta = item.eta\r\n        data.siClosingTime = item.siClosingTime\r\n        data.sqdVgmStatus = item.sqdVgmStatus\r\n        data.sqdAmsEnsPostStatus = item.sqdAmsEnsPostStatus\r\n        // data.podEta = item.podEta\r\n        // data.destinationPortEta = item.destinationPortEta\r\n        data.bookingChargeRemark = item.bookingChargeRemark\r\n        data.bookingAgentRemark = item.bookingAgentRemark\r\n\r\n        data.bookingId = this.$store.state.user.sid\r\n        data.bookingStatus = \"1\" // 已订舱\r\n        data.bookingTime = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n        data.distributionStatus = data.sqdPsaNo ? data.distributionStatus : \"2\" // 操作自订舱\r\n        // 主费用币种、单价、单位 = 例：usd8600/40HQ\r\n        data.rctNo = this.form.rctNo\r\n        data.clientShortName = this.form.clientName.split(\"/\")[1]\r\n        data.salesId = this.form.salesId\r\n        data.salesAssistantId = this.form.salesAssistantId\r\n        // data.opId = this.$store.state.user.sid\r\n\r\n        return data\r\n      }) : null\r\n      this.form.rsOpSeaLclList ? this.form.rsOpSeaLclList = this.form.rsOpSeaLclList.map(item => {\r\n        let data = this.customMerge(this.form, item)\r\n        // 合并后填充其他属性\r\n        data.sqdPsaNo = item.sqdPsaNo\r\n        data.soNo = item.soNo\r\n        data.blNo = item.blNo\r\n        data.sqdContainersSealsSum = item.sqdContainersSealsSum\r\n        data.carrierId = item.carrierId\r\n        data.firstVessel = item.firstVessel\r\n        data.secondVessel = item.secondVessel\r\n        data.inquiryScheduleSummary = item.inquiryScheduleSummary\r\n        data.firstCyOpenTime = item.firstCyOpenTime\r\n        data.firstCyClosingTime = item.firstCyClosingTime\r\n        data.cvClosingTime = item.cvClosingTime\r\n        data.etd = item.etd\r\n        data.eta = item.eta\r\n        data.siClosingTime = item.siClosingTime\r\n        data.sqdVgmStatus = item.sqdVgmStatus\r\n        data.sqdAmsEnsPostStatus = item.sqdAmsEnsPostStatus\r\n        // data.podEta = item.podEta\r\n        // data.destinationPortEta = item.destinationPortEta\r\n        data.bookingChargeRemark = item.bookingChargeRemark\r\n        data.bookingAgentRemark = item.bookingAgentRemark\r\n\r\n        data.bookingId = this.$store.state.user.sid\r\n        data.bookingStatus = \"1\" // 已订舱\r\n        data.bookingTime = moment().format(\"yyyy-MM-DD HH:mm:ss\")\r\n        data.distributionStatus = data.sqdPsaNo ? data.distributionStatus : \"2\" // 操作自订舱\r\n        // 主费用币种、单价、单位 = 例：usd8600/40HQ\r\n        data.rctNo = this.form.rctNo\r\n        data.clientShortName = this.form.clientName.split(\"/\")[1]\r\n        data.salesId = this.form.salesId\r\n        data.salesAssistantId = this.form.salesAssistantId\r\n        // data.opId = this.$store.state.user.sid\r\n\r\n        return data\r\n      }) : null\r\n    },\r\n    chooseWhenOnlyOne() {\r\n      if (this.form.rsOpSeaFclList && this.form.rsOpSeaFclList.length === 1) {\r\n        this.form.rsOpSeaFclList.map(item => {\r\n          this.form.sqdPsaNo = item.sqdPsaNo\r\n          this.form.soNo = item.soNo\r\n          this.form.blNo = item.blNo\r\n          this.form.sqdContainersSealsSum = item.sqdContainersSealsSum\r\n          this.form.carrierId = item.carrierId\r\n          this.form.firstVessel = item.firstVessel\r\n          this.form.secondVessel = item.secondVessel\r\n          this.form.inquiryScheduleSummary = item.inquiryScheduleSummary\r\n          this.form.firstCyOpenTime = item.firstCyOpenTime\r\n          this.form.firstCyClosingTime = item.firstCyClosingTime\r\n          this.form.cvClosingTime = item.cvClosingTime\r\n          this.form.etd = item.etd\r\n          this.form.eta = item.eta\r\n          this.form.siClosingTime = item.siClosingTime\r\n          this.form.sqdVgmStatus = item.sqdVgmStatus\r\n          this.form.sqdAmsEnsPostStatus = item.sqdAmsEnsPostStatus\r\n          // this.form.podEta = item.podEta\r\n          // this.form.destinationPortEta = item.destinationPortEta\r\n          this.form.bookingChargeRemark = item.bookingChargeRemark\r\n          this.form.bookingAgentRemark = item.bookingAgentRemark\r\n\r\n          this.form.bookingId = item.bookingId\r\n          this.form.bookingStatus = item.bookingStatus\r\n          this.form.bookingTime = item.bookingTime\r\n          this.form.distributionStatus = item.distributionStatus\r\n          // 主费用币种、单价、单位 = 例：usd8600/40HQ\r\n          this.form.rctNo = item.rctNo\r\n          this.form.clientShortName = item.clientShortName\r\n          this.form.salesId = item.salesId\r\n          this.form.salesAssistantId = item.salesAssistantId\r\n          // this.form.opId = item.opId\r\n        })\r\n      } else if (this.form.rsOpSeaLclList && this.form.rsOpSeaLclList.length === 1) {\r\n        this.form.rsOpSeaLclList.map(item => {\r\n          this.form.sqdPsaNo = item.sqdPsaNo\r\n          this.form.soNo = item.soNo\r\n          this.form.blNo = item.blNo\r\n          this.form.sqdContainersSealsSum = item.sqdContainersSealsSum\r\n          this.form.carrierId = item.carrierId\r\n          this.form.firstVessel = item.firstVessel\r\n          this.form.secondVessel = item.secondVessel\r\n          this.form.inquiryScheduleSummary = item.inquiryScheduleSummary\r\n          this.form.firstCyOpenTime = item.firstCyOpenTime\r\n          this.form.firstCyClosingTime = item.firstCyClosingTime\r\n          this.form.cvClosingTime = item.cvClosingTime\r\n          this.form.etd = item.etd\r\n          this.form.eta = item.eta\r\n          this.form.siClosingTime = item.siClosingTime\r\n          this.form.sqdVgmStatus = item.sqdVgmStatus\r\n          this.form.sqdAmsEnsPostStatus = item.sqdAmsEnsPostStatus\r\n          // this.form.podEta = item.podEta\r\n          // this.form.destinationPortEta = item.destinationPortEta\r\n          this.form.bookingChargeRemark = item.bookingChargeRemark\r\n          this.form.bookingAgentRemark = item.bookingAgentRemark\r\n\r\n          this.form.bookingId = item.bookingId\r\n          this.form.bookingStatus = item.bookingStatus\r\n          this.form.bookingTime = item.bookingTime\r\n          this.form.distributionStatus = item.distributionStatus\r\n          // 主费用币种、单价、单位 = 例：usd8600/40HQ\r\n          this.form.rctNo = item.rctNo\r\n          this.form.clientShortName = item.clientShortName\r\n          this.form.salesId = item.salesId\r\n          this.form.salesAssistantId = item.salesAssistantId\r\n          // this.form.opId = item.opId\r\n        })\r\n      }\r\n    },\r\n    saveAll(id) {\r\n      this.rsClientServiceInstance.rctId = id\r\n      this.rsClientServiceInstance.serviceBelongTo = \"client\"\r\n      this.rsClientMessage.rsServiceInstances = this.rsClientServiceInstance\r\n      this.form.rsClientMessage = this.rsClientMessage\r\n\r\n      this.saveAllService(id)\r\n    },\r\n    saveAsAll(id) {\r\n      this.rsClientServiceInstance.rctId = id\r\n      this.rsClientServiceInstance.serviceBelongTo = \"client\"\r\n      this.rsClientMessage.rsServiceInstances = this.rsClientServiceInstance\r\n      this.form.rsClientMessage = this.rsClientMessage\r\n\r\n      this.saveAsAllService(id)\r\n    },\r\n    saveAllService(id) {\r\n      if (this.form.rctId == null) {\r\n        this.$message.error(\"请先确定单据\")\r\n        return\r\n      }\r\n      saveAllService(this.form).then(response => {\r\n        if (typeof id != \"number\") {\r\n          this.$message.success(\"保存成功\")\r\n        }\r\n\r\n        this.updateAllService(response.data)\r\n      })\r\n    },\r\n    saveAsAllService(id) {\r\n      if (this.form.rctId == null) {\r\n        this.$message.error(\"请先确定单据\")\r\n        return\r\n      }\r\n      saveAsAllService(this.form).then(response => {\r\n        if (typeof id != \"number\") {\r\n          this.$message.success(\"另存成功\")\r\n        }\r\n\r\n      })\r\n    },\r\n    updateAllService(rsRct) {\r\n      if (rsRct.serviceTypeIds.indexOf(1) !== -1 && rsRct.rsOpSeaFclList !== null) {\r\n        this.form.rsOpSeaFclList = rsRct.rsOpSeaFclList\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(2) !== -1 && rsRct.rsOpSeaLclList !== null) {\r\n        this.form.rsOpSeaLclList = rsRct.rsOpSeaLclList\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(10) !== -1 && rsRct.rsOpAirList !== null) {\r\n        this.form.rsOpAirList = rsRct.rsOpAirList\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(50) !== -1 && rsRct.rsOpCtnrTruckList !== null) {\r\n        this.form.rsOpCtnrTruckList = rsRct.rsOpCtnrTruckList\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(51) !== -1 && rsRct.rsOpBulkTruckList !== null) {\r\n        this.form.rsOpBulkTruckList = rsRct.rsOpBulkTruckList\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(60) !== -1 && rsRct.rsOpDocDeclareList !== null) {\r\n        this.form.rsOpDocDeclareList = rsRct.rsOpDocDeclareList\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(61) !== -1 && rsRct.rsOpFreeDeclareList !== null) {\r\n        this.form.rsOpFreeDeclareList = rsRct.rsOpFreeDeclareList\r\n      }\r\n\r\n      if (rsRct.rsClientMessage != null) {\r\n        this.rsClientMessage = rsRct.rsClientMessage\r\n        this.rsClientServiceInstance = rsRct.rsClientMessage.rsServiceInstances\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(20) !== -1 && rsRct.rsOpRailFCL !== null) {\r\n        this.rsOpRailFCL = rsRct.rsOpRailFCL\r\n        this.rsOpRailFclServiceInstance = rsRct.rsOpRailFCL.rsServiceInstances\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(21) !== -1 && rsRct.rsOpRailLCL !== null) {\r\n        this.rsOpRailLCL = rsRct.rsOpRailLCL\r\n        this.rsOpRailLclServiceInstance = rsRct.rsOpRailLCL.rsServiceInstances\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(40) !== -1 && rsRct.rsOpExpress !== null) {\r\n        this.rsOpExpress = rsRct.rsOpExpress\r\n        this.rsOpExpressServiceInstance = rsRct.rsOpExpress.rsServiceInstances\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(70) !== -1 && rsRct.rsOpDOAgent !== null) {\r\n        this.rsOpDOAgent = rsRct.rsOpDOAgent\r\n        this.rsOpDOAgentServiceInstance = rsRct.rsOpDOAgent.rsServiceInstances\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(71) !== -1 && rsRct.rsOpClearAgent !== null) {\r\n        this.rsOpClearAgent = rsRct.rsOpClearAgent\r\n        this.rsOpClearAgentServiceInstance = rsRct.rsOpClearAgent.rsServiceInstances\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(80) !== -1 && rsRct.rsOpWHS !== null) {\r\n        this.rsOpWHS = rsRct.rsOpWHS\r\n        this.rsOpWHSServiceInstance = rsRct.rsOpWHS.rsServiceInstances\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(90) !== -1 && rsRct.rsOp3rdCert !== null) {\r\n        this.rsOp3rdCert = rsRct.rsOp3rdCert\r\n        this.rsOp3rdCertServiceInstance = rsRct.rsOp3rdCert.rsServiceInstances\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(100) !== -1 && rsRct.rsOpINS !== null) {\r\n        this.rsOpINS = rsRct.rsOpINS\r\n        this.rsOpINSServiceInstance = rsRct.rsOpINS.rsServiceInstances\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(101) !== -1 && rsRct.rsOpTrading !== null) {\r\n        this.rsOpTrading = rsRct.rsOpTrading\r\n        this.rsOpTradingServiceInstance = rsRct.rsOpTrading.rsServiceInstances\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(102) !== -1 && rsRct.rsOpFumigation !== null) {\r\n        this.rsOpFumigation = rsRct.rsOpFumigation\r\n        this.rsOpFumigationServiceInstance = rsRct.rsOpFumigation.rsServiceInstances\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(103) !== -1 && rsRct.rsOpCO !== null) {\r\n        this.rsOpCO = rsRct.rsOpCO\r\n        this.rsOpCOServiceInstance = rsRct.rsOpCO.rsServiceInstances\r\n      }\r\n      if (rsRct.serviceTypeIds.indexOf(104) !== -1 && rsRct.rsOpOther !== null) {\r\n        this.rsOpOther = rsRct.rsOpOther\r\n        this.rsOpOtherServiceInstance = rsRct.rsOpOther.rsServiceInstances\r\n      }\r\n    },\r\n    generateRct(v) {\r\n      if (!checkRole([\"Op\"])) {\r\n        this.$message.warning(\"无权限修改操作单号\")\r\n        return\r\n      }\r\n      if (v) {\r\n        getRctMon().then(v => {\r\n          let num = v.data\r\n          if (num.toString().length < 3) {\r\n            const j = 3 - (num.toString().length)\r\n            for (let i = 0; i < j; i++) {\r\n              num = \"0\" + num\r\n            }\r\n          }\r\n          let date = new Date()\r\n          let month = (date.getMonth() + Number(this.rct.month)).toString()\r\n          let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)\r\n          this.rct.rctNo = this.rct.leadingCharacter + year + (month.length == 1 ? \"0\" + month : month) + num.toString()\r\n        })\r\n      } else {\r\n        this.openGenerateRct = true\r\n      }\r\n    },\r\n    confirmRct() {\r\n      this.form.rctNo = this.rct.rctNo\r\n      this.openGenerateRct = false\r\n    },\r\n    cancel() {\r\n      // this.reset()\r\n      this.loading = false\r\n      this.open = false\r\n      this.openGenerateRct = false\r\n    },\r\n    // autoCompletion方法已移动到opDataHandlerMixin中\r\n    getType(n) {\r\n      if (!n) {\r\n        return\r\n      }\r\n      for (const s of this.$store.state.data.serviceTypeList) {\r\n        if (s.children) {\r\n          for (const c of s.children) {\r\n            if (c.serviceTypeId == n) {\r\n              this.logisticsType = c.typeId\r\n            }\r\n          }\r\n        }\r\n        if (s.serviceTypeId == n) {\r\n          this.logisticsType = s.typeId\r\n        }\r\n      }\r\n    },\r\n    addEmptyService(serviceTypeId) {\r\n      if (1 === serviceTypeId && (!this.form.rsOpSeaFclList || (this.form.rsOpSeaFclList && this.form.rsOpSeaFclList.length === 0))) {\r\n        if (!this.form.rsOpSeaFclList) {\r\n          this.form.rsOpSeaFclList = []\r\n        }\r\n        let rsOpSeaFcl = this.rsOpSeaFcl\r\n        rsOpSeaFcl.rsServiceInstances = this.rsServiceInstances\r\n        this.form.rsOpSeaFclList.push(rsOpSeaFcl)\r\n      }\r\n      if (2 === serviceTypeId && (!this.form.rsOpSeaLclList || (this.form.rsOpSeaLclList && this.form.rsOpSeaLclList.length === 0))) {\r\n        if (!this.form.rsOpSeaLclList) {\r\n          this.form.rsOpSeaLclList = []\r\n        }\r\n        let rsOpSeaLcl = this.rsOpSeaLcl\r\n        rsOpSeaLcl.rsServiceInstances = this.rsServiceInstances\r\n        this.form.rsOpSeaLclList.push(rsOpSeaLcl)\r\n      }\r\n      if (10 === serviceTypeId && (!this.form.rsOpAirList || (this.form.rsOpAirList && this.form.rsOpAirList.length === 0))) {\r\n        if (!this.form.rsOpAirList) {\r\n          this.form.rsOpAirList = []\r\n        }\r\n        let rsOpAir = this.rsOpAir\r\n        rsOpAir.rsServiceInstances = this.rsServiceInstances\r\n        this.form.rsOpAirList.push(rsOpAir)\r\n      }\r\n      if (50 === serviceTypeId && (!this.form.rsOpCtnrTruckList || (this.form.rsOpCtnrTruckList && this.form.rsOpCtnrTruckList.length === 0))) {\r\n        if (!this.form.rsOpCtnrTruckList) {\r\n          this.form.rsOpCtnrTruckList = []\r\n        }\r\n        let rsOpCtnrTruck = this.rsOpCtnrTruck\r\n        rsOpCtnrTruck.rsServiceInstances = this.rsServiceInstances\r\n        this.form.rsOpCtnrTruckList.push(rsOpCtnrTruck)\r\n      }\r\n      if (51 === serviceTypeId && (!this.form.rsOpBulkTruckList || (this.form.rsOpBulkTruckList && this.form.rsOpBulkTruckList.length === 0))) {\r\n        if (!this.form.rsOpBulkTruckList) {\r\n          this.form.rsOpBulkTruckList = []\r\n        }\r\n        let rsOpBulkTruck = this.rsOpBulkTruck\r\n        rsOpBulkTruck.rsServiceInstances = this.rsServiceInstances\r\n        this.form.rsOpBulkTruckList.push(rsOpBulkTruck)\r\n      }\r\n      if (60 === serviceTypeId && (!this.form.rsOpDocDeclareList || (this.form.rsOpDocDeclareList && this.form.rsOpDocDeclareList.length === 0))) {\r\n        if (!this.form.rsOpDocDeclareList) {\r\n          this.form.rsOpDocDeclareList = []\r\n        }\r\n        let rsOpDocDeclare = this.rsOpDocDeclare\r\n        rsOpDocDeclare.rsServiceInstances = this.rsServiceInstances\r\n        this.form.rsOpDocDeclareList.push(rsOpDocDeclare)\r\n      }\r\n      if (61 === serviceTypeId && (!this.form.rsOpFreeDeclareList || (this.form.rsOpFreeDeclareList && this.form.rsOpFreeDeclareList.length === 0))) {\r\n        if (!this.form.rsOpFreeDeclareList) {\r\n          this.form.rsOpFreeDeclareList = []\r\n        }\r\n        let rsOpFreeDeclare = this.rsOpFreeDeclare\r\n        rsOpFreeDeclare.rsServiceInstances = this.rsServiceInstances\r\n        this.form.rsOpFreeDeclareList.push(rsOpFreeDeclare)\r\n      }\r\n    },\r\n    async getServiceTypeList(val) {\r\n      if (this.$store.state.data.serviceTypeList.length == 0) {\r\n        await this.$store.dispatch(\"getServiceTypeList\")\r\n      }\r\n      this.list.clear()\r\n      this.serviceList.clear()\r\n      this.RAIL.clear()\r\n      this.EXPRESS.clear()\r\n      this.CLEAR.clear()\r\n      this.WHS.clear()\r\n      this.EXTEND.clear()\r\n      this.form.serviceTypeIds = val\r\n      for (const s of this.$store.state.data.serviceTypeList) {\r\n        if (s.children) {\r\n          for (const c of s.children) {\r\n            if (val.includes(c.serviceTypeId)) {\r\n              if (c.typeId != null) {\r\n                this.list.add(c.typeId)\r\n              }\r\n              this.serviceList.add(c.serviceTypeId)\r\n\r\n              this.addEmptyService(c.serviceTypeId)\r\n              c.typeId === \"3\" ? this.RAIL.add(c) : null\r\n              c.typeId === \"4\" ? this.EXPRESS.add(c) : null\r\n              c.typeId === \"7\" ? this.CLEAR.add(c) : null\r\n              c.typeId === \"8\" ? this.WHS.add(c) : null\r\n              c.typeId === \"9\" ? this.EXTEND.add(c) : null\r\n            }\r\n          }\r\n        }\r\n        if (val.includes(s.serviceTypeId)) {\r\n          if (s.typeId != null) {\r\n            this.list.add(s.typeId)\r\n          }\r\n          this.serviceList.add(s.serviceTypeId)\r\n\r\n          this.addEmptyService(s.serviceTypeId)\r\n          s.typeId === \"1\" ? this.SEA.add(s) : null\r\n          s.typeId === \"2\" ? this.AIR.add(s) : null\r\n          s.typeId === \"3\" ? this.RAIL.add(s) : null\r\n          s.typeId === \"4\" ? this.EXPRESS.add(s) : null\r\n          s.typeId === \"5\" ? this.TRUCK.add(s) : null\r\n          s.typeId === \"6\" ? this.CUSTOM.add(s) : null\r\n          s.typeId === \"7\" ? this.CLEAR.add(s) : null\r\n          s.typeId === \"8\" ? this.WHS.add(s) : null\r\n          s.typeId === \"9\" ? this.EXTEND.add(s) : null\r\n        }\r\n      }\r\n      this.$forceUpdate()\r\n    },\r\n    carrierNormalizer(node) {\r\n      return {\r\n        id: node.carrierId,\r\n        label: (node.carrierShortName != null ? node.carrierShortName : \"\") + \" \" + (node.carrierLocalName != null ? node.carrierLocalName : \"\") + \" \" + (node.carrierEnName != null ? node.carrierEnName : \"\") + \",\" + pinyin.getFullChars((node.carrierShortName != null ? node.carrierShortName : \"\") + \" \" + (node.carrierLocalName != null ? node.carrierLocalName : \"\"))\r\n      }\r\n    },\r\n    loadCarrier() {\r\n      if (this.$store.state.data.carrierList.length == 0 || this.$store.state.data.redisList.carrier) {\r\n        store.dispatch(\"getCarrierList\").then(() => {\r\n            this.carrierList = this.$store.state.data.carrierList\r\n          }\r\n        )\r\n      } else {\r\n        this.carrierList = this.$store.state.data.carrierList\r\n      }\r\n    },\r\n    loadSelection() {\r\n      if (this.$store.state.data.unitList.length == 0 || this.$store.state.data.redisList.unit) {\r\n        this.$store.dispatch(\"getUnitList\")\r\n      }\r\n      if (this.$store.state.data.currencyList.length == 0 || this.$store.state.data.redisList.currency) {\r\n        this.$store.dispatch(\"getCurrencyList\")\r\n      }\r\n      if (this.$store.state.data.chargeList.length == 0 || this.$store.state.data.redisList.charge) {\r\n        this.$store.dispatch(\"getChargeList\")\r\n      }\r\n      // 加载货运类型\r\n      if (this.$store.state.data.serviceTypeList.length == 0 || this.$store.state.data.redisList.serviceType) {\r\n        this.$store.dispatch(\"getServiceTypeList\")\r\n      }\r\n      this.loadOp()\r\n      this.loadCarrier()\r\n      this.loadSales()\r\n      this.loadBusinesses()\r\n      this.loadStaffList()\r\n    },\r\n    // 查询操作部用户\r\n    loadOp() {\r\n      if (this.$store.state.data.opList.length == 0 || this.$store.state.data.redisList.opList) {\r\n        store.dispatch(\"getOpList\").then(() => {\r\n          this.opList = this.$store.state.data.opList\r\n        })\r\n      } else {\r\n        this.opList = this.$store.state.data.opList\r\n      }\r\n    },\r\n    // 查询业务部用户\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch(\"getSalesList\").then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    // 查询商务部用户\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        store.dispatch(\"getBusinessesList\").then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    confirmed(v, rsChargeList) {\r\n      if (v == \"op\") {\r\n        if (this.rsClientServiceInstance.isDnOpConfirmed) {\r\n          this.rsClientServiceInstance.isDnOpConfirmed = null\r\n          this.rsClientServiceInstance.opConfirmedTime = null\r\n          this.opConfirmedName = null\r\n          this.opConfirmedDate = null\r\n        } else {\r\n          this.rsClientServiceInstance.isDnOpConfirmed = this.$store.state.user.sid\r\n          this.rsClientServiceInstance.opConfirmedTime = parseTime(new Date(), \"{y}-{m}-{d}\")\r\n          this.opConfirmedName = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isDnOpConfirmed)[0].staffFamilyLocalName + \"\" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isDnOpConfirmed)[0].staffGivingLocalName\r\n          this.opConfirmedDate = this.rsClientServiceInstance.opConfirmedTime\r\n        }\r\n        this.updateServiceInstance(this.rsClientServiceInstance)\r\n      }\r\n      if (v == \"account\") {\r\n        if (this.rsClientServiceInstance.isAccountConfirmed) {\r\n          this.rsClientServiceInstance.isAccountConfirmed = null\r\n          this.rsClientServiceInstance.accountConfirmTime = null\r\n          this.accountConfirmedName = null\r\n          this.accountConfirmedDate = null\r\n        } else {\r\n          this.rsClientServiceInstance.isAccountConfirmed = this.$store.state.user.sid\r\n          this.rsClientServiceInstance.accountConfirmTime = parseTime(new Date(), \"{y}-{m}-{d}\")\r\n          this.accountConfirmedName = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isAccountConfirmed)[0].staffFamilyLocalName + \"\" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isAccountConfirmed)[0].staffGivingLocalName\r\n          this.accountConfirmedDate = this.rsClientServiceInstance.accountConfirmTime\r\n\r\n          rsChargeList = rsChargeList.map(item => {\r\n            if (item.isAccountConfirmed != 1) {\r\n              item.isAccountConfirmed = 1\r\n              updateCharge(item)\r\n              return item\r\n            }\r\n          })\r\n        }\r\n\r\n        this.updateServiceInstance(this.rsClientServiceInstance)\r\n      }\r\n      if (v == \"sales\") {\r\n        if (this.rsClientServiceInstance.isDnSalesConfirmed) {\r\n          this.rsClientServiceInstance.isDnSalesConfirmed = null\r\n          this.rsClientServiceInstance.salesConfirmedTime = null\r\n          this.salesConfirmedName = null\r\n          this.salesConfirmedDate = null\r\n        } else {\r\n          this.rsClientServiceInstance.isDnSalesConfirmed = this.$store.state.user.sid\r\n          this.rsClientServiceInstance.salesConfirmedTime = parseTime(new Date(), \"{y}-{m}-{d}\")\r\n          this.salesConfirmedName = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isDnSalesConfirmed)[0].staffFamilyLocalName + \"\" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isDnSalesConfirmed)[0].staffGivingLocalName\r\n          this.salesConfirmedDate = this.rsClientServiceInstance.salesConfirmedTime\r\n        }\r\n        this.updateServiceInstance(this.rsClientServiceInstance)\r\n      }\r\n      if (v == \"client\") {\r\n        if (this.rsClientServiceInstance.isDnClientConfirmed) {\r\n          this.rsClientServiceInstance.isDnClientConfirmed = null\r\n          this.rsClientServiceInstance.clientConfirmedTime = null\r\n          this.clientConfirmedName = null\r\n          this.clientConfirmedDate = null\r\n        } else {\r\n          this.rsClientServiceInstance.isDnClientConfirmed = this.$store.state.user.sid\r\n          this.rsClientServiceInstance.clientConfirmedTime = parseTime(new Date(), \"{y}-{m}-{d}\")\r\n          this.clientConfirmedName = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isDnClientConfirmed)[0].staffFamilyLocalName + \"\" + this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == this.rsClientServiceInstance.isDnClientConfirmed)[0].staffGivingLocalName\r\n          this.clientConfirmedDate = this.rsClientServiceInstance.clientConfirmedTime\r\n        }\r\n        this.updateServiceInstance(this.rsClientServiceInstance)\r\n      }\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    },\r\n    businessesNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      return {\r\n        id: node.staffId,\r\n        label: l,\r\n        isDisabled: node.staffId == null && node.children == undefined\r\n      }\r\n    },\r\n    reset() {\r\n      this.RelationClientList = []\r\n      this.bookingMessageList = []\r\n      this.form = {\r\n        noTransferAllowed: false,\r\n        noDividedAllowed: false,\r\n        noAgreementShowed: false,\r\n        isCustomsIntransitShowed: false,\r\n        rctId: null,\r\n        rctNo: null,\r\n        clientId: null,\r\n        clientSummary: null,\r\n        clientRoleId: null,\r\n        clientContact: null,\r\n        clientContactTel: null,\r\n        clientContactEmail: null,\r\n        relationClientIdList: null,\r\n        emergencyLevel: null,\r\n        difficultyLevel: null,\r\n        releaseType: null,\r\n        paymentTitleCode: null,\r\n        impExpType: null,\r\n        tradingTerms: null,\r\n        logisticsTerms: null,\r\n        tradingPaymentChannel: null,\r\n        clientContractNo: null,\r\n        clientInvoiceNo: null,\r\n        cargoTypeIdSum: null,\r\n        goodsNameSummary: null,\r\n        packageQuantity: null,\r\n        goodsVolume: null,\r\n        grossWeight: null,\r\n        weightUnitCode: null,\r\n        goodsCurrencyCode: null,\r\n        goodsValue: null,\r\n        logisticsTypeId: null,\r\n        revenueTon: null,\r\n        polId: null,\r\n        localBasicPortId: null,\r\n        transitPortId: null,\r\n        podId: null,\r\n        destinationPortId: null,\r\n        cvClosingTime: null,\r\n        siClosingTime: null,\r\n        firstVessel: null,\r\n        firstVoyage: null,\r\n        firstCyOpenTime: null,\r\n        firstCyClosingTime: null,\r\n        firstEtd: null,\r\n        basicVessel: null,\r\n        basicVoyage: null,\r\n        basicFinalGateinTime: null,\r\n        basicEtd: null,\r\n        podEta: null,\r\n        destinationPortEta: null,\r\n        carrierId: null,\r\n        inquiryScheduleSummary: null,\r\n        polBookingAgent: null,\r\n        podHandleAgent: null,\r\n        serviceTypeIdList: null,\r\n        sqdSoNoSum: null,\r\n        sqdMblNoSum: null,\r\n        sqdContainersSealsSum: null,\r\n        blFormCode: null,\r\n        sqdExportCustomsType: null,\r\n        sqdTrailerType: null,\r\n        rctProcessStatusSummary: null,\r\n        transportStatusSummary: null,\r\n        docStatusSummary: null,\r\n        paymentReceivingStatusSummary: null,\r\n        paymentPayingStatusSummary: null,\r\n        transportStatus: \"0\",\r\n        docStatus: \"0\",\r\n        paymentPayingStatus: \"0\",\r\n        rctProcessId: null,\r\n        porcessId: null,\r\n        porcessStatusId: null,\r\n        processStatusTime: moment().format(\"yyyy-MM-DD HH:mm:ss\"),\r\n        statusUpdateTime: moment().format(\"yyyy-MM-DD HH:mm:ss\"),\r\n        processRemark: null,\r\n        qoutationNo: null,\r\n        qoutationSketch: null,\r\n        salesId: null,\r\n        qoutationTime: null,\r\n        newBookingNo: null,\r\n        newBookingRemark: null,\r\n        salesAssistantId: null,\r\n        salesObserverId: null,\r\n        newBookingTime: moment().format(\"yyyy-MM-DD HH:mm:ss\"),\r\n        inquiryNoticeSum: null,\r\n        inquiryInnerRemarkSum: null,\r\n        verifyPsaId: null,\r\n        psaVerifyTime: null,\r\n        opLeaderNotice: null,\r\n        opInnerRemark: null,\r\n        opId: null,\r\n        bookingOpId: null,\r\n        docOpId: null,\r\n        opObserverId: null,\r\n        rctCreateTime: null,\r\n        customTitle: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: \"0\",\r\n        precarriageRegionId: null,\r\n        precarriageAddress: null,\r\n        precarriageTime: null,\r\n        precarriageContact: null,\r\n        precarriageTel: null,\r\n        precarriageRemark: null,\r\n        dispatchRegionId: null,\r\n        dispatchAddress: null,\r\n        dispatchTime: null,\r\n        dispatchContact: null,\r\n        dispatchTel: null,\r\n        dispatchRemark: null,\r\n        paymentReceivingStatus: null,\r\n        sqdWarehousingStatus: null,\r\n        sqdShippingBookingStatus: null,\r\n        sqdTrailerBookingStatus: null,\r\n        sqdContainerBookingStatus: null,\r\n        sqdContainerLoadingStatus: null,\r\n        sqdVesselArrangeStatus: null,\r\n        sqdVgmStatus: null,\r\n        sqdCustomDocsStatus: null,\r\n        sqdCustomAuthorizedStatus: null,\r\n        sqdCustomExamineStatus: null,\r\n        sqdCustomReleaseStatus: null,\r\n        sqdSiVerifyStatus: null,\r\n        sqdSiPostStatus: null,\r\n        sqdAmsEnsPostStatus: null,\r\n        sqdIsfEmnfPostStatus: null,\r\n        sqdMainServicePayingStatus: null,\r\n        sqdBlGettingStatus: null,\r\n        sqdBlReleasingStatus: null,\r\n        sqdContainerNoSum: null,\r\n        sqdPolBookingAgent: null,\r\n        sqdPodHandleAgent: null,\r\n        sqdCarrierId: null,\r\n        logisticsPaymentTermsCode: null,\r\n        sqdDocDeliveryWay: null,\r\n        sqdDnRmbSumVat: null,\r\n        sqdCnRmbSumVat: null,\r\n        sqdProfitRmbSum: null,\r\n        sqdProfitRmbSumVat: null,\r\n        warehousingNo: null,\r\n        clientJobNo: null,\r\n        bookingShipper: null,\r\n        bookingConsignee: null,\r\n        bookingNotifyParty: null,\r\n        sqdInsuranceType: null,\r\n        isOpConfirmed: null,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        isSalesConfirmed: null,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        isClientConfirmed: null,\r\n        clientConfirmedId: null,\r\n        clientConfirmedName: null,\r\n        clientConfirmedDate: null,\r\n        isAccountConfirmed: null,\r\n        accountConfirmedId: null,\r\n        accountConfirmedName: null,\r\n        accountConfirmedDate: null,\r\n        _PaymentTitleCode: [],\r\n        _RelationClientIdList: [],\r\n        rsOpSeaFclList: [],\r\n        rsOpSeaLclList: [],\r\n        rsOpAirList: [],\r\n        rsOpCtnrTruckList: [],\r\n        rsOpBulkTruckList: [],\r\n        rsOpDocDeclareList: [],\r\n        rsOpFreeDeclareList: [],\r\n        orderBelongsTo: null\r\n      }\r\n\r\n      this.rsClientMessage = {\r\n        rsChargeList: [],\r\n        rsOpLogList: [],\r\n        rsDocList: []\r\n      },\r\n        // new 子服务数据\r\n        this.rsOpSeaFcl = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpSeaLcl = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpAir = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpRailFCL = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpRailLCL = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpRail = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpExpress = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpTruck = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpCtnrTruck = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: [],\r\n          rsOpTruckList: []\r\n        },\r\n        this.rsOpBulkTruck = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: [],\r\n          rsOpTruckList: []\r\n        },\r\n        //正单报关\r\n        this.rsOpDocDeclare = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        // 全包报关\r\n        this.rsOpFreeDeclare = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpExportCustomsClearance = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpImportCustomsClearance = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpImportDispatchTruck = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        // 代理放单\r\n        this.rsOpDOAgent = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpClearAgent = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpWHS = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpWarehouse = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpInspectionAndCertificate = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpInsurance = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpExpandService = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOp3rdCert = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpINS = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpTrading = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpFumigation = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpCO = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        },\r\n        this.rsOpOther = {\r\n          rsChargeList: [],\r\n          rsOpLogList: [],\r\n          rsDocList: []\r\n        }\r\n\r\n      this.rsClientServiceInstance = {\r\n        serviceId: null,\r\n        serviceTypeId: null,\r\n        rctId: null,\r\n        rctNo: null,\r\n        supplierId: null,\r\n        supplierSummary: null,\r\n        supplierContact: null,\r\n        supplierTel: null,\r\n        paymentTitleCode: null,\r\n        logisticsPaymentTermsCode: null,\r\n        inquiryNo: null,\r\n        agreementTypeCode: null,\r\n        agreementNo: null,\r\n        maxWeight: null,\r\n        inquiryNotice: null,\r\n        inquiryInnerRemark: null,\r\n        inquiryPsaId: null,\r\n        inquiryLeatestUpdatedTime: null,\r\n        serviceBelongTo: null,\r\n        isDnSalesConfirmed: null,\r\n        isDnClientConfirmed: null,\r\n        isDnOpConfirmed: null,\r\n        isDnPsaConfirmed: null,\r\n        isDnSupplierConfirmed: null,\r\n        isAccountConfirmed: null,\r\n        confirmAccountId: null,\r\n        accountConfirmTime: null,\r\n        salesConfirmedTime: null,\r\n        clientConfirmedTime: null,\r\n        opConfirmedTime: null,\r\n        psaConfirmedTime: null,\r\n        supplierConfirmedTime: null\r\n      },\r\n        this.rsOpSeaFclServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpSeaLclServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpAirServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpRailServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpRailFclServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpRailLclServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpExpressServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpTruckServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpCtnrTruckServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpBulkTruckServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpExportCustomsClearanceServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpImportCustomsClearanceServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpDocDeclareServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpFreeDeclareServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpDOAgentServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpClearAgentServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpImportDispatchTruckServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpInspectionAndCertificateServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpLandServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpInsuranceServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpExpandServiceServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpWHSServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOp3rdCertServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpINSServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpTradingServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpFumigationServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.rsOpCOServiceInstance = {\r\n          serviceId: null,\r\n          serviceTypeId: null,\r\n          rctId: null,\r\n          rctNo: null,\r\n          supplierId: null,\r\n          supplierSummary: null,\r\n          supplierContact: null,\r\n          supplierTel: null,\r\n          paymentTitleCode: null,\r\n          logisticsPaymentTermsCode: null,\r\n          inquiryNo: null,\r\n          agreementTypeCode: null,\r\n          agreementNo: null,\r\n          maxWeight: null,\r\n          inquiryNotice: null,\r\n          inquiryInnerRemark: null,\r\n          inquiryPsaId: null,\r\n          inquiryLeatestUpdatedTime: null,\r\n          serviceBelongTo: null,\r\n          isDnSalesConfirmed: null,\r\n          isDnClientConfirmed: null,\r\n          isDnOpConfirmed: null,\r\n          isDnPsaConfirmed: null,\r\n          isDnSupplierConfirmed: null,\r\n          isAccountConfirmed: null,\r\n          confirmAccountId: null,\r\n          accountConfirmTime: null,\r\n          salesConfirmedTime: null,\r\n          clientConfirmedTime: null,\r\n          opConfirmedTime: null,\r\n          psaConfirmedTime: null,\r\n          supplierConfirmedTime: null\r\n        },\r\n        this.resetForm(\"form\")\r\n    },\r\n    loadStaffList() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList\r\n      }\r\n    },\r\n    copyFreight(row) {\r\n      row.showQuotationUnit = false\r\n      let item = this._.cloneDeep(row)\r\n      item.clearingCompanyId = row.payClearingCompanyId\r\n      item.companyName = row.payCompanyName\r\n      // 如果是订舱\r\n      if (this.$route.query.id) {\r\n        item.clearingCompanyId = this.form.clientId\r\n        item.companyName = this.form.company\r\n      }\r\n      this.rsClientMessage.rsChargeList.push(item)\r\n\r\n      this.rsClientMessageCharge(this.rsClientMessage.rsChargeList)\r\n    },\r\n    rsClientMessageCharge(n) {\r\n      // 初始化变量\r\n      const receivable = {RMB: 0, USD: 0, taxRMB: 0, taxUSD: 0}\r\n      const payable = {RMB: 0, USD: 0, taxRMB: 0, taxUSD: 0}\r\n      const unreceived = {RMB: 0, USD: 0}\r\n      const unpaid = {RMB: 0, USD: 0}\r\n\r\n      // 计算客户信息中的应收\r\n      n.forEach(rsCharge => {\r\n        if (rsCharge.subtotal) {\r\n          const isUSD = rsCharge.dnCurrencyCode === \"USD\"\r\n          const currencyKey = isUSD ? \"USD\" : \"RMB\"\r\n          const taxCurrencyKey = isUSD ? \"taxUSD\" : \"taxRMB\"\r\n          const unreceivedKey = isUSD ? \"USD\" : \"RMB\"\r\n\r\n          const subtotalWithoutTax = currency(rsCharge.subtotal).divide(currency(1).add(currency(rsCharge.dutyRate).divide(100))).value\r\n\r\n          receivable[currencyKey] = currency(receivable[currencyKey]).add(subtotalWithoutTax).value\r\n          receivable[taxCurrencyKey] = currency(receivable[taxCurrencyKey]).add(rsCharge.subtotal).value\r\n          unreceived[unreceivedKey] = currency(unreceived[unreceivedKey]).add(rsCharge.sqdDnCurrencyBalance).value\r\n        }\r\n      })\r\n\r\n      // 更新应收相关数据\r\n      this.rsClientMessageReceivableRMB = receivable.RMB\r\n      this.rsClientMessageReceivableUSD = receivable.USD\r\n      this.rsClientMessageReceivableTaxRMB = receivable.taxRMB\r\n      this.rsClientMessageReceivableTaxUSD = receivable.taxUSD\r\n      this.sqdUnreceivedRmbSum = unreceived.RMB\r\n      this.sqdUnreceivedUsdSum = unreceived.USD\r\n\r\n      // 处理各种费用列表的通用函数\r\n      const processChargeList = (chargeList) => {\r\n        if (!chargeList) return\r\n\r\n        chargeList.forEach(rsCharge => {\r\n          if (rsCharge.subtotal) {\r\n            const isUSD = rsCharge.dnCurrencyCode === \"USD\"\r\n            const currencyKey = isUSD ? \"USD\" : \"RMB\"\r\n            const taxCurrencyKey = isUSD ? \"taxUSD\" : \"taxRMB\"\r\n            const unpaidKey = isUSD ? \"USD\" : \"RMB\"\r\n\r\n            const subtotalWithoutTax = currency(rsCharge.subtotal).divide(currency(1).add(currency(rsCharge.dutyRate).divide(100))).value\r\n\r\n            payable[currencyKey] = currency(payable[currencyKey]).add(subtotalWithoutTax).value\r\n            payable[taxCurrencyKey] = currency(payable[taxCurrencyKey]).add(rsCharge.subtotal).value\r\n            unpaid[unpaidKey] = currency(unpaid[unpaidKey]).add(rsCharge.sqdDnCurrencyBalance).value\r\n          }\r\n        })\r\n      }\r\n\r\n      // 处理各种服务类型的费用\r\n      const serviceTypes = [\r\n        {list: this.form.rsOpSeaFclList, property: \"rsChargeList\"},\r\n        {list: this.form.rsOpSeaLclList, property: \"rsChargeList\"},\r\n        {list: this.form.rsOpAirList, property: \"rsChargeList\"},\r\n        {list: this.form.rsOpCtnrTruckList, property: \"rsChargeList\"},\r\n        {list: this.form.rsOpBulkTruckList, property: \"rsChargeList\"},\r\n        {list: this.form.rsOpDocDeclareList, property: \"rsChargeList\"},\r\n        {list: this.form.rsOpFreeDeclareList, property: \"rsChargeList\"}\r\n      ]\r\n\r\n      // 处理列表类型的服务\r\n      serviceTypes.forEach(service => {\r\n        if (service.list) {\r\n          service.list.forEach(item => {\r\n            processChargeList(item[service.property])\r\n          })\r\n        }\r\n      })\r\n\r\n      // 处理单一对象类型的服务\r\n      const singleServices = [\r\n        \"rsOpRailFCL\", \"rsOpRailLCL\", \"rsOpExpress\", \"rsOpDOAgent\",\r\n        \"rsOpClearAgent\", \"rsOpWHS\", \"rsOp3rdCert\", \"rsOpINS\",\r\n        \"rsOpTrading\", \"rsOpFumigation\", \"rsOpCO\", \"rsOpOther\"\r\n      ]\r\n\r\n      singleServices.forEach(serviceName => {\r\n        if (this[serviceName]) {\r\n          processChargeList(this[serviceName].rsChargeList)\r\n        }\r\n      })\r\n\r\n      // 更新应付相关数据\r\n      this.rsClientMessagePayableRMB = payable.RMB\r\n      this.rsClientMessagePayableTaxRMB = payable.taxRMB\r\n      this.rsClientMessagePayableUSD = payable.USD\r\n      this.rsClientMessagePayableTaxUSD = payable.taxUSD\r\n      this.sqdUnpaidRmbSum = unpaid.RMB\r\n      this.sqdUnpaidUsdSum = unpaid.USD\r\n\r\n      // 计算利润\r\n      this.rsClientMessageProfitRMB = currency(receivable.RMB).subtract(payable.RMB).value\r\n      this.rsClientMessageProfitUSD = currency(receivable.USD).subtract(payable.USD).value\r\n\r\n      // 计算含税利润\r\n      if (n.length > 0) {\r\n        this.rsClientMessageProfitTaxRMB = currency(receivable.taxRMB).subtract(payable.taxRMB).value\r\n        this.rsClientMessageProfitTaxUSD = currency(receivable.taxUSD).subtract(payable.taxUSD).value\r\n      } else {\r\n        this.rsClientMessageProfitTaxRMB = currency(receivable.RMB).subtract(payable.taxRMB).value\r\n        this.rsClientMessageProfitTaxUSD = currency(receivable.USD).subtract(payable.taxUSD).value\r\n      }\r\n    },\r\n    calculateCharge(serviceTypeId, n, rsOpService) {\r\n      // 如果没有费用项，直接返回\r\n      if (!n || n.length === 0) {\r\n        return\r\n      }\r\n\r\n      // 初始化变量\r\n      let payableRMB = 0\r\n      let payableRMBTax = 0\r\n      let payableUSD = 0\r\n      let payableUSDTax = 0\r\n\r\n      // 计算费用\r\n      n.forEach(rsCharge => {\r\n        const subtotal = rsCharge.subtotal\r\n        const taxDivisor = currency(1).add(currency(rsCharge.dutyRate).divide(100))\r\n        const netAmount = currency(subtotal).divide(taxDivisor).value\r\n\r\n        if (rsCharge.dnCurrencyCode === \"USD\") {\r\n          payableUSD = currency(payableUSD).add(netAmount).value\r\n          payableUSDTax = currency(payableUSDTax).add(subtotal).value\r\n        } else {\r\n          payableRMB = currency(payableRMB).add(netAmount).value\r\n          payableRMBTax = currency(payableRMBTax).add(subtotal).value\r\n        }\r\n      })\r\n\r\n      // 设置服务费用属性\r\n      rsOpService.payableRMB = payableRMB\r\n      rsOpService.payableUSD = payableUSD\r\n      rsOpService.payableRMBTax = payableRMBTax\r\n      rsOpService.payableUSDTax = payableUSDTax\r\n\r\n      // 更新客户消息费用\r\n      this.rsClientMessageCharge(this.rsClientMessage.rsChargeList,\r\n        serviceTypeId === 1 || serviceTypeId === 104 ? rsOpService : undefined)\r\n\r\n      // 特殊处理订舱服务类型的费用备注\r\n      if ((serviceTypeId === 1 || serviceTypeId === 2) &&\r\n        (!rsOpService.bookingChargeRemark || rsOpService.bookingChargeRemark === \"\")) {\r\n        let bookingChargeRemark = \"\"\r\n        n.forEach(rsCharge => {\r\n          bookingChargeRemark += `${rsCharge.chargeName}:\\t${rsCharge.dnCurrencyCode} ${rsCharge.dnUnitRate}/${rsCharge.dnUnitCode}\\n`\r\n        })\r\n        rsOpService.bookingChargeRemark = bookingChargeRemark\r\n      }\r\n    },\r\n    selectCarrier(item, v) {\r\n      item.carrierId = v.carrierId\r\n      // this.form.carrierId = v.carrierId\r\n      this.form.sqdCarrier = v.carrierIntlCode\r\n      this.form.carrierEnName = v.carrierIntlCode\r\n    },\r\n    handleDeselectCompanyIds(id) {\r\n      this.RelationClientIdList = this.RelationClientIdList.filter(item => item !== id)\r\n    },\r\n    handleSelectCompanyIds(node) {\r\n      if (node.filter(item => this.RelationClientIdList.indexOf(item.companyId) === -1).length > 0) {\r\n        node.filter(item => this.RelationClientIdList.indexOf(item.companyId) === -1).map(item => this.RelationClientIdList.push(item.companyId))\r\n      }\r\n      node.map(item => {\r\n        this.companyList.indexOf(item) === -1 ? this.companyList.push(item) : null\r\n      })\r\n    },\r\n    selectRelationClient(id) {\r\n    },\r\n    // 查询附加费列表\r\n    getLocalList(row) {\r\n      let index = row.locals\r\n      if (index == null || index.length == 0) {\r\n        return new Promise((resolve, reject) => {\r\n          queryLocal(row).then(response => {\r\n            row.locals = response.data\r\n            resolve(row)\r\n          })\r\n        })\r\n      }\r\n    },\r\n    handleChargeSelect(charges) {\r\n      charges.map(item => {\r\n        this.chargeSelectItem.rsChargeList.push({\r\n          ...this._.cloneDeep(item),\r\n          isAccountConfirmed: 0,\r\n          sqdDnCurrencyPaid: 0,\r\n          sqdDnCurrencyBalance: currency(item.subtotal).value,\r\n          currencyRateCalculateDate: new Date(),\r\n          chargeId: null,\r\n          sqdRctId: this.form.rctId,\r\n          serviceId: this.chargeSelectItem.serviceId,\r\n          sqdServiceTypeId: this.chargeSelectItem.sqdServiceTypeId,\r\n          sqdRctNo: this.form.rctNo\r\n        })\r\n      })\r\n    },\r\n    /**\r\n     *\r\n     * @param serviceTypeId\r\n     * @param unit 单位\r\n     * @param freight 选择的整条费用\r\n     * @returns {Promise<void>}\r\n     */\r\n    async handleFreightSelect(serviceTypeId, unit, freight) {\r\n      // 费用录入时显示加载\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: freight.inquiryNo + \" 的 \" + unit + \"费用自动录入中...\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\"\r\n      })\r\n\r\n      try {\r\n        this.setBasicInfo(serviceTypeId, freight, this.curFreightSelectRow)\r\n        let unitCode = unit.split(\"x\")[1]\r\n        // 将请求的local设置到freight中\r\n        await this.getLocalList(freight)\r\n        if (this.freightSelectData.revenueTonList && this.freightSelectData.revenueTonList.length > 0) {\r\n          // GP FR HQ（计费单位）\r\n          // good => 2x20GP\r\n          for (const good of this.freightSelectData.revenueTonList) {\r\n            // 计费单位的id等于用户在确认选择时选择的计费单位id\r\n            if (good.split(\"x\")[1] == unitCode) {\r\n              // 先将所选的运费加入报价费用中(服务项目)\r\n              // 添加费用时某些费用没有被添加进去--> FR单位时priceB、C、D都不适用，priceA为空那么这条费用就不会进入到服务项目列表中\r\n              await this.addCharge(good, freight, freight.freightId, serviceTypeId)\r\n              // 再将所选运费的基础附加费（lcoal）也加入报价费用中（服务项目）\r\n              for (const local of freight.locals) {\r\n                await this.addCharge(good, local, freight.freightId, serviceTypeId)\r\n              }\r\n              // 只匹配一个单位\r\n              break\r\n            }\r\n          }\r\n        }\r\n        this.$message.success(\"费用录入成功\")\r\n      } catch (error) {\r\n        console.error(\"费用录入失败:\", error)\r\n        this.$message.error(\"费用录入失败：\" + (error.message || \"未知错误\"))\r\n      } finally {\r\n        this.curFreightSelectRow = null\r\n        loading.close()\r\n      }\r\n      // this.openFreightSelect = false\r\n    },\r\n    // 基础信息设置\r\n    setBasicInfo(serviceTypeId, freight, curRow) {\r\n      if (curRow) {\r\n        curRow.rsServiceInstances.inquiryNo = freight.inquiryNo\r\n        curRow.rsServiceInstances.supplierId = freight.supplierId\r\n        curRow.rsServiceInstances.supplier_tel = freight.supplierTel\r\n        curRow.rsServiceInstances.supplierName = freight.company\r\n        curRow.rsServiceInstances.agreementNo = freight.agreementNo\r\n        curRow.rsServiceInstances.agreementTypeCode = freight.contractType\r\n        curRow.rsServiceInstances.inquiryInnerRemark = freight.psaRemark\r\n        curRow.rsServiceInstances.inquiryLeatestUpdatedTime = freight.createTime\r\n        curRow.rsServiceInstances.inquiryNotice = freight.noticeForSales\r\n        curRow.rsServiceInstances.maxWeight = freight.maxWeight\r\n        let curCarrier = this.carrierList.filter(item => item.carrierIntlCode === freight.carrierCode)[0]\r\n        if (curCarrier) {\r\n          curRow.carrierId = curCarrier.carrierId\r\n          // this.form.carrierId = v.carrierId\r\n          this.form.sqdCarrier = curCarrier.carrierIntlCode\r\n          this.form.carrierEnName = curCarrier.carrierIntlCode\r\n        }\r\n        curRow.inquiryScheduleSummary = freight.logisticsSchedule\r\n      }\r\n    },\r\n    async addCharge(revenueTons, row, freightId, serviceTypeId) {\r\n      let unitCode = revenueTons.split(\"x\")[1]\r\n      let amount = revenueTons.split(\"x\")[0]\r\n      if (revenueTons.split(\"x\").length === 1) {\r\n        unitCode = revenueTons.split(\"x\")[0]\r\n        amount = 1\r\n      }\r\n\r\n      // 初始化一个费用，清空quotationFreight的属性\r\n      this.resetCharge()\r\n      // 费用排序\r\n      this.rsCharge.chargeOrderNum = row.chargeOrderNum\r\n      // 费用类型排序\r\n      this.rsCharge.chargeTypeOrderNum = row.chargeTypeOrderNum\r\n      this.rsCharge.sqdServiceTypeId = row.serviceTypeId\r\n      this.rsCharge.typeId = this.typeId\r\n      // 计费单位为'票'\r\n      if (row.unit === \"BL\") {\r\n        this.rsCharge.inquiryAmount = 1\r\n        this.rsCharge.quotationAmount = 1\r\n        this.rsCharge.dnAmount = 1\r\n      } else {\r\n        this.rsCharge.inquiryAmount = amount != null ? amount : 1\r\n        this.rsCharge.quotationAmount = amount != null ? amount : 1\r\n        this.rsCharge.dnAmount = amount != null ? amount : 1\r\n      }\r\n      this.rsCharge.dnUnitCode = row.unitCode !== \"Ctnr\" ? row.unitCode : (revenueTons !== null ? unitCode : null)\r\n      // 单位是柜，成本赋值（涉及到后面是否插入服务项目）\r\n      if (row.unitCode === \"Ctnr\" && revenueTons != null) {\r\n        if (unitCode === \"20GP\") {\r\n          this.rsCharge.dnUnitRate = row.priceB\r\n        } else if (unitCode === \"40GP\") {\r\n          this.rsCharge.dnUnitRate = row.priceC\r\n        } else if (unitCode === \"40HQ\") {\r\n          this.rsCharge.dnUnitRate = row.priceD\r\n        }\r\n      }\r\n      if (this.rsCharge.dnUnitRate == null) {\r\n        this.rsCharge.dnUnitRate = row.priceA\r\n      }\r\n      this.rsCharge.costCurrencyCode = row.currencyCode\r\n      this.rsCharge.costCurrency = row.currency\r\n      //\r\n      this.rsCharge.quotationRate = this.rsCharge.inquiryRate\r\n      // row.charge --> 标准/THC\r\n      // 取费用英文简写\r\n      if (row.charge != null && row.charge.includes(\"/\")) {\r\n        this.rsCharge.charge = row.charge.split(\"/\")[1]\r\n        this.rsCharge.chargeName = row.charge.split(\"/\")[1]\r\n      } else {\r\n        this.rsCharge.charge = row.charge\r\n        this.rsCharge.chargeName = row.charge\r\n      }\r\n      this.rsCharge.chargeEn = row.chargeEn\r\n      this.rsCharge.freightId = freightId\r\n      this.rsCharge.localChargeId = row.localChargeId\r\n      this.rsCharge.profit = 0\r\n      this.rsCharge.taxRate = 0\r\n      this.rsCharge.quotationCurrencyCode = row.currencyCode\r\n      this.rsCharge.quotationCurrency = row.currency\r\n      this.rsCharge.createTime = parseTime(new Date())\r\n      this.rsCharge.createBy = this.$store.state.user.sid\r\n      this.rsCharge.company = row.company\r\n      this.rsCharge.richNo = row.richNo\r\n      this.rsCharge.dnCurrencyCode = row.currency ? row.currency : row.currencyCode\r\n      this.rsCharge.clearingCompanyId = row.supplierId\r\n      this.rsCharge.companyName = row.company\r\n      this.rsCharge.dnChargeNameId = row.chargeId\r\n      this.rsCharge.dutyRate = 0\r\n\r\n      this.rsCharge.showAmount = false\r\n      this.rsCharge.showClient = false\r\n      this.rsCharge.showCostCharge = false\r\n      this.rsCharge.showCostCurrency = false\r\n      this.rsCharge.showCostUnit = false\r\n      this.rsCharge.showCurrencyRate = false\r\n      this.rsCharge.showDutyRate = false\r\n      this.rsCharge.showQuotationCharge = false\r\n      this.rsCharge.showQuotationCurrency = false\r\n      this.rsCharge.showQuotationUnit = false\r\n      this.rsCharge.showStrategy = true\r\n      this.rsCharge.showSupplier = false\r\n      this.rsCharge.showUnitRate = false\r\n\r\n      // 获取汇率\r\n      let exchangeRate\r\n      /*       this.getExchangeRate(this.rsCharge, (result) => {\r\n              exchangeRate = result\r\n            })\r\n\r\n            exchangeRate = await this.getExchangeRateDirect(this.rsCharge) */\r\n\r\n      if (this.rsCharge) {\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (a.localCurrency == \"RMB\"\r\n            && row.dnCurrencyCode == a.overseaCurrency\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      }\r\n\r\n      if (exchangeRate == null) {\r\n        this.rsCharge.basicCurrencyRate = 1\r\n      } else {\r\n        this.rsCharge.basicCurrencyRate = exchangeRate\r\n      }\r\n\r\n      let dutyRate = currency(this.rsCharge.dnUnitRate).multiply(this.rsCharge.dnAmount).multiply(currency(row.dutyRate).divide(100)).value\r\n      this.rsCharge.subtotal = currency(currency(this.rsCharge.dnUnitRate).multiply(this.rsCharge.dnAmount)).add(dutyRate).value\r\n      this.rsCharge.sqdDnCurrencyBalance = currency(this.rsCharge.dnUnitRate).multiply(this.rsCharge.dnAmount).value\r\n\r\n      // 是否要插入新的服务项目\r\n      if (this.rsCharge.dnUnitRate != null) {\r\n        // 插入到对应服务的chargeList中\r\n        this.insertCharge(this.rsCharge, serviceTypeId)\r\n      }\r\n    },\r\n    insertCharge(row, serviceTypeId) {\r\n      if (serviceTypeId === 1) {\r\n        // this.rsOpSeaFcl.rsChargeList.push(row)\r\n        this.form.rsOpSeaFclList = this.form.rsOpSeaFclList.map(item => {\r\n          if (item.seaId === this.curFreightSelectRow.seaId) {\r\n            row.clearingCompanyId = item.rsServiceInstances.supplierId\r\n            row.companyName = item.rsServiceInstances.supplierName\r\n            item.rsChargeList.push(row)\r\n          }\r\n          return item\r\n        })\r\n      }\r\n      if (serviceTypeId === 2) {\r\n        this.form.rsOpSeaLclList = this.form.rsOpSeaLclList.map(item => {\r\n          if (item.seaId === this.curFreightSelectRow.seaId) {\r\n            row.clearingCompanyId = item.rsServiceInstances.supplierId\r\n            row.companyName = item.rsServiceInstances.supplierName\r\n            item.rsChargeList.push(row)\r\n          }\r\n          return item\r\n        })\r\n      }\r\n      if (serviceTypeId === 10) {\r\n        row.clearingCompanyId = this.rsOpAirServiceInstance.supplierId\r\n        row.companyName = this.rsOpAirServiceInstance.supplierName\r\n        this.rsOpAir.rsChargeList.push(row)\r\n      }\r\n      if (serviceTypeId === 20) {\r\n        row.clearingCompanyId = this.rsOpRailFclServiceInstance.supplierId\r\n        row.companyName = this.rsOpRailFclServiceInstance.supplierName\r\n        this.rsOpRailFCL.rsChargeList.push(row)\r\n      }\r\n      if (serviceTypeId === 21) {\r\n        row.clearingCompanyId = this.rsOpRailLclServiceInstance.supplierId\r\n        row.companyName = this.rsOpRailLclServiceInstance.supplierName\r\n        this.rsOpRailLCL.rsChargeList.push(row)\r\n      }\r\n      if (serviceTypeId === 40) {\r\n        row.clearingCompanyId = this.rsOpExpressServiceInstance.supplierId\r\n        row.companyName = this.rsOpExpressServiceInstance.supplierName\r\n        this.rsOpExpress.rsChargeList.push(row)\r\n      }\r\n      if (serviceTypeId === 50) {\r\n        this.form.rsOpCtnrTruckList = this.form.rsOpCtnrTruckList.map(item => {\r\n          if (item.seaId === this.curFreightSelectRow.seaId) {\r\n            row.clearingCompanyId = item.rsServiceInstances.supplierId\r\n            row.companyName = item.rsServiceInstances.supplierName\r\n            item.rsChargeList.push(row)\r\n          }\r\n          return item\r\n        })\r\n      }\r\n      if (serviceTypeId === 51) {\r\n        this.form.rsOpBulkTruckList = this.form.rsOpBulkTruckList.map(item => {\r\n          if (item.seaId === this.curFreightSelectRow.seaId) {\r\n            row.clearingCompanyId = item.rsServiceInstances.supplierId\r\n            row.companyName = item.rsServiceInstances.supplierName\r\n            item.rsChargeList.push(row)\r\n          }\r\n          return item\r\n        })\r\n      }\r\n      if (serviceTypeId === 60) {\r\n        this.form.rsOpDocDeclareList = this.form.rsOpDocDeclareList.map(item => {\r\n          if (item.seaId === this.curFreightSelectRow.seaId) {\r\n            row.clearingCompanyId = item.rsServiceInstances.supplierId\r\n            row.companyName = item.rsServiceInstances.supplierName\r\n            item.rsChargeList.push(row)\r\n          }\r\n          return item\r\n        })\r\n      }\r\n      if (serviceTypeId === 61) {\r\n        this.form.rsOpFreeDeclareList = this.form.rsOpFreeDeclareList.map(item => {\r\n          if (item.seaId === this.curFreightSelectRow.seaId) {\r\n            row.clearingCompanyId = item.rsServiceInstances.supplierId\r\n            row.companyName = item.rsServiceInstances.supplierName\r\n            item.rsChargeList.push(row)\r\n          }\r\n          return item\r\n        })\r\n      }\r\n      if (serviceTypeId === 70) {\r\n        row.clearingCompanyId = this.rsOpDOAgentServiceInstance.supplierId\r\n        row.companyName = this.rsOpDOAgentServiceInstance.supplierName\r\n        this.rsOpDOAgent.rsChargeList.push(row)\r\n      }\r\n      if (serviceTypeId === 71) {\r\n        row.clearingCompanyId = this.rsOpClearAgentServiceInstance.supplierId\r\n        row.companyName = this.rsOpClearAgentServiceInstance.supplierName\r\n        this.rsOpClearAgent.rsChargeList.push(row)\r\n      }\r\n      if (serviceTypeId === 80) {\r\n        row.clearingCompanyId = this.rsOpWHSServiceInstance.supplierId\r\n        row.companyName = this.rsOpWHSServiceInstance.supplierName\r\n        this.rsOpWHS.rsChargeList.push(row)\r\n      }\r\n      if (serviceTypeId === 90) {\r\n        row.clearingCompanyId = this.rsOp3rdCertServiceInstance.supplierId\r\n        row.companyName = this.rsOp3rdCertServiceInstance.supplierName\r\n        this.rsOp3rdCert.rsChargeList.push(row)\r\n      }\r\n      if (serviceTypeId === 100) {\r\n        row.clearingCompanyId = this.rsOpINSServiceInstance.supplierId\r\n        row.companyName = this.rsOpINSServiceInstance.supplierName\r\n        this.rsOpINS.rsChargeList.push(row)\r\n      }\r\n      if (serviceTypeId === 101) {\r\n        row.clearingCompanyId = this.rsOpTradingServiceInstance.supplierId\r\n        row.companyName = this.rsOpTradingServiceInstance.supplierName\r\n        this.rsOpTrading.rsChargeList.push(row)\r\n      }\r\n      if (serviceTypeId === 102) {\r\n        row.clearingCompanyId = this.rsOpFumigationServiceInstance.supplierId\r\n        row.companyName = this.rsOpFumigationServiceInstance.supplierName\r\n        this.rsOpFumigation.rsChargeList.push(row)\r\n      }\r\n      if (serviceTypeId === 103) {\r\n        row.clearingCompanyId = this.rsOpCOServiceInstance.supplierId\r\n        row.companyName = this.rsOpCOServiceInstance.supplierName\r\n        this.rsOpCO.rsChargeList.push(row)\r\n      }\r\n      if (serviceTypeId === 104) {\r\n        row.clearingCompanyId = this.rsOpCOServiceInstance.supplierId\r\n        row.companyName = this.rsOpCOServiceInstance.supplierName\r\n        this.rsOpOther.rsChargeList.push(row)\r\n      }\r\n    },\r\n    getExchangeRate(row, callback) {\r\n      let re\r\n      if (row) {\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (a.basicCurrency == \"RMB\"\r\n            && row.dnCurrencyCode == a.currencyCode\r\n            && parseTime(a.validFrom) <= parseTime(row.createTime)\r\n            && parseTime(row.createTime) <= parseTime(a.validTo)) {\r\n            re = a.exchangeRate / a.base\r\n          }\r\n        }\r\n        callback(re)\r\n      }\r\n    },\r\n    resetCharge() {\r\n      this.rsCharge = {\r\n        chargeId: null,\r\n        sqdRctId: null,\r\n        serviceId: null,\r\n        sqdServiceTypeId: null,\r\n        sqdRctNo: null,\r\n        relatedFreightId: null,\r\n        isRecievingOrPaying: null,\r\n        clearingCompanyId: null,\r\n        clearingCompanySummary: null,\r\n        quotationStrategyId: null,\r\n        dnChargeNameId: null,\r\n        dnCurrencyCode: null,\r\n        dnUnitRate: null,\r\n        dnUnitCode: null,\r\n        dnAmount: null,\r\n        basicCurrencyRate: null,\r\n        dutyRate: null,\r\n        subtotal: null,\r\n        chargeRemark: null,\r\n        clearingCurrencyCode: null,\r\n        dnCurrencyReceived: null,\r\n        dnCurrencyPaid: null,\r\n        dnCurrencyBalance: null,\r\n        accountReceivedIdList: null,\r\n        accountPaidIdList: null,\r\n        logisticsInvoiceIdList: null\r\n      }\r\n    },\r\n    selectCompany() {\r\n      this.openCompanySelect = true\r\n    },\r\n    selectCompanyData(row) {\r\n      this.form.clientName = row.companyTaxCode + \"/\" + (row.companyShortName ? row.companyShortName : row.companyEnShortName) + \"/\" + row.companyLocalName\r\n      this.form.clientId = row.companyId\r\n      this.form.clientContact = row.mainStaffOfficialName\r\n      this.form.clientContactTel = row.staffMobile\r\n      this.form.clientContactEmail = row.staffEmail\r\n      this.form.roleIds = row.roleIds\r\n      this.form.salesId = row.belongTo\r\n      // this.salesId = row.belongTo\r\n      this.form.salesAssistantId = row.followUp\r\n      // this.salesAssistantId = row.followUp\r\n      this.form.orderBelongsTo = row.companyBelongTo\r\n      this.form.agreementNumber = row.agreementNumber\r\n      this.form.paymentNode = (row.receiveStandard ? row.receiveStandard : \"\") + (row.receiveTerm ? row.receiveTerm : \"\")\r\n      this.companyList.indexOf(row.companyId) === -1 ? this.companyList.push(row) : null\r\n\r\n      this.openCompanySelect = false\r\n\r\n      if (this.belongList != undefined) {\r\n        for (const a of this.belongList) {\r\n          if (a.children != undefined) {\r\n            for (const b of a.children) {\r\n              if (b.children != undefined) {\r\n                for (const c of b.children) {\r\n                  if (c.staffId == this.form.salesId) {\r\n                    this.salesId = c.deptId\r\n                  }\r\n                  if (c.staffId == this.form.salesAssistantId) {\r\n                    this.salesAssistantId = c.deptId\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    getCompanyRoleIds(val) {\r\n      this.form.roleIds = val\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document.scss';\r\n</style>\r\n"]}]}