package com.rich.system.service.async;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.ApplicationEvent;

/**
 * 服务处理开始事件
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ServiceProcessingStartedEvent extends ApplicationEvent {
    
    /**
     * 操作单ID
     */
    private Long rctId;
    
    /**
     * 操作单号
     */
    private String rctNo;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 开始时间
     */
    private long startTime;
    
    public ServiceProcessingStartedEvent(Long rctId, String rctNo, String serviceName) {
        super(serviceName);
        this.rctId = rctId;
        this.rctNo = rctNo;
        this.serviceName = serviceName;
        this.startTime = System.currentTimeMillis();
    }
}
