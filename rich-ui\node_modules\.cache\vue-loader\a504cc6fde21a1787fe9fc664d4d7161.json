{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\index.vue", "mtime": 1754646305888}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBkZWxDb21wYW55LA0KICBnZXRCYW5rLA0KICBnZXRDb21wYW55LA0KICBnZXRDb25uZWN0LA0KICBsaXN0Q29tcGFueSwNCiAgbWVyZ2VDb21wYW55LA0KICBxdWVyeVNhbWUsDQogIHVwZGF0ZUNvbXBhbnkNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL2NvbXBhbnkiDQppbXBvcnQge2dldEluZm9CeVN0YWZmSWR9IGZyb20gIkAvYXBpL3N5c3RlbS9yb2xlIg0KaW1wb3J0IHthZGRNZXNzYWdlfSBmcm9tICJAL2FwaS9zeXN0ZW0vbWVzc2FnZSINCmltcG9ydCB7bGlzdENvbW11bmljYXRpb259IGZyb20gIkAvYXBpL3N5c3RlbS9jb21tdW5pY2F0aW9uIg0KaW1wb3J0IHtsaXN0QWdyZWVtZW50cmVjb3JkfSBmcm9tICJAL2FwaS9zeXN0ZW0vYWdyZWVtZW50cmVjb3JkIg0KDQppbXBvcnQgcGlueWluIGZyb20gImpzLXBpbnlpbiINCmltcG9ydCBzdG9yZSBmcm9tICJAL3N0b3JlIg0KaW1wb3J0IFRyZWVzZWxlY3QgZnJvbSAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QiDQppbXBvcnQgIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0L2Rpc3QvdnVlLXRyZWVzZWxlY3QubWluLmNzcyINCg0KaW1wb3J0IEJsYWNrTGlzdCBmcm9tICJAL2NvbXBvbmVudHMvQmxhY2tMaXN0Ig0KaW1wb3J0IGNvbW11bmljYXRpb25zIGZyb20gIkAvdmlld3Mvc3lzdGVtL2NvbW11bmljYXRpb24iDQppbXBvcnQgYWdyZWVtZW50UmVjb3JkIGZyb20gIkAvdmlld3Mvc3lzdGVtL2FncmVlbWVudFJlY29yZCINCmltcG9ydCBzdGFmZkluZm8gZnJvbSAiQC92aWV3cy9zeXN0ZW0vY29tcGFueS9zdGFmZkluZm8iDQoNCmltcG9ydCBjb21wYW55IGZyb20gIkAvdmlld3Mvc3lzdGVtL2NvbXBhbnkvY29tcGFueSINCmltcG9ydCBjb250YWN0b3IgZnJvbSAiQC92aWV3cy9zeXN0ZW0vY29tcGFueS9jb250YWN0b3IiDQppbXBvcnQgbG9jYXRpb24gZnJvbSAiQC92aWV3cy9zeXN0ZW0vY29tcGFueS9sb2NhdGlvbiINCmltcG9ydCByb2xlIGZyb20gIkAvdmlld3Mvc3lzdGVtL2NvbXBhbnkvcm9sZSINCmltcG9ydCBzZXJ2aWNlVHlwZSBmcm9tICJAL3ZpZXdzL3N5c3RlbS9jb21wYW55L3NlcnZpY2VUeXBlIg0KaW1wb3J0IGRlcGFydHVyZSBmcm9tICJAL3ZpZXdzL3N5c3RlbS9jb21wYW55L2RlcGFydHVyZSINCmltcG9ydCBkZXN0aW5hdGlvbiBmcm9tICJAL3ZpZXdzL3N5c3RlbS9jb21wYW55L2Rlc3RpbmF0aW9uIg0KaW1wb3J0IGNhcmdvVHlwZSBmcm9tICJAL3ZpZXdzL3N5c3RlbS9jb21wYW55L2NhcmdvVHlwZSINCmltcG9ydCBjYXJyaWVyIGZyb20gIkAvdmlld3Mvc3lzdGVtL2NvbXBhbnkvY2FycmllciINCmltcG9ydCBhY2NvdW50IGZyb20gIkAvdmlld3Mvc3lzdGVtL2NvbXBhbnkvYWNjb3VudCINCmltcG9ydCBhZ3JlZW1lbnQgZnJvbSAiQC92aWV3cy9zeXN0ZW0vY29tcGFueS9hZ3JlZW1lbnQiDQppbXBvcnQgY29tbXVuaWNhdGlvbiBmcm9tICJAL3ZpZXdzL3N5c3RlbS9jb21wYW55L2NvbW11bmljYXRpb24iDQppbXBvcnQgZ3JhZGUgZnJvbSAiQC92aWV3cy9zeXN0ZW0vY29tcGFueS9ncmFkZSINCmltcG9ydCBhY2hpZXZlbWVudCBmcm9tICJAL3ZpZXdzL3N5c3RlbS9jb21wYW55L2FjaGlldmVtZW50Ig0KaW1wb3J0IHJlbWFyayBmcm9tICJAL3ZpZXdzL3N5c3RlbS9jb21wYW55L3JlbWFyayINCmltcG9ydCBiZWxvbmcgZnJvbSAiQC92aWV3cy9zeXN0ZW0vY29tcGFueS9iZWxvbmciDQppbXBvcnQgYXV0aCBmcm9tICJAL3BsdWdpbnMvYXV0aCINCmltcG9ydCB7cGFyc2VUaW1lfSBmcm9tICJAL3V0aWxzL3JpY2giDQppbXBvcnQge01lc3NhZ2V9IGZyb20gImVsZW1lbnQtdWkiDQppbXBvcnQgQ29uZmlybWVkIGZyb20gIkAvY29tcG9uZW50cy9Db25maXJtZWQvaW5kZXgudnVlIg0KaW1wb3J0IHJzUGF5bWVudFRpdGxlIGZyb20gIkAvdmlld3Mvc3lzdGVtL2NvbXBhbnkvcnNQYXltZW50VGl0bGUudnVlIg0KaW1wb3J0IEFjY291bnRJbmZvIGZyb20gIkAvdmlld3Mvc3lzdGVtL2NvbXBhbnkvYWNjb3VudEluZm8udnVlIg0KaW1wb3J0IHtjaGVja1JvbGV9IGZyb20gIkAvdXRpbHMvcGVybWlzc2lvbiINCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiQ29tcGFueSIsDQogIGRpY3RzOiBbInN5c19pc19pZGxlIl0sDQogIGNvbXBvbmVudHM6IHsNCiAgICBBY2NvdW50SW5mbywNCiAgICBDb25maXJtZWQsDQogICAgVHJlZXNlbGVjdCwNCiAgICBjb21tdW5pY2F0aW9uLA0KICAgIGNvbW11bmljYXRpb25zLA0KICAgIEJsYWNrTGlzdCwNCiAgICBiZWxvbmcsDQogICAgY29tcGFueSwNCiAgICBjb250YWN0b3IsDQogICAgc3RhZmZJbmZvLA0KICAgIGxvY2F0aW9uLA0KICAgIHJvbGUsDQogICAgc2VydmljZVR5cGUsDQogICAgZGVwYXJ0dXJlLA0KICAgIGRlc3RpbmF0aW9uLA0KICAgIGNhcmdvVHlwZSwNCiAgICBjYXJyaWVyLA0KICAgIGFjY291bnQsDQogICAgYWdyZWVtZW50LA0KICAgIGFncmVlbWVudFJlY29yZCwNCiAgICBncmFkZSwNCiAgICBhY2hpZXZlbWVudCwNCiAgICByZW1hcmssDQogICAgcnNQYXltZW50VGl0bGUNCiAgfSwNCiAgcHJvcHM6IFsicm9sZVR5cGVJZCIsICJyb2xlUmljaCIsICJyb2xlQ2xpZW50IiwgInJvbGVTdXBwbGllciIsICJyb2xlU3VwcG9ydCJdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICBzaG93TGVmdDogMywNCiAgICAgIHNob3dSaWdodDogMjEsDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgYWRkOiBmYWxzZSwNCiAgICAgIHNlbGVjdFR3bzogdHJ1ZSwNCiAgICAgIHNpemU6IHRoaXMuJHN0b3JlLnN0YXRlLmFwcC5zaXplIHx8ICJtaW5pIiwNCiAgICAgIC8vIOWFrOWPuOihqOagvOaVsOaNrg0KICAgICAgbWVyZ2VMaXN0OiBbXSwNCiAgICAgIGNvbXBhbnlMaXN0OiBbXSwNCiAgICAgIHN0YWZmTGlzdDogW10sDQogICAgICBhY2NvdW50TGlzdDogW10sDQogICAgICBjb21tdW5pY2F0aW9uTGlzdDogW10sDQogICAgICBhZ3JlZW1lbnRMaXN0OiBbXSwNCiAgICAgIGJlbG9uZ0xpc3Q6IFtdLA0KICAgICAgY2Fycmllckxpc3Q6IFtdLA0KICAgICAgYnVzaW5lc3NMaXN0OiBbXSwNCiAgICAgIHRlbUNhcnJpZXJMaXN0OiBbXSwNCiAgICAgIGxvY2F0aW9uT3B0aW9uczogW10sDQogICAgICBjYXJyaWVySWRzOiBbXSwNCiAgICAgIGNvbXBhbnlJbmZvOiB7fSwNCiAgICAgIHF1ZXJ5Q2FycmllcklkczogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgbWVyZ2U6IGZhbHNlLA0KICAgICAgb3BlbkNvbXBhbnk6IGZhbHNlLA0KICAgICAgb3BlblN0YWZmOiBmYWxzZSwNCiAgICAgIG9wZW5BY2NvdW50OiBmYWxzZSwNCiAgICAgIG9wZW5Db21tdW5pY2F0aW9uOiBmYWxzZSwNCiAgICAgIG9wZW5BZ3JlZW1lbnQ6IGZhbHNlLA0KICAgICAgb3BlbkJsYWNrTGlzdDogZmFsc2UsDQogICAgICBlZGl0OiBmYWxzZSwNCiAgICAgIGJlbG9uZ1RvOiBudWxsLA0KICAgICAgZm9sbG93VXA6IG51bGwsDQogICAgICBxdWVyeUJGU3RhZmZJZDogbnVsbCwNCiAgICAgIHF1ZXJ5QlN0YWZmSWQ6IG51bGwsDQogICAgICByZWZyZXNoVGFibGU6IHRydWUsDQogICAgICBjdG90bGU6IG51bGwsDQogICAgICBhdG90bGU6IG51bGwsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAyMCwNCiAgICAgICAgLy8gcm9sZVR5cGVJZDogdGhpcy5yb2xlVHlwZUlkLA0KICAgICAgICByb2xlUmljaDogdGhpcy5yb2xlUmljaCA/IHRoaXMucm9sZVJpY2ggOiBudWxsLA0KICAgICAgICByb2xlQ2xpZW50OiB0aGlzLnJvbGVDbGllbnQgPyB0aGlzLnJvbGVDbGllbnQgOiBudWxsLA0KICAgICAgICByb2xlU3VwcGxpZXI6IHRoaXMucm9sZVN1cHBsaWVyID8gdGhpcy5yb2xlU3VwcGxpZXIgOiBudWxsLA0KICAgICAgICByb2xlU3VwcG9ydDogdGhpcy5yb2xlU3VwcG9ydCA/IHRoaXMucm9sZVN1cHBvcnQgOiBudWxsLA0KICAgICAgICBjb21wYW55UXVlcnk6IG51bGwsDQogICAgICAgIGxvY2F0aW9uSWQ6IG51bGwsDQogICAgICAgIGlkbGVTdGF0dXM6IG51bGwsDQogICAgICAgIHF1ZXJ5U3RhZmZJZDogbnVsbCwNCiAgICAgICAgc2hvd1ByaW9yaXR5OiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkczogW10sDQogICAgICAgIGNhcmdvVHlwZUlkczogW10sDQogICAgICAgIGxvY2F0aW9uRGVwYXJ0dXJlSWRzOiBbXSwNCiAgICAgICAgbGluZURlcGFydHVyZUlkczogW10sDQogICAgICAgIGxvY2F0aW9uRGVzdGluYXRpb25JZHM6IFtdLA0KICAgICAgICBsaW5lRGVzdGluYXRpb25JZHM6IFtdLA0KICAgICAgICByb2xlSWRzOiBbXSwNCiAgICAgICAgY2FycmllcklkczogW10NCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHsNCiAgICAgICAgYWdyZWVtZW50U3RhcnREYXRlOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRFbmREYXRlOiBudWxsLA0KICAgICAgICBzZXR0bGVtZW50RGF0ZTogbnVsbA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHt9LA0KICAgICAgLy8g5b2T5YmN54K55Ye75L+u5pS55pe26YCJ5Lit55qE6K6w5b2VDQogICAgICBjb21wYW55Um93OiBudWxsLA0KICAgICAgaXNMb2NrOiB0cnVlLA0KICAgICAgc2hvd0NvbmZpcm06IGZhbHNlLA0KICAgICAgbG9jYWxTdGFuZGFyZDogbnVsbCwNCiAgICAgIGRlc2NyaXB0aW9uOiBudWxsDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIGNvbHVtbnM6IHsNCiAgICAgIGdldCgpIHsNCiAgICAgICAgaWYgKHRoaXMucm9sZVR5cGVJZCA9PSAiMiIpIHsNCiAgICAgICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUubGlzdFNldHRpbmdzLnN1cHBsaWVyU2V0dGluZw0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLnJvbGVUeXBlSWQgPT0gIjEiKSB7DQogICAgICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLmxpc3RTZXR0aW5ncy5jbGllbnRTZXR0aW5nDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMucm9sZUNsaWVudCkgew0KICAgICAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5saXN0U2V0dGluZ3MuY2xpZW50U2V0dGluZw0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLnJvbGVTdXBwbGllcikgew0KICAgICAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5saXN0U2V0dGluZ3Muc3VwcGxpZXJTZXR0aW5nDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMucm9sZVJpY2gpIHsNCiAgICAgICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUubGlzdFNldHRpbmdzLnN1cHBsaWVyU2V0dGluZw0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLnJvbGVTdXBwb3J0KSB7DQogICAgICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLmxpc3RTZXR0aW5ncy5zdXBwbGllclNldHRpbmcNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgY29tbW9uTG9jaygpIHsNCiAgICAgIHJldHVybiAodGhpcy5mb3JtLnBzYUNvbmZpcm1lZCA9PSAxIHx8IHRoaXMuZm9ybS5zYWxlc0NvbmZpcm1lZCA9PSAxKSA/IHRydWUgOiBmYWxzZQ0KICAgIH0sDQogICAgYmFzaWNMb2NrKCkgew0KICAgICAgcmV0dXJuIHRoaXMuZm9ybS5vcENvbmZpcm1lZCA9PSAxID8gdHJ1ZSA6IGZhbHNlDQogICAgfSwNCiAgICBhZ3JlZW1lbnRMb2NrKCkgew0KICAgICAgcmV0dXJuIHRoaXMuZm9ybS5hY2NDb25maXJtZWQgPT0gMSA/IHRydWUgOiBmYWxzZQ0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBzaG93U2VhcmNoKG4pIHsNCiAgICAgIGlmIChuID09IHRydWUpIHsNCiAgICAgICAgdGhpcy5zaG93UmlnaHQgPSAyMQ0KICAgICAgICB0aGlzLnNob3dMZWZ0ID0gMw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5zaG93UmlnaHQgPSAyNA0KICAgICAgICB0aGlzLnNob3dMZWZ0ID0gMA0KICAgICAgfQ0KICAgIH0sDQogICAgcXVlcnlTdGFmZklkKCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5xdWVyeVN0YWZmSWQgPSB0aGlzLnF1ZXJ5U3RhZmZJZA0KICAgIH0sDQogICAgImZvcm0uYmVsb25nVG8iKCkgew0KICAgICAgaWYgKHRoaXMuZm9ybS5iZWxvbmdUbyA9PSB0aGlzLmZvcm0uZm9sbG93VXApIHsNCiAgICAgICAgdGhpcy5mb3JtLmZvbGxvd1VwID0gMA0KICAgICAgICB0aGlzLmZvbGxvd1VwID0gbnVsbA0KICAgICAgfQ0KICAgIH0sDQogICAgImZvcm0uc2VydmljZVR5cGVJZHMiKG4pIHsNCiAgICAgIHRoaXMubG9hZENhcnJpZXIoKQ0KICAgICAgbGV0IGxpc3QgPSBbXQ0KICAgICAgaWYgKHRoaXMuY2Fycmllckxpc3QgIT0gdW5kZWZpbmVkICYmIG4gIT0gbnVsbCAmJiBuLmluY2x1ZGVzKC0xKSkgew0KICAgICAgICB0aGlzLnRlbUNhcnJpZXJMaXN0ID0gdGhpcy5jYXJyaWVyTGlzdA0KICAgICAgICBmb3IgKGNvbnN0IHYgb2YgdGhpcy5jYXJyaWVyTGlzdCkgew0KICAgICAgICAgIGlmICh2LmNoaWxkcmVuICE9IHVuZGVmaW5lZCAmJiB2LmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIGZvciAoY29uc3QgYSBvZiB2LmNoaWxkcmVuKSB7DQogICAgICAgICAgICAgIGlmIChhLmNoaWxkcmVuICE9IHVuZGVmaW5lZCAmJiBhLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGIgb2YgYS5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuZm9ybS5jYXJyaWVySWRzICE9IG51bGwgJiYgdGhpcy5mb3JtLmNhcnJpZXJJZHMuaW5jbHVkZXMoYi5jYXJyaWVyLmNhcnJpZXJJZCkgJiYgIXRoaXMuY2Fycmllcklkcy5pbmNsdWRlcyhiLnNlcnZpY2VUeXBlSWQpKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuY2Fycmllcklkcy5wdXNoKGIuc2VydmljZVR5cGVJZCkNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmNhcnJpZXJMaXN0ICE9IHVuZGVmaW5lZCAmJiBuICE9IG51bGwgJiYgIW4uaW5jbHVkZXMoLTEpKSB7DQogICAgICAgIGZvciAoY29uc3QgYyBvZiB0aGlzLmNhcnJpZXJMaXN0KSB7DQogICAgICAgICAgaWYgKG4gIT0gbnVsbCAmJiBuICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgZm9yIChjb25zdCBzIG9mIG4pIHsNCiAgICAgICAgICAgICAgaWYgKGMuc2VydmljZVR5cGVJZCA9PSBzKSB7DQogICAgICAgICAgICAgICAgbGlzdC5wdXNoKGMpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgaWYgKGMuY2hpbGRyZW4gIT0gdW5kZWZpbmVkICYmIGMuY2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICAgIGZvciAoY29uc3QgY2ggb2YgYy5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgICAgaWYgKGNoLnNlcnZpY2VUeXBlSWQgPT0gcykgew0KICAgICAgICAgICAgICAgICAgICBsaXN0LnB1c2goY2gpDQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHRoaXMudGVtQ2Fycmllckxpc3QgPSBsaXN0DQogICAgICAgIGlmICh0aGlzLnRlbUNhcnJpZXJMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICBmb3IgKGNvbnN0IHYgb2YgdGhpcy50ZW1DYXJyaWVyTGlzdCkgew0KICAgICAgICAgICAgaWYgKHYuY2hpbGRyZW4gIT0gdW5kZWZpbmVkICYmIHYuY2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICBmb3IgKGNvbnN0IGEgb2Ygdi5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgIGlmICh0aGlzLmZvcm0uY2FycmllcklkcyAhPSBudWxsICYmIHRoaXMuZm9ybS5jYXJyaWVySWRzLmluY2x1ZGVzKGEuY2Fycmllci5jYXJyaWVySWQpICYmICF0aGlzLmNhcnJpZXJJZHMuaW5jbHVkZXMoYS5zZXJ2aWNlVHlwZUlkKSkgew0KICAgICAgICAgICAgICAgICAgdGhpcy5jYXJyaWVySWRzLnB1c2goYS5zZXJ2aWNlVHlwZUlkKQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgZm9ybSgpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0uc2FsZXNDb25maXJtZWQgPT0gMSB8fCB0aGlzLmZvcm0uYWNjQ29uZmlybWVkID09IDEgfHwgdGhpcy5mb3JtLnBzYUNvbmZpcm1lZCA9PSAxIHx8IHRoaXMuZm9ybS5vcENvbmZpcm1lZCA9PSAxKSB7DQogICAgICAgIHRoaXMuaXNMb2NrID0gdHJ1ZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5pc0xvY2sgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQogICAgbG9jYWxTdGFuZGFyZCh2KSB7DQogICAgICB0aGlzLmhhbmRsZVRlcm1DaGFuZ2UoKQ0KICAgIH0sDQogICAgImZvcm0ucmVjZWl2ZVRlcm0iKHYpIHsNCiAgICAgIHRoaXMuaGFuZGxlVGVybUNoYW5nZSgpDQogICAgfSwNCiAgICAiZm9ybS5wYXlUZXJtIih2KSB7DQogICAgICB0aGlzLmhhbmRsZVRlcm1DaGFuZ2UoKQ0KICAgIH0sDQogICAgImZvcm0ucmVjZWl2ZVdheSIodikgew0KICAgICAgdGhpcy5oYW5kbGVUZXJtQ2hhbmdlKCkNCiAgICB9LA0KICAgICJmb3JtLnBheVdheSIodikgew0KICAgICAgdGhpcy5oYW5kbGVUZXJtQ2hhbmdlKCkNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCkudGhlbigoKSA9PiB7DQogICAgICB0aGlzLmxvYWRCdXNpbmVzc2VzKCkNCiAgICAgIHRoaXMubG9hZENhcnJpZXIoKQ0KICAgICAgdGhpcy5sb2FkU2FsZXMoKQ0KICAgIH0pDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBwYXJzZVRpbWUsDQogICAgaGFuZGxlVGVybUNoYW5nZSh2KSB7DQogICAgICBpZiAodGhpcy5mb3JtLnJlY2VpdmVXYXkgPT09ICLmnIjnu5MiIHx8IHRoaXMuZm9ybS5wYXlXYXkgPT09ICLmnIjnu5MiKSB7DQogICAgICAgIGlmICh0aGlzLnJvbGVDbGllbnQgPT0gMSB8fCB0aGlzLnJvbGVSaWNoID09IDEpIHsNCiAgICAgICAgICB0aGlzLmRlc2NyaXB0aW9uID0gKHRoaXMuZm9ybS5yZWNlaXZlU3RhbmRhcmQgPyB0aGlzLmZvcm0ucmVjZWl2ZVN0YW5kYXJkIDogIiIpICsgKHRoaXMuZm9ybS5yZWNlaXZlVGVybSA/ICh0aGlzLmZvcm0ucmVjZWl2ZVRlcm0uc3Vic3RyaW5nKDAsIDEpID09PSAiLSIgPyAoIueahOS4i+S4quaciCIgKyB0aGlzLmZvcm0ucmVjZWl2ZVRlcm0uc3Vic3RyaW5nKDEsIDMpICsgIuWPt+S5i+WQjiIpIDogKCLnmoTkuIvkuKrmnIgiICsgdGhpcy5mb3JtLnJlY2VpdmVUZXJtLnN1YnN0cmluZygxLCAzKSArICLlj7fkuYvliY0iKSkgOiAi5YmNIikNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmRlc2NyaXB0aW9uID0gKHRoaXMuZm9ybS5wYXlTdGFuZGFyZCA/IHRoaXMuZm9ybS5wYXlTdGFuZGFyZCA6ICIiKSArICh0aGlzLmZvcm0ucGF5VGVybSA/ICh0aGlzLmZvcm0ucGF5VGVybS5zdWJzdHJpbmcoMCwgMSkgPT09ICItIiA/ICgi55qE5LiL5Liq5pyIIiArIHRoaXMuZm9ybS5wYXlUZXJtLnN1YnN0cmluZygxLCAzKSArICLlj7fkuYvlkI4iKSA6ICgi55qE5LiL5Liq5pyIIiArIHRoaXMuZm9ybS5wYXlUZXJtLnN1YnN0cmluZygxLCAzKSArICLlj7fkuYvliY0iKSkgOiAi5YmNIikNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaWYgKHRoaXMucm9sZUNsaWVudCA9PSAxIHx8IHRoaXMucm9sZVJpY2ggPT0gMSkgew0KICAgICAgICAgIHRoaXMuZGVzY3JpcHRpb24gPSAodGhpcy5mb3JtLnJlY2VpdmVTdGFuZGFyZCA/IHRoaXMuZm9ybS5yZWNlaXZlU3RhbmRhcmQgOiAiIikgKyAodGhpcy5mb3JtLnJlY2VpdmVUZXJtID8gKHRoaXMuZm9ybS5yZWNlaXZlVGVybS5zdWJzdHJpbmcoMCwgMSkgPT09ICItIiA/IHRoaXMuZm9ybS5yZWNlaXZlVGVybS5zdWJzdHJpbmcoMSwgMykgKyAi5aSp5YmNIiA6ICLlkI4iICsgdGhpcy5mb3JtLnJlY2VpdmVUZXJtLnN1YnN0cmluZygxLCAzKSArICLlpKnlhoUiKSA6ICLliY0iKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuZGVzY3JpcHRpb24gPSAodGhpcy5mb3JtLnBheVN0YW5kYXJkID8gdGhpcy5mb3JtLnBheVN0YW5kYXJkIDogIiIpICsgKHRoaXMuZm9ybS5wYXlUZXJtID8gKHRoaXMuZm9ybS5wYXlUZXJtLnN1YnN0cmluZygwLCAxKSA9PT0gIi0iID8gdGhpcy5mb3JtLnBheVRlcm0uc3Vic3RyaW5nKDEsIDMpICsgIuWkqeWJjSIgOiAi5ZCOIiArIHRoaXMuZm9ybS5wYXlUZXJtLnN1YnN0cmluZygxLCAzKSArICLlpKnlhoUiKSA6ICLliY0iKQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBsb2FkU2FsZXMoKSB7DQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5zYWxlc0xpc3QubGVuZ3RoID09IDAgfHwgdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5yZWRpc0xpc3Quc2FsZXNMaXN0KSB7DQogICAgICAgIGlmIChjaGVja1JvbGUoWyJPcGVyYXRvciJdKSkgew0KICAgICAgICAgIHN0b3JlLmRpc3BhdGNoKCJnZXRTYWxlc0xpc3QiKS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuYmVsb25nTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuc2FsZXNMaXN0DQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBzdG9yZS5kaXNwYXRjaCgiZ2V0U2FsZXNMaXN0QyIpLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5iZWxvbmdMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5zYWxlc0xpc3QNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmJlbG9uZ0xpc3QgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnNhbGVzTGlzdA0KICAgICAgfQ0KICAgIH0sDQogICAgbG9hZEJ1c2luZXNzZXMoKSB7DQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5idXNpbmVzc2VzTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5idXNpbmVzc2VzTGlzdCkgew0KICAgICAgICBzdG9yZS5kaXNwYXRjaCgiZ2V0QnVzaW5lc3Nlc0xpc3QiKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmJ1c2luZXNzTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYnVzaW5lc3Nlc0xpc3QNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuYnVzaW5lc3NMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5idXNpbmVzc2VzTGlzdA0KICAgICAgfQ0KICAgIH0sDQogICAgbG9hZENhcnJpZXIoKSB7DQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5zZXJ2aWNlVHlwZUNhcnJpZXJzLmxlbmd0aCA9PSAwIHx8IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEucmVkaXNMaXN0LnNlcnZpY2VUeXBlQ2FycmllcnMpIHsNCiAgICAgICAgc3RvcmUuZGlzcGF0Y2goImdldFNlcnZpY2VUeXBlQ2FycmllcnNMaXN0IikudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5jYXJyaWVyTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuc2VydmljZVR5cGVDYXJyaWVycw0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5jYXJyaWVyTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuc2VydmljZVR5cGVDYXJyaWVycw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5p+l6YeNDQogICAgcXVlcnlTYW1lKCkgew0KICAgICAgLy8g5Yid5aeL5YyW6K+35rGC5pWw5o2uLOeJueWIq+azqOaEj2RlbGV0ZVN0YXR1c+iuvue9ruacqjEs5ZCO56uv5Lya5a+56L+Z5Liq5YC855qE5pWw5o2u5YGa6YeN5aSN5qCh6aqMDQogICAgICBsZXQgZGF0YSA9IHsNCiAgICAgICAgY2FyZ29UeXBlSWRzOiBbXSwNCiAgICAgICAgbG9jYXRpb25EZXBhcnR1cmVJZHM6IFtdLA0KICAgICAgICBsb2NhdGlvbkRlc3RpbmF0aW9uSWRzOiBbXSwNCiAgICAgICAgbGluZURlcGFydHVyZUlkczogW10sDQogICAgICAgIGxpbmVEZXN0aW5hdGlvbklkczogW10sDQogICAgICAgIGNvbXBhbnlTaG9ydE5hbWU6IHRoaXMuZm9ybS5jb21wYW55U2hvcnROYW1lLA0KICAgICAgICBjb21wYW55TG9jYWxOYW1lOiB0aGlzLmZvcm0uY29tcGFueUxvY2FsTmFtZSwNCiAgICAgICAgc2VydmljZVR5cGVJZHM6IHRoaXMuZm9ybS5zZXJ2aWNlVHlwZUlkcywNCiAgICAgICAgcm9sZVR5cGVJZDogdGhpcy5yb2xlVHlwZUlkLA0KICAgICAgICByb2xlUmljaDogdGhpcy5yb2xlUmljaCA/IHRoaXMucm9sZVJpY2ggOiBudWxsLA0KICAgICAgICByb2xlQ2xpZW50OiB0aGlzLnJvbGVDbGllbnQgPyB0aGlzLnJvbGVDbGllbnQgOiBudWxsLA0KICAgICAgICByb2xlU3VwcGxpZXI6IHRoaXMucm9sZVN1cHBsaWVyID8gdGhpcy5yb2xlU3VwcGxpZXIgOiBudWxsLA0KICAgICAgICByb2xlU3VwcG9ydDogdGhpcy5yb2xlU3VwcG9ydCA/IHRoaXMucm9sZVN1cHBvcnQgOiBudWxsLA0KICAgICAgICBiZWxvbmdUbzogdGhpcy5mb3JtLmJlbG9uZ1RvLA0KICAgICAgICBmb2xsb3dVcDogdGhpcy5mb3JtLmZvbGxvd1VwLA0KICAgICAgICBkZWxldGVTdGF0dXM6IDENCiAgICAgIH0NCiAgICAgIGdldEluZm9CeVN0YWZmSWQodGhpcy4kc3RvcmUuc3RhdGUudXNlci5zaWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBkYXRhLmNhcmdvVHlwZUlkcyA9IHJlc3BvbnNlLmNhcmdvVHlwZUlkcw0KICAgICAgICBkYXRhLmxvY2F0aW9uRGVwYXJ0dXJlSWRzID0gcmVzcG9uc2UubG9jYXRpb25EZXBhcnR1cmVJZHMNCiAgICAgICAgZGF0YS5sb2NhdGlvbkRlc3RpbmF0aW9uSWRzID0gcmVzcG9uc2UubG9jYXRpb25EZXN0aW5hdGlvbklkcw0KICAgICAgICBkYXRhLmxpbmVEZXBhcnR1cmVJZHMgPSByZXNwb25zZS5saW5lRGVwYXJ0dXJlSWRzDQogICAgICAgIGRhdGEubGluZURlc3RpbmF0aW9uSWRzID0gcmVzcG9uc2UubGluZURlc3RpbmF0aW9uSWRzDQogICAgICB9KQ0KICAgICAgLy8g5YWs5Y+45omA5bGeDQogICAgICBpZiAoZGF0YS5iZWxvbmdUbyA9PSBudWxsKSB7DQogICAgICAgIGlmICh0aGlzLmJlbG9uZ0xpc3QgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgZm9yIChjb25zdCBhIG9mIHRoaXMuYmVsb25nTGlzdCkgew0KICAgICAgICAgICAgaWYgKGEuY2hpbGRyZW4gIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgIGZvciAoY29uc3QgYiBvZiBhLmNoaWxkcmVuKSB7DQogICAgICAgICAgICAgICAgaWYgKGIuY2hpbGRyZW4gIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGMgb2YgYi5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgICAgICBpZiAoYy5zdGFmZklkID09IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIuc2lkKSB7DQogICAgICAgICAgICAgICAgICAgICAgZGF0YS5iZWxvbmdUbyA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIuc2lkDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHF1ZXJ5U2FtZShkYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIGxldCByZXMgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgICBpZiAocmVzICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICBsZXQgbmV3Um9sZVR5cGUgPSB0aGlzLnJvbGVSaWNoID8gIueRnuaXl+WIhuaUryIgOiB0aGlzLnJvbGVDbGllbnQgPyAi5a6i5oi3IiA6IHRoaXMucm9sZVN1cHBsaWVyID8gIuS+m+W6lOWVhiIgOiB0aGlzLnJvbGVTdXBwb3J0ID8gIuaUr+aMgSIgOiAiIg0KICAgICAgICAgICAgICBsZXQgb2xkUm9sZVR5cGUgPSByZXMucm9sZVJpY2ggPT0gMSA/ICLnkZ7ml5fliIbmlK8iIDogcmVzLnJvbGVDbGllbnQgPT0gMSA/ICLlrqLmiLciIDogcmVzLnJvbGVTdXBwbGllciA9PSAxID8gIuS+m+W6lOWVhiIgOiByZXMucm9sZVN1cHBvcnQgPT0gMSA/ICLmlK/mjIEiIDogIiINCiAgICAgICAgICAgICAgdGhpcy4kY29uZmlybShyZXMuZGVsZXRlU3RhdHVzID09IDAgPyAi5q2k5YWs5Y+45bey5a2Y5Zyo77yM6KeS6Imy5Li6IiArIG9sZFJvbGVUeXBlICsgIu+8jOaWsOWinuinkuiJsuS4uiIgKyBuZXdSb2xlVHlwZSArICIs5piv5ZCm56Gu6K6k5paw5aKeIiA6ICLlrZjlnKjph43lpI3mlbDmja7vvIzkvYblt7LliKDpmaTvvIzmmK/lkKbph43mlrDor7vlj5YiLCAi5o+Q56S6Iiwgew0KICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgICAgICAgICAgY3VzdG9tQ2xhc3M6ICJtb2RhbC1jb25maXJtIg0KICAgICAgICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgICAgICByZXMuZGVsZXRlU3RhdHVzID0gMA0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybSA9IHJlcw0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5yb2xlVHlwZUlkID0gdGhpcy5yb2xlVHlwZUlkDQogICAgICAgICAgICAgICAgaWYgKHRoaXMuYmVsb25nTGlzdCAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgYSBvZiB0aGlzLmJlbG9uZ0xpc3QpIHsNCiAgICAgICAgICAgICAgICAgICAgaWYgKGEuY2hpbGRyZW4gIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBiIG9mIGEuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChiLmNoaWxkcmVuICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGMgb2YgYi5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjLnN0YWZmSWQgPT0gcmVzcG9uc2UuZGF0YS5iZWxvbmdUbykgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5iZWxvbmdUbyA9IGMuZGVwdElkDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjLnN0YWZmSWQgPT0gcmVzcG9uc2UuZGF0YS5mb2xsb3dVcCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5mb2xsb3dVcCA9IGMuZGVwdElkDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5yb2xlSWRzID0gcmVzcG9uc2Uucm9sZUlkcw0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5zZXJ2aWNlVHlwZUlkcyA9IHJlc3BvbnNlLnNlcnZpY2VUeXBlSWRzDQogICAgICAgICAgICAgICAgdGhpcy5mb3JtLmNhcmdvVHlwZUlkcyA9IHJlc3BvbnNlLmNhcmdvVHlwZUlkcw0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5saW5lRGVwYXJ0dXJlSWRzID0gcmVzcG9uc2UubGluZURlcGFydHVyZUlkcw0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5sb2NhdGlvbkRlcGFydHVyZUlkcyA9IHJlc3BvbnNlLmxvY2F0aW9uRGVwYXJ0dXJlSWRzDQogICAgICAgICAgICAgICAgdGhpcy5mb3JtLmxpbmVEZXN0aW5hdGlvbklkcyA9IHJlc3BvbnNlLmxpbmVEZXN0aW5hdGlvbklkcw0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5sb2NhdGlvbkRlc3RpbmF0aW9uSWRzID0gcmVzcG9uc2UubG9jYXRpb25EZXN0aW5hdGlvbklkcw0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5jYXJyaWVySWRzID0gcmVzcG9uc2UuY2Fycmllcklkcw0KICAgICAgICAgICAgICAgIC8vIHRoaXMuZm9ybS5vcmdhbml6YXRpb25JZHMgPSByZXNwb25zZS5jb21wYW55T3JnYW5pemF0aW9uSWRzDQogICAgICAgICAgICAgICAgdGhpcy5mb3JtLm9yZ2FuaXphdGlvbklkcyA9IHJlc3BvbnNlLm9yZ2FuaXphdGlvbklkcw0KICAgICAgICAgICAgICAgIHRoaXMubG9jYXRpb25PcHRpb25zID0gcmVzcG9uc2UubG9jYXRpb25PcHRpb25zDQogICAgICAgICAgICAgICAgdGhpcy5vcGVuQ29tcGFueSA9IHRydWUNCiAgICAgICAgICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueWFrOWPuOS/oeaBryINCiAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDplJnor6/lpITnkIYs5by55Ye65o+Q56S65ZCO54K55Ye756Gu5a6a5Y+R6YCB6K+35rGC5pu05paw5YWs5Y+45L+h5oGvDQogICAgICAgICAgICBpZiAocmVzcG9uc2UubXNnLnRvU3RyaW5nKCkuaW5kZXhPZigiRXJyb3IiKSA+IC0xKSB7DQogICAgICAgICAgICAgIHRoaXMuJGNvbmZpcm0ocmVzcG9uc2UubXNnLCAi5o+Q56S6Iiwgew0KICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgICAgICAgICAgY3VzdG9tQ2xhc3M6ICJtb2RhbC1jb25maXJtIg0KICAgICAgICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHsNCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2VPd25lcjogMSwNCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2VUeXBlOiAzLA0KICAgICAgICAgICAgICAgICAgbWVzc2FnZUZyb206IG51bGwsDQogICAgICAgICAgICAgICAgICBtZXNzYWdlVGl0bGU6IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIubmFtZS5zcGxpdCgiICIpWzFdICsgIuivt+axguabtOaWsOWFrOWPuCIsDQogICAgICAgICAgICAgICAgICBtZXNzYWdlQ29udGVudDogcmVzcG9uc2UubXNnDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGFkZE1lc3NhZ2UoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLA0KICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+R6YCB6K+35rGCISINCiAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLy8g5paw5aKe6aqM6K+B6YCa6L+HKOmAmui/h+Wwhuivt+axguaVsOaNruS4reeahGRlbGV0ZVN0YXR1c+iuvue9ruacqjApDQogICAgICAgICAgICBpZiAocmVzcG9uc2UubXNnLnRvU3RyaW5nKCkuaW5kZXhPZigiU3VjY2VzcyIpID4gLTEpIHsNCiAgICAgICAgICAgICAgdGhpcy4kY29uZmlybSgi5LiN5a2Y5Zyo6YeN5aSN55qE5YWs5Y+4566A56ew77yM5piv5ZCm56Gu5a6a5paw5aKe5a6i5oi377yfIiwgIuaPkOekuiIsIHsNCiAgICAgICAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICAgICAgICAgIGN1c3RvbUNsYXNzOiAibW9kYWwtY29uZmlybSINCiAgICAgICAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgICAgICAgZGF0YS5kZWxldGVTdGF0dXMgPSAwDQogICAgICAgICAgICAgICAgLy8g55yf5q2j5byA5aeL5paw5aKeDQogICAgICAgICAgICAgICAgcXVlcnlTYW1lKGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmt7vliqDmiJDlip8iKQ0KICAgICAgICAgICAgICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5yb2xlVHlwZUlkID0gdGhpcy5yb2xlVHlwZUlkDQogICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmJlbG9uZ0xpc3QgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBhIG9mIHRoaXMuYmVsb25nTGlzdCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGEuY2hpbGRyZW4gIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgYiBvZiBhLmNoaWxkcmVuKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGIuY2hpbGRyZW4gIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGMgb2YgYi5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoYy5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEuYmVsb25nVG8pIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmJlbG9uZ1RvID0gYy5kZXB0SWQNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoYy5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEuZm9sbG93VXApIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmZvbGxvd1VwID0gYy5kZXB0SWQNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB0aGlzLmZvcm0ucm9sZUlkcyA9IHJlc3BvbnNlLnJvbGVJZHMNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLnNlcnZpY2VUeXBlSWRzID0gcmVzcG9uc2Uuc2VydmljZVR5cGVJZHMNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLmNhcmdvVHlwZUlkcyA9IHJlc3BvbnNlLmNhcmdvVHlwZUlkcw0KICAgICAgICAgICAgICAgICAgICB0aGlzLmZvcm0ubGluZURlcGFydHVyZUlkcyA9IHJlc3BvbnNlLmxpbmVEZXBhcnR1cmVJZHMNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLmxvY2F0aW9uRGVwYXJ0dXJlSWRzID0gcmVzcG9uc2UubG9jYXRpb25EZXBhcnR1cmVJZHMNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLmxpbmVEZXN0aW5hdGlvbklkcyA9IHJlc3BvbnNlLmxpbmVEZXN0aW5hdGlvbklkcw0KICAgICAgICAgICAgICAgICAgICB0aGlzLmZvcm0ubG9jYXRpb25EZXN0aW5hdGlvbklkcyA9IHJlc3BvbnNlLmxvY2F0aW9uRGVzdGluYXRpb25JZHMNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLmNhcnJpZXJJZHMgPSByZXNwb25zZS5jYXJyaWVySWRzDQogICAgICAgICAgICAgICAgICAgIC8vIHRoaXMuZm9ybS5vcmdhbml6YXRpb25JZHMgPSByZXNwb25zZS5jb21wYW55T3JnYW5pemF0aW9uSWRzDQogICAgICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5vcmdhbml6YXRpb25JZHMgPSByZXNwb25zZS5vcmdhbml6YXRpb25JZHMNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5sb2NhdGlvbk9wdGlvbnMgPSByZXNwb25zZS5sb2NhdGlvbk9wdGlvbnMNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5vcGVuQ29tcGFueSA9IHRydWUNCiAgICAgICAgICAgICAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnlhazlj7jkv6Hmga8iDQogICAgICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOafpeivouWFrOWPuOWIl+ihqCAqLw0KICAgIGFzeW5jIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICBhd2FpdCBsaXN0Q29tcGFueSh7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMsDQogICAgICAgIHBlcm1pc3Npb25MZXZlbDogdGhpcy4kc3RvcmUuc3RhdGUudXNlci5wZXJtaXNzaW9uTGV2ZWxMaXN0LkMNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmNvbXBhbnlMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICBpZiAoIWlzTmFOKHJlc3BvbnNlLnRvdGFsKSkgew0KICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbA0KICAgICAgICB9DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgc3RhZmZOb3JtYWxpemVyKG5vZGUpIHsNCiAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmICFub2RlLmNoaWxkcmVuLmxlbmd0aCkgew0KICAgICAgICBkZWxldGUgbm9kZS5jaGlsZHJlbg0KICAgICAgfQ0KICAgICAgbGV0IGwNCiAgICAgIGlmIChub2RlLnN0YWZmKSB7DQogICAgICAgIGlmIChub2RlLnN0YWZmLnN0YWZmRmFtaWx5TG9jYWxOYW1lID09IG51bGwgJiYgbm9kZS5zdGFmZi5zdGFmZkdpdmluZ0xvY2FsTmFtZSA9PSBudWxsKSB7DQogICAgICAgICAgaWYgKG5vZGUucm9sZS5yb2xlTG9jYWxOYW1lICE9IG51bGwpIHsNCiAgICAgICAgICAgIGwgPSBub2RlLnJvbGUucm9sZUxvY2FsTmFtZSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMobm9kZS5yb2xlLnJvbGVMb2NhbE5hbWUpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGwgPSBub2RlLmRlcHQuZGVwdExvY2FsTmFtZSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMobm9kZS5kZXB0LmRlcHRMb2NhbE5hbWUpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGwgPSBub2RlLnN0YWZmLnN0YWZmQ29kZSArICIgIiArIG5vZGUuc3RhZmYuc3RhZmZGYW1pbHlMb2NhbE5hbWUgKyBub2RlLnN0YWZmLnN0YWZmR2l2aW5nTG9jYWxOYW1lICsgIiAiICsgbm9kZS5zdGFmZi5zdGFmZkdpdmluZ0VuTmFtZSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMobm9kZS5zdGFmZi5zdGFmZkZhbWlseUxvY2FsTmFtZSArIG5vZGUuc3RhZmYuc3RhZmZHaXZpbmdMb2NhbE5hbWUpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGlmIChub2RlLnJvbGVJZCkgew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGlkOiBub2RlLnJvbGVJZCwNCiAgICAgICAgICBsYWJlbDogbCwNCiAgICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbiwNCiAgICAgICAgICBpc0Rpc2FibGVkOiBub2RlLnN0YWZmSWQgPT0gbnVsbCAmJiBub2RlLmNoaWxkcmVuID09IHVuZGVmaW5lZA0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGlkOiBub2RlLmRlcHRJZCwNCiAgICAgICAgICBsYWJlbDogbCwNCiAgICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbiwNCiAgICAgICAgICBpc0Rpc2FibGVkOiBub2RlLnN0YWZmSWQgPT0gbnVsbCAmJiBub2RlLmNoaWxkcmVuID09IHVuZGVmaW5lZA0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICB2YWxpZGF0ZUNvbXBhbnlTaG9ydE5hbWUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7DQoNCiAgICAgIC8vIOajgOafpSB2YWx1ZSDmmK/lkKbkuLrnqbrmiJbpnZ7lrZfnrKbkuLINCiAgICAgIGlmICh2YWx1ZSB8fCB0eXBlb2YgdmFsdWUgPT09ICJzdHJpbmciKSB7DQogICAgICAgIC8vIOajgOafpeaYr+WQpuWMheWQq+WkmuS4quS4reaoque6vw0KICAgICAgICBjb25zdCBoeXBoZW5Db3VudCA9IHZhbHVlLnNwbGl0KCItIikubGVuZ3RoIC0gMSAvLyDpgJrov4cgc3BsaXQg5YiG5Ymy5p2l57uf6K6h5Lit5qiq57q/5pWw6YePDQogICAgICAgIGlmIChoeXBoZW5Db3VudCA+IDEpIHsNCiAgICAgICAgICByZXR1cm4gY2FsbGJhY2sobmV3IEVycm9yKCLlj6rog73ljIXlkKvkuIDkuKrkuK3mqKrnur8iKSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLy8g6aqM6K+B6YCa6L+HDQogICAgICBjYWxsYmFjaygpDQogICAgfSwNCiAgICBjYXJyaWVyTm9ybWFsaXplcihub2RlKSB7DQogICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiAhbm9kZS5jaGlsZHJlbi5sZW5ndGgpIHsNCiAgICAgICAgZGVsZXRlIG5vZGUuY2hpbGRyZW4NCiAgICAgIH0NCiAgICAgIGxldCBsDQogICAgICBpZiAoIW5vZGUuY2FycmllciB8fCAobm9kZS5jYXJyaWVyLmNhcnJpZXJMb2NhbE5hbWUgPT0gbnVsbCAmJiBub2RlLmNhcnJpZXIuY2FycmllckVuTmFtZSA9PSBudWxsKSkgew0KICAgICAgICBsID0gbm9kZS5zZXJ2aWNlTG9jYWxOYW1lICsgIiAiICsgbm9kZS5zZXJ2aWNlRW5OYW1lICsgIiwiICsgcGlueWluLmdldEZ1bGxDaGFycyhub2RlLnNlcnZpY2VMb2NhbE5hbWUpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBsID0gKG5vZGUuY2Fycmllci5jYXJyaWVySW50bENvZGUgIT0gbnVsbCA/IG5vZGUuY2Fycmllci5jYXJyaWVySW50bENvZGUgOiAiIikgKyAiICIgKyAobm9kZS5jYXJyaWVyLmNhcnJpZXJFbk5hbWUgIT0gbnVsbCA/IG5vZGUuY2Fycmllci5jYXJyaWVyRW5OYW1lIDogIiIpICsgIiAiICsgKG5vZGUuY2Fycmllci5jYXJyaWVyTG9jYWxOYW1lICE9IG51bGwgPyBub2RlLmNhcnJpZXIuY2FycmllckxvY2FsTmFtZSA6ICIiKSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMoKG5vZGUuY2Fycmllci5jYXJyaWVyTG9jYWxOYW1lICE9IG51bGwgPyBub2RlLmNhcnJpZXIuY2FycmllckxvY2FsTmFtZSA6ICIiKSkNCiAgICAgIH0NCiAgICAgIHJldHVybiB7DQogICAgICAgIGlkOiBub2RlLnNlcnZpY2VUeXBlSWQsDQogICAgICAgIGxhYmVsOiBsLA0KICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbg0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuQ29tcGFueSA9IGZhbHNlDQogICAgICB0aGlzLm9wZW5BY2NvdW50ID0gZmFsc2UNCiAgICAgIHRoaXMub3BlblN0YWZmID0gZmFsc2UNCiAgICAgIHRoaXMub3BlbkNvbW11bmljYXRpb24gPSBmYWxzZQ0KICAgICAgdGhpcy5vcGVuQWdyZWVtZW50ID0gZmFsc2UNCiAgICAgIHRoaXMub3BlbkJsYWNrTGlzdCA9IGZhbHNlDQogICAgICB0aGlzLmFkZCA9IGZhbHNlDQogICAgICB0aGlzLm1lcmdlID0gZmFsc2UNCiAgICAgIHRoaXMuZWRpdCA9IGZhbHNlDQogICAgICB0aGlzLnJlc2V0KCkNCiAgICAgIHRoaXMuYmVsb25nVG8gPSBudWxsDQogICAgICB0aGlzLmZvbGxvd1VwID0gbnVsbA0KICAgICAgLy8gdGhpcy5nZXRMaXN0KCkNCiAgICB9LA0KICAgIGFjY291bnRDYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW5BY2NvdW50ID0gZmFsc2UNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5iZWxvbmdUbyA9IG51bGwNCiAgICAgIHRoaXMuZm9sbG93VXAgPSBudWxsDQogICAgICB0aGlzLmNhcnJpZXJJZHMgPSBbXQ0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBiZWxvbmdUbzogbnVsbCwNCiAgICAgICAgZm9sbG93VXA6IG51bGwsDQogICAgICAgIGNhcnJpZXJJZHM6IG51bGwsDQogICAgICAgIGxvY2F0aW9uRGV0YWlsOiBudWxsLA0KICAgICAgICBjb21wYW55SWQ6IG51bGwsDQogICAgICAgIGNvbXBhbnlJbnRsQ29kZTogbnVsbCwNCiAgICAgICAgY29tcGFueVNob3J0TmFtZTogbnVsbCwNCiAgICAgICAgY29tcGFueUVuU2hvcnROYW1lOiBudWxsLA0KICAgICAgICBjb21wYW55TG9jYWxOYW1lOiBudWxsLA0KICAgICAgICBjb21wYW55RW5OYW1lOiBudWxsLA0KICAgICAgICBjb21wYW55VGF4Q29kZTogbnVsbCwNCiAgICAgICAgcm9sZUlkczogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZHM6IG51bGwsDQogICAgICAgIGNhcmdvVHlwZUlkczogbnVsbCwNCiAgICAgICAgbG9jYXRpb25EZXBhcnR1cmVJZHM6IG51bGwsDQogICAgICAgIGxpbmVEZXBhcnR1cmVJZHM6IG51bGwsDQogICAgICAgIGxvY2F0aW9uRGVzdGluYXRpb25JZHM6IG51bGwsDQogICAgICAgIGxpbmVEZXN0aW5hdGlvbklkczogbnVsbCwNCiAgICAgICAgb3JnYW5pemF0aW9uSWRzOiBudWxsLA0KICAgICAgICBjb21wYW55UG9ydElkczogbnVsbCwNCiAgICAgICAgcm9sZVR5cGVJZDogdGhpcy5yb2xlVHlwZUlkLA0KICAgICAgICByb2xlUmljaDogdGhpcy5yb2xlUmljaCA/IHRoaXMucm9sZVJpY2ggOiBudWxsLA0KICAgICAgICByb2xlQ2xpZW50OiB0aGlzLnJvbGVDbGllbnQgPyB0aGlzLnJvbGVDbGllbnQgOiBudWxsLA0KICAgICAgICByb2xlU3VwcGxpZXI6IHRoaXMucm9sZVN1cHBsaWVyID8gdGhpcy5yb2xlU3VwcGxpZXIgOiBudWxsLA0KICAgICAgICByb2xlU3VwcG9ydDogdGhpcy5yb2xlU3VwcG9ydCA/IHRoaXMucm9sZVN1cHBvcnQgOiBudWxsLA0KICAgICAgICBsb2NhdGlvbklkOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZDogMCwNCiAgICAgICAgc2FsZXNDb25maXJtZWRJZDogbnVsbCwNCiAgICAgICAgc2FsZXNDb25maXJtZWROYW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZERhdGU6IG51bGwsDQogICAgICAgIHBzYUNvbmZpcm1lZDogMCwNCiAgICAgICAgcHNhQ29uZmlybWVkSWQ6IG51bGwsDQogICAgICAgIHBzYUNvbmZpcm1lZE5hbWU6IG51bGwsDQogICAgICAgIHBzYUNvbmZpcm1lZERhdGU6IG51bGwsDQogICAgICAgIGFjY0NvbmZpcm1lZDogMCwNCiAgICAgICAgYWNjQ29uZmlybWVkSWQ6IG51bGwsDQogICAgICAgIGFjY0NvbmZpcm1lZE5hbWU6IG51bGwsDQogICAgICAgIGFjY0NvbmZpcm1lZERhdGU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkOiAwLA0KICAgICAgICBvcENvbmZpcm1lZElkOiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZE5hbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkRGF0ZTogbnVsbCwNCiAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICByc1BheW1lbnRUaXRsZXM6IFtdDQogICAgICB9DQogICAgICB0aGlzLmNhcnJpZXJJZHMgPSBbXQ0KICAgICAgdGhpcy5jb21wYW55Um93ID0gbnVsbA0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKQ0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMQ0KICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpDQogICAgICB0aGlzLnF1ZXJ5QlN0YWZmSWQgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmxvY2F0aW9uSWQgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNlcnZpY2VUeXBlSWRzID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jYXJnb1R5cGVJZHMgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmxvY2F0aW9uRGVwYXJ0dXJlSWRzID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5saW5lRGVwYXJ0dXJlSWRzID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5sb2NhdGlvbkRlc3RpbmF0aW9uSWRzID0gbnVsbA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5saW5lRGVzdGluYXRpb25JZHMgPSBudWxsDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm9yZ2FuaXphdGlvbklkcyA9IG51bGwNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5jb21wYW55SWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgICB0aGlzLnNlbGVjdFR3byA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMg0KICAgICAgaWYgKHNlbGVjdGlvbi5sZW5ndGggPT0gMSkgew0KICAgICAgICB0aGlzLnNldENvbXBhbnlJbmZvKHNlbGVjdGlvblswXSkNCiAgICAgIH0NCiAgICAgIGlmIChzZWxlY3Rpb24ubGVuZ3RoID09IDIpIHsNCiAgICAgICAgdGhpcy5tZXJnZUxpc3QgPSBzZWxlY3Rpb24NCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCkNCiAgICAgIHRoaXMuZWRpdCA9IGZhbHNlDQogICAgICB0aGlzLmZvcm0uYmVsb25nVG8gPSBudWxsDQogICAgICB0aGlzLm9wZW5Db21wYW55ID0gdHJ1ZQ0KICAgICAgdGhpcy50aXRsZSA9ICLmlrDlop7lhazlj7jkv6Hmga8iDQogICAgICB0aGlzLmZvcm0uc2VydmljZVR5cGVJZHMgPSBbXQ0KICAgICAgdGhpcy50ZW1DYXJyaWVyTGlzdCA9IHRoaXMuY2Fycmllckxpc3QNCiAgICAgIGlmICh0aGlzLnRlbUNhcnJpZXJMaXN0ICE9IHVuZGVmaW5lZCAmJiB0aGlzLmZvcm0uc2VydmljZVR5cGVJZHMgIT0gbnVsbCkgew0KICAgICAgICBmb3IgKGNvbnN0IGEgb2YgdGhpcy50ZW1DYXJyaWVyTGlzdCkgew0KICAgICAgICAgIC8vIHRoaXMuZm9ybS5zZXJ2aWNlVHlwZUlkcy5wdXNoKGEuc2VydmljZVR5cGVJZCkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgdGhpcy5hZGQgPSB0cnVlDQogICAgICBpZiAodGhpcy5iZWxvbmdMaXN0ICE9IHVuZGVmaW5lZCkgew0KICAgICAgICBmb3IgKGNvbnN0IGEgb2YgdGhpcy5iZWxvbmdMaXN0KSB7DQogICAgICAgICAgaWYgKGEuY2hpbGRyZW4gIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICBmb3IgKGNvbnN0IGIgb2YgYS5jaGlsZHJlbikgew0KICAgICAgICAgICAgICBpZiAoYi5jaGlsZHJlbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGMgb2YgYi5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgICAgaWYgKGMuc3RhZmZJZCA9PSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnNpZCkgew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmJlbG9uZ1RvID0gYy5kZXB0SWQNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC8vIOW4geenjem7mOiupOS4uuS6uuawkeW4gVJNQg0KICAgICAgdGhpcy5mb3JtLmFncmVlbWVudEN1cnJlbmN5Q29kZSA9ICJSTUIiDQogICAgICB0aGlzLnNob3dDb25maXJtID0gZmFsc2UNCiAgICB9LA0KICAgIGdldFJldHVybihyb3cpIHsNCiAgICAgIGlmIChyb3cua2V5ID09ICJjb250YWN0b3IiKSB7DQogICAgICAgIHRoaXMuc2V0Q29tcGFueUluZm8ocm93LnZhbHVlKQ0KICAgICAgICBnZXRDb25uZWN0KHJvdy52YWx1ZS5jb21wYW55SWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMuc3RhZmZMaXN0ID0gcmVzcG9uc2Uuc3RhZmZMaXN0DQogICAgICAgICAgdGhpcy5vcGVuU3RhZmYgPSB0cnVlDQogICAgICAgIH0pDQogICAgICB9DQogICAgICBpZiAocm93LmtleSA9PSAiY29tbXVuaWNhdGlvbiIpIHsNCiAgICAgICAgdGhpcy5zZXRDb21wYW55SW5mbyhyb3cudmFsdWUpDQogICAgICAgIGxpc3RDb21tdW5pY2F0aW9uKHtzcWRDb21wYW55SWQ6IHJvdy52YWx1ZS5jb21wYW55SWR9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLmNvbW11bmljYXRpb25MaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICAgIHRoaXMuY3RvdGxlID0gcmVzcG9uc2UudG90bGUNCiAgICAgICAgICB0aGlzLm9wZW5Db21tdW5pY2F0aW9uID0gdHJ1ZQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgaWYgKHJvdy5rZXkgPT0gImFncmVlbWVudCIpIHsNCiAgICAgICAgdGhpcy5zZXRDb21wYW55SW5mbyhyb3cudmFsdWUpDQogICAgICAgIGxpc3RBZ3JlZW1lbnRyZWNvcmQoe3NxZENvbXBhbnlJZDogcm93LnZhbHVlLmNvbXBhbnlJZH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMuYWdyZWVtZW50TGlzdCA9IHJlc3BvbnNlLnJvd3MNCiAgICAgICAgICB0aGlzLmF0b3RsZSA9IHJlc3BvbnNlLnRvdGxlDQogICAgICAgICAgdGhpcy5vcGVuQWdyZWVtZW50ID0gdHJ1ZQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgaWYgKHJvdy5rZXkgPT0gImFjY291bnQiKSB7DQogICAgICAgIHRoaXMuc2V0Q29tcGFueUluZm8ocm93LnZhbHVlKQ0KICAgICAgICBnZXRCYW5rKHJvdy52YWx1ZS5jb21wYW55SWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMuYWNjb3VudExpc3QgPSByZXNwb25zZS5hY2NvdW50TGlzdA0KICAgICAgICAgIHRoaXMub3BlbkFjY291bnQgPSB0cnVlDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICAvLyDorr7nva7lrqLmiLfkv6Hmga8NCiAgICBzZXRDb21wYW55SW5mbyhyb3cpIHsNCiAgICAgIHRoaXMuY29tcGFueUluZm8gPSB7DQogICAgICAgIGNvbXBhbnlJZDogcm93LmNvbXBhbnlJZCAhPSBudWxsID8gcm93LmNvbXBhbnlJZCA6ICIiLA0KICAgICAgICBjb21wYW55VGF4Q29kZTogcm93LmNvbXBhbnlUYXhDb2RlICE9IG51bGwgPyByb3cuY29tcGFueVRheENvZGUgOiAiIiwNCiAgICAgICAgY29tcGFueVNob3J0TmFtZTogcm93LmNvbXBhbnlTaG9ydE5hbWUgIT0gbnVsbCA/IHJvdy5jb21wYW55U2hvcnROYW1lIDogIiIsDQogICAgICAgIGNvbXBhbnlFblNob3J0TmFtZTogcm93LmNvbXBhbnlFblNob3J0TmFtZSAhPSBudWxsID8gcm93LmNvbXBhbnlFblNob3J0TmFtZSA6ICIiLA0KICAgICAgICBjb21wYW55TG9jYWxOYW1lOiByb3cuY29tcGFueUxvY2FsTmFtZSAhPSBudWxsID8gcm93LmNvbXBhbnlMb2NhbE5hbWUgOiAiIiwNCiAgICAgICAgY29tcGFueUVuTmFtZTogcm93LmNvbXBhbnlFbk5hbWUgIT0gbnVsbCA/IHJvdy5jb21wYW55RW5OYW1lIDogIiIsDQogICAgICAgIGNvbXBhbnlMb2NhdGlvbjogcm93LmxvY2F0aW9uSWQgIT0gbnVsbCA/IHJvdy5sb2NhdGlvbklkIDogIiIsDQogICAgICAgIGNvbXBhbnlJbnRsQ29kZTogcm93LmNvbXBhbnlJbnRsQ29kZSAhPSBudWxsID8gcm93LmNvbXBhbnlJbnRsQ29kZSA6ICIiLA0KICAgICAgICBtYWluU3RhZmZJZDogcm93LnN0YWZmICE9IG51bGwgPyByb3cuc3RhZmYuc3RhZmZJZCA6ICIiDQogICAgICB9DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpDQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICB0aGlzLmVkaXQgPSB0cnVlDQogICAgICB0aGlzLmNvbXBhbnlSb3cgPSByb3cNCiAgICAgIHRoaXMuYWRkID0gYXV0aC5oYXNQZXJtaSgic3lzdGVtOmNsaWVudDpkaXN0cmlidXRlIikNCiAgICAgIGNvbnN0IGNvbXBhbnlJZCA9IHJvdy5jb21wYW55SWQgfHwgdGhpcy5pZHMNCiAgICAgIHRoaXMuc2hvd0NvbmZpcm0gPSB0cnVlDQogICAgICBnZXRDb21wYW55KGNvbXBhbnlJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgaWYgKHRoaXMuYmVsb25nTGlzdCAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICBmb3IgKGNvbnN0IGEgb2YgdGhpcy5iZWxvbmdMaXN0KSB7DQogICAgICAgICAgICBpZiAoYS5jaGlsZHJlbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBiIG9mIGEuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgICBpZiAoYi5jaGlsZHJlbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgYyBvZiBiLmNoaWxkcmVuKSB7DQogICAgICAgICAgICAgICAgICAgIGlmIChjLnN0YWZmSWQgPT0gcmVzcG9uc2UuZGF0YS5iZWxvbmdUbykgew0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuYmVsb25nVG8gPSBjLmRlcHRJZA0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIGlmIChjLnN0YWZmSWQgPT0gcmVzcG9uc2UuZGF0YS5mb2xsb3dVcCkgew0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZm9sbG93VXAgPSBjLmRlcHRJZA0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuZm9ybS5yb2xlSWRzID0gcmVzcG9uc2Uucm9sZUlkcw0KICAgICAgICB0aGlzLmZvcm0uc2VydmljZVR5cGVJZHMgPSByZXNwb25zZS5zZXJ2aWNlVHlwZUlkcw0KICAgICAgICB0aGlzLmZvcm0uY2FyZ29UeXBlSWRzID0gcmVzcG9uc2UuY2FyZ29UeXBlSWRzDQogICAgICAgIHRoaXMuZm9ybS5saW5lRGVwYXJ0dXJlSWRzID0gcmVzcG9uc2UubGluZURlcGFydHVyZUlkcw0KICAgICAgICB0aGlzLmZvcm0ubG9jYXRpb25EZXBhcnR1cmVJZHMgPSByZXNwb25zZS5sb2NhdGlvbkRlcGFydHVyZUlkcw0KICAgICAgICB0aGlzLmZvcm0ubGluZURlc3RpbmF0aW9uSWRzID0gcmVzcG9uc2UubGluZURlc3RpbmF0aW9uSWRzDQogICAgICAgIHRoaXMuZm9ybS5sb2NhdGlvbkRlc3RpbmF0aW9uSWRzID0gcmVzcG9uc2UubG9jYXRpb25EZXN0aW5hdGlvbklkcw0KICAgICAgICB0aGlzLmZvcm0uY2FycmllcklkcyA9IHJlc3BvbnNlLmNhcnJpZXJJZHMNCiAgICAgICAgLy8gdGhpcy5mb3JtLm9yZ2FuaXphdGlvbklkcyA9IHJlc3BvbnNlLmNvbXBhbnlPcmdhbml6YXRpb25JZHMNCiAgICAgICAgdGhpcy5mb3JtLm9yZ2FuaXphdGlvbklkcyA9IHJlc3BvbnNlLm9yZ2FuaXphdGlvbklkcw0KICAgICAgICB0aGlzLmxvY2F0aW9uT3B0aW9ucyA9IHJlc3BvbnNlLmxvY2F0aW9uT3B0aW9ucw0KICAgICAgICB0aGlzLm9wZW5Db21wYW55ID0gdHJ1ZQ0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueWFrOWPuOS/oeaBryINCiAgICAgICAgLy8gdGhpcy5mb3JtLnJzUGF5bWVudFRpdGxlcyA9IHJlc3BvbnNlLmRhdGEucnNQYXltZW50VGl0bGUgPyByZXNwb25zZS5kYXRhLnJzUGF5bWVudFRpdGxlLnNwbGl0KCcsJykgOiBbXQ0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KDQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmFncmVlbWVudFN0YXJ0RGF0ZSAhPT0gbnVsbCAmJiByZXNwb25zZS5kYXRhLmFncmVlbWVudEVuZERhdGUgIT09IG51bGwpIHsNCiAgICAgICAgICB0aGlzLmZvcm0uYWdyZWVtZW50RGF0ZVJhbmdlID0gW10NCiAgICAgICAgICB0aGlzLmZvcm0uYWdyZWVtZW50RGF0ZVJhbmdlLnB1c2gocmVzcG9uc2UuZGF0YS5hZ3JlZW1lbnRTdGFydERhdGUpDQogICAgICAgICAgdGhpcy5mb3JtLmFncmVlbWVudERhdGVSYW5nZS5wdXNoKHJlc3BvbnNlLmRhdGEuYWdyZWVtZW50RW5kRGF0ZSkNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IGZvcm1hdHRlciA9IG5ldyBJbnRsLk51bWJlckZvcm1hdCgiZW4tVVMiLCB7DQogICAgICAgICAgc3R5bGU6ICJkZWNpbWFsIiwNCiAgICAgICAgICBtaW5pbXVtRnJhY3Rpb25EaWdpdHM6IDIsDQogICAgICAgICAgbWF4aW11bUZyYWN0aW9uRGlnaXRzOiAyDQogICAgICAgIH0pDQogICAgICAgIGlmICgodGhpcy5yb2xlQ2xpZW50ID09IDEgfHwgdGhpcy5yb2xlUmljaCA9PSAxKSAmJiByZXNwb25zZS5kYXRhLnJlY2VpdmVDcmVkaXRMaW1pdCAhPT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5yZWNlaXZlQ3JlZGl0TGltaXQgPSByZXNwb25zZS5kYXRhLnJlY2VpdmVDcmVkaXRMaW1pdC50b0xvY2FsZVN0cmluZygiZW4tVVMiKQ0KICAgICAgICAgIHRoaXMuZm9ybS5yZWNlaXZlQ3JlZGl0TGltaXQgPSB0aGlzLmZvcm0ucmVjZWl2ZUNyZWRpdExpbWl0ID8gdGhpcy5mb3JtLnJlY2VpdmVDcmVkaXRMaW1pdC5yZXBsYWNlKC8sL2csICIiKSA6IDANCiAgICAgICAgICB0aGlzLmZvcm0ucmVjZWl2ZUNyZWRpdExpbWl0ID0gZm9ybWF0dGVyLmZvcm1hdCh0aGlzLmZvcm0ucmVjZWl2ZUNyZWRpdExpbWl0KQ0KICAgICAgICB9DQogICAgICAgIGlmICgodGhpcy5yb2xlU3VwcGxpZXIgPT0gMSB8fCB0aGlzLnJvbGVTdXBwb3J0ID09IDEpICYmIHJlc3BvbnNlLmRhdGEucGF5Q3JlZGl0TGltaXQgIT09IG51bGwpIHsNCiAgICAgICAgICB0aGlzLmZvcm0ucGF5Q3JlZGl0TGltaXQgPSByZXNwb25zZS5kYXRhLnBheUNyZWRpdExpbWl0LnRvTG9jYWxlU3RyaW5nKCJlbi1VUyIpDQogICAgICAgICAgdGhpcy5mb3JtLnBheUNyZWRpdExpbWl0ID0gdGhpcy5mb3JtLnBheUNyZWRpdExpbWl0ID8gdGhpcy5mb3JtLnBheUNyZWRpdExpbWl0LnJlcGxhY2UoLywvZywgIiIpIDogMA0KICAgICAgICAgIHRoaXMuZm9ybS5wYXlDcmVkaXRMaW1pdCA9IGZvcm1hdHRlci5mb3JtYXQodGhpcy5mb3JtLnBheUNyZWRpdExpbWl0KQ0KICAgICAgICB9DQoNCiAgICAgIH0pDQoNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgLy8g5pS25LuY5qy+5o+P6L+wDQogICAgICAgIGlmICh0aGlzLnJvbGVDbGllbnQgPT0gMSB8fCB0aGlzLnJvbGVSaWNoID09IDEpIHsNCiAgICAgICAgICB0aGlzLmZvcm0uc3FkUmVjZWl2ZVRlcm1zU3VtbWFyeSA9IHRoaXMuZGVzY3JpcHRpb24NCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5yb2xlQ2xpZW50ID09IDEgfHwgdGhpcy5yb2xlUmljaCA9PSAxKSB7DQogICAgICAgICAgdGhpcy5mb3JtLnNxZFBheVRlcm1zU3VtbWFyeSA9IHRoaXMuZGVzY3JpcHRpb24NCiAgICAgICAgfQ0KICAgICAgICAvLyDlhazlj7jnsbvlnosNCiAgICAgICAgdGhpcy5mb3JtLnJvbGVSaWNoID0gdGhpcy5yb2xlUmljaCA/IHRoaXMucm9sZVJpY2ggOiBudWxsDQogICAgICAgIHRoaXMuZm9ybS5yb2xlQ2xpZW50ID0gdGhpcy5yb2xlQ2xpZW50ID8gdGhpcy5yb2xlQ2xpZW50IDogbnVsbA0KICAgICAgICB0aGlzLmZvcm0ucm9sZVN1cHBsaWVyID0gdGhpcy5yb2xlU3VwcGxpZXIgPyB0aGlzLnJvbGVTdXBwbGllciA6IG51bGwNCiAgICAgICAgdGhpcy5mb3JtLnJvbGVTdXBwb3J0ID0gdGhpcy5yb2xlU3VwcG9ydCA/IHRoaXMucm9sZVN1cHBvcnQgOiBudWxsDQogICAgICAgIC8vIOi9rOaNoumineW6puaYvuekug0KICAgICAgICB0aGlzLmZvcm0ucmVjZWl2ZUNyZWRpdExpbWl0ID0gdGhpcy5mb3JtLnJlY2VpdmVDcmVkaXRMaW1pdCA/IFN0cmluZyh0aGlzLmZvcm0ucmVjZWl2ZUNyZWRpdExpbWl0KS5yZXBsYWNlKC8sL2csICIiKSA6IDANCiAgICAgICAgdGhpcy5mb3JtLnBheUNyZWRpdExpbWl0ID0gdGhpcy5mb3JtLnBheUNyZWRpdExpbWl0ID8gU3RyaW5nKHRoaXMuZm9ybS5wYXlDcmVkaXRMaW1pdCkucmVwbGFjZSgvLC9nLCAiIikgOiAwDQogICAgICAgIGlmICh0aGlzLmZvcm0uYWdyZWVtZW50RGF0ZVJhbmdlICYmIHRoaXMuZm9ybS5hZ3JlZW1lbnREYXRlUmFuZ2UubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5hZ3JlZW1lbnRTdGFydERhdGUgPSB0aGlzLmZvcm0uYWdyZWVtZW50RGF0ZVJhbmdlWzBdDQogICAgICAgICAgdGhpcy5mb3JtLmFncmVlbWVudEVuZERhdGUgPSB0aGlzLmZvcm0uYWdyZWVtZW50RGF0ZVJhbmdlWzFdDQogICAgICAgIH0NCiAgICAgICAgLy8g5Yik5pat5pel5pyf5byA5aeL5pe26Ze05piv5ZCm5aSn5LqO57uT5p2f5pe26Ze0DQogICAgICAgIGxldCBzdGFydERhdGUgPSBuZXcgRGF0ZSh0aGlzLmZvcm0uYWdyZWVtZW50U3RhcnREYXRlKQ0KICAgICAgICBsZXQgZW5kRGF0ZSA9IG5ldyBEYXRlKHRoaXMuZm9ybS5hZ3JlZW1lbnRFbmREYXRlKQ0KICAgICAgICBpZiAoc3RhcnREYXRlID4gZW5kRGF0ZSkgew0KICAgICAgICAgIE1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogIuWNj+iuruW8gOWni+aXtumXtOS4jeiDveWkp+S6jue7k+adn+aXtumXtCIsDQogICAgICAgICAgICB0eXBlOiAiZXJyb3IiDQogICAgICAgICAgfSkNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgICAvLyDkv6HnlKjlkajmnJ/opoHkuLrmlbTmlbANCiAgICAgICAgaWYgKHRoaXMuZm9ybS5jcmVkaXRDeWNsZU1vbnRoICE9IG51bGwgJiYgdGhpcy5mb3JtLmNyZWRpdEN5Y2xlTW9udGggJSAxICE9IDApIHsNCiAgICAgICAgICBNZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLkv6HnlKjlkajmnJ/lv4XpobvkuLrmlbTmlbAiLA0KICAgICAgICAgICAgdHlwZTogImVycm9yIg0KICAgICAgICAgIH0pDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5jb21wYW55SWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlQ29tcGFueSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKQ0KICAgICAgICAgICAgICB0aGlzLm9wZW5Db21wYW55ID0gZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLnJlc2V0KCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCLmnKrmn6Xph43vvIzlhYjlr7nnroDnp7Dmn6Xph43lkKciKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICByb3cucm9sZVJpY2ggPSB0aGlzLnJvbGVSaWNoID8gdGhpcy5yb2xlUmljaCA6IG51bGwNCiAgICAgIHJvdy5yb2xlQ2xpZW50ID0gdGhpcy5yb2xlQ2xpZW50ID8gdGhpcy5yb2xlQ2xpZW50IDogbnVsbA0KICAgICAgcm93LnJvbGVTdXBwbGllciA9IHRoaXMucm9sZVN1cHBsaWVyID8gdGhpcy5yb2xlU3VwcGxpZXIgOiBudWxsDQogICAgICByb3cucm9sZVN1cHBvcnQgPSB0aGlzLnJvbGVTdXBwb3J0ID8gdGhpcy5yb2xlU3VwcG9ydCA6IG51bGwNCg0KICAgICAgY29uc3QgY29tcGFueUlkcyA9IHJvdy5jb21wYW55SWQgfHwgdGhpcy5pZHMNCiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpuehruiupOWIoOmZpOWFrOWPuOe8luWPt+S4ulwiIiArIGNvbXBhbnlJZHMgKyAiXCLnmoTmlbDmja7pobnvvJ8iLCAi5o+Q56S6Iiwge2N1c3RvbUNsYXNzOiAibW9kYWwtY29uZmlybSJ9KS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGRlbENvbXBhbnkocm93KQ0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlQmxhY2tMaXN0KCkgew0KICAgICAgdGhpcy5vcGVuQmxhY2tMaXN0ID0gdHJ1ZQ0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoInN5c3RlbS9jb21wYW55L2V4cG9ydCIsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYGNvbXBhbnlfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICBxdWVyeUxvY2F0aW9uSWQodmFsKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmxvY2F0aW9uSWQgPSB2YWwNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgZ2V0TG9jYXRpb25JZCh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5sb2NhdGlvbklkID0gdmFsDQogICAgfSwNCiAgICBnZXRTb3VyY2VJZCh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5zb3VyY2VJZCA9IHZhbC5zb3VyY2VTaG9ydE5hbWUNCiAgICB9LA0KICAgIGdldE9yZ2FuaXphdGlvbklkcyh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5vcmdhbml6YXRpb25JZHMgPSB2YWwNCiAgICB9LA0KICAgIGdldFNlcnZpY2VUeXBlSWRzKHZhbCkgew0KICAgICAgdGhpcy5mb3JtLnNlcnZpY2VUeXBlSWRzID0gdmFsDQogICAgICBpZiAodmFsID09IHVuZGVmaW5lZCkgew0KICAgICAgICB0aGlzLmNhcnJpZXJJZHMgPSBudWxsDQogICAgICAgIHRoaXMuZm9ybS5jYXJyaWVySWRzID0gbnVsbA0KICAgICAgfQ0KICAgIH0sDQogICAgcXVlcnlTZXJ2aWNlVHlwZUlkcyh2YWwpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc2VydmljZVR5cGVJZHMgPSB2YWwNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgZ2V0Q2FyZ29UeXBlSWRzKHZhbCkgew0KICAgICAgdGhpcy5mb3JtLmNhcmdvVHlwZUlkcyA9IHZhbA0KICAgIH0sDQogICAgcXVlcnlDYXJnb1R5cGVJZHModmFsKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNhcmdvVHlwZUlkcyA9IHZhbA0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgfSwNCiAgICBnZXRDb21wYW55Um9sZUlkcyh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5yb2xlSWRzID0gdmFsDQogICAgfSwNCiAgICBxdWVyeUNvbXBhbnlSb2xlSWRzKHZhbCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5yb2xlSWRzID0gdmFsDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIHF1ZXJ5TG9jYXRpb25EZXBhcnR1cmVJZHModmFsKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmxvY2F0aW9uRGVwYXJ0dXJlSWRzID0gdmFsDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIGdldExpbmVEZXBhcnR1cmVJZHModmFsKSB7DQogICAgICB0aGlzLmZvcm0ubGluZURlcGFydHVyZUlkcyA9IHZhbA0KICAgIH0sDQogICAgZ2V0TG9jYXRpb25EZXN0aW5hdGlvbklkcyh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5sb2NhdGlvbkRlc3RpbmF0aW9uSWRzID0gdmFsDQogICAgfSwNCiAgICBxdWVyeUxpbmVEZXBhcnR1cmVJZHModmFsKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmxpbmVEZXBhcnR1cmVJZHMgPSB2YWwNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgZ2V0TG9jYXRpb25EZXBhcnR1cmVJZHModmFsKSB7DQogICAgICB0aGlzLmZvcm0ubG9jYXRpb25EZXBhcnR1cmVJZHMgPSB2YWwNCiAgICB9LA0KICAgIHF1ZXJ5TG9jYXRpb25EZXN0aW5hdGlvbklkcyh2YWwpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMubG9jYXRpb25EZXN0aW5hdGlvbklkcyA9IHZhbA0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgfSwNCiAgICBnZXRMaW5lRGVzdGluYXRpb25JZHModmFsKSB7DQogICAgICB0aGlzLmZvcm0ubGluZURlc3RpbmF0aW9uSWRzID0gdmFsDQogICAgfSwNCiAgICBxdWVyeUxpbmVEZXN0aW5hdGlvbklkcyh2YWwpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMubGluZURlc3RpbmF0aW9uSWRzID0gdmFsDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdEJlbG9uZ1RvKG5vZGUpIHsNCiAgICAgIHRoaXMuZm9ybS5iZWxvbmdUbyA9IG5vZGUuc3RhZmZJZA0KICAgIH0sDQogICAgaGFuZGxlRGVzZWxlY3RCZWxvbmdUbyh2KSB7DQogICAgICBpZiAodiA9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgdGhpcy5mb3JtLmJlbG9uZ1RvID0gMA0KICAgICAgICB0aGlzLmJlbG9uZ1RvID0gbnVsbA0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlU2VsZWN0Rm9sbG93VXAobm9kZSkgew0KICAgICAgdGhpcy5mb3JtLmZvbGxvd1VwID0gbm9kZS5zdGFmZklkDQogICAgfSwNCiAgICBoYW5kbGVEZXNlbGVjdEZvbGxvd1VwKHZhbHVlKSB7DQogICAgICBpZiAodmFsdWUgPT0gdW5kZWZpbmVkKSB7DQogICAgICAgIHRoaXMuZm9ybS5mb2xsb3dVcCA9IDANCiAgICAgICAgdGhpcy5mb2xsb3dVcCA9IG51bGwNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdEJGU3RhZmZJZChub2RlKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnF1ZXJ5QkZTdGFmZklkID0gbm9kZS5zdGFmZklkDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIGNsZWFuQkZTdGFmZklkKHZhbCkgew0KICAgICAgaWYgKHZhbCA9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5xdWVyeUJGU3RhZmZJZCA9IG51bGwNCiAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgICB9DQogICAgfSwNCiAgICBjbGVhbkJTdGFmZklkKHZhbCkgew0KICAgICAgaWYgKHZhbCA9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5xdWVyeUJTdGFmZklkID0gbnVsbA0KICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdEJTdGFmZklkKG5vZGUpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucXVlcnlCU3RhZmZJZCA9IG5vZGUuc3RhZmZJZA0KICAgICAgZ2V0SW5mb0J5U3RhZmZJZChub2RlLnN0YWZmSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNhcmdvVHlwZUlkcyA9IHJlc3BvbnNlLmNhcmdvVHlwZUlkcw0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNlcnZpY2VUeXBlSWRzID0gcmVzcG9uc2Uuc2VydmljZVR5cGVJZHMNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5sb2NhdGlvbkRlcGFydHVyZUlkcyA9IHJlc3BvbnNlLmxvY2F0aW9uRGVwYXJ0dXJlSWRzDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMubGluZURlcGFydHVyZUlkcyA9IHJlc3BvbnNlLmxpbmVEZXBhcnR1cmVJZHMNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5sb2NhdGlvbkRlc3RpbmF0aW9uSWRzID0gcmVzcG9uc2UubG9jYXRpb25EZXN0aW5hdGlvbklkcw0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmxpbmVEZXN0aW5hdGlvbklkcyA9IHJlc3BvbnNlLmxpbmVEZXN0aW5hdGlvbklkcw0KICAgICAgICB0aGlzLmxvY2F0aW9uT3B0aW9ucyA9IHJlc3BvbnNlLmxvY2F0aW9uT3B0aW9ucw0KICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3RDYXJyaWVySWRzKG5vZGUpIHsNCiAgICAgIHRoaXMuZm9ybS5jYXJyaWVySWRzLnB1c2gobm9kZS5jYXJyaWVyLmNhcnJpZXJJZCkNCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdFF1ZXJ5Q2Fycmllcklkcyhub2RlKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNhcnJpZXJJZHMucHVzaChub2RlLmNhcnJpZXIuY2FycmllcklkKQ0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgfSwNCiAgICBoYW5kbGVEZXNlbGVjdENhcnJpZXJJZHMobm9kZSkgew0KICAgICAgdGhpcy5mb3JtLmNhcnJpZXJJZHMgPSB0aGlzLmZvcm0uY2Fycmllcklkcy5maWx0ZXIoKGl0ZW0pID0+IHsNCiAgICAgICAgcmV0dXJuIGl0ZW0gIT0gbm9kZS5jYXJyaWVyLmNhcnJpZXJJZA0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZURlc2VsZWN0UXVlcnlDYXJyaWVySWRzKG5vZGUpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY2FycmllcklkcyA9IHRoaXMucXVlcnlQYXJhbXMuY2Fycmllcklkcy5maWx0ZXIoKGl0ZW0pID0+IHsNCiAgICAgICAgcmV0dXJuIGl0ZW0gIT0gbm9kZS5jYXJyaWVyLmNhcnJpZXJJZA0KICAgICAgfSkNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgcmVmcmVzaENvbHVtbnMoKSB7DQogICAgICB0aGlzLnJlZnJlc2hUYWJsZSA9IGZhbHNlDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMucmVmcmVzaFRhYmxlID0gdHJ1ZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZU1lcmdlQ29tcGFueSgpIHsNCiAgICAgIHRoaXMubWVyZ2UgPSB0cnVlDQogICAgfSwNCiAgICBoYW5kbGVNZXJnZShzYXZlLCBkZWwpIHsNCiAgICAgIG1lcmdlQ29tcGFueShzYXZlLCBkZWwpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MocmVzcG9uc2UubXNnKQ0KICAgICAgICB0aGlzLm1lcmdlID0gZmFsc2UNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgIH0pDQogICAgfSwNCiAgICBkZXB0TG9jaygpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0uY29tcGFueUlkICE9IG51bGwpIHsNCiAgICAgICAgdGhpcy5mb3JtLmRlcHRDb25maXJtZWQgPSB0aGlzLmZvcm0uZGVwdENvbmZpcm1lZCA9PSAwID8gMSA6IDANCiAgICAgICAgdGhpcy5mb3JtLmRlcHRDb25maXJtZWRJZCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIuc2lkDQogICAgICAgIHRoaXMuZm9ybS5kZXB0Q29uZmlybWVkTmFtZSA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIubmFtZS5zcGxpdCgiICIpWzFdDQogICAgICAgIHRoaXMuZm9ybS5kZXB0Q29uZmlybWVkRGF0ZSA9IHBhcnNlVGltZShuZXcgRGF0ZSgpKQ0KICAgICAgICB1cGRhdGVDb21wYW55KHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIikNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLplJnor6/mk43kvZwiKQ0KICAgICAgfQ0KICAgIH0sDQogICAgZmluYW5jZUxvY2soKSB7DQogICAgICBpZiAodGhpcy5mb3JtLmNvbXBhbnlJZCAhPSBudWxsKSB7DQogICAgICAgIHRoaXMuZm9ybS5maW5hbmNlQ29uZmlybWVkID0gdGhpcy5mb3JtLmZpbmFuY2VDb25maXJtZWQgPT0gMCA/IDEgOiAwDQogICAgICAgIHRoaXMuZm9ybS5maW5hbmNlQ29uZmlybWVkSWQgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnNpZA0KICAgICAgICB0aGlzLmZvcm0uZmluYW5jZUNvbmZpcm1lZE5hbWUgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLm5hbWUuc3BsaXQoIiAiKVsxXQ0KICAgICAgICB0aGlzLmZvcm0uZmluYW5jZUNvbmZpcm1lZERhdGUgPSBwYXJzZVRpbWUobmV3IERhdGUoKSkNCiAgICAgICAgdXBkYXRlQ29tcGFueSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6ZSZ6K+v5pON5L2cIikNCiAgICAgIH0NCiAgICB9LA0KICAgIHBzYUxvY2soKSB7DQogICAgICBpZiAodGhpcy5mb3JtLmNvbXBhbnlJZCAhPSBudWxsKSB7DQogICAgICAgIHRoaXMuZm9ybS5wc2FDb25maXJtZWQgPSB0aGlzLmZvcm0ucHNhQ29uZmlybWVkID09IDAgPyAxIDogMA0KICAgICAgICB0aGlzLmZvcm0ucHNhQ29uZmlybWVkSWQgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnNpZA0KICAgICAgICB0aGlzLmZvcm0ucHNhQ29uZmlybWVkTmFtZSA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIubmFtZS5zcGxpdCgiICIpWzFdDQogICAgICAgIHRoaXMuZm9ybS5wc2FDb25maXJtZWREYXRlID0gcGFyc2VUaW1lKG5ldyBEYXRlKCkpDQogICAgICAgIHVwZGF0ZUNvbXBhbnkodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIumUmeivr+aTjeS9nCIpDQogICAgICB9DQogICAgfSwNCiAgICBvcExvY2soKSB7DQogICAgICBpZiAodGhpcy5mb3JtLmNvbXBhbnlJZCAhPSBudWxsKSB7DQogICAgICAgIHRoaXMuZm9ybS5vcENvbmZpcm1lZCA9IHRoaXMuZm9ybS5vcENvbmZpcm1lZCA9PT0gMCA/IDEgOiAwDQogICAgICAgIHRoaXMuZm9ybS5vcENvbmZpcm1lZElkID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5zaWQNCiAgICAgICAgdGhpcy5mb3JtLm9wQ29uZmlybWVkTmFtZSA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIubmFtZS5zcGxpdCgiICIpWzFdDQogICAgICAgIHRoaXMuZm9ybS5vcENvbmZpcm1lZERhdGUgPSBwYXJzZVRpbWUobmV3IERhdGUoKSkNCiAgICAgICAgdXBkYXRlQ29tcGFueSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6ZSZ6K+v5pON5L2cIikNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldEN1cnJlbmN5Q29kZSh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5hZ3JlZW1lbnRDdXJyZW5jeUNvZGUgPSB2YWwNCiAgICB9LA0KICAgIGdldGNyZWRpdExldmVsKHZhbCkgew0KICAgICAgdGhpcy5mb3JtLmNyZWRpdExldmVsID0gdmFsDQogICAgfSwNCiAgICBnZXRSc1BheW1lbnRUaXRsZSh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5yc1BheW1lbnRUaXRsZSA9IHZhbA0KICAgIH0sDQogICAgYXN5bmMgdXBkYXRlQ29tcGFueShmb3JtKSB7DQogICAgICAvLyBUT0RPIOWPquabtOaWsOacieS/ruaUueeahOWtl+autQ0KICAgICAgLy8gY29uc29sZS5sb2coT2JqZWN0LmFzc2lnbih0aGlzLmZvcm0sIGZvcm0pKQ0KDQogICAgICAvLyB0aGlzLmZvcm0uY3JlZGl0TGltaXQgPSB0aGlzLmZvcm0uY3JlZGl0TGltaXQucmVwbGFjZSgvLC9nLCAnJykNCg0KICAgICAgaWYgKHRoaXMucm9sZUNsaWVudCA9PSAxIHx8IHRoaXMucm9sZVJpY2ggPT0gMSkgew0KICAgICAgICB0aGlzLmZvcm0ucmVjZWl2ZUNyZWRpdExpbWl0ID0gdGhpcy5mb3JtLnJlY2VpdmVDcmVkaXRMaW1pdCA/IHRoaXMuZm9ybS5yZWNlaXZlQ3JlZGl0TGltaXQucmVwbGFjZSgvLC9nLCAiIikgOiAwDQogICAgICB9DQogICAgICBpZiAodGhpcy5yb2xlU3VwcGxpZXIgPT0gMSB8fCB0aGlzLnJvbGVTdXBwb3J0ID09IDEpIHsNCiAgICAgICAgdGhpcy5mb3JtLnBheUNyZWRpdExpbWl0ID0gdGhpcy5mb3JtLnBheUNyZWRpdExpbWl0ID8gdGhpcy5mb3JtLnBheUNyZWRpdExpbWl0LnJlcGxhY2UoLywvZywgIiIpIDogMA0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5mb3JtLmFncmVlbWVudERhdGVSYW5nZSAmJiB0aGlzLmZvcm0uYWdyZWVtZW50RGF0ZVJhbmdlLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy5mb3JtLmFncmVlbWVudFN0YXJ0RGF0ZSA9IHRoaXMuZm9ybS5hZ3JlZW1lbnREYXRlUmFuZ2VbMF0NCiAgICAgICAgdGhpcy5mb3JtLmFncmVlbWVudEVuZERhdGUgPSB0aGlzLmZvcm0uYWdyZWVtZW50RGF0ZVJhbmdlWzFdDQogICAgICB9DQoNCiAgICAgIGF3YWl0IHVwZGF0ZUNvbXBhbnkoZm9ybSkNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpDQogICAgICBsZXQgcmVzcG9uc2UgPSBhd2FpdCBnZXRDb21wYW55KGZvcm0uY29tcGFueUlkKQ0KICAgICAgLy8g5pu05paw5L+h5oGvDQogICAgICAvLyB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICBpZiAodGhpcy5iZWxvbmdMaXN0ICE9IHVuZGVmaW5lZCkgew0KICAgICAgICBmb3IgKGNvbnN0IGEgb2YgdGhpcy5iZWxvbmdMaXN0KSB7DQogICAgICAgICAgaWYgKGEuY2hpbGRyZW4gIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICBmb3IgKGNvbnN0IGIgb2YgYS5jaGlsZHJlbikgew0KICAgICAgICAgICAgICBpZiAoYi5jaGlsZHJlbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGMgb2YgYi5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgICAgaWYgKGMuc3RhZmZJZCA9PSByZXNwb25zZS5kYXRhLmJlbG9uZ1RvKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuYmVsb25nVG8gPSBjLmRlcHRJZA0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgaWYgKGMuc3RhZmZJZCA9PSByZXNwb25zZS5kYXRhLmZvbGxvd1VwKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuZm9sbG93VXAgPSBjLmRlcHRJZA0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgdGhpcy5mb3JtLnJvbGVJZHMgPSByZXNwb25zZS5yb2xlSWRzDQogICAgICB0aGlzLmZvcm0uc2VydmljZVR5cGVJZHMgPSByZXNwb25zZS5zZXJ2aWNlVHlwZUlkcw0KICAgICAgdGhpcy5mb3JtLmNhcmdvVHlwZUlkcyA9IHJlc3BvbnNlLmNhcmdvVHlwZUlkcw0KICAgICAgdGhpcy5mb3JtLmxpbmVEZXBhcnR1cmVJZHMgPSByZXNwb25zZS5saW5lRGVwYXJ0dXJlSWRzDQogICAgICB0aGlzLmZvcm0ubG9jYXRpb25EZXBhcnR1cmVJZHMgPSByZXNwb25zZS5sb2NhdGlvbkRlcGFydHVyZUlkcw0KICAgICAgdGhpcy5mb3JtLmxpbmVEZXN0aW5hdGlvbklkcyA9IHJlc3BvbnNlLmxpbmVEZXN0aW5hdGlvbklkcw0KICAgICAgdGhpcy5mb3JtLmxvY2F0aW9uRGVzdGluYXRpb25JZHMgPSByZXNwb25zZS5sb2NhdGlvbkRlc3RpbmF0aW9uSWRzDQogICAgICB0aGlzLmZvcm0uY2FycmllcklkcyA9IHJlc3BvbnNlLmNhcnJpZXJJZHMNCiAgICAgIC8vIHRoaXMuZm9ybS5vcmdhbml6YXRpb25JZHMgPSByZXNwb25zZS5jb21wYW55T3JnYW5pemF0aW9uSWRzDQogICAgICB0aGlzLmZvcm0ub3JnYW5pemF0aW9uSWRzID0gcmVzcG9uc2Uub3JnYW5pemF0aW9uSWRzDQogICAgICB0aGlzLmxvY2F0aW9uT3B0aW9ucyA9IHJlc3BvbnNlLmxvY2F0aW9uT3B0aW9ucw0KICAgICAgdGhpcy5vcGVuQ29tcGFueSA9IHRydWUNCiAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55YWs5Y+45L+h5oGvIg0KICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCg0KICAgICAgY29uc3QgZm9ybWF0dGVyID0gbmV3IEludGwuTnVtYmVyRm9ybWF0KCJlbi1VUyIsIHsNCiAgICAgICAgc3R5bGU6ICJkZWNpbWFsIiwNCiAgICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiAyLA0KICAgICAgICBtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDINCiAgICAgIH0pDQogICAgICAvKiB0aGlzLmZvcm0uY3JlZGl0TGltaXQgPSByZXNwb25zZS5kYXRhLmNyZWRpdExpbWl0LnRvTG9jYWxlU3RyaW5nKCdlbi1VUycpDQogICAgICB0aGlzLmZvcm0uY3JlZGl0TGltaXQgPSB0aGlzLmZvcm0uY3JlZGl0TGltaXQucmVwbGFjZSgvLC9nLCAnJykNCiAgICAgIHRoaXMuZm9ybS5jcmVkaXRMaW1pdCA9IGZvcm1hdHRlci5mb3JtYXQodGhpcy5mb3JtLmNyZWRpdExpbWl0KSAqLw0KDQogICAgICBpZiAodGhpcy5yb2xlQ2xpZW50ID09IDEgfHwgdGhpcy5yb2xlUmljaCA9PSAxKSB7DQogICAgICAgIHRoaXMuZm9ybS5yZWNlaXZlQ3JlZGl0TGltaXQgPSByZXNwb25zZS5kYXRhLnJlY2VpdmVDcmVkaXRMaW1pdC50b0xvY2FsZVN0cmluZygiZW4tVVMiKQ0KICAgICAgICB0aGlzLmZvcm0ucmVjZWl2ZUNyZWRpdExpbWl0ID0gdGhpcy5mb3JtLnJlY2VpdmVDcmVkaXRMaW1pdCA/IFN0cmluZyh0aGlzLmZvcm0ucmVjZWl2ZUNyZWRpdExpbWl0KS5yZXBsYWNlKC8sL2csICIiKSA6IDANCiAgICAgICAgdGhpcy5mb3JtLnJlY2VpdmVDcmVkaXRMaW1pdCA9IGZvcm1hdHRlci5mb3JtYXQodGhpcy5mb3JtLnJlY2VpdmVDcmVkaXRMaW1pdCkNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnJvbGVTdXBwbGllciA9PSAxIHx8IHRoaXMucm9sZVN1cHBvcnQgPT0gMSkgew0KICAgICAgICB0aGlzLmZvcm0ucGF5Q3JlZGl0TGltaXQgPSByZXNwb25zZS5kYXRhLnBheUNyZWRpdExpbWl0LnRvTG9jYWxlU3RyaW5nKCJlbi1VUyIpDQogICAgICAgIHRoaXMuZm9ybS5wYXlDcmVkaXRMaW1pdCA9IHRoaXMuZm9ybS5wYXlDcmVkaXRMaW1pdCA/IFN0cmluZyh0aGlzLmZvcm0ucGF5Q3JlZGl0TGltaXQpLnJlcGxhY2UoLywvZywgIiIpIDogMA0KICAgICAgICB0aGlzLmZvcm0ucGF5Q3JlZGl0TGltaXQgPSBmb3JtYXR0ZXIuZm9ybWF0KHRoaXMuZm9ybS5wYXlDcmVkaXRMaW1pdCkNCiAgICAgIH0NCg0KICAgICAgLy8g5pu05paw5pel5pyfDQogICAgICB0aGlzLmZvcm0uYWdyZWVtZW50RGF0ZVJhbmdlID0gKHJlc3BvbnNlLmRhdGEuYWdyZWVtZW50U3RhcnREYXRlICE9IG51bGwgJiYgcmVzcG9uc2UuZGF0YS5hZ3JlZW1lbnRFbmREYXRlICE9IG51bGwpID8gW3Jlc3BvbnNlLmRhdGEuYWdyZWVtZW50U3RhcnREYXRlLCByZXNwb25zZS5kYXRhLmFncmVlbWVudEVuZERhdGVdIDogW10NCiAgICAgIC8qIGlmICh0aGlzLmZvcm0uYWdyZWVtZW50RGF0ZVJhbmdlICYmIHRoaXMuZm9ybS5hZ3JlZW1lbnREYXRlUmFuZ2UubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLmZvcm0uYWdyZWVtZW50U3RhcnREYXRlID0gdGhpcy5mb3JtLmFncmVlbWVudERhdGVSYW5nZVswXQ0KICAgICAgICB0aGlzLmZvcm0uYWdyZWVtZW50RW5kRGF0ZSA9IHRoaXMuZm9ybS5hZ3JlZW1lbnREYXRlUmFuZ2VbMV0NCiAgICAgIH0gKi8NCg0KICAgICAgLy8gfSkNCiAgICAgIHRoaXMuZm9ybS5zYWxlc0NvbmZpcm1lZCA9IHJlc3BvbnNlLmRhdGEuc2FsZXNDb25maXJtZWQNCiAgICAgIHRoaXMuZm9ybS5wc2FDb25maXJtZWQgPSByZXNwb25zZS5kYXRhLnBzYUNvbmZpcm1lZA0KICAgICAgdGhpcy5mb3JtLm9wQ29uZmlybWVkID0gcmVzcG9uc2UuZGF0YS5vcENvbmZpcm1lZA0KICAgICAgdGhpcy5mb3JtLmFjY0NvbmZpcm1lZCA9IHJlc3BvbnNlLmRhdGEuYWNjQ29uZmlybWVkDQogICAgfSwNCiAgICBmb3JtYXRDcmVkaXRMaW1pdCgpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0ucmVjZWl2ZUNyZWRpdExpbWl0ICE9IG51bGwgfHwgdGhpcy5mb3JtLnBheUNyZWRpdExpbWl0ICE9IG51bGwpIHsNCiAgICAgICAgY29uc3QgZm9ybWF0dGVyID0gbmV3IEludGwuTnVtYmVyRm9ybWF0KCJlbi1VUyIsIHsNCiAgICAgICAgICBzdHlsZTogImRlY2ltYWwiLA0KICAgICAgICAgIG1pbmltdW1GcmFjdGlvbkRpZ2l0czogMiwNCiAgICAgICAgICBtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDINCiAgICAgICAgfSkNCiAgICAgICAgaWYgKHRoaXMucm9sZUNsaWVudCA9PSAxKSB7DQogICAgICAgICAgdGhpcy5mb3JtLnJlY2VpdmVDcmVkaXRMaW1pdCA9IFN0cmluZyh0aGlzLmZvcm0ucmVjZWl2ZUNyZWRpdExpbWl0KS5yZXBsYWNlKC8sL2csICIiKQ0KICAgICAgICAgIHRoaXMuZm9ybS5yZWNlaXZlQ3JlZGl0TGltaXQgPSBmb3JtYXR0ZXIuZm9ybWF0KHRoaXMuZm9ybS5yZWNlaXZlQ3JlZGl0TGltaXQpDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMucm9sZVN1cHBsaWVyID09IDEpIHsNCiAgICAgICAgICB0aGlzLmZvcm0ucGF5Q3JlZGl0TGltaXQgPSBTdHJpbmcodGhpcy5mb3JtLnBheUNyZWRpdExpbWl0KS5yZXBsYWNlKC8sL2csICIiKQ0KICAgICAgICAgIHRoaXMuZm9ybS5wYXlDcmVkaXRMaW1pdCA9IGZvcm1hdHRlci5mb3JtYXQodGhpcy5mb3JtLnBheUNyZWRpdExpbWl0KQ0KICAgICAgICB9DQoNCiAgICAgIH0NCiAgICB9LA0KICAgIGZvcm1hdERpc3BsYXlDcmVkaXRMaW1pdCh2YWx1ZSkgew0KICAgICAgY29uc3QgZm9ybWF0dGVyID0gbmV3IEludGwuTnVtYmVyRm9ybWF0KCJlbi1VUyIsIHsNCiAgICAgICAgbm90YXRpb246ICJjb21wYWN0Ig0KICAgICAgfSkNCiAgICAgIHJldHVybiBmb3JtYXR0ZXIuZm9ybWF0KHZhbHVlKQ0KICAgIH0sDQogICAgY2hhbmdlRGF0ZSgpIHsNCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs1BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/company", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-if=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" :size=\"size\"\r\n                 label-width=\"35px\"\r\n        >\r\n          <el-form-item v-if=\"roleClient==='1'\" label=\"所属\" prop=\"queryStaffId\">\r\n            <treeselect v-model=\"queryBFStaffId\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"belongList\" :show-count=\"true\" placeholder=\"所属人\" style=\"width: 100%\"\r\n                        @input=\"cleanBFStaffId\" @open=\"loadSales\" @select=\"handleSelectBFStaffId\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"roleTypeId==='2'\" label=\"所属\" prop=\"queryStaffId\">\r\n            <treeselect v-model=\"queryBStaffId\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :options=\"businessList\"\r\n                        :show-count=\"true\" placeholder=\"所属人\" style=\"width: 100%\"\r\n                        @input=\"cleanBStaffId\" @open=\"loadBusinesses\" @select=\"handleSelectBStaffId\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <!--          <el-form-item v-if=\"roleTypeId==='1'\" label=\"闲置\">\r\n                      <el-select v-model=\"queryParams.idleStatus\" placeholder=\"是否闲置\" style=\"width: 100%\"\r\n                                 @change=\"handleQuery\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.sys_is_idle\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>-->\r\n          <el-form-item label=\"客户\" prop=\"companyQuery\">\r\n            <el-input v-model=\"queryParams.companyQuery\" clearable placeholder=\"客户名称/代码\" style=\"width: 100%\"\r\n                      @change=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <!--行政区域-->\r\n          <el-form-item label=\"地址\" prop=\"locationId\">\r\n            <location-select :multiple=\"false\" :pass=\"queryParams.locationId\"\r\n                             :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationId\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"信用\" prop=\"creditLevel\">\r\n            <tree-select :multiple=\"false\" :pass=\"queryParams.creditLevel\"\r\n                         :type=\"'creditLevel'\" style=\"width: 100%\"\r\n                         @return=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <!--供应商提供哪些服务如：海运、铁路等-->\r\n          <el-form-item label=\"服务\" prop=\"serviceTypeIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.serviceTypeIds\"\r\n                         :placeholder=\"'服务类型'\" :type=\"'serviceType'\" style=\"width: 100%\"\r\n                         :d-load=\"true\" @return=\"queryServiceTypeIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"启运\" prop=\"locationDepartureIds\">\r\n            <location-select :multiple=\"true\" :pass=\"queryParams.locationDepartureIds\"\r\n                             :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationDepartureIds\"\r\n            />\r\n          </el-form-item>\r\n          <!--目的区域-->\r\n          <el-form-item label=\"目的\" prop=\"locationDestinationIds\">\r\n            <location-select :multiple=\"true\" :pass=\"queryParams.locationDestinationIds\"\r\n                             :en=\"true\" :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationDestinationIds\"\r\n            />\r\n          </el-form-item>\r\n          <!--公司角色-->\r\n          <el-form-item label=\"角色\" prop=\"locationDestinationIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.roleIds\" :type=\"'companyRole'\"\r\n                         @return=\"queryCompanyRoleIds\"\r\n            />\r\n          </el-form-item>\r\n          <!--目的航线-->\r\n          <el-form-item label=\"航线\" prop=\"lineDestinationIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.lineDestinationIds\"\r\n                         :placeholder=\"'目的航线'\" :type=\"'line'\" style=\"width: 100%\"\r\n                         :d-load=\"true\" @return=\"queryLineDestinationIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"货物\" prop=\"cargoTypeIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.cargoTypeIds\" :placeholder=\"'货物特征'\"\r\n                         :d-load=\"true\" :type=\"'cargoType'\" style=\"width: 100%\" @return=\"queryCargoTypeIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"承运\" prop=\"carrierIds\">\r\n            <treeselect v-model=\"queryCarrierIds\" :disable-fuzzy-matching=\"true\" :disable-branch-nodes=\"true\"\r\n                        :flat=\"true\" :flatten-search-results=\"true\" :multiple=\"true\"\r\n                        :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\" @open=\"loadCarrier\"\r\n                        placeholder=\"优选承运人\" style=\"width: 100%\" @deselect=\"handleDeselectQueryCarrierIds\"\r\n                        @select=\"handleSelectQueryCarrierIds\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{ node.raw.carrier.carrierIntlCode }}\r\n                {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : \"\" }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"评级\" prop=\"creditLevel\">\r\n            <el-input v-model=\"queryParams.creditLevel\" placeholder=\"A~E\" style=\"width: 100%\"\r\n                      @change=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button :size=\"size\" icon=\"el-icon-search\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button :size=\"size\" icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:company:add']\"\r\n              :size=\"size\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:company:export']\"\r\n              :size=\"size\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              :disabled=\"single\"\r\n              :size=\"size\"\r\n              icon=\"el-icon-user-solid\"\r\n              type=\"info\"\r\n              @click=\"handleBlackList\"\r\n            >\r\n              拉黑\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              :disabled=\"selectTwo\"\r\n              :size=\"size\"\r\n              icon=\"el-icon-connection\"\r\n              @click=\"handleMergeCompany\"\r\n            >\r\n              合并\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :columns=\"columns\" :showSearch.sync=\"showSearch\"\r\n                         :types=\"this.roleTypeId=='2'?'supplier':this.roleTypeId=='1'?'client':''\"\r\n                         @queryTable=\"getList\" @refreshColumns=\"refreshColumns\"\r\n          />\r\n        </el-row>\r\n        <!--合并公司-->\r\n        <el-dialog\r\n          :close-on-click-modal=\"false\"\r\n          :modal-append-to-body=\"false\"\r\n          v-dialogDrag v-dialogDragWidth\r\n          title=\"选择保留公司\"\r\n          :visible.sync=\"merge\"\r\n          width=\"800px\"\r\n        >\r\n          <el-row v-if=\"mergeList.length>0\" :gutter=\"5\">\r\n            <el-col :span=\"12\">\r\n              <el-descriptions title=\"公司1\" direction=\"vertical\" border>\r\n                <el-descriptions-item label=\"简称\">{{ mergeList[0].companyShortName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"中文名\">{{ mergeList[0].companyLocalName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"英文名\">{{ mergeList[0].companyEnName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"选择\">\r\n                  <el-button @click=\"handleMerge(mergeList[0].companyId,mergeList[1].companyId)\">\r\n                    留下{{ mergeList[0].companyShortName }}\r\n                  </el-button>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-descriptions title=\"公司2\" direction=\"vertical\" border>\r\n                <el-descriptions-item label=\"简称\">{{ mergeList[1].companyShortName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"中文名\">{{ mergeList[1].companyLocalName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"英文名\">{{ mergeList[1].companyEnName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"选择\">\r\n                  <el-button @click=\"handleMerge(mergeList[1].companyId,mergeList[0].companyId)\">\r\n                    留下{{ mergeList[1].companyShortName }}\r\n                  </el-button>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </el-col>\r\n          </el-row>\r\n        </el-dialog>\r\n        <!--公司列表-->\r\n        <el-table v-if=\"refreshTable\" v-loading=\"loading\" :data=\"companyList\" border\r\n                  @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column\r\n            v-for=\"data in columns\"\r\n            v-if=\"data.visible\"\r\n            :key=\"data.key\"\r\n            :align=\"data.align\"\r\n            :label=\"data.label\"\r\n            :width=\"data.width\"\r\n            :show-overflow-tooltip=\"data.tooltip\"\r\n          >\r\n            <template v-slot=\"scope\">\r\n              <!--内置组件，根据:is动态加载组件-->\r\n              <!--渲染组件根据组件内部的return方法返回指针渲染内容-->\r\n              <component :is=\"data.prop\" :scope=\"scope\" @return=\"getReturn\"/>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"结款方式\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <h6 style=\"margin: 0;\">{{ }}</h6>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"额度\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <!--币种-->\r\n              <span style=\"margin: 0;\">{{\r\n                  (roleClient == 1 || roleRich == 1) ? scope.row.receiveCurrencyCode : scope.row.payCurrencyCode\r\n                }}</span>\r\n              <!--额度-->\r\n              <span style=\"margin: 0;\">{{\r\n                  formatDisplayCreditLimit(roleClient == 1 || roleRich == 1 ? scope.row.receiveCreditLimit : scope.row.payCreditLimit)\r\n                }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"录入人\" show-overflow-tooltip width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                <h6 class=\"text-display\" style=\"margin: 0;\">{{ scope.row.updateByName }}</h6>\r\n                <h6 class=\"text-display\" style=\"margin: 0;\">{{ parseTime(scope.row.updateTime, \"{y}.{m}.{d}\") }}</h6>\r\n              </div>\r\n\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"90px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-hasPermi=\"['system:company:edit']\"\r\n                         :size=\"size\" icon=\"el-icon-edit\" type=\"success\" style=\"margin: 0 3px\"\r\n                         @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button v-hasPermi=\"['system:company:remove']\"\r\n                         :size=\"size\" icon=\"el-icon-delete\" type=\"danger\" style=\"margin: 0\"\r\n                         @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 分页 -->\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改公司对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      append-to-body\r\n      v-dialogDrag v-dialogDragWidth\r\n      :title=\"title\"\r\n      :visible.sync=\"openCompany\"\r\n      width=\"55%\"\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"68px\" class=\"edit\">\r\n        <el-row :gutter=\"5\">\r\n          <el-col :span=\"12\">\r\n            <!--从属信息-->\r\n            <el-row v-if=\"roleClient==='1'\" :gutter=\"5\">\r\n              <el-divider content-position=\"left\">从属信息</el-divider>\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"业务员\">\r\n                    <treeselect v-model=\"belongTo\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                                :class=\"isLock ?'disable-form':''\" :disabled=\" isLock\"\r\n                                :flatten-search-results=\"true\"\r\n                                :normalizer=\"staffNormalizer\" :options=\"belongList\"\r\n                                :show-count=\"true\" class=\"sss\" placeholder=\"选择所属人\" @input=\"handleDeselectBelongTo\"\r\n                                @open=\"loadSales\" @select=\"handleSelectBelongTo\"\r\n                    >\r\n                      <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                        {{\r\n                          node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                        }}\r\n                      </div>\r\n                      <label slot=\"option-label\"\r\n                             slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                             :class=\"labelClassName\"\r\n                      >\r\n                        {{\r\n                          node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                        }}\r\n                        <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                      </label>\r\n                    </treeselect>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"业务助理\">\r\n                    <treeselect v-model=\"followUp\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                                :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                                :options=\"belongList\" :show-count=\"true\" class=\"sss\"\r\n                                :class=\"isLock ?'disable-form':''\" :disabled=\"isLock\"\r\n                                placeholder=\"业务员自己跟进的情况无须填写\"\r\n                                @input=\"handleDeselectFollowUp\" @open=\"loadSales\" @select=\"handleSelectFollowUp\"\r\n                    >\r\n                      <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                        {{\r\n                          node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                        }}\r\n                      </div>\r\n                      <label slot=\"option-label\"\r\n                             slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                             :class=\"labelClassName\"\r\n                      >\r\n                        {{\r\n                          node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                        }}\r\n                        <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                      </label>\r\n                    </treeselect>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"所属公司\">\r\n                    <tree-select\r\n                      :pass=\"form.companyBelongTo\" :placeholder=\"'收付路径'\"\r\n                      :class=\"isLock ?'disable-form':''\" :disabled=\"isLock\" :type=\"'rsPaymentTitle'\"\r\n                      @return=\"form.companyBelongTo=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n            <!--基本信息-->\r\n            <el-row>\r\n              <el-divider content-position=\"left\">基础资料</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"公司代码\" prop=\"companyTaxCode\">\r\n                    <el-input v-model=\"form.companyTaxCode\" class=\" disable-form\" disabled\r\n                              :class=\"'sss'\" placeholder=\"国际通用简称\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"税号\" prop=\"companyTaxCode\">\r\n                    <el-input v-model=\"form.taxNo\" :class=\"basicLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"basicLock || commonLock\"\r\n                              placeholder=\"公司税号/统一社会信用代码\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item\r\n                    :rules=\"[{ required: true, message: '简称不能为空', trigger: 'blur'},{validator: validateCompanyShortName, trigger: 'blur'}]\"\r\n                    label=\"公司简称\"\r\n                    prop=\"companyShortName\"\r\n                  >\r\n                    <el-input v-model=\"form.companyShortName\" :class=\"basicLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"basicLock || commonLock\"\r\n                              class=\"sss\" placeholder=\"公司简称\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item :rules=\"[{ required: true, message: '抬头不能为空', trigger: 'blur'}]\" label=\"发票抬头\"\r\n                                prop=\"companyLocalName\"\r\n                  >\r\n                    <el-input v-model=\"form.companyLocalName\" :class=\"basicLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"basicLock || commonLock\" placeholder=\"公司发票抬头\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"行政区域\" prop=\"locationId\">\r\n                    <location-select :load-options=\"locationOptions\" :multiple=\"false\" :pass=\"form.locationId\"\r\n                                     class=\"sss\" @return=\"getLocationId\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"详细地址\" prop=\"locationDetail\">\r\n                    <el-input v-model=\"form.locationDetail\"\r\n                              placeholder=\"详细地址信息\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"主联系人\" prop=\"mainStaffOfficialName\">\r\n                    <el-input v-model=\"form.mainStaffOfficialName\" class=\"sss\"\r\n                              placeholder=\"联系人名称\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"微信\" prop=\"staffWechat\">\r\n                    <el-input v-model=\"form.staffWechat\" class=\"sss\"\r\n                              placeholder=\"联系人微信\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"QQ\" prop=\"staffQq\">\r\n                    <el-input v-model=\"form.staffQq\"\r\n                              placeholder=\"联系人QQ\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"手机\" prop=\"staffMobile\">\r\n                    <el-input v-model=\"form.staffMobile\" class=\"sss\"\r\n                              placeholder=\"手机\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"邮箱\" prop=\"staffEmail\">\r\n                    <el-input v-model=\"form.staffEmail\"\r\n                              placeholder=\"联系人邮箱\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"Whatsapp\" prop=\"staffWhatsapp\">\r\n                    <el-input v-model=\"form.staffWhatsapp\" class=\"sss\"\r\n                              placeholder=\"联系人Whatsapp\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"联系方式\" prop=\"staffOtherContact\">\r\n                    <el-input v-model=\"form.staffOtherContact\"\r\n                              placeholder=\"员工其他联系方式\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n            <!--协议信息-->\r\n            <el-row>\r\n              <el-divider content-position=\"left\">协议信息</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"协议号\" prop=\"agreementNumber\">\r\n                    <el-input v-model=\"form.agreementNumber\" :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"agreementLock || commonLock\"\r\n                              class=\"sss\" placeholder=\"协议号\"\r\n\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"起讫日期\" prop=\"agreementStartDate\">\r\n                    <el-date-picker\r\n                      v-model=\"form.agreementDateRange\"\r\n                      :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                      :disabled=\"agreementLock || commonLock\" class=\"sss\"\r\n                      end-placeholder=\"结束日期\"\r\n                      range-separator=\"至\"\r\n                      start-placeholder=\"开始日期\"\r\n                      style=\"width: 100%;\"\r\n                      :default-time=\"['00:00:00', '23:59:59']\"\r\n                      type=\"daterange\"\r\n                      @input=\"changeDate\"\r\n                      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    >\r\n                    </el-date-picker>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"信用等级\" prop=\"creditLevel\">\r\n                    <tree-select :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :disabled=\"agreementLock || commonLock\" :pass=\"form.creditLevel\"\r\n                                 :type=\"'creditLevel'\" class=\"sss\"\r\n                                 @return=\"getcreditLevel\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item\r\n                    :label=\"roleClient==1|| roleRich==1?'收款方式':roleSupplier==1|| roleSupport==1?'付款方式':''\"\r\n                    prop=\"creditLevel\"\r\n                  >\r\n                    <tree-select :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :disabled=\"agreementLock || commonLock\"\r\n                                 v-if=\"roleClient==1|| roleRich==1\" :multiple=\"false\"\r\n                                 :pass=\"form.receiveWay\" :placeholder=\"'收款方式'\"\r\n                                 :flat=\"false\" :type=\"'releaseTypeCode'\"\r\n                                 class=\"sss\"\r\n                                 @returnData=\"form.receiveWay=$event.releaseTypeShortName\"\r\n                    />\r\n                    <tree-select v-if=\"roleSupplier==1|| roleSupport==1\"\r\n                                 :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :flat=\"false\" :multiple=\"false\"\r\n                                 :disabled=\"agreementLock || commonLock\" :pass=\"form.payWay\"\r\n                                 :placeholder=\"'付款方式'\" :type=\"'releaseTypeCode'\" class=\"sss\"\r\n                                 @returnData=\"form.payWay=$event.releaseTypeShortName\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item\r\n                    :label=\"roleClient==1|| roleRich==1?'收款信用':roleSupplier==1|| roleSupport==1?'付款信用':''\"\r\n                    prop=\"creditLevel\"\r\n                  >\r\n                    <el-col v-if=\"roleClient==1|| roleRich==1\">\r\n                      <el-col :span=\"9\">\r\n                        <tree-select :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                     :disabled=\"agreementLock || commonLock\"\r\n                                     :pass=\"form.receiveCurrencyCode\"\r\n                                     class=\" currency\" @return=\"form.receiveCurrencyCode=$event\"\r\n                                     :type=\"'currency'\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"15\">\r\n                        <el-input v-model=\"form.receiveCreditLimit\"\r\n                                  :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                  :disabled=\"agreementLock || commonLock\"\r\n                                  placeholder=\"信用额度(填入整数)\" @change=\"formatCreditLimit\"\r\n                                  class=\" limit\"\r\n                        />\r\n                      </el-col>\r\n                    </el-col>\r\n                    <el-col v-if=\"roleSupplier==1|| roleSupport==1\">\r\n                      <el-col :span=\"9\">\r\n                        <tree-select :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                     :disabled=\"agreementLock || commonLock\"\r\n                                     :pass=\"form.payCurrencyCode\"\r\n                                     class=\" currency\" @return=\"form.payCurrencyCode=$event\"\r\n                                     :type=\"'currency'\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"15\">\r\n                        <el-input v-model=\"form.payCreditLimit\" :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                  :disabled=\"agreementLock || commonLock\"\r\n                                  placeholder=\"信用额度(填入整数)\" @change=\"formatCreditLimit\"\r\n                                  class=\" limit\"\r\n                        />\r\n                      </el-col>\r\n                    </el-col>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"账单基准\" prop=\"creditLevel\">\r\n                    <tree-select v-if=\"roleClient==1 || roleRich==1\"\r\n                                 :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :disabled=\"agreementLock || commonLock\"\r\n                                 :flat=\"false\" :multiple=\"false\"\r\n                                 :pass=\"form.receiveStandard\" :placeholder=\"'收款账单基准'\"\r\n                                 :type=\"'commonInfoOfTime'\" class=\"sss\"\r\n                                 @return=\"form.receiveStandard=$event\"\r\n                                 @returnData=\"localStandard=$event.infoLocalName\"\r\n                    />\r\n                    <tree-select v-if=\"roleSupplier==1 || roleSupport==1\"\r\n                                 :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :disabled=\"agreementLock || commonLock\" :flat=\"false\"\r\n                                 :multiple=\"false\"\r\n                                 :pass=\"form.payStandard\" :placeholder=\"'付款账单基准'\"\r\n                                 :type=\"'commonInfoOfTime'\" class=\"sss\"\r\n                                 @returnData=\"form.payStandard=$event.infoShortName\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item\r\n                    :label=\"roleClient==1|| roleRich==1?'收款期限':roleSupplier==1|| roleSupport==1?'付款期限':''\"\r\n                    :class=\"agreementLock || commonLock ?'disable-form':''\" :disabled=\"agreementLock || commonLock\"\r\n                    prop=\"agreementNumber\"\r\n                  >\r\n                    <el-input v-if=\"roleClient==1|| roleRich==1\" v-model=\"form.receiveTerm\"\r\n                              :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"agreementLock || commonLock\" class=\"sss\"\r\n                              placeholder=\"收款期限(填写±n)\"\r\n                              @change=\"handleTermChange\"\r\n                    />\r\n                    <el-input v-if=\"roleSupplier==1|| roleSupport==1\" v-model=\"form.payTerm\"\r\n                              :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"agreementLock || commonLock\" class=\"sss\"\r\n                              placeholder=\"付款期限(填写±n)\"\r\n                              @change=\"handleTermChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-input :value=\"description\" class=\"disable-form\" disabled/>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-form-item label=\"协议备注\" prop=\"agreementRemark\">\r\n                  <el-input v-model=\"form.agreementRemark\" :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                            :disabled=\"agreementLock || commonLock\"\r\n                            placeholder=\"协议备注\"\r\n                  />\r\n                </el-form-item>\r\n              </el-row>\r\n            </el-row>\r\n            <!--审核意见-->\r\n            <el-row v-if=\"showConfirm\">\r\n              <el-divider content-position=\"left\">\r\n                审核意见\r\n              </el-divider>\r\n              <el-row>\r\n                <el-col v-if=\"this.roleClient==1\" :span=\"4\">\r\n                  <confirmed :id=\"'companyId'\" :confirmed=\"this.form.salesConfirmed==0\" :row=\"form\" :type=\"'sales'\"\r\n                             @lockMethod=\"updateCompany\"\r\n                  />\r\n                </el-col>\r\n                <el-col v-if=\"this.roleSupplier==1\" :span=\"4\">\r\n                  <!--商务锁定-->\r\n                  <confirmed :id=\"'companyId'\" :confirmed=\"this.form.psaConfirmed==0\" :row=\"form\" :type=\"'psa'\"\r\n                             @lockMethod=\"updateCompany\"\r\n                  />\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <!--操作锁定-->\r\n                  <confirmed :id=\"'companyId'\" :confirmed=\"this.form.opConfirmed==0\" :row=\"form\" :type=\"'op'\"\r\n                             @lockMethod=\"updateCompany\"\r\n                  />\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <!--财务锁定-->\r\n                  <confirmed :id=\"'companyId'\" :confirmed=\"this.form.accConfirmed==0\" :row=\"form\" :type=\"'acc'\"\r\n                             @lockMethod=\"updateCompany\"\r\n                  />\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <!--分类信息-->\r\n            <el-row v-if=\"roleClient==1\">\r\n              <el-divider content-position=\"left\">\r\n                开发分类\r\n              </el-divider>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"客户来源\">\r\n                    <tree-select :pass=\"form.sourceId\" :type=\"'source'\" class=\"sss\"\r\n                                 @returnData=\"getSourceId\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"开发权重\">\r\n                    <el-input v-model=\"form.developmentWeight\" class=\"sss\"\r\n                              placeholder=\"重要程度\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"业务标记\" prop=\"salesRemark\">\r\n                    <el-input v-model=\"form.salesRemark\" class=\"sss\"\r\n                              placeholder=\"重要程度\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n            <!--物流分类-->\r\n            <el-row>\r\n              <el-divider content-position=\"left\">\r\n                物流分类\r\n              </el-divider>\r\n              <el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <!--供应商公司的角色-->\r\n                    <el-form-item label=\"公司角色\" prop=\"roleIds\">\r\n                      <!--<tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"true\"\r\n                                   :pass=\"queryParams.roleIds\" :placeholder=\"'公司角色'\" :type=\"'companyRole'\"\r\n                                   class=\"sss\" style=\"width: 100%\" @return=\"queryCompanyRoleIds\"\r\n                      />-->\r\n                      <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.roleIds\" :type=\"'companyRole'\"\r\n                                   class=\"sss\" @return=\"getCompanyRoleIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"所属组织\" prop=\"organizationIds\">\r\n                      <tree-select :multiple=\"true\" :pass=\"form.organizationIds\" :type=\"'organization'\"\r\n                                   class=\"sss\" @return=\"getOrganizationIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"服务类型\" prop=\"serviceTypeIds\">\r\n                      <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.serviceTypeIds\" :type=\"'serviceType'\"\r\n                                   class=\"sss\" @return=\"getServiceTypeIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"货物类型\" prop=\"cargoTypeIds\">\r\n                      <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.cargoTypeIds\" :type=\"'cargoType'\"\r\n                                   class=\"sss\" @return=\"getCargoTypeIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"启运区域\" prop=\"locationDepartureIds\">\r\n                      <location-select :load-options=\"locationOptions\" :multiple=\"true\"\r\n                                       :pass=\"form.locationDepartureIds\" class=\"sss\"\r\n                                       @return=\"getLocationDepartureIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"优选承运\" prop=\"carrierIds\">\r\n                      <treeselect v-model=\"carrierIds\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                                  :flat=\"true\" :flatten-search-results=\"true\" :multiple=\"true\"\r\n                                  :normalizer=\"carrierNormalizer\"\r\n                                  :options=\"temCarrierList\" :show-count=\"true\" class=\"sss\"\r\n                                  placeholder=\"选择承运人\" @deselect=\"handleDeselectCarrierIds\" @open=\"loadCarrier\"\r\n                                  @select=\"handleSelectCarrierIds\"\r\n                      >\r\n                        <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                          {{ node.raw.carrier.carrierIntlCode }}\r\n                          {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : \"\" }}\r\n                        </div>\r\n                        <label slot=\"option-label\"\r\n                               slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                               :class=\"labelClassName\"\r\n                        >\r\n                          {{\r\n                            node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                          }}\r\n                          <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                        </label>\r\n                      </treeselect>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"目的区域\" prop=\"locationDestinationIds\">\r\n                      <location-select :en=\"true\" :load-options=\"locationOptions\"\r\n                                       :multiple=\"true\" :pass=\"form.locationDestinationIds\"\r\n                                       class=\"sss\"\r\n                                       @return=\"getLocationDestinationIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"目的航线\" prop=\"lineDestinationIds\">\r\n                      <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.lineDestinationIds\" :type=\"'line'\"\r\n                                   class=\"sss\" @return=\"getLineDestinationIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"备注信息\" prop=\"remark\">\r\n                    <el-input v-model=\"form.remark\" class=\"sss\"\r\n                              placeholder=\"备注信息\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n            <!--客户习惯-->\r\n            <el-row>\r\n              <el-divider content-position=\"left\">\r\n                客户习惯\r\n              </el-divider>\r\n              <el-col :span=\"24\">\r\n                <el-input v-model=\"form.partnerHabit\" :autosize=\"{ minRows: 15, maxRows: 10}\" maxlength=\"150\"\r\n                          placeholder=\"备注\" show-word-limit type=\"textarea\"\r\n                />\r\n              </el-col>\r\n            </el-row>\r\n          </el-col>\r\n        </el-row>\r\n\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button v-if=\"!edit\" :size=\"size\" type=\"primary\" @click=\"querySame\">查重</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <div>\r\n      <staff-info :company=\"companyInfo\" :load-options=\"staffList\" :open=\"openStaff\" @openStaffs=\"cancel\"/>\r\n      <!--    <account-info :company=\"companyInfo\" :load-options=\"accountList\" :open=\"openAccount\" :type=\"'supplier'\"\r\n                        @openAccounts=\"cancel\"\r\n          />-->\r\n      <account-info :company=\"companyInfo\" :is-lock=\"isLock\" :load-options=\"accountList\" :open=\"openAccount\"\r\n                    :type=\"'company'\"\r\n                    @openAccounts=\"accountCancel\"\r\n      />\r\n      <communications :company=\"companyInfo\" :load-options=\"communicationList\" :open=\"openCommunication\" :totle=\"ctotle\"\r\n                      @openCommunications=\"cancel\"\r\n      />\r\n      <agreement-record :company=\"companyInfo\" :load-options=\"agreementList\" :open=\"openAgreement\" :totle=\"atotle\"\r\n                        @openCommunications=\"cancel\"\r\n      />\r\n      <BlackList :company=\"companyInfo\" :open=\"openBlackList\" @openBlackList=\"cancel\"/>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  delCompany,\r\n  getBank,\r\n  getCompany,\r\n  getConnect,\r\n  listCompany,\r\n  mergeCompany,\r\n  querySame,\r\n  updateCompany\r\n} from \"@/api/system/company\"\r\nimport {getInfoByStaffId} from \"@/api/system/role\"\r\nimport {addMessage} from \"@/api/system/message\"\r\nimport {listCommunication} from \"@/api/system/communication\"\r\nimport {listAgreementrecord} from \"@/api/system/agreementrecord\"\r\n\r\nimport pinyin from \"js-pinyin\"\r\nimport store from \"@/store\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.min.css\"\r\n\r\nimport BlackList from \"@/components/BlackList\"\r\nimport communications from \"@/views/system/communication\"\r\nimport agreementRecord from \"@/views/system/agreementRecord\"\r\nimport staffInfo from \"@/views/system/company/staffInfo\"\r\n\r\nimport company from \"@/views/system/company/company\"\r\nimport contactor from \"@/views/system/company/contactor\"\r\nimport location from \"@/views/system/company/location\"\r\nimport role from \"@/views/system/company/role\"\r\nimport serviceType from \"@/views/system/company/serviceType\"\r\nimport departure from \"@/views/system/company/departure\"\r\nimport destination from \"@/views/system/company/destination\"\r\nimport cargoType from \"@/views/system/company/cargoType\"\r\nimport carrier from \"@/views/system/company/carrier\"\r\nimport account from \"@/views/system/company/account\"\r\nimport agreement from \"@/views/system/company/agreement\"\r\nimport communication from \"@/views/system/company/communication\"\r\nimport grade from \"@/views/system/company/grade\"\r\nimport achievement from \"@/views/system/company/achievement\"\r\nimport remark from \"@/views/system/company/remark\"\r\nimport belong from \"@/views/system/company/belong\"\r\nimport auth from \"@/plugins/auth\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport {Message} from \"element-ui\"\r\nimport Confirmed from \"@/components/Confirmed/index.vue\"\r\nimport rsPaymentTitle from \"@/views/system/company/rsPaymentTitle.vue\"\r\nimport AccountInfo from \"@/views/system/company/accountInfo.vue\"\r\nimport {checkRole} from \"@/utils/permission\"\r\n\r\nexport default {\r\n  name: \"Company\",\r\n  dicts: [\"sys_is_idle\"],\r\n  components: {\r\n    AccountInfo,\r\n    Confirmed,\r\n    Treeselect,\r\n    communication,\r\n    communications,\r\n    BlackList,\r\n    belong,\r\n    company,\r\n    contactor,\r\n    staffInfo,\r\n    location,\r\n    role,\r\n    serviceType,\r\n    departure,\r\n    destination,\r\n    cargoType,\r\n    carrier,\r\n    account,\r\n    agreement,\r\n    agreementRecord,\r\n    grade,\r\n    achievement,\r\n    remark,\r\n    rsPaymentTitle\r\n  },\r\n  props: [\"roleTypeId\", \"roleRich\", \"roleClient\", \"roleSupplier\", \"roleSupport\"],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      add: false,\r\n      selectTwo: true,\r\n      size: this.$store.state.app.size || \"mini\",\r\n      // 公司表格数据\r\n      mergeList: [],\r\n      companyList: [],\r\n      staffList: [],\r\n      accountList: [],\r\n      communicationList: [],\r\n      agreementList: [],\r\n      belongList: [],\r\n      carrierList: [],\r\n      businessList: [],\r\n      temCarrierList: [],\r\n      locationOptions: [],\r\n      carrierIds: [],\r\n      companyInfo: {},\r\n      queryCarrierIds: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      merge: false,\r\n      openCompany: false,\r\n      openStaff: false,\r\n      openAccount: false,\r\n      openCommunication: false,\r\n      openAgreement: false,\r\n      openBlackList: false,\r\n      edit: false,\r\n      belongTo: null,\r\n      followUp: null,\r\n      queryBFStaffId: null,\r\n      queryBStaffId: null,\r\n      refreshTable: true,\r\n      ctotle: null,\r\n      atotle: null,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        // roleTypeId: this.roleTypeId,\r\n        roleRich: this.roleRich ? this.roleRich : null,\r\n        roleClient: this.roleClient ? this.roleClient : null,\r\n        roleSupplier: this.roleSupplier ? this.roleSupplier : null,\r\n        roleSupport: this.roleSupport ? this.roleSupport : null,\r\n        companyQuery: null,\r\n        locationId: null,\r\n        idleStatus: null,\r\n        queryStaffId: null,\r\n        showPriority: null,\r\n        serviceTypeIds: [],\r\n        cargoTypeIds: [],\r\n        locationDepartureIds: [],\r\n        lineDepartureIds: [],\r\n        locationDestinationIds: [],\r\n        lineDestinationIds: [],\r\n        roleIds: [],\r\n        carrierIds: []\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        agreementStartDate: null,\r\n        agreementEndDate: null,\r\n        settlementDate: null\r\n      },\r\n      // 表单校验\r\n      rules: {},\r\n      // 当前点击修改时选中的记录\r\n      companyRow: null,\r\n      isLock: true,\r\n      showConfirm: false,\r\n      localStandard: null,\r\n      description: null\r\n    }\r\n  },\r\n  computed: {\r\n    columns: {\r\n      get() {\r\n        if (this.roleTypeId == \"2\") {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n        if (this.roleTypeId == \"1\") {\r\n          return this.$store.state.listSettings.clientSetting\r\n        }\r\n        if (this.roleClient) {\r\n          return this.$store.state.listSettings.clientSetting\r\n        }\r\n        if (this.roleSupplier) {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n        if (this.roleRich) {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n        if (this.roleSupport) {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n      }\r\n    },\r\n    commonLock() {\r\n      return (this.form.psaConfirmed == 1 || this.form.salesConfirmed == 1) ? true : false\r\n    },\r\n    basicLock() {\r\n      return this.form.opConfirmed == 1 ? true : false\r\n    },\r\n    agreementLock() {\r\n      return this.form.accConfirmed == 1 ? true : false\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    queryStaffId() {\r\n      this.queryParams.queryStaffId = this.queryStaffId\r\n    },\r\n    \"form.belongTo\"() {\r\n      if (this.form.belongTo == this.form.followUp) {\r\n        this.form.followUp = 0\r\n        this.followUp = null\r\n      }\r\n    },\r\n    \"form.serviceTypeIds\"(n) {\r\n      this.loadCarrier()\r\n      let list = []\r\n      if (this.carrierList != undefined && n != null && n.includes(-1)) {\r\n        this.temCarrierList = this.carrierList\r\n        for (const v of this.carrierList) {\r\n          if (v.children != undefined && v.children.length > 0) {\r\n            for (const a of v.children) {\r\n              if (a.children != undefined && a.children.length > 0) {\r\n                for (const b of a.children) {\r\n                  if (this.form.carrierIds != null && this.form.carrierIds.includes(b.carrier.carrierId) && !this.carrierIds.includes(b.serviceTypeId)) {\r\n                    this.carrierIds.push(b.serviceTypeId)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      if (this.carrierList != undefined && n != null && !n.includes(-1)) {\r\n        for (const c of this.carrierList) {\r\n          if (n != null && n != undefined) {\r\n            for (const s of n) {\r\n              if (c.serviceTypeId == s) {\r\n                list.push(c)\r\n              }\r\n              if (c.children != undefined && c.children.length > 0) {\r\n                for (const ch of c.children) {\r\n                  if (ch.serviceTypeId == s) {\r\n                    list.push(ch)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.temCarrierList = list\r\n        if (this.temCarrierList.length > 0) {\r\n          for (const v of this.temCarrierList) {\r\n            if (v.children != undefined && v.children.length > 0) {\r\n              for (const a of v.children) {\r\n                if (this.form.carrierIds != null && this.form.carrierIds.includes(a.carrier.carrierId) && !this.carrierIds.includes(a.serviceTypeId)) {\r\n                  this.carrierIds.push(a.serviceTypeId)\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    form() {\r\n      if (this.form.salesConfirmed == 1 || this.form.accConfirmed == 1 || this.form.psaConfirmed == 1 || this.form.opConfirmed == 1) {\r\n        this.isLock = true\r\n      } else {\r\n        this.isLock = false\r\n      }\r\n    },\r\n    localStandard(v) {\r\n      this.handleTermChange()\r\n    },\r\n    \"form.receiveTerm\"(v) {\r\n      this.handleTermChange()\r\n    },\r\n    \"form.payTerm\"(v) {\r\n      this.handleTermChange()\r\n    },\r\n    \"form.receiveWay\"(v) {\r\n      this.handleTermChange()\r\n    },\r\n    \"form.payWay\"(v) {\r\n      this.handleTermChange()\r\n    }\r\n  },\r\n  created() {\r\n    this.getList().then(() => {\r\n      this.loadBusinesses()\r\n      this.loadCarrier()\r\n      this.loadSales()\r\n    })\r\n  },\r\n  methods: {\r\n    parseTime,\r\n    handleTermChange(v) {\r\n      if (this.form.receiveWay === \"月结\" || this.form.payWay === \"月结\") {\r\n        if (this.roleClient == 1 || this.roleRich == 1) {\r\n          this.description = (this.form.receiveStandard ? this.form.receiveStandard : \"\") + (this.form.receiveTerm ? (this.form.receiveTerm.substring(0, 1) === \"-\" ? (\"的下个月\" + this.form.receiveTerm.substring(1, 3) + \"号之后\") : (\"的下个月\" + this.form.receiveTerm.substring(1, 3) + \"号之前\")) : \"前\")\r\n        } else {\r\n          this.description = (this.form.payStandard ? this.form.payStandard : \"\") + (this.form.payTerm ? (this.form.payTerm.substring(0, 1) === \"-\" ? (\"的下个月\" + this.form.payTerm.substring(1, 3) + \"号之后\") : (\"的下个月\" + this.form.payTerm.substring(1, 3) + \"号之前\")) : \"前\")\r\n        }\r\n      } else {\r\n        if (this.roleClient == 1 || this.roleRich == 1) {\r\n          this.description = (this.form.receiveStandard ? this.form.receiveStandard : \"\") + (this.form.receiveTerm ? (this.form.receiveTerm.substring(0, 1) === \"-\" ? this.form.receiveTerm.substring(1, 3) + \"天前\" : \"后\" + this.form.receiveTerm.substring(1, 3) + \"天内\") : \"前\")\r\n        } else {\r\n          this.description = (this.form.payStandard ? this.form.payStandard : \"\") + (this.form.payTerm ? (this.form.payTerm.substring(0, 1) === \"-\" ? this.form.payTerm.substring(1, 3) + \"天前\" : \"后\" + this.form.payTerm.substring(1, 3) + \"天内\") : \"前\")\r\n        }\r\n      }\r\n    },\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        if (checkRole([\"Operator\"])) {\r\n          store.dispatch(\"getSalesList\").then(() => {\r\n            this.belongList = this.$store.state.data.salesList\r\n          })\r\n        } else {\r\n          store.dispatch(\"getSalesListC\").then(() => {\r\n            this.belongList = this.$store.state.data.salesList\r\n          })\r\n        }\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        store.dispatch(\"getBusinessesList\").then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    loadCarrier() {\r\n      if (this.$store.state.data.serviceTypeCarriers.length == 0 || this.$store.state.data.redisList.serviceTypeCarriers) {\r\n        store.dispatch(\"getServiceTypeCarriersList\").then(() => {\r\n          this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n        })\r\n      } else {\r\n        this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n      }\r\n    },\r\n    // 查重\r\n    querySame() {\r\n      // 初始化请求数据,特别注意deleteStatus设置未1,后端会对这个值的数据做重复校验\r\n      let data = {\r\n        cargoTypeIds: [],\r\n        locationDepartureIds: [],\r\n        locationDestinationIds: [],\r\n        lineDepartureIds: [],\r\n        lineDestinationIds: [],\r\n        companyShortName: this.form.companyShortName,\r\n        companyLocalName: this.form.companyLocalName,\r\n        serviceTypeIds: this.form.serviceTypeIds,\r\n        roleTypeId: this.roleTypeId,\r\n        roleRich: this.roleRich ? this.roleRich : null,\r\n        roleClient: this.roleClient ? this.roleClient : null,\r\n        roleSupplier: this.roleSupplier ? this.roleSupplier : null,\r\n        roleSupport: this.roleSupport ? this.roleSupport : null,\r\n        belongTo: this.form.belongTo,\r\n        followUp: this.form.followUp,\r\n        deleteStatus: 1\r\n      }\r\n      getInfoByStaffId(this.$store.state.user.sid).then(response => {\r\n        data.cargoTypeIds = response.cargoTypeIds\r\n        data.locationDepartureIds = response.locationDepartureIds\r\n        data.locationDestinationIds = response.locationDestinationIds\r\n        data.lineDepartureIds = response.lineDepartureIds\r\n        data.lineDestinationIds = response.lineDestinationIds\r\n      })\r\n      // 公司所属\r\n      if (data.belongTo == null) {\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == this.$store.state.user.sid) {\r\n                      data.belongTo = this.$store.state.user.sid\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          querySame(data).then(response => {\r\n            let res = response.data\r\n            if (res != undefined) {\r\n              let newRoleType = this.roleRich ? \"瑞旗分支\" : this.roleClient ? \"客户\" : this.roleSupplier ? \"供应商\" : this.roleSupport ? \"支持\" : \"\"\r\n              let oldRoleType = res.roleRich == 1 ? \"瑞旗分支\" : res.roleClient == 1 ? \"客户\" : res.roleSupplier == 1 ? \"供应商\" : res.roleSupport == 1 ? \"支持\" : \"\"\r\n              this.$confirm(res.deleteStatus == 0 ? \"此公司已存在，角色为\" + oldRoleType + \"，新增角色为\" + newRoleType + \",是否确认新增\" : \"存在重复数据，但已删除，是否重新读取\", \"提示\", {\r\n                confirmButtonText: \"确定\",\r\n                cancelButtonText: \"取消\",\r\n                type: \"warning\",\r\n                customClass: \"modal-confirm\"\r\n              }).then(() => {\r\n                res.deleteStatus = 0\r\n                this.form = res\r\n                this.form.roleTypeId = this.roleTypeId\r\n                if (this.belongList != undefined) {\r\n                  for (const a of this.belongList) {\r\n                    if (a.children != undefined) {\r\n                      for (const b of a.children) {\r\n                        if (b.children != undefined) {\r\n                          for (const c of b.children) {\r\n                            if (c.staffId == response.data.belongTo) {\r\n                              this.belongTo = c.deptId\r\n                            }\r\n                            if (c.staffId == response.data.followUp) {\r\n                              this.followUp = c.deptId\r\n                            }\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n                this.form.roleIds = response.roleIds\r\n                this.form.serviceTypeIds = response.serviceTypeIds\r\n                this.form.cargoTypeIds = response.cargoTypeIds\r\n                this.form.lineDepartureIds = response.lineDepartureIds\r\n                this.form.locationDepartureIds = response.locationDepartureIds\r\n                this.form.lineDestinationIds = response.lineDestinationIds\r\n                this.form.locationDestinationIds = response.locationDestinationIds\r\n                this.form.carrierIds = response.carrierIds\r\n                // this.form.organizationIds = response.companyOrganizationIds\r\n                this.form.organizationIds = response.organizationIds\r\n                this.locationOptions = response.locationOptions\r\n                this.openCompany = true\r\n                this.title = \"修改公司信息\"\r\n                this.loading = false\r\n              })\r\n            }\r\n\r\n            // 错误处理,弹出提示后点击确定发送请求更新公司信息\r\n            if (response.msg.toString().indexOf(\"Error\") > -1) {\r\n              this.$confirm(response.msg, \"提示\", {\r\n                confirmButtonText: \"确定\",\r\n                cancelButtonText: \"取消\",\r\n                type: \"warning\",\r\n                customClass: \"modal-confirm\"\r\n              }).then(() => {\r\n                let data = {\r\n                  messageOwner: 1,\r\n                  messageType: 3,\r\n                  messageFrom: null,\r\n                  messageTitle: this.$store.state.user.name.split(\" \")[1] + \"请求更新公司\",\r\n                  messageContent: response.msg\r\n                }\r\n                addMessage(data).then(response => {\r\n                  this.$message({\r\n                    type: \"success\",\r\n                    message: \"已发送请求!\"\r\n                  })\r\n                })\r\n              })\r\n            }\r\n\r\n            // 新增验证通过(通过将请求数据中的deleteStatus设置未0)\r\n            if (response.msg.toString().indexOf(\"Success\") > -1) {\r\n              this.$confirm(\"不存在重复的公司简称，是否确定新增客户？\", \"提示\", {\r\n                confirmButtonText: \"确定\",\r\n                cancelButtonText: \"取消\",\r\n                type: \"warning\",\r\n                customClass: \"modal-confirm\"\r\n              }).then(() => {\r\n                data.deleteStatus = 0\r\n                // 真正开始新增\r\n                querySame(data).then(response => {\r\n                  if (response.data) {\r\n                    this.$message.success(\"添加成功\")\r\n                    this.form = response.data\r\n                    this.form.roleTypeId = this.roleTypeId\r\n                    if (this.belongList != undefined) {\r\n                      for (const a of this.belongList) {\r\n                        if (a.children != undefined) {\r\n                          for (const b of a.children) {\r\n                            if (b.children != undefined) {\r\n                              for (const c of b.children) {\r\n                                if (c.staffId == response.data.belongTo) {\r\n                                  this.belongTo = c.deptId\r\n                                }\r\n                                if (c.staffId == response.data.followUp) {\r\n                                  this.followUp = c.deptId\r\n                                }\r\n                              }\r\n                            }\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n                    this.form.roleIds = response.roleIds\r\n                    this.form.serviceTypeIds = response.serviceTypeIds\r\n                    this.form.cargoTypeIds = response.cargoTypeIds\r\n                    this.form.lineDepartureIds = response.lineDepartureIds\r\n                    this.form.locationDepartureIds = response.locationDepartureIds\r\n                    this.form.lineDestinationIds = response.lineDestinationIds\r\n                    this.form.locationDestinationIds = response.locationDestinationIds\r\n                    this.form.carrierIds = response.carrierIds\r\n                    // this.form.organizationIds = response.companyOrganizationIds\r\n                    this.form.organizationIds = response.organizationIds\r\n                    this.locationOptions = response.locationOptions\r\n                    this.openCompany = true\r\n                    this.title = \"修改公司信息\"\r\n                    this.loading = false\r\n                  }\r\n                })\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /** 查询公司列表 */\r\n    async getList() {\r\n      this.loading = true\r\n      await listCompany({\r\n        ...this.queryParams,\r\n        permissionLevel: this.$store.state.user.permissionLevelList.C\r\n      }).then(response => {\r\n        this.companyList = response.rows\r\n        if (!isNaN(response.total)) {\r\n          this.total = response.total\r\n        }\r\n        this.loading = false\r\n      })\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    },\r\n    validateCompanyShortName(rule, value, callback) {\r\n\r\n      // 检查 value 是否为空或非字符串\r\n      if (value || typeof value === \"string\") {\r\n        // 检查是否包含多个中横线\r\n        const hyphenCount = value.split(\"-\").length - 1 // 通过 split 分割来统计中横线数量\r\n        if (hyphenCount > 1) {\r\n          return callback(new Error(\"只能包含一个中横线\"))\r\n        }\r\n      }\r\n      // 验证通过\r\n      callback()\r\n    },\r\n    carrierNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (!node.carrier || (node.carrier.carrierLocalName == null && node.carrier.carrierEnName == null)) {\r\n        l = node.serviceLocalName + \" \" + node.serviceEnName + \",\" + pinyin.getFullChars(node.serviceLocalName)\r\n      } else {\r\n        l = (node.carrier.carrierIntlCode != null ? node.carrier.carrierIntlCode : \"\") + \" \" + (node.carrier.carrierEnName != null ? node.carrier.carrierEnName : \"\") + \" \" + (node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : \"\") + \",\" + pinyin.getFullChars((node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : \"\"))\r\n      }\r\n      return {\r\n        id: node.serviceTypeId,\r\n        label: l,\r\n        children: node.children\r\n      }\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.openCompany = false\r\n      this.openAccount = false\r\n      this.openStaff = false\r\n      this.openCommunication = false\r\n      this.openAgreement = false\r\n      this.openBlackList = false\r\n      this.add = false\r\n      this.merge = false\r\n      this.edit = false\r\n      this.reset()\r\n      this.belongTo = null\r\n      this.followUp = null\r\n      // this.getList()\r\n    },\r\n    accountCancel() {\r\n      this.openAccount = false\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.belongTo = null\r\n      this.followUp = null\r\n      this.carrierIds = []\r\n      this.form = {\r\n        belongTo: null,\r\n        followUp: null,\r\n        carrierIds: null,\r\n        locationDetail: null,\r\n        companyId: null,\r\n        companyIntlCode: null,\r\n        companyShortName: null,\r\n        companyEnShortName: null,\r\n        companyLocalName: null,\r\n        companyEnName: null,\r\n        companyTaxCode: null,\r\n        roleIds: null,\r\n        serviceTypeIds: null,\r\n        cargoTypeIds: null,\r\n        locationDepartureIds: null,\r\n        lineDepartureIds: null,\r\n        locationDestinationIds: null,\r\n        lineDestinationIds: null,\r\n        organizationIds: null,\r\n        companyPortIds: null,\r\n        roleTypeId: this.roleTypeId,\r\n        roleRich: this.roleRich ? this.roleRich : null,\r\n        roleClient: this.roleClient ? this.roleClient : null,\r\n        roleSupplier: this.roleSupplier ? this.roleSupplier : null,\r\n        roleSupport: this.roleSupport ? this.roleSupport : null,\r\n        locationId: null,\r\n        salesConfirmed: 0,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        psaConfirmed: 0,\r\n        psaConfirmedId: null,\r\n        psaConfirmedName: null,\r\n        psaConfirmedDate: null,\r\n        accConfirmed: 0,\r\n        accConfirmedId: null,\r\n        accConfirmedName: null,\r\n        accConfirmedDate: null,\r\n        opConfirmed: 0,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        remark: null,\r\n        rsPaymentTitles: []\r\n      }\r\n      this.carrierIds = []\r\n      this.companyRow = null\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.queryBStaffId = null\r\n      this.queryParams.locationId = null\r\n      this.queryParams.serviceTypeIds = null\r\n      this.queryParams.cargoTypeIds = null\r\n      this.queryParams.locationDepartureIds = null\r\n      this.queryParams.lineDepartureIds = null\r\n      this.queryParams.locationDestinationIds = null\r\n      this.queryParams.lineDestinationIds = null\r\n      this.queryParams.organizationIds = null\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.companyId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n      this.selectTwo = selection.length != 2\r\n      if (selection.length == 1) {\r\n        this.setCompanyInfo(selection[0])\r\n      }\r\n      if (selection.length == 2) {\r\n        this.mergeList = selection\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.edit = false\r\n      this.form.belongTo = null\r\n      this.openCompany = true\r\n      this.title = \"新增公司信息\"\r\n      this.form.serviceTypeIds = []\r\n      this.temCarrierList = this.carrierList\r\n      if (this.temCarrierList != undefined && this.form.serviceTypeIds != null) {\r\n        for (const a of this.temCarrierList) {\r\n          // this.form.serviceTypeIds.push(a.serviceTypeId)\r\n        }\r\n      }\r\n      this.add = true\r\n      if (this.belongList != undefined) {\r\n        for (const a of this.belongList) {\r\n          if (a.children != undefined) {\r\n            for (const b of a.children) {\r\n              if (b.children != undefined) {\r\n                for (const c of b.children) {\r\n                  if (c.staffId == this.$store.state.user.sid) {\r\n                    this.belongTo = c.deptId\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      // 币种默认为人民币RMB\r\n      this.form.agreementCurrencyCode = \"RMB\"\r\n      this.showConfirm = false\r\n    },\r\n    getReturn(row) {\r\n      if (row.key == \"contactor\") {\r\n        this.setCompanyInfo(row.value)\r\n        getConnect(row.value.companyId).then(response => {\r\n          this.staffList = response.staffList\r\n          this.openStaff = true\r\n        })\r\n      }\r\n      if (row.key == \"communication\") {\r\n        this.setCompanyInfo(row.value)\r\n        listCommunication({sqdCompanyId: row.value.companyId}).then(response => {\r\n          this.communicationList = response.rows\r\n          this.ctotle = response.totle\r\n          this.openCommunication = true\r\n        })\r\n      }\r\n      if (row.key == \"agreement\") {\r\n        this.setCompanyInfo(row.value)\r\n        listAgreementrecord({sqdCompanyId: row.value.companyId}).then(response => {\r\n          this.agreementList = response.rows\r\n          this.atotle = response.totle\r\n          this.openAgreement = true\r\n        })\r\n      }\r\n      if (row.key == \"account\") {\r\n        this.setCompanyInfo(row.value)\r\n        getBank(row.value.companyId).then(response => {\r\n          this.accountList = response.accountList\r\n          this.openAccount = true\r\n        })\r\n      }\r\n    },\r\n    // 设置客户信息\r\n    setCompanyInfo(row) {\r\n      this.companyInfo = {\r\n        companyId: row.companyId != null ? row.companyId : \"\",\r\n        companyTaxCode: row.companyTaxCode != null ? row.companyTaxCode : \"\",\r\n        companyShortName: row.companyShortName != null ? row.companyShortName : \"\",\r\n        companyEnShortName: row.companyEnShortName != null ? row.companyEnShortName : \"\",\r\n        companyLocalName: row.companyLocalName != null ? row.companyLocalName : \"\",\r\n        companyEnName: row.companyEnName != null ? row.companyEnName : \"\",\r\n        companyLocation: row.locationId != null ? row.locationId : \"\",\r\n        companyIntlCode: row.companyIntlCode != null ? row.companyIntlCode : \"\",\r\n        mainStaffId: row.staff != null ? row.staff.staffId : \"\"\r\n      }\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      this.loading = true\r\n      this.edit = true\r\n      this.companyRow = row\r\n      this.add = auth.hasPermi(\"system:client:distribute\")\r\n      const companyId = row.companyId || this.ids\r\n      this.showConfirm = true\r\n      getCompany(companyId).then(response => {\r\n        this.form = response.data\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.belongTo) {\r\n                      this.belongTo = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.followUp) {\r\n                      this.followUp = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.form.roleIds = response.roleIds\r\n        this.form.serviceTypeIds = response.serviceTypeIds\r\n        this.form.cargoTypeIds = response.cargoTypeIds\r\n        this.form.lineDepartureIds = response.lineDepartureIds\r\n        this.form.locationDepartureIds = response.locationDepartureIds\r\n        this.form.lineDestinationIds = response.lineDestinationIds\r\n        this.form.locationDestinationIds = response.locationDestinationIds\r\n        this.form.carrierIds = response.carrierIds\r\n        // this.form.organizationIds = response.companyOrganizationIds\r\n        this.form.organizationIds = response.organizationIds\r\n        this.locationOptions = response.locationOptions\r\n        this.openCompany = true\r\n        this.title = \"修改公司信息\"\r\n        // this.form.rsPaymentTitles = response.data.rsPaymentTitle ? response.data.rsPaymentTitle.split(',') : []\r\n        this.loading = false\r\n\r\n        if (response.data.agreementStartDate !== null && response.data.agreementEndDate !== null) {\r\n          this.form.agreementDateRange = []\r\n          this.form.agreementDateRange.push(response.data.agreementStartDate)\r\n          this.form.agreementDateRange.push(response.data.agreementEndDate)\r\n        }\r\n\r\n        const formatter = new Intl.NumberFormat(\"en-US\", {\r\n          style: \"decimal\",\r\n          minimumFractionDigits: 2,\r\n          maximumFractionDigits: 2\r\n        })\r\n        if ((this.roleClient == 1 || this.roleRich == 1) && response.data.receiveCreditLimit !== null) {\r\n          this.form.receiveCreditLimit = response.data.receiveCreditLimit.toLocaleString(\"en-US\")\r\n          this.form.receiveCreditLimit = this.form.receiveCreditLimit ? this.form.receiveCreditLimit.replace(/,/g, \"\") : 0\r\n          this.form.receiveCreditLimit = formatter.format(this.form.receiveCreditLimit)\r\n        }\r\n        if ((this.roleSupplier == 1 || this.roleSupport == 1) && response.data.payCreditLimit !== null) {\r\n          this.form.payCreditLimit = response.data.payCreditLimit.toLocaleString(\"en-US\")\r\n          this.form.payCreditLimit = this.form.payCreditLimit ? this.form.payCreditLimit.replace(/,/g, \"\") : 0\r\n          this.form.payCreditLimit = formatter.format(this.form.payCreditLimit)\r\n        }\r\n\r\n      })\r\n\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        // 收付款描述\r\n        if (this.roleClient == 1 || this.roleRich == 1) {\r\n          this.form.sqdReceiveTermsSummary = this.description\r\n        }\r\n        if (this.roleClient == 1 || this.roleRich == 1) {\r\n          this.form.sqdPayTermsSummary = this.description\r\n        }\r\n        // 公司类型\r\n        this.form.roleRich = this.roleRich ? this.roleRich : null\r\n        this.form.roleClient = this.roleClient ? this.roleClient : null\r\n        this.form.roleSupplier = this.roleSupplier ? this.roleSupplier : null\r\n        this.form.roleSupport = this.roleSupport ? this.roleSupport : null\r\n        // 转换额度显示\r\n        this.form.receiveCreditLimit = this.form.receiveCreditLimit ? String(this.form.receiveCreditLimit).replace(/,/g, \"\") : 0\r\n        this.form.payCreditLimit = this.form.payCreditLimit ? String(this.form.payCreditLimit).replace(/,/g, \"\") : 0\r\n        if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {\r\n          this.form.agreementStartDate = this.form.agreementDateRange[0]\r\n          this.form.agreementEndDate = this.form.agreementDateRange[1]\r\n        }\r\n        // 判断日期开始时间是否大于结束时间\r\n        let startDate = new Date(this.form.agreementStartDate)\r\n        let endDate = new Date(this.form.agreementEndDate)\r\n        if (startDate > endDate) {\r\n          Message({\r\n            message: \"协议开始时间不能大于结束时间\",\r\n            type: \"error\"\r\n          })\r\n          return\r\n        }\r\n        // 信用周期要为整数\r\n        if (this.form.creditCycleMonth != null && this.form.creditCycleMonth % 1 != 0) {\r\n          Message({\r\n            message: \"信用周期必须为整数\",\r\n            type: \"error\"\r\n          })\r\n          return\r\n        }\r\n        if (valid) {\r\n          if (this.form.companyId != null) {\r\n            updateCompany(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.openCompany = false\r\n              this.getList()\r\n            })\r\n            this.reset()\r\n          } else {\r\n            this.$message.info(\"未查重，先对简称查重吧\")\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      row.roleRich = this.roleRich ? this.roleRich : null\r\n      row.roleClient = this.roleClient ? this.roleClient : null\r\n      row.roleSupplier = this.roleSupplier ? this.roleSupplier : null\r\n      row.roleSupport = this.roleSupport ? this.roleSupport : null\r\n\r\n      const companyIds = row.companyId || this.ids\r\n      this.$confirm(\"是否确认删除公司编号为\\\"\" + companyIds + \"\\\"的数据项？\", \"提示\", {customClass: \"modal-confirm\"}).then(function () {\r\n        return delCompany(row)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    handleBlackList() {\r\n      this.openBlackList = true\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/company/export\", {\r\n        ...this.queryParams\r\n      }, `company_${new Date().getTime()}.xlsx`)\r\n    },\r\n    queryLocationId(val) {\r\n      this.queryParams.locationId = val\r\n      this.handleQuery()\r\n    },\r\n    getLocationId(val) {\r\n      this.form.locationId = val\r\n    },\r\n    getSourceId(val) {\r\n      this.form.sourceId = val.sourceShortName\r\n    },\r\n    getOrganizationIds(val) {\r\n      this.form.organizationIds = val\r\n    },\r\n    getServiceTypeIds(val) {\r\n      this.form.serviceTypeIds = val\r\n      if (val == undefined) {\r\n        this.carrierIds = null\r\n        this.form.carrierIds = null\r\n      }\r\n    },\r\n    queryServiceTypeIds(val) {\r\n      this.queryParams.serviceTypeIds = val\r\n      this.handleQuery()\r\n    },\r\n    getCargoTypeIds(val) {\r\n      this.form.cargoTypeIds = val\r\n    },\r\n    queryCargoTypeIds(val) {\r\n      this.queryParams.cargoTypeIds = val\r\n      this.handleQuery()\r\n    },\r\n    getCompanyRoleIds(val) {\r\n      this.form.roleIds = val\r\n    },\r\n    queryCompanyRoleIds(val) {\r\n      this.queryParams.roleIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLocationDepartureIds(val) {\r\n      this.queryParams.locationDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    getLineDepartureIds(val) {\r\n      this.form.lineDepartureIds = val\r\n    },\r\n    getLocationDestinationIds(val) {\r\n      this.form.locationDestinationIds = val\r\n    },\r\n    queryLineDepartureIds(val) {\r\n      this.queryParams.lineDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    getLocationDepartureIds(val) {\r\n      this.form.locationDepartureIds = val\r\n    },\r\n    queryLocationDestinationIds(val) {\r\n      this.queryParams.locationDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    getLineDestinationIds(val) {\r\n      this.form.lineDestinationIds = val\r\n    },\r\n    queryLineDestinationIds(val) {\r\n      this.queryParams.lineDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    handleSelectBelongTo(node) {\r\n      this.form.belongTo = node.staffId\r\n    },\r\n    handleDeselectBelongTo(v) {\r\n      if (v == undefined) {\r\n        this.form.belongTo = 0\r\n        this.belongTo = null\r\n      }\r\n    },\r\n    handleSelectFollowUp(node) {\r\n      this.form.followUp = node.staffId\r\n    },\r\n    handleDeselectFollowUp(value) {\r\n      if (value == undefined) {\r\n        this.form.followUp = 0\r\n        this.followUp = null\r\n      }\r\n    },\r\n    handleSelectBFStaffId(node) {\r\n      this.queryParams.queryBFStaffId = node.staffId\r\n      this.handleQuery()\r\n    },\r\n    cleanBFStaffId(val) {\r\n      if (val == undefined) {\r\n        this.queryParams.queryBFStaffId = null\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    cleanBStaffId(val) {\r\n      if (val == undefined) {\r\n        this.queryParams.queryBStaffId = null\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    handleSelectBStaffId(node) {\r\n      this.queryParams.queryBStaffId = node.staffId\r\n      getInfoByStaffId(node.staffId).then(response => {\r\n        this.queryParams.cargoTypeIds = response.cargoTypeIds\r\n        this.queryParams.serviceTypeIds = response.serviceTypeIds\r\n        this.queryParams.locationDepartureIds = response.locationDepartureIds\r\n        this.queryParams.lineDepartureIds = response.lineDepartureIds\r\n        this.queryParams.locationDestinationIds = response.locationDestinationIds\r\n        this.queryParams.lineDestinationIds = response.lineDestinationIds\r\n        this.locationOptions = response.locationOptions\r\n        this.handleQuery()\r\n      })\r\n    },\r\n    handleSelectCarrierIds(node) {\r\n      this.form.carrierIds.push(node.carrier.carrierId)\r\n    },\r\n    handleSelectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds.push(node.carrier.carrierId)\r\n      this.handleQuery()\r\n    },\r\n    handleDeselectCarrierIds(node) {\r\n      this.form.carrierIds = this.form.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n    },\r\n    handleDeselectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds = this.queryParams.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n      this.handleQuery()\r\n    },\r\n    refreshColumns() {\r\n      this.refreshTable = false\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true\r\n      })\r\n    },\r\n    handleMergeCompany() {\r\n      this.merge = true\r\n    },\r\n    handleMerge(save, del) {\r\n      mergeCompany(save, del).then(response => {\r\n        this.$message.success(response.msg)\r\n        this.merge = false\r\n        this.getList()\r\n      })\r\n    },\r\n    deptLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.deptConfirmed = this.form.deptConfirmed == 0 ? 1 : 0\r\n        this.form.deptConfirmedId = this.$store.state.user.sid\r\n        this.form.deptConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.deptConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    financeLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.financeConfirmed = this.form.financeConfirmed == 0 ? 1 : 0\r\n        this.form.financeConfirmedId = this.$store.state.user.sid\r\n        this.form.financeConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.financeConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    psaLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.psaConfirmed = this.form.psaConfirmed == 0 ? 1 : 0\r\n        this.form.psaConfirmedId = this.$store.state.user.sid\r\n        this.form.psaConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.psaConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    opLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.opConfirmed = this.form.opConfirmed === 0 ? 1 : 0\r\n        this.form.opConfirmedId = this.$store.state.user.sid\r\n        this.form.opConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.opConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    getCurrencyCode(val) {\r\n      this.form.agreementCurrencyCode = val\r\n    },\r\n    getcreditLevel(val) {\r\n      this.form.creditLevel = val\r\n    },\r\n    getRsPaymentTitle(val) {\r\n      this.form.rsPaymentTitle = val\r\n    },\r\n    async updateCompany(form) {\r\n      // TODO 只更新有修改的字段\r\n      // console.log(Object.assign(this.form, form))\r\n\r\n      // this.form.creditLimit = this.form.creditLimit.replace(/,/g, '')\r\n\r\n      if (this.roleClient == 1 || this.roleRich == 1) {\r\n        this.form.receiveCreditLimit = this.form.receiveCreditLimit ? this.form.receiveCreditLimit.replace(/,/g, \"\") : 0\r\n      }\r\n      if (this.roleSupplier == 1 || this.roleSupport == 1) {\r\n        this.form.payCreditLimit = this.form.payCreditLimit ? this.form.payCreditLimit.replace(/,/g, \"\") : 0\r\n      }\r\n\r\n      if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {\r\n        this.form.agreementStartDate = this.form.agreementDateRange[0]\r\n        this.form.agreementEndDate = this.form.agreementDateRange[1]\r\n      }\r\n\r\n      await updateCompany(form)\r\n      this.$modal.msgSuccess(\"修改成功\")\r\n      let response = await getCompany(form.companyId)\r\n      // 更新信息\r\n      // this.$nextTick(() => {\r\n      this.form = response.data\r\n      if (this.belongList != undefined) {\r\n        for (const a of this.belongList) {\r\n          if (a.children != undefined) {\r\n            for (const b of a.children) {\r\n              if (b.children != undefined) {\r\n                for (const c of b.children) {\r\n                  if (c.staffId == response.data.belongTo) {\r\n                    this.belongTo = c.deptId\r\n                  }\r\n                  if (c.staffId == response.data.followUp) {\r\n                    this.followUp = c.deptId\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      this.form.roleIds = response.roleIds\r\n      this.form.serviceTypeIds = response.serviceTypeIds\r\n      this.form.cargoTypeIds = response.cargoTypeIds\r\n      this.form.lineDepartureIds = response.lineDepartureIds\r\n      this.form.locationDepartureIds = response.locationDepartureIds\r\n      this.form.lineDestinationIds = response.lineDestinationIds\r\n      this.form.locationDestinationIds = response.locationDestinationIds\r\n      this.form.carrierIds = response.carrierIds\r\n      // this.form.organizationIds = response.companyOrganizationIds\r\n      this.form.organizationIds = response.organizationIds\r\n      this.locationOptions = response.locationOptions\r\n      this.openCompany = true\r\n      this.title = \"修改公司信息\"\r\n      this.loading = false\r\n\r\n      const formatter = new Intl.NumberFormat(\"en-US\", {\r\n        style: \"decimal\",\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      })\r\n      /* this.form.creditLimit = response.data.creditLimit.toLocaleString('en-US')\r\n      this.form.creditLimit = this.form.creditLimit.replace(/,/g, '')\r\n      this.form.creditLimit = formatter.format(this.form.creditLimit) */\r\n\r\n      if (this.roleClient == 1 || this.roleRich == 1) {\r\n        this.form.receiveCreditLimit = response.data.receiveCreditLimit.toLocaleString(\"en-US\")\r\n        this.form.receiveCreditLimit = this.form.receiveCreditLimit ? String(this.form.receiveCreditLimit).replace(/,/g, \"\") : 0\r\n        this.form.receiveCreditLimit = formatter.format(this.form.receiveCreditLimit)\r\n      }\r\n      if (this.roleSupplier == 1 || this.roleSupport == 1) {\r\n        this.form.payCreditLimit = response.data.payCreditLimit.toLocaleString(\"en-US\")\r\n        this.form.payCreditLimit = this.form.payCreditLimit ? String(this.form.payCreditLimit).replace(/,/g, \"\") : 0\r\n        this.form.payCreditLimit = formatter.format(this.form.payCreditLimit)\r\n      }\r\n\r\n      // 更新日期\r\n      this.form.agreementDateRange = (response.data.agreementStartDate != null && response.data.agreementEndDate != null) ? [response.data.agreementStartDate, response.data.agreementEndDate] : []\r\n      /* if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {\r\n        this.form.agreementStartDate = this.form.agreementDateRange[0]\r\n        this.form.agreementEndDate = this.form.agreementDateRange[1]\r\n      } */\r\n\r\n      // })\r\n      this.form.salesConfirmed = response.data.salesConfirmed\r\n      this.form.psaConfirmed = response.data.psaConfirmed\r\n      this.form.opConfirmed = response.data.opConfirmed\r\n      this.form.accConfirmed = response.data.accConfirmed\r\n    },\r\n    formatCreditLimit() {\r\n      if (this.form.receiveCreditLimit != null || this.form.payCreditLimit != null) {\r\n        const formatter = new Intl.NumberFormat(\"en-US\", {\r\n          style: \"decimal\",\r\n          minimumFractionDigits: 2,\r\n          maximumFractionDigits: 2\r\n        })\r\n        if (this.roleClient == 1) {\r\n          this.form.receiveCreditLimit = String(this.form.receiveCreditLimit).replace(/,/g, \"\")\r\n          this.form.receiveCreditLimit = formatter.format(this.form.receiveCreditLimit)\r\n        }\r\n        if (this.roleSupplier == 1) {\r\n          this.form.payCreditLimit = String(this.form.payCreditLimit).replace(/,/g, \"\")\r\n          this.form.payCreditLimit = formatter.format(this.form.payCreditLimit)\r\n        }\r\n\r\n      }\r\n    },\r\n    formatDisplayCreditLimit(value) {\r\n      const formatter = new Intl.NumberFormat(\"en-US\", {\r\n        notation: \"compact\"\r\n      })\r\n      return formatter.format(value)\r\n    },\r\n    changeDate() {\r\n      this.$forceUpdate()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n\r\n<style scoped lang=\"scss\">\r\n.sss {\r\n  //padding-left: 5px !important;\r\n  padding-right: 15px !important;\r\n  width: 100%;\r\n}\r\n\r\n.creditLimit {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n.limit {\r\n  flex: 3\r\n}\r\n\r\n.currency {\r\n  flex: 1\r\n}\r\n\r\n.confirm {\r\n  display: flex;\r\n  flex-direction: row;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.box-card {\r\n  width: 20%;\r\n  flex-wrap: wrap;\r\n}\r\n</style>\r\n"]}]}