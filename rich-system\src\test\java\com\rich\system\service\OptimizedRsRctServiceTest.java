package com.rich.system.service;

import com.rich.common.core.domain.entity.RsRct;
import com.rich.common.core.domain.entity.RsServiceInstances;
import com.rich.common.core.domain.entity.RsOpSeaFcl;
import com.rich.common.core.domain.entity.RsCharge;
import com.rich.system.service.context.ServiceProcessingContext;
import com.rich.system.service.processor.ServiceProcessorFactory;
import com.rich.system.service.monitor.PerformanceMonitor;
import com.rich.system.service.monitor.TransactionMonitor;
import com.rich.system.mapper.RsServiceInstancesMapper;
import com.rich.system.mapper.OptimizedRsChargeMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 优化的操作单服务测试类
 * 验证优化后的性能和数据一致性
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class OptimizedRsRctServiceTest {
    
    @MockBean
    private OptimizedRsRctService optimizedRsRctService;
    
    @MockBean
    private ServiceProcessorFactory processorFactory;
    
    @MockBean
    private PerformanceMonitor performanceMonitor;
    
    @MockBean
    private TransactionMonitor transactionMonitor;
    
    @MockBean
    private RsServiceInstancesMapper rsServiceInstancesMapper;
    
    @MockBean
    private OptimizedRsChargeMapper optimizedRsChargeMapper;
    
    private RsRct testRsRct;
    private List<RsServiceInstances> existingInstances;
    
    @BeforeEach
    void setUp() {
        // 准备测试数据
        testRsRct = createTestRsRct();
        existingInstances = createExistingServiceInstances();
        
        // 模拟依赖服务的行为
        when(rsServiceInstancesMapper.selectRsServiceInstancesByRctId(anyLong()))
                .thenReturn(existingInstances);
    }
    
    @Test
    @DisplayName("测试优化服务处理的基本功能")
    void testSaveAllServicesOptimized_BasicFunctionality() {
        // Given
        when(optimizedRsRctService.saveAllServicesOptimized(any(RsRct.class)))
                .thenReturn(testRsRct);
        
        // When
        RsRct result = optimizedRsRctService.saveAllServicesOptimized(testRsRct);
        
        // Then
        assertNotNull(result);
        assertEquals(testRsRct.getRctId(), result.getRctId());
        assertEquals(testRsRct.getRctNo(), result.getRctNo());
        
        // 验证方法被调用
        verify(optimizedRsRctService, times(1)).saveAllServicesOptimized(testRsRct);
    }
    
    @Test
    @DisplayName("测试服务处理上下文初始化")
    void testServiceProcessingContextInitialization() {
        // Given
        when(optimizedRsRctService.prepareServiceContext(any(RsRct.class)))
                .thenReturn(ServiceProcessingContext.initialize(testRsRct, existingInstances));
        
        // When
        ServiceProcessingContext context = optimizedRsRctService.prepareServiceContext(testRsRct);
        
        // Then
        assertNotNull(context);
        assertEquals(testRsRct.getRctId(), context.getRsRct().getRctId());
        assertFalse(context.getExistingServiceInstances().isEmpty());
        
        // 验证统计信息初始化
        assertNotNull(context.getStatistics());
        assertEquals(0, context.getStatistics().getProcessedServices().get());
    }
    
    @Test
    @DisplayName("测试关键服务处理")
    void testCriticalServicesProcessing() {
        // Given
        ServiceProcessingContext context = ServiceProcessingContext.initialize(testRsRct, existingInstances);
        
        // When
        assertDoesNotThrow(() -> {
            optimizedRsRctService.processCriticalServices(testRsRct, context);
        });
        
        // Then
        verify(optimizedRsRctService, times(1)).processCriticalServices(testRsRct, context);
    }
    
    @Test
    @DisplayName("测试高优先级服务处理")
    void testHighPriorityServicesProcessing() {
        // Given
        ServiceProcessingContext context = ServiceProcessingContext.initialize(testRsRct, existingInstances);
        
        // When
        assertDoesNotThrow(() -> {
            optimizedRsRctService.processHighPriorityServices(testRsRct, context);
        });
        
        // Then
        verify(optimizedRsRctService, times(1)).processHighPriorityServices(testRsRct, context);
    }
    
    @Test
    @DisplayName("测试批量费用记录处理")
    void testBatchChargeProcessing() {
        // Given
        List<RsCharge> charges = createTestCharges();
        when(optimizedRsChargeMapper.batchUpsertCharges(anyList())).thenReturn(charges.size());
        
        // When
        int result = optimizedRsChargeMapper.batchUpsertCharges(charges);
        
        // Then
        assertEquals(charges.size(), result);
        verify(optimizedRsChargeMapper, times(1)).batchUpsertCharges(charges);
    }
    
    @Test
    @DisplayName("测试孤立费用记录清理")
    void testOrphanedChargesCleanup() {
        // Given
        Long rctId = testRsRct.getRctId();
        Set<Long> activeServiceIds = Set.of(1L, 2L, 3L);
        when(optimizedRsChargeMapper.deleteOrphanedCharges(rctId, activeServiceIds)).thenReturn(5);
        
        // When
        int deletedCount = optimizedRsChargeMapper.deleteOrphanedCharges(rctId, activeServiceIds);
        
        // Then
        assertEquals(5, deletedCount);
        verify(optimizedRsChargeMapper, times(1)).deleteOrphanedCharges(rctId, activeServiceIds);
    }
    
    @Test
    @DisplayName("测试异步处理性能")
    void testAsyncProcessingPerformance() {
        // Given
        ServiceProcessingContext context = ServiceProcessingContext.initialize(testRsRct, existingInstances);
        
        // When
        long startTime = System.currentTimeMillis();
        
        // 模拟异步处理
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                optimizedRsRctService.processLowPriorityServicesAsync(testRsRct, context);
            } catch (Exception e) {
                fail("异步处理失败: " + e.getMessage());
            }
        });
        
        // Then
        assertDoesNotThrow(() -> {
            future.get(10, TimeUnit.SECONDS); // 10秒超时
        });
        
        long duration = System.currentTimeMillis() - startTime;
        assertTrue(duration < 10000, "异步处理应该在10秒内完成");
    }
    
    @Test
    @DisplayName("测试错误处理和恢复")
    void testErrorHandlingAndRecovery() {
        // Given
        ServiceProcessingContext context = ServiceProcessingContext.initialize(testRsRct, existingInstances);
        RuntimeException testException = new RuntimeException("测试异常");
        
        // When
        context.recordError("TestService", testException);
        
        // Then
        assertTrue(context.hasErrors());
        assertEquals(1, context.getErrors().size());
        assertTrue(context.getErrorSummary().contains("TestService"));
        assertTrue(context.getErrorSummary().contains("测试异常"));
    }
    
    @Test
    @DisplayName("测试性能监控指标")
    void testPerformanceMetrics() {
        // Given
        ServiceProcessingContext context = ServiceProcessingContext.initialize(testRsRct, existingInstances);
        
        // When
        context.addServiceToStatistics("TestService1");
        context.addServiceToStatistics("TestService2");
        context.setServiceStatus("TestService1", ServiceProcessingContext.ServiceProcessingStatus.COMPLETED);
        context.setServiceStatus("TestService2", ServiceProcessingContext.ServiceProcessingStatus.FAILED);
        context.complete();
        
        // Then
        assertEquals(2, context.getStatistics().getTotalServices().get());
        assertEquals(1, context.getStatistics().getProcessedServices().get());
        assertEquals(1, context.getStatistics().getFailedServices().get());
        assertEquals(50.0, context.getStatistics().getSuccessRate(), 0.01);
        assertTrue(context.getStatistics().getDuration() >= 0);
    }
    
    /**
     * 创建测试用的操作单数据
     */
    private RsRct createTestRsRct() {
        RsRct rsRct = new RsRct();
        rsRct.setRctId(12345L);
        rsRct.setRctNo("TEST-RCT-001");
        
        // 添加海运整箱服务
        List<RsOpSeaFcl> seaFclList = new ArrayList<>();
        RsOpSeaFcl seaFcl = new RsOpSeaFcl();
        seaFcl.setSeaId(1L);
        seaFcl.setServiceId(100L);
        seaFcl.setRsChargeList(createTestCharges());
        seaFclList.add(seaFcl);
        rsRct.setRsOpSeaFclList(seaFclList);
        
        return rsRct;
    }
    
    /**
     * 创建现有服务实例
     */
    private List<RsServiceInstances> createExistingServiceInstances() {
        List<RsServiceInstances> instances = new ArrayList<>();
        
        RsServiceInstances instance1 = new RsServiceInstances();
        instance1.setServiceId(100L);
        instance1.setServiceBelongTo("SeaFCL");
        instance1.setServiceTypeId(1L);
        instance1.setRctId(12345L);
        instances.add(instance1);
        
        RsServiceInstances instance2 = new RsServiceInstances();
        instance2.setServiceId(101L);
        instance2.setServiceBelongTo("SeaLCL");
        instance2.setServiceTypeId(2L);
        instance2.setRctId(12345L);
        instances.add(instance2);
        
        return instances;
    }
    
    /**
     * 创建测试费用记录
     */
    private List<RsCharge> createTestCharges() {
        List<RsCharge> charges = new ArrayList<>();
        
        for (int i = 1; i <= 5; i++) {
            RsCharge charge = new RsCharge();
            charge.setChargeId((long) i);
            charge.setServiceId(100L);
            charge.setSqdRctId(12345L);
            charge.setDnAmount(1000.0 * i);
            charge.setSubtotal(1000.0 * i);
            charges.add(charge);
        }
        
        return charges;
    }
}
