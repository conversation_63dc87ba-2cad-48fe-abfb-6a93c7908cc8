<template>
  <el-dialog
    v-dialogDrag
    v-dialogDragWidth
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    :title="title"
    :visible.sync="dialogVisible"
    append-to-body
    width="80%"
    @close="handleClose"
  >
    <el-form ref="form" :model="formData" :rules="rules" class="edit" label-width="80px" size="mini">
      <!-- 第一行 - 基本信息 -->
      <el-row :gutter="10">
        <el-col :span="4">
          <el-form-item label="发票流水号">
            <el-input v-model="formData.invoiceCodeNo" :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                      placeholder="发票流水号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="进销标志">
            <el-select v-model="formData.saleBuy" :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                       placeholder="进销标志"
                       style="width: 100%"
            >
              <el-option label="销项" value="sale"/>
              <el-option label="进项" value="buy"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="发票性质">
            <!-- 主营业务收入/非主营收入/营业外收入/成本/费用 -->
            <el-select v-model="formData.taxClass" :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                       placeholder="发票性质"
                       style="width: 100%"
            >
              <el-option label="主营业务收入" value="主营业务收入"/>
              <el-option label="非主营业务收入" value="非主营业务收入"/>
              <el-option label="营业外收入" value="营业外收入"/>
              <el-option label="成本" value="成本"/>
              <el-option label="费用" value="费用"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="发票类型">
            <el-select v-model="formData.invoiceType" :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                       placeholder="发票类型" style="width: 100%"
            >
              <el-option label="增值税专用发票" value="增值税专用发票"/>
              <el-option label="普通发票" value="普通发票"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="合并发票">
            <el-row>
              <el-col :span="8">
                <el-checkbox v-model="formData.mergeInvoice" :class="{'disable-form': isDisabled()}"
                             :disabled="isDisabled()" false-label="0"
                             true-label="1"
                >√
                </el-checkbox>
              </el-col>
              <el-col :span="16">
                <el-button size="mini" type="primary" @click="searchAvailableInvoice">检索可用发票</el-button>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="发票号码">
            <el-input v-model="formData.invoiceOfficalNo" :class="{'disable-form': isDisabled()}"
                      :disabled="isDisabled()"
                      placeholder="发票号码"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行 - 公司和账户信息 -->
      <el-row :gutter="10">
        <el-col :span="16">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="所属公司">
                <el-input v-model="formData.invoiceBelongsTo" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="所属公司"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="我司账户">
                <tree-select :flat="false" :multiple="false"
                             :pass="formData.richBankCode" :placeholder="'银行账户'"
                             :type="'companyAccount'" @return="formData.richBankCode=$event"
                             :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                             @returnData="handleRichBankCodeChange"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="对方公司">
                <company-select :load-options="companyList"
                                :multiple="false" :no-parent="true"
                                :pass="formData.cooperatorId" :placeholder="''"
                                @return="formData.cooperatorId=$event"
                                :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="对方账户">
                <el-select v-model="formData.cooperatorBankCode" :class="{'disable-form': isDisabled()}"
                           :disabled="isDisabled()"
                           placeholder="对方账户" style="width: 100%"
                           @change="handleBankAccountChange" @click.native="fetchCompanyBankAccounts"
                >
                  <el-option v-for="item in availableBankList" :key="item.bankAccId"
                             :label="item.bankAccCode+ '('+item.bankAccount+')'" :value="item.bankAccCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="发票抬头">
                <el-input v-model="formData.richCompanyTitle" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="我司发票抬头"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发票抬头">
                <el-input v-model="formData.cooperatorCompanyTitle" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="对方发票抬头"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>

        <el-col :span="8">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-input v-model="formData.officalChargeNameSummary" :minrows="3" :rows="2"
                        placeholder="发票项目汇总" type="textarea"
                        :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
              />
            </el-col>
            <el-col :span="12">
              <el-input v-model="formData.relatedOrderNo" :minrows="3" :rows="2" placeholder="相关订单号"
                        type="textarea"
                        :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
              />
            </el-col>
          </el-row>
        </el-col>
      </el-row>

      <!-- 第四行 - 税号信息 -->
      <el-row :gutter="10">
        <el-col :span="16">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="税号">
                <el-input v-model="formData.richVatSerialNo" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="我司纳税人识别号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="税号">
                <el-input v-model="formData.cooperatorVatSerialNo" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()" placeholder="对方纳税人识别号"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="银行全称">
                <el-input v-model="formData.richBankFullname" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="我司银行全称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="银行全称">
                <el-input v-model="formData.cooperatorBankFullname" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="对方银行全称"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="银行账号">
                <el-input v-model="formData.richBankAccount" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="我司账号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="银行账号">
                <el-input v-model="formData.cooperatorBankAccount" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="对方账号"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>

        <el-col :span="4">
          <el-input v-model="formData.invoiceRemark" :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                    :minrows="4" :rows="4"
                    placeholder="备注" type="textarea"
          />
        </el-col>

        <el-col :span="4">
          <el-col>
            <el-form-item label="期望支付日">
              <el-date-picker v-model="formData.expectedPayDate"
                              clearable
                              placeholder="期望支付日期"
                              style="width: 100%;"
                              type="date"
                              value-format="yyyy-MM-dd"
                              :class="{'disable-form': isDisabled()}"
                              :disabled="isDisabled()"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="批复支付日">
              <el-date-picker v-model="formData.approvedPayDate"
                              clearable
                              placeholder="批复支付日期"
                              style="width: 100%;"
                              type="date"
                              value-format="yyyy-MM-dd"
                              :class="{'disable-form': isDisabled()}"
                              :disabled="isDisabled()"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="实际支付日">
              <el-date-picker v-model="formData.actualPayDate"
                              clearable
                              placeholder="实际支付日期"
                              style="width: 100%;"
                              type="date"
                              value-format="yyyy-MM-dd"
                              :class="{'disable-form': isDisabled()}"
                              :disabled="isDisabled()"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-col>

      </el-row>

      <!-- 第七行 - 发票信息 -->
      <el-row :gutter="10">
        <el-col :span="4">
          <el-form-item label="发票汇率">
            <el-input v-model="formData.invoiceExchangeRate" :class="{'disable-form': isDisabled()}"
                      :disabled="isDisabled()"
                      placeholder="发票汇率"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="发票币种">
            <tree-select :pass="formData.invoiceCurrencyCode"
                         :placeholder="'币种'" :type="'currency'"
                         style="width: 100%" @return="formData.invoiceCurrencyCode=$event"
                         :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发票金额">
            <el-input v-model="formData.invoiceNetAmount" :class="{'disable-form': isDisabled()}"
                      :disabled="isDisabled()"
                      placeholder="金额计算公式"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="发票状态">
            <el-row>
              <el-col :offset="1" :span="8">
                <el-tag :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                        :type="getInvoiceStatusType(formData.invoiceStatus)"
                >{{ getInvoiceStatusText(formData.invoiceStatus) }}
                </el-tag>
              </el-col>
              <el-col :span="10">
                <el-popover
                  placement="top-start"
                  title="发票附件管理"
                  trigger="hover"
                  width="300"
                >
                  <div>
                    <file-upload
                      :class="isDisabled()?'disable-form':''"
                      :file-type="['pdf']"
                      :is-disabled="isDisabled()"
                      :is-tip-flex="true"
                      :value="formData.invoiceAttachment"
                      @input="formData.invoiceAttachment=$event"
                    />
                  </div>
                  <template #reference>
                    <el-button size="mini" type="primary">
                      <i class="el-icon-upload"></i>
                      发票附件
                      <i v-if="formData.invoiceAttachment && formData.invoiceAttachment.trim()" class="el-icon-check"
                         style="color: #67C23A; margin-left: 5px;"></i>
                    </el-button>
                  </template>
                </el-popover>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="报税月份">
            <el-input v-model="formData.belongsToMonth" :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                      class="yellow-bg" placeholder="2025/7/31"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider></el-divider>

      <!-- 发票明细表格 -->
      <el-row :gutter="10">
        <el-col :span="24">
          <el-table :data="formData.rsCharges" border size="mini" style="width: 100%">
            <el-table-column align="center" type="selection" width="35"/>
            <el-table-column align="center" label="账单编号" prop="debitNoteId" width="100"/>
            <el-table-column align="center" label="RCT号" prop="sqdRctNo"/>
            <el-table-column align="center" label="所属服务" prop="serviceLocalName" width="100">
              <template #default="scope">
                {{ scope.row.sqdServiceTypeId == 0 ? "客户应收" : scope.row.serviceLocalName }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="费用名称" prop="chargeName" width="100"/>
            <el-table-column align="center" label="备注" prop="chargeRemark" width="100"/>
            <el-table-column align="center" label="收付标志" prop="isReceivingOrPaying" width="80">
              <template #default="scope">{{ scope.row.isReceivingOrPaying == 0 ? "应收" : "应付" }}</template>
            </el-table-column>
            <el-table-column align="center" label="报价币种" prop="quoteCurrency" width="80"/>
            <el-table-column align="center" label="单价" prop="dnUnitRate" width="80"/>
            <el-table-column align="center" label="数量" prop="dnAmount" width="80"/>
            <el-table-column align="center" label="单位" prop="dnUnitCode" width="80"/>
            <el-table-column align="center" label="结算汇率" prop="basicCurrencyRate" width="80"/>
            <el-table-column align="center" label="结算币种" prop="dnCurrencyCode" width="80"/>
            <el-table-column align="center" label="税率" prop="dutyRate" width="80"/>
            <el-table-column align="center" label="含税小计" prop="subtotal" width="100"/>
            <el-table-column align="center" label="开票项目名称" prop="invoicingItem" width="150">
              <template #default="scope">
                <treeselect v-model="scope.row.invoicingItem"
                            :class="{'disable-form': isDisabled()}"
                            :default-expand-level="1"
                            :disable-branch-nodes="false"
                            :disabled="isDisabled()"
                            :normalizer="invoicingItemNormalizer"
                            :options="invoicingItemOptions"
                            :show-count="true"
                            :z-index="9999"
                            append-to-body
                            placeholder="开票项目名称"
                            searchable
                >
                  <div slot="value-label" slot-scope="{node}">
                    {{ node.raw.invoicingItemName || node.label }}
                  </div>
                  <label slot="option-label"
                         slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                         :class="labelClassName">
                    {{ node.label }}
                    <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                  </label>
                </treeselect>
              </template>
            </el-table-column>
            <el-table-column align="center" label="税收编码" prop="taxCode" width="100"/>
          </el-table>
        </el-col>
      </el-row>

      <!-- 操作按钮组 -->
      <el-row :gutter="10" style="margin-top: 10px;">
        <el-col :span="3">
          <el-button icon="el-icon-check" size="mini" type="primary">√默认对冲</el-button>
          <div>已选总计:</div>
        </el-col>
        <el-col :span="3">
          <el-button size="mini" type="primary">智选</el-button>
          <div>未选总计:</div>
        </el-col>
        <el-col :span="3">
          <el-button size="mini" type="primary">反选</el-button>
          <div>全部总计:</div>
        </el-col>
        <el-col :span="3">
          <el-button size="mini" type="primary">申请开票</el-button>
          <div>申请人+时间</div>
        </el-col>
        <el-col :span="3">
          <el-button size="mini" type="primary">信息审核</el-button>
          <div>确认人+时间</div>
        </el-col>
        <el-col :span="3">
          <el-button size="mini" type="success">发送开票</el-button>
          <div>开票人+时间</div>
        </el-col>
        <el-col :span="3">
          <el-button size="mini" type="warning">打印</el-button>
        </el-col>
        <el-col :span="3">
          <el-button size="mini" type="info">报税锁定</el-button>
          <div>报税人+时间</div>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ formData.invoiceId ? "更 新" : "确 定" }}</el-button>
        <el-button @click="handleCancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import {getCompany} from "@/api/system/company"
import {listAccount} from "@/api/system/account"
import FileUpload from "@/components/FileUpload"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

// 防抖函数
function debounce(fn, delay) {
  let timer = null
  return function () {
    const context = this
    const args = arguments
    clearTimeout(timer)
    timer = setTimeout(function () {
      fn.apply(context, args)
    }, delay)
  }
}

export default {
  name: "VatinvoiceDialog",
  components: {
    FileUpload,
    Treeselect
  },
  props: {
    // 是否显示对话框
    visible: {
      type: Boolean,
      default: false
    },
    // 标题
    title: {
      type: String,
      default: ""
    },
    // 表单数据
    form: {
      type: Object,
      default: () => ({})
    },
    // 表单验证规则
    rules: {
      type: Object,
      default: () => ({})
    },
    // 发票明细列表
    invoiceItems: {
      type: Array,
      default: () => []
    },
    companyList: {
      type: Array,
      default: () => []
    },
    // 银行账户列表
    bankAccountList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 内部对话框可见性状态
      dialogVisible: false,
      // 表单数据的副本
      formData: {},
      // 发票明细列表的副本
      invoiceItemList: [],
      // 防抖后的获取公司信息方法
      debouncedFetchCompanyInfo: null,
      // 公司银行账户列表
      companyBankList: [],
      // 开票项目选项数据
      invoicingItemOptions: [
        {
          id: "1",
          invoicingItemName: "经纪代理服务",
          children: [
            {id: "1-1", invoicingItemName: "代理运费"},
            {id: "1-2", invoicingItemName: "国际货物运输代理服务费"},
            {id: "1-3", invoicingItemName: "代理港杂费"},
            {id: "1-4", invoicingItemName: "国际货物运输代理服务"},
            {id: "1-5", invoicingItemName: "代理报关服务费"},
            {id: "1-6", invoicingItemName: "代理服务费"},
            {id: "1-7", invoicingItemName: "代理报关费"},
            {id: "1-8", invoicingItemName: "代理拖车费"},
            {id: "1-9", invoicingItemName: "国际货物运输代理服务-代理运费"},
            {id: "1-10", invoicingItemName: "代理国内运费"},
            {id: "1-11", invoicingItemName: "国际货物运输代理海运费"},
            {id: "1-12", invoicingItemName: "代理运费费"},
            {id: "1-13", invoicingItemName: "运输代理费"},
            {id: "1-14", invoicingItemName: "货物运输代理服务费"},
            {id: "1-15", invoicingItemName: "国际货物运输代理港杂费"},
            {id: "1-16", invoicingItemName: "国际货物运输代理运费"},
            {id: "1-17", invoicingItemName: "货物运输代理费"},
            {id: "1-18", invoicingItemName: "国际货物运输代理费"},
            {id: "1-19", invoicingItemName: "代理杂费"},
            {id: "1-20", invoicingItemName: "代理文件费"},
            {id: "1-21", invoicingItemName: "代理设备交接单费用"},
            {id: "1-22", invoicingItemName: "代理舱单申报费"},
            {id: "1-23", invoicingItemName: "代理操作费"},
            {id: "1-24", invoicingItemName: "代理封条费"},
            {id: "1-25", invoicingItemName: "代理码头操作费"},
            {id: "1-26", invoicingItemName: "代理电放费"},
            {id: "1-27", invoicingItemName: "代理核重费"}
          ]
        }
      ]
    }
  },
  computed: {
    // 可用的银行账户列表：优先使用companyBankList，为空时使用传入的bankAccountList
    availableBankList() {
      return this.companyBankList.length > 0 ? this.companyBankList : this.bankAccountList
    }
  },

  created() {
    // 创建防抖版本的fetchCompanyInfo方法，设置300ms延迟
    this.debouncedFetchCompanyInfo = debounce(this.fetchCompanyInfo, 300)
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          // 当对话框显示时，复制传入的数据
          this.formData = JSON.parse(JSON.stringify(this.form))
          this.invoiceItemList = JSON.parse(JSON.stringify(this.invoiceItems))

          // 确保发票附件字段存在
          if (!this.formData.invoiceAttachment) {
            this.formData.invoiceAttachment = ""
          }

          // 如果所属公司已有值，自动填充相关信息
          if (this.formData.invoiceBelongsTo) {
            this.autoFillCompanyInfo(this.formData.invoiceBelongsTo)
          }
        }
      },
      immediate: true
    },
    // 监听对方公司ID变化
    "formData.cooperatorId": {
      handler(newVal, oldVal) {
        // 只有当值真正变化时才触发查询
        if (newVal && newVal !== oldVal) {
          this.debouncedFetchCompanyInfo(newVal)
        }
      }
    },
    // 监听所属公司变化，自动填充我司发票抬头和税号
    "formData.invoiceBelongsTo": {
      handler(newVal) {
        if (newVal) {
          this.autoFillCompanyInfo(newVal)
        }
      }
    }
  },
  methods: {
    // 开票项目数据标准化函数
    invoicingItemNormalizer(node) {
      const normalized = {
        id: node.invoicingItemName, // 使用invoicingItemName作为id
        label: node.invoicingItemName,
        invoicingItemName: node.invoicingItemName
      }

      if (node.children && node.children.length > 0) {
        normalized.children = node.children.map(child => this.invoicingItemNormalizer(child))
      }

      return normalized
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit("submit", this.formData)
        }
      })
    },
    // 取消按钮
    handleCancel() {
      this.$emit("cancel")
      this.handleClose()
    },
    // 关闭对话框
    handleClose() {
      this.$emit("update:visible", false)
    },
    searchAvailableInvoice() {
      console.log("检索可用发票")
    },

    // 获取公司银行账户列表
    fetchCompanyBankAccounts() {
      // 检查是否有选择对方公司
      if (!this.formData.cooperatorId) {
        this.$message.warning("请先选择对方公司")
        return
      }

      // 调用API获取该公司的银行账户
      listAccount({belongToCompany: this.formData.cooperatorId}).then(response => {
        if (response.code === 200) {
          this.companyBankList = response.rows || []
          // 如果没有账户，显示提示
          if (this.companyBankList.length === 0) {
            this.$message.info("该公司没有银行账户记录")
          }
        }
      }).catch(error => {
        console.error("获取公司银行账户失败", error)
        this.$message.error("获取公司银行账户失败")
      })
    },

    // 处理银行账户选择变化
    handleBankAccountChange(bankCode) {
      // 根据选择的bankCode找到对应的银行账户信息
      const selectedAccount = this.availableBankList.find(item => item.bankCode === bankCode)

      if (selectedAccount) {
        // 自动填充银行全称和银行账号
        this.formData.cooperatorBankFullname = selectedAccount.bankName || ""
        this.formData.cooperatorBankAccount = selectedAccount.bankAccount || ""
      }
    },
    // 获取公司信息
    fetchCompanyInfo(companyId) {
      getCompany(companyId).then(response => {
        if (response.code === 200) {
          const companyInfo = response.data
          // 更新表单中与对方公司相关的字段
          this.formData.cooperatorCompanyTitle = companyInfo.companyLocalName || ""
          this.formData.cooperatorVatSerialNo = companyInfo.taxNo || ""
        }
      }).catch(error => {
        console.error("获取公司信息失败", error)
      })
    },

    // 自动填充我司发票抬头和税号
    autoFillCompanyInfo(companyCode) {
      // 根据所属公司代码(GZRS/HKRS/SZRS/GZCF)自动填充我司发票抬头和税号
      const companyInfoMap = {
        "GZRS": {
          title: "广州瑞旗国际货运代理有限公司",
          taxNo: "91440101MA59UQXX7B"
        },
        "HKRS": {
          title: "香港瑞旗国际货运代理有限公司",
          taxNo: "HK12345678"
        },
        "SZRS": {
          title: "深圳市瑞旗国际货运代理有限公司",
          taxNo: "91440300MA5G9UB57Q"
        },
        "GZCF": {
          title: "广州正泽国际货运代理有限公司",
          taxNo: "91440101MA9XRGLH0F"
        }
      }

      // 获取对应公司的信息
      const companyInfo = companyInfoMap[companyCode]

      // 如果找到对应的公司信息，则填充表单
      if (companyInfo) {
        this.formData.richCompanyTitle = companyInfo.title
        this.formData.richVatSerialNo = companyInfo.taxNo
      }
    },

    // 判断表单项是否禁用
    isDisabled() {
      // 根据以下条件判断表单是否应该禁用：
      // 1. 如果发票状态为已开票
      if (this.formData.invoiceStatus === "1") {
        return true
      }

      // 2. 如果报税已锁定
      if (this.formData.taxLocked === "1") {
        return true
      }

      // 3. 如果已支付
      if (this.formData.actualPayDate) {
        return true
      }

      // 如果没有禁用条件，则表单可编辑
      return false
    },
    handleRichBankCodeChange(row) {
      this.formData.richBankFullname = row.bankName
      this.formData.richBankAccount = row.bankAccount
    },
    // 获取发票状态类型
    getInvoiceStatusType(status) {
      const statusMap = {
        "unissued": "info",
        "issued": "success",
        "applied": "warning",
        "canceled": "danger"
      }
      return statusMap[status] || "info"
    },
    // 获取发票状态文本
    getInvoiceStatusText(status) {
      const statusMap = {
        "unissued": "未开票",
        "issued": "已开票",
        "applied": "已申请",
        "canceled": "已作废"
      }
      return statusMap[status] || "未知"
    }
  }
}
</script>

<style scoped>
.yellow-bg {
  background-color: #fffbe6;
}

.disable-form {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.uploaded-file-preview {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.preview-title {
  font-weight: bold;
  color: #495057;
  margin-bottom: 8px;
  font-size: 14px;
}

.file-links {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.file-links .el-link {
  font-size: 12px;
}

/* treeselect组件样式 */
:deep(.vue-treeselect__menu) {
  z-index: 9999 !important;
  position: fixed !important;
}

:deep(.vue-treeselect__menu-container) {
  z-index: 9999 !important;
}

:deep(.vue-treeselect__dropdown) {
  z-index: 9999 !important;
}

/* 确保下拉框在表格之上 */
:deep(.vue-treeselect__menu-arrow) {
  z-index: 9999 !important;
}

:deep(.vue-treeselect__list) {
  z-index: 9999 !important;
}
</style>
