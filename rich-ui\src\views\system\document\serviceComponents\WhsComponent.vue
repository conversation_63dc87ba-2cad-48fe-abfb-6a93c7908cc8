<template>
  <div class="whs-component">
    <!--仓储-->
    <div v-for="(item, index) in whsServices" :key="`whs-${index}`" class="whs-item">
      <el-row>
        <el-col :span="18">
          <!--标题栏-->
          <div class="service-bar">
            <a
              :class="[
                'service-toggle-icon',
                getFold(item.serviceTypeId) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'
              ]"
            />
            <h3 class="service-title" @click="changeFold(item.serviceTypeId)">
              仓储-{{ item.serviceShortName }}
            </h3>
            <!--审核信息-->
            <audit
              v-if="auditInfo"
              :audit="true"
              :basic-info="getServiceInstance(item.serviceTypeId)"
              :disabled="disabled"
              :payable="getPayable(item.serviceTypeId)"
              :rs-charge-list="item.rsChargeList"
              @auditFee="auditCharge(item.serviceTypeId, $event)"
              @return="changeServiceObject(item.serviceTypeId, $event)"
            />
            <div class="outbound-plan-container">
              <a
                class="outbound-plan-link"
                target="_blank"
                @click="outboundPlan"
              >
                [出仓计划]
              </a>
            </div>
          </div>
        </el-col>
      </el-row>
      <!--内容区域-->
      <transition name="fade">
        <el-row
          v-if="getFold(item.serviceTypeId)"
          :gutter="10"
          class="service-content-area"
        >
          <!--服务信息栏-->
          <transition name="fade">
            <el-col v-if="branchInfo" :span="3" class="service-info-col">
              <el-form-item label="询价单号">
                <el-input
                  v-model="getServiceInstance(item.serviceTypeId).inquiryNo"
                  placeholder="询价单号"
                  @focus="generateFreight(8, item.serviceTypeId, getServiceObject(item.serviceTypeId))"
                />
              </el-form-item>

              <el-form-item v-if="!booking && branchInfo" label="供应商">
                <el-popover
                  :content="getSupplierEmail()"
                  placement="bottom"
                  trigger="hover"
                  width="200"
                >
                  <template #reference>
                    <el-input
                      :value="getServiceInstance().supplierName"
                      class="disable-form"
                      disabled
                    />
                  </template>
                </el-popover>
              </el-form-item>

              <el-form-item label="合约类型">
                <el-input
                  :value="getAgreementDisplay(item.serviceTypeId)"
                  class="disable-form"
                  disabled
                  placeholder="合约类型"
                />
              </el-form-item>

              <el-form-item label="业务须知">
                <el-input
                  v-model="getServiceInstance(item.serviceTypeId).inquiryNotice"
                  class="disable-form"
                  disabled
                  placeholder="业务须知"
                />
              </el-form-item>
            </el-col>
          </transition>

          <!--主表信息-->
          <transition name="fade">
            <el-col v-if="branchInfo" :span="3">
              <el-form-item label="商务单号">
                <el-row>
                  <el-col :span="20">
                    <el-input :value="form.psaNo" class="disable-form" disabled/>
                  </el-col>
                  <el-col :span="4">
                    <el-button
                      size="mini"
                      style="color: red"
                      type="text"
                      @click="psaBookingCancel"
                    >
                      取消
                    </el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
          </transition>

          <!--分支信息-->
          <transition name="fade">
            <el-col v-if="branchInfo" :span="3">
              <el-form-item label="入仓号">
                <el-input
                  v-model="getServiceObject(item.serviceTypeId).warehousingNo"
                  :class="psaVerify || disabled ? 'disable-form' : ''"
                  :disabled="psaVerify || disabled"
                  placeholder="入仓号"
                />
              </el-form-item>
            </el-col>
          </transition>

          <!--占位-->
          <el-col v-if="branchInfo" :span="9"></el-col>

          <!--物流进度-->
          <transition name="fade">
            <el-col v-if="logisticsInfo" :span="4">
              <logistics-progress
                :disabled="getFormDisable() || disabled || psaVerify"
                :logistics-progress-data="getServiceObject().rsOpLogList || []"
                :open-logistics-progress-list="true"
                :process-type="4"
                :service-type="80"
                @deleteItem="deleteLogItem(item.serviceTypeId, $event)"
                @return="updateLogList(item.serviceTypeId, $event)"
              />
            </el-col>
          </transition>

          <!--费用列表-->
          <el-col v-if="chargeInfo" :span="10.3">
            <charge-list
              :a-t-d="form.podEta"
              :charge-data="getServiceObject().rsChargeList || []"
              :company-list="companyList"
              :disabled="getFormDisable() || disabled"
              :is-receivable="false"
              :open-charge-list="true"
              :pay-detail-r-m-b="getServiceObject().payableRMB"
              :pay-detail-r-m-b-tax="getServiceObject().payableRMBTax"
              :pay-detail-u-s-d="getServiceObject().payableUSD"
              :pay-detail-u-s-d-tax="getServiceObject().payableUSDTax"
              :service-type-id="item.serviceTypeId"
              @copyFreight="copyFreight($event)"
              @deleteAll="deleteAllCharge(item.serviceTypeId)"
              @deleteItem="deleteChargeItem(item.serviceTypeId, $event)"
              @return="calculateCharge(item.serviceTypeId, $event, getServiceObject())"
            />
          </el-col>
        </el-row>
      </transition>
    </div>
  </div>
</template>

<script>
import Audit from "@/views/system/document/audit.vue"
import LogisticsProgress from "@/views/system/document/logisticsProgress.vue"
import ChargeList from "@/views/system/document/chargeList.vue"

export default {
  name: "WhsComponent",
  components: {
    Audit,
    LogisticsProgress,
    ChargeList
  },
  props: {
    // 码头与仓储服务数据集合
    whsServices: {
      type: [Array, Set],
      default: () => []
    },
    // 表单数据
    form: {
      type: Object,
      default: () => ({})
    },
    // 显示控制
    branchInfo: {
      type: Boolean,
      default: true
    },
    logisticsInfo: {
      type: Boolean,
      default: true
    },
    chargeInfo: {
      type: Boolean,
      default: true
    },
    auditInfo: {
      type: Boolean,
      default: false
    },
    // 状态控制
    disabled: {
      type: Boolean,
      default: false
    },
    booking: {
      type: Boolean,
      default: false
    },
    psaVerify: {
      type: Boolean,
      default: false
    },
    // 数据列表
    supplierList: {
      type: Array,
      default: () => []
    },
    companyList: {
      type: Array,
      default: () => []
    },
    // 新增的props，直接从父组件传入
    foldState: {
      type: Boolean,
      default: false
    },
    serviceInstance: {
      type: Object,
      default: () => ({})
    },
    serviceObject: {
      type: Object,
      default: () => ({})
    },
    formDisable: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 判断是否禁用状态
    isDisabled() {
      return this.disabled || this.psaVerify
    }
  },
  methods: {
    // 获取供应商邮箱
    getSupplierEmail() {
      const serviceInstance = this.getServiceInstance()
      if (!serviceInstance || !serviceInstance.supplierId) return ''

      const supplier = this.supplierList.find(v => v.companyId === serviceInstance.supplierId)
      return supplier ? supplier.staffEmail : ''
    },
    // 获取合约显示文本
    getAgreementDisplay() {
      const serviceInstance = this.getServiceInstance()
      if (!serviceInstance) return ''
      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo
    },
    // 事件转发给父组件
    changeFold(serviceTypeId) {
      this.$emit("changeFold", serviceTypeId)
    },
    getFold() {
      return this.foldState
    },
    getServiceInstance() {
      return this.serviceInstance || {}
    },
    getServiceObject() {
      return this.serviceObject || {}
    },
    getPayable() {
      return this.serviceObject && this.serviceObject.payable || null
    },
    getFormDisable() {
      return this.formDisable
    },
    changeServiceObject(serviceTypeId, serviceObject) {
      this.$emit("changeServiceObject", serviceTypeId, serviceObject)
    },
    auditCharge(serviceTypeId, event) {
      this.$emit("auditCharge", serviceTypeId, event)
    },
    generateFreight(type1, type2, item) {
      this.$emit("generateFreight", type1, type2, item)
    },
    psaBookingCancel() {
      this.$emit("psaBookingCancel")
    },
    copyFreight(event) {
      this.$emit("copyFreight", event)
    },
    calculateCharge(serviceTypeId, event, item) {
      this.$emit("calculateCharge", serviceTypeId, event, item)
    },
    outboundPlan() {
      this.$emit("outboundPlan")
    },
    // 物流进度相关方法
    deleteLogItem(serviceTypeId, event) {
      const serviceObject = this.getServiceObject()
      if (serviceObject && serviceObject.rsOpLogList) {
        serviceObject.rsOpLogList = serviceObject.rsOpLogList.filter(item => item !== event)
      }
    },
    updateLogList(serviceTypeId, event) {
      const serviceObject = this.getServiceObject()
      if (serviceObject) {
        serviceObject.rsOpLogList = event
      }
    },
    // 费用列表相关方法
    deleteAllCharge(serviceTypeId) {
      const serviceObject = this.getServiceObject()
      if (serviceObject) {
        serviceObject.rsChargeList = []
      }
    },
    deleteChargeItem(serviceTypeId, event) {
      const serviceObject = this.getServiceObject()
      if (serviceObject && serviceObject.rsChargeList) {
        serviceObject.rsChargeList = serviceObject.rsChargeList.filter(item => item !== event)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/op-document';

// WHS组件特定样式
.whs-component {
  width: 100%;

  .whs-item {
    margin-bottom: 10px;
  }

  .service-bar {
    display: flex;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%;

    .service-toggle-icon {
      cursor: pointer;
      margin-right: 5px;
    }

    .service-title {
      margin: 0;
      width: 250px;
      text-align: left;
      cursor: pointer;
    }

    .outbound-plan-container {
      margin-left: auto;

      .outbound-plan-link {
        color: blue;
        padding: 0;
        margin-left: 5px;
        text-decoration: none;
        cursor: pointer;
      }
    }
  }

  .service-content-area {
    margin-bottom: 15px;
    display: -webkit-box;

    .service-info-col {
      .el-form-item {
        margin-bottom: 10px;
      }
    }
  }

  // 优化表单输入框样式
  .el-input {
    width: 100%;
  }

  // 优化日期选择器样式
  .el-date-picker {
    width: 100%;
  }
}
</style>
