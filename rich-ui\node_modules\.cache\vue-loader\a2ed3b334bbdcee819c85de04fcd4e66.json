{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\BillOfLadingInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\BillOfLadingInfo.vue", "mtime": 1754646305897}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgTG9naXN0aWNzUHJvZ3Jlc3MgZnJvbSAiLi4vbG9naXN0aWNzUHJvZ3Jlc3MiOw0KaW1wb3J0IENoYXJnZUxpc3QgZnJvbSAiLi4vY2hhcmdlTGlzdCI7DQppbXBvcnQgVHJlZVNlbGVjdCBmcm9tICJAL2NvbXBvbmVudHMvVHJlZVNlbGVjdCI7DQppbXBvcnQgTG9jYXRpb25TZWxlY3QgZnJvbSAiQC9jb21wb25lbnRzL0xvY2F0aW9uU2VsZWN0IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiQmlsbE9mTGFkaW5nSW5mbyIsDQogIGNvbXBvbmVudHM6IHsNCiAgICBMb2dpc3RpY3NQcm9ncmVzcywNCiAgICBDaGFyZ2VMaXN0LA0KICAgIFRyZWVTZWxlY3QsDQogICAgTG9jYXRpb25TZWxlY3QNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICBib29raW5nTWVzc2FnZUZvcm06IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgZGVmYXVsdDogbnVsbA0KICAgIH0sDQogICAgb3BlbkJvb2tpbmdNZXNzYWdlOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIGJvb2tpbmdNZXNzYWdlU3RhdHVzOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAiPFVOSz4iDQogICAgfSwNCiAgICBib29raW5nTWVzc2FnZUxpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogW10NCiAgICB9LA0KICAgIHJzQ2xpZW50TWVzc2FnZTogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgcmVxdWlyZWQ6IHRydWUNCiAgICB9LA0KICAgIGZvcm06IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIHJlcXVpcmVkOiB0cnVlDQogICAgfSwNCiAgICBkaXNhYmxlZDogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICBwc2FWZXJpZnk6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgYXVkaXRJbmZvOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0sDQogICAgYnJhbmNoSW5mbzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUNCiAgICB9LA0KICAgIGxvZ2lzdGljc0luZm86IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiB0cnVlDQogICAgfSwNCiAgICBjaGFyZ2VJbmZvOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0sDQogICAgY29tcGFueUxpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogKCkgPT4gW10NCiAgICB9LA0KICAgIHJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVSTUI6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDANCiAgICB9LA0KICAgIHJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVVU0Q6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDANCiAgICB9LA0KICAgIHJzQ2xpZW50TWVzc2FnZVBheWFibGVSTUI6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDANCiAgICB9LA0KICAgIHJzQ2xpZW50TWVzc2FnZVBheWFibGVVU0Q6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDANCiAgICB9LA0KICAgIHJzQ2xpZW50TWVzc2FnZVByb2ZpdFJNQjogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogMA0KICAgIH0sDQogICAgcnNDbGllbnRNZXNzYWdlUHJvZml0VVNEOiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAwDQogICAgfSwNCiAgICByc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4Uk1COiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAwDQogICAgfSwNCiAgICByc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4VVNEOiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAwDQogICAgfSwNCiAgICByc0NsaWVudE1lc3NhZ2VQYXlhYmxlVGF4Uk1COiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAwDQogICAgfSwNCiAgICByc0NsaWVudE1lc3NhZ2VQYXlhYmxlVGF4VVNEOiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAwDQogICAgfSwNCiAgICByc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhSTUI6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDANCiAgICB9LA0KICAgIHJzQ2xpZW50TWVzc2FnZVByb2ZpdFRheFVTRDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogMA0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdmlzaWJsZTogdHJ1ZSwNCiAgICAgIGJvb2tpbmdNZXNzYWdlVGl0bGU6ICLmj5DljZXkv6Hmga8iLA0KICAgICAgcHNhQm9va2luZ1NlbGVjdERhdGE6IHsNCiAgICAgICAgbG9jYXRpb25PcHRpb25zOiBbXQ0KICAgICAgfSwNCiAgICAgIHJzQ2xpZW50TWVzc2FnZUZvcm1EaXNhYmxlOiBmYWxzZSwNCiAgICAgIGZpbGVPcHRpb25zOiBbDQogICAgICAgIHtmaWxlOiAn5pON5L2c5Y2VJywgbGluazogJ2dldE9wQmlsbCcsIHRlbXBsYXRlTGlzdDogWyfmlbTmn5wnLCAn5pWj6LSnJywgJ+epuui/kCcsICflhbbku5YnXX0sDQogICAgICAgIHtmaWxlOiAn5o+Q5Y2VJywgbGluazogJ2dldEJpbGxPZkxhZGluZycsIHRlbXBsYXRlTGlzdDogWyflpZfmiZPmj5DljZUnLCAn55S15pS+5o+Q5Y2VJ119LA0KICAgICAgICB7DQogICAgICAgICAgZmlsZTogJ+i0ueeUqOa4heWNlScsDQogICAgICAgICAgbGluazogJ2dldENoYXJnZUxpc3RCaWxsJywNCiAgICAgICAgICB0ZW1wbGF0ZUxpc3Q6IFsnQ04t5bm/5bee55Ge5peXW+aLm+ihjFVTRCvlt6XooYxSTUJdJywgJ0NOLeW5v+W3nueRnuaXl1tVU0QtPlJNQl0nLCAnRU4t5bm/5bee55Ge5peXW+aLm+ihjFVTRF0nLCAnRU4t5bm/5bee55Ge5peXW1JNQi0+VVNEXScsICdFTi0g55Ge5peX6aaZ5riv6LSm5oi3W0hTQkMgUk1CLT5VU0RdJywgJ0VOLSDpppnmuK/nkZ7ml5dbSFNCQ10nLCAnQ04t5bm/5bee5q2j5rO9W+aLm+ihjFVTRCtSTUJdJywgJ0NOLeW5v+W3nuato+azvVtVU0QtPlJNQl0nXQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgcGF5V2F5T3B0aW9uczogWw0KICAgICAgICB7bGFiZWw6ICfpooTku5gnLCB2YWx1ZTogJ0ZSRUlHSFRQIFJFUEFJRCd9LA0KICAgICAgICB7bGFiZWw6ICfliLDku5gnLCB2YWx1ZTogJ0ZSRUlHSFRQIENPTExFQ1QnfQ0KICAgICAgXQ0KICAgIH07DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgcnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UoKSB7DQogICAgICByZXR1cm4gdGhpcy5yc0NsaWVudE1lc3NhZ2UgfHwge307DQogICAgfSwNCiAgICBvcENvbmZpcm1lZE5hbWUoKSB7DQogICAgICByZXR1cm4gdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5kbk9wQ29uZmlybWVkTmFtZSB8fCAnJzsNCiAgICB9LA0KICAgIG9wQ29uZmlybWVkRGF0ZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmRuT3BDb25maXJtZWREYXRlIHx8ICcnOw0KICAgIH0sDQogICAgc2FsZXNDb25maXJtZWROYW1lKCkgew0KICAgICAgcmV0dXJuIHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuZG5TYWxlc0NvbmZpcm1lZE5hbWUgfHwgJyc7DQogICAgfSwNCiAgICBzYWxlc0NvbmZpcm1lZERhdGUoKSB7DQogICAgICByZXR1cm4gdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5kblNhbGVzQ29uZmlybWVkRGF0ZSB8fCAnJzsNCiAgICB9LA0KICAgIGNsaWVudENvbmZpcm1lZE5hbWUoKSB7DQogICAgICByZXR1cm4gdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5kbkNsaWVudENvbmZpcm1lZE5hbWUgfHwgJyc7DQogICAgfSwNCiAgICBjbGllbnRDb25maXJtZWREYXRlKCkgew0KICAgICAgcmV0dXJuIHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuZG5DbGllbnRDb25maXJtZWREYXRlIHx8ICcnOw0KICAgIH0sDQogICAgYWNjb3VudENvbmZpcm1lZE5hbWUoKSB7DQogICAgICByZXR1cm4gdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5hY2NvdW50Q29uZmlybWVkTmFtZSB8fCAnJzsNCiAgICB9LA0KICAgIGFjY291bnRDb25maXJtZWREYXRlKCkgew0KICAgICAgcmV0dXJuIHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuYWNjb3VudENvbmZpcm1lZERhdGUgfHwgJyc7DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIC8vIOWIneWni+WMluaXtueahOaTjeS9nA0KICB9LA0KICBtZXRob2RzOiB7DQogICAgdG9nZ2xlVmlzaWJsZSgpIHsNCiAgICAgIHRoaXMudmlzaWJsZSA9ICF0aGlzLnZpc2libGU7DQogICAgfSwNCiAgICBoYW5kbGVGaWxlQWN0aW9uKG1ldGhvZE5hbWUsIHRlbXBsYXRlVHlwZSkgew0KICAgICAgdGhpcy4kZW1pdChtZXRob2ROYW1lLCB0ZW1wbGF0ZVR5cGUpOw0KICAgIH0sDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy4kZW1pdCgnaGFuZGxlU2VsZWN0aW9uQ2hhbmdlJywgc2VsZWN0aW9uKTsNCiAgICB9LA0KICAgIGhhbmRsZUJvb2tpbmdNZXNzYWdlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy4kZW1pdCgnaGFuZGxlQm9va2luZ01lc3NhZ2VVcGRhdGUnLCByb3cpOw0KICAgIH0sDQogICAgYWRkQm9va2luZ01lc3NhZ2UoKSB7DQogICAgICB0aGlzLiRlbWl0KCdoYW5kbGVBZGRCb29raW5nTWVzc2FnZScsIHRoaXMuYm9va2luZ01lc3NhZ2VGb3JtKTsNCiAgICB9LA0KICAgIGJvb2tpbmdNZXNzYWdlQ29uZmlybSgpIHsNCiAgICAgIC8vIOWwhuaTjeS9nOmAmui/h+S6i+S7tuWPkemAgee7meeItue7hOS7tg0KICAgICAgdGhpcy4kZW1pdCgnYm9va2luZ01lc3NhZ2VDb25maXJtJywgdGhpcy5ib29raW5nTWVzc2FnZUZvcm0pOw0KDQogICAgfSwNCiAgICBjbG9zZUJvb2tpbmdNZXNzYWdlKCkgew0KICAgICAgdGhpcy4kZW1pdCgnY2xvc2VCb29raW5nTWVzc2FnZScpOw0KICAgIH0sDQogICAgZGVsZXRlQm9va2luZ01lc3NhZ2Uocm93KSB7DQogICAgICAvLyDpgJrov4fkuovku7blj5HpgIHnu5nniLbnu4Tku7YNCiAgICAgIHRoaXMuJGVtaXQoJ2RlbGV0ZUJvb2tpbmdNZXNzYWdlJywgcm93KTsNCiAgICB9LA0KICAgIGRlbGV0ZUxvZ2lzdGljc0l0ZW0oaXRlbSkgew0KICAgICAgaWYgKHRoaXMucnNDbGllbnRNZXNzYWdlICYmIHRoaXMucnNDbGllbnRNZXNzYWdlLnJzT3BMb2dMaXN0KSB7DQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlLnJzT3BMb2dMaXN0ID0gdGhpcy5yc0NsaWVudE1lc3NhZ2UucnNPcExvZ0xpc3QuZmlsdGVyKGxvZyA9PiBsb2cgIT09IGl0ZW0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgdXBkYXRlTG9naXN0aWNzUHJvZ3Jlc3MoZGF0YSkgew0KICAgICAgaWYgKHRoaXMucnNDbGllbnRNZXNzYWdlKSB7DQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlLnJzT3BMb2dMaXN0ID0gZGF0YTsNCiAgICAgIH0NCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["BillOfLadingInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyWA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BillOfLadingInfo.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div>\r\n    <!--title-->\r\n    <el-row>\r\n      <el-col :span=\"18\">\r\n        <div class=\"service-bar\" style=\"display: flex;margin-top: 10px;margin-bottom: 10px;width: 100%\">\r\n          <a :class=\"{'el-icon-arrow-down':visible,'el-icon-arrow-right':!visible}\"/>\r\n          <div style=\"width:150px;display: flex\">\r\n            <h3 style=\"margin: 0;width: 250px;text-align: left\" @click=\"toggleVisible\">提单信息</h3>\r\n            <el-button style=\"margin-left: 10px;\" type=\"text\" @click=\"$emit('openChargeSelect', rsClientMessage)\">\r\n              [DN...]\r\n            </el-button>\r\n          </div>\r\n\r\n          <el-col v-if=\"auditInfo\"\r\n                  :span=\"15\" style=\"display: flex\"\r\n          >\r\n            <div v-hasPermi=\"['system:booking:opapproval','system:rct:opapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isDnOpConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'op')\"\r\n              >操作确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ opConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ opConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n            <div v-hasPermi=\"['system:booking:salesapproval','system:rct:salesapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isDnSalesConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'sales')\"\r\n              >业务确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ salesConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ salesConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n            <div v-hasPermi=\"['system:booking:clientapproval','system:rct:clientapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isDnClientConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'client')\"\r\n              >客户确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ clientConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ clientConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n            <div v-hasPermi=\"['system:booking:financeapproval','system:rct:financeapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isAccountConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'account', rsClientMessage.rsChargeList)\"\r\n              >财务确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ accountConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ accountConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n\r\n          <div style=\"margin-left: auto\">\r\n            <el-popover\r\n              v-for=\"(item,index) in fileOptions\" :key=\"index\"\r\n              placement=\"top\" trigger=\"click\" width=\"100\"\r\n            >\r\n              <el-button v-for=\"(item2,index) in item.templateList\" :key=\"index\"\r\n                         @click=\"handleFileAction(item.link, item2)\"\r\n              >{{ item2 }}\r\n              </el-button>\r\n              <a slot=\"reference\" style=\"color: blue;padding: 0;margin-left: 10px\" target=\"_blank\"\r\n              >[{{ item.file }}]</a>\r\n            </el-popover>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!--content-->\r\n    <transition name=\"fade\">\r\n      <el-row v-if=\"visible\" :gutter=\"10\" style=\"margin-bottom:15px;display:-webkit-box\">\r\n        <!--主表信息-->\r\n        <transition name=\"fade\">\r\n          <el-col v-if=\"branchInfo\" :span=\"18\">\r\n            <el-table :data=\"bookingMessageList\" border @selection-change=\"handleSelectionChange\">\r\n              <el-table-column\r\n                type=\"selection\"\r\n                width=\"55\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"50\"\r\n              />\r\n              <el-table-column label=\"MB/L No\" prop=\"mBlNo\"/>\r\n              <el-table-column label=\"HB/L No\" prop=\"hBlNo\"/>\r\n              <el-table-column label=\"发货人\" prop=\"bookingShipper\"/>\r\n              <el-table-column label=\"收货人\" prop=\"bookingConsignee\"/>\r\n              <el-table-column label=\"通知人\" prop=\"bookingNotifyParty\"/>\r\n              <el-table-column label=\"代理\" prop=\"bookingAgent\"/>\r\n              <el-table-column label=\"柜号\" prop=\"containerNo\"/>\r\n              <el-table-column label=\"封号\" prop=\"sealNo\"/>\r\n              <el-table-column label=\"柜型\" prop=\"containerType\"/>\r\n              <el-table-column label=\"唛头\" prop=\"shippingMark\"/>\r\n              <el-table-column label=\"件数\" prop=\"packageQuantity\"/>\r\n              <el-table-column label=\"货描\" prop=\"goodsDescription\"/>\r\n              <el-table-column label=\"体积\" prop=\"goodsVolume\"/>\r\n              <el-table-column label=\"重量\" prop=\"grossWeight\"/>\r\n              <el-table-column label=\"提单类型\" prop=\"blTypeCode\"/>\r\n              <el-table-column label=\"出单方式\" prop=\"blFormCode\"/>\r\n              <el-table-column label=\"交单方式\" prop=\"sqdDocDeliveryWay\">\r\n                <template slot-scope=\"scope\">\r\n                  <tree-select :class=\"'disable-form'\" :disabled=\"true\"\r\n                               :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"scope.row.sqdDocDeliveryWay\"\r\n                               :placeholder=\"'货代单交单方式'\"\r\n                               :type=\"'docReleaseWay'\" @return=\"scope.row.sqdDocDeliveryWay=$event\"\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" prop=\"sqdDocDeliveryWay\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" @click=\"handleBookingMessageUpdate(scope.row)\">修改</el-button>\r\n                  <el-button style=\"color: red\" type=\"text\"\r\n                             @click=\"deleteBookingMessage(scope.row)\"\r\n                  >删除\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!--弹出层-->\r\n            <el-dialog\r\n              v-dialogDrag\r\n              v-dialogDragWidth\r\n              :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n              :show-close=\"false\" :title=\"bookingMessageTitle\" @close=\"closeBookingMessage\"\r\n              :visible.sync=\"openBookingMessage\" append-to-body width=\"30%\"\r\n            >\r\n              <el-form ref=\"bookingMessageForm\" :model=\"bookingMessageForm\" class=\"edit\" label-width=\"80px\"\r\n                       style=\"\"\r\n              >\r\n                <div v-if=\"bookingMessageForm.blTypeCode==='MBL'\">\r\n                  <el-form-item label=\"提单号码\">\r\n                    <el-input v-model=\"bookingMessageForm.mBlNo\" style=\"padding: 0;margin: 0;\"/>\r\n                  </el-form-item>\r\n                </div>\r\n                <div v-else>\r\n                  <el-form-item label=\"MB/L No\">\r\n                    <el-input v-model=\"bookingMessageForm.mBlNo\" style=\"padding: 0;margin: 0;\"/>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"HB/L No\">\r\n                    <el-input v-model=\"bookingMessageForm.hBlNo\" style=\"padding: 0;margin: 0;\"/>\r\n                  </el-form-item>\r\n                </div>\r\n                <el-form-item label=\"发货人\">\r\n                  <template slot=\"label\">\r\n                    <div>发货人</div>\r\n                    <el-button style=\"color: blue\" type=\"text\" @click=\"$emit('handleAddCommon', 'release')\">[↗]\r\n                    </el-button>\r\n                    <el-button style=\"color: blue\" type=\"text\" @click=\"$emit('openReleaseUsed')\">[...]</el-button>\r\n                  </template>\r\n                  <el-input v-model=\"bookingMessageForm.bookingShipper\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"收货人\">\r\n                  <el-input v-model=\"bookingMessageForm.bookingConsignee\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"通知人\">\r\n                  <el-input v-model=\"bookingMessageForm.bookingNotifyParty\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"代理\">\r\n                  <el-input v-model=\"bookingMessageForm.bookingAgent\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"启运港\">\r\n                  <el-input v-model=\"bookingMessageForm.polName\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"卸货港\">\r\n                  <el-input v-model=\"bookingMessageForm.podName\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"目的港\">\r\n                  <el-input v-model=\"bookingMessageForm.destinationPort\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"柜号\">\r\n                  <el-input v-model=\"bookingMessageForm.containerNo\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"封号\">\r\n                  <el-input v-model=\"bookingMessageForm.sealNo\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"柜型\">\r\n                  <el-input v-model=\"bookingMessageForm.containerType\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"唛头\">\r\n                  <el-input v-model=\"bookingMessageForm.shippingMark\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"件数\">\r\n                  <el-input v-model=\"bookingMessageForm.packageQuantity\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\"\r\n                            placeholder=\"件数\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"货描\">\r\n                  <el-input v-model=\"bookingMessageForm.goodsDescription\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"重量\">\r\n                  <el-input v-model=\"bookingMessageForm.grossWeight\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\"\r\n                            placeholder=\"重量\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"体积\">\r\n                  <el-input v-model=\"bookingMessageForm.goodsVolume\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\"\r\n                            placeholder=\"体积\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\">\r\n                  <el-input v-model=\"bookingMessageForm.blRemark\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"出单地\">\r\n                  <location-select :load-options=\"psaBookingSelectData.locationOptions\" :no-parent=\"true\"\r\n                                   :pass=\"bookingMessageForm.polIds\" :placeholder=\"'启运港'\"\r\n                                   @returnData=\"bookingMessageForm.city=$event.locationEnShortName\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"开船日期\">\r\n                  <el-date-picker\r\n                    v-model=\"bookingMessageForm.onBoardDate\"\r\n                    placeholder=\"选择日期\"\r\n                    type=\"date\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"付款方式\">\r\n                  <el-select v-model=\"bookingMessageForm.payWay\" placeholder=\"请选择\">\r\n                    <el-option\r\n                      v-for=\"item in payWayOptions\"\r\n                      :key=\"item.value\"\r\n                      :label=\"item.label\"\r\n                      :value=\"item.value\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"提单类型\">\r\n                  <tree-select :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"bookingMessageForm.blTypeCode\"\r\n                               :placeholder=\"'提单类型'\" :type=\"'blType'\"\r\n                               @return=\"bookingMessageForm.blTypeCode=$event\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"出单方式\">\r\n                  <tree-select :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"bookingMessageForm.blFormCode\"\r\n                               :placeholder=\"'出单方式'\" :type=\"'blForm'\"\r\n                               @return=\"bookingMessageForm.blFormCode=$event\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"交单方式\">\r\n                  <tree-select :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"bookingMessageForm.sqdDocDeliveryWay\"\r\n                               :placeholder=\"'货代单交单方式'\"\r\n                               :type=\"'docReleaseWay'\" @return=\"bookingMessageForm.sqdDocDeliveryWay=$event\"\r\n                  />\r\n                </el-form-item>\r\n              </el-form>\r\n              <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button size=\"mini\" type=\"primary\" @click=\"bookingMessageConfirm\">确 定</el-button>\r\n                <el-button size=\"mini\" @click=\"closeBookingMessage\">取 消</el-button>\r\n              </div>\r\n            </el-dialog>\r\n\r\n            <el-button :disabled=\"psaVerify || disabled\"\r\n                       style=\"padding: 0\"\r\n                       type=\"text\"\r\n                       @click=\"addBookingMessage\"\r\n            >[＋]\r\n            </el-button>\r\n          </el-col>\r\n        </transition>\r\n\r\n        <!--物流进度-->\r\n        <transition name=\"fade\">\r\n          <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n            <el-form-item label=\"进度需求\" prop=\"goodsNameSummary\"/>\r\n            <div>\r\n              <logistics-progress :disabled=\"rsClientMessageFormDisable || disabled || psaVerify\"\r\n                                  :logistics-progress-data=\"rsClientMessage.rsOpLogList\"\r\n                                  :open-logistics-progress-list=\"true\"\r\n                                  @deleteItem=\"deleteLogisticsItem\"\r\n                                  @return=\"updateLogisticsProgress\"\r\n              />\r\n            </div>\r\n          </el-col>\r\n        </transition>\r\n\r\n        <!--费用列表-->\r\n        <transition name=\"fade\">\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.5\">\r\n            <charge-list :a-t-d=\"form.podEta\" :charge-data=\"rsClientMessage.rsChargeList\"\r\n                         :company-list=\"companyList\"\r\n                         :disabled=\"rsClientMessageFormDisable || disabled\"\r\n                         :is-receivable=\"true\"\r\n                         :open-charge-list=\"true\"\r\n                         :rs-client-message-payable-r-m-b=\"rsClientMessagePayableRMB\"\r\n                         :rs-client-message-payable-tax-r-m-b=\"rsClientMessagePayableTaxRMB\"\r\n                         :rs-client-message-payable-tax-u-s-d=\"rsClientMessagePayableTaxUSD\"\r\n                         :rs-client-message-payable-u-s-d=\"rsClientMessagePayableUSD\"\r\n                         :rs-client-message-profit-r-m-b=\"rsClientMessageProfitRMB\"\r\n                         :rs-client-message-profit-tax-r-m-b=\"rsClientMessageProfitTaxRMB\"\r\n                         :rs-client-message-profit-tax-u-s-d=\"rsClientMessageProfitTaxUSD\"\r\n                         :rs-client-message-profit-u-s-d=\"rsClientMessageProfitUSD\"\r\n                         :rs-client-message-receivable-r-m-b=\"rsClientMessageReceivableRMB\"\r\n                         :rs-client-message-receivable-tax-r-m-b=\"rsClientMessageReceivableTaxRMB\"\r\n                         :rs-client-message-receivable-tax-u-s-d=\"rsClientMessageReceivableTaxUSD\"\r\n                         :rs-client-message-receivable-u-s-d=\"rsClientMessageReceivableUSD\"\r\n                         @copyFreight=\"$emit('copyFreight', $event)\"\r\n                         @deleteAll=\"rsClientMessage.rsChargeList=[]\"\r\n                         @deleteItem=\"rsClientMessage.rsChargeList=rsClientMessage.rsChargeList.filter(item=>{return item!=$event})\"\r\n                         @return=\"$emit('rsClientMessageCharge', $event)\"\r\n                         @returnProfit=\"$emit('handleProfit', $event)\"\r\n                         @selectRow=\"$emit('handleReceiveSelected', $event)\"\r\n            />\r\n          </el-col>\r\n        </transition>\r\n      </el-row>\r\n    </transition>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport LogisticsProgress from \"../logisticsProgress\";\r\nimport ChargeList from \"../chargeList\";\r\nimport TreeSelect from \"@/components/TreeSelect\";\r\nimport LocationSelect from \"@/components/LocationSelect\";\r\n\r\nexport default {\r\n  name: \"BillOfLadingInfo\",\r\n  components: {\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    TreeSelect,\r\n    LocationSelect\r\n  },\r\n  props: {\r\n    bookingMessageForm: {\r\n      type: Object,\r\n      required: true,\r\n      default: null\r\n    },\r\n    openBookingMessage: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    bookingMessageStatus: {\r\n      type: String,\r\n      default: \"<UNK>\"\r\n    },\r\n    bookingMessageList: {\r\n      type: Array,\r\n      default: []\r\n    },\r\n    rsClientMessage: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    form: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    rsClientMessageReceivableRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageReceivableUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageReceivableTaxRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageReceivableTaxUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableTaxRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableTaxUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitTaxRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitTaxUSD: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      visible: true,\r\n      bookingMessageTitle: \"提单信息\",\r\n      psaBookingSelectData: {\r\n        locationOptions: []\r\n      },\r\n      rsClientMessageFormDisable: false,\r\n      fileOptions: [\r\n        {file: '操作单', link: 'getOpBill', templateList: ['整柜', '散货', '空运', '其他']},\r\n        {file: '提单', link: 'getBillOfLading', templateList: ['套打提单', '电放提单']},\r\n        {\r\n          file: '费用清单',\r\n          link: 'getChargeListBill',\r\n          templateList: ['CN-广州瑞旗[招行USD+工行RMB]', 'CN-广州瑞旗[USD->RMB]', 'EN-广州瑞旗[招行USD]', 'EN-广州瑞旗[RMB->USD]', 'EN- 瑞旗香港账户[HSBC RMB->USD]', 'EN- 香港瑞旗[HSBC]', 'CN-广州正泽[招行USD+RMB]', 'CN-广州正泽[USD->RMB]']\r\n        }\r\n      ],\r\n      payWayOptions: [\r\n        {label: '预付', value: 'FREIGHTP REPAID'},\r\n        {label: '到付', value: 'FREIGHTP COLLECT'}\r\n      ]\r\n    };\r\n  },\r\n  computed: {\r\n    rsClientServiceInstance() {\r\n      return this.rsClientMessage || {};\r\n    },\r\n    opConfirmedName() {\r\n      return this.rsClientServiceInstance.dnOpConfirmedName || '';\r\n    },\r\n    opConfirmedDate() {\r\n      return this.rsClientServiceInstance.dnOpConfirmedDate || '';\r\n    },\r\n    salesConfirmedName() {\r\n      return this.rsClientServiceInstance.dnSalesConfirmedName || '';\r\n    },\r\n    salesConfirmedDate() {\r\n      return this.rsClientServiceInstance.dnSalesConfirmedDate || '';\r\n    },\r\n    clientConfirmedName() {\r\n      return this.rsClientServiceInstance.dnClientConfirmedName || '';\r\n    },\r\n    clientConfirmedDate() {\r\n      return this.rsClientServiceInstance.dnClientConfirmedDate || '';\r\n    },\r\n    accountConfirmedName() {\r\n      return this.rsClientServiceInstance.accountConfirmedName || '';\r\n    },\r\n    accountConfirmedDate() {\r\n      return this.rsClientServiceInstance.accountConfirmedDate || '';\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化时的操作\r\n  },\r\n  methods: {\r\n    toggleVisible() {\r\n      this.visible = !this.visible;\r\n    },\r\n    handleFileAction(methodName, templateType) {\r\n      this.$emit(methodName, templateType);\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.$emit('handleSelectionChange', selection);\r\n    },\r\n    handleBookingMessageUpdate(row) {\r\n      this.$emit('handleBookingMessageUpdate', row);\r\n    },\r\n    addBookingMessage() {\r\n      this.$emit('handleAddBookingMessage', this.bookingMessageForm);\r\n    },\r\n    bookingMessageConfirm() {\r\n      // 将操作通过事件发送给父组件\r\n      this.$emit('bookingMessageConfirm', this.bookingMessageForm);\r\n\r\n    },\r\n    closeBookingMessage() {\r\n      this.$emit('closeBookingMessage');\r\n    },\r\n    deleteBookingMessage(row) {\r\n      // 通过事件发送给父组件\r\n      this.$emit('deleteBookingMessage', row);\r\n    },\r\n    deleteLogisticsItem(item) {\r\n      if (this.rsClientMessage && this.rsClientMessage.rsOpLogList) {\r\n        this.rsClientMessage.rsOpLogList = this.rsClientMessage.rsOpLogList.filter(log => log !== item);\r\n      }\r\n    },\r\n    updateLogisticsProgress(data) {\r\n      if (this.rsClientMessage) {\r\n        this.rsClientMessage.rsOpLogList = data;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document.scss';\r\n</style>\r\n"]}]}