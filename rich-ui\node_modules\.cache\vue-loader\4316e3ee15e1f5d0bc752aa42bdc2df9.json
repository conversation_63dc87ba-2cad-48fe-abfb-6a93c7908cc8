{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\op.vue?vue&type=template&id=4fcf148f&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\op.vue", "mtime": 1754646305892}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}